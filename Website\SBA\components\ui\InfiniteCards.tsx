'use client';
import Image from 'next/image';
import React, { useEffect, useRef, useState } from 'react';

import { cn } from '@/lib/utils';

interface InfiniteMovingCardsProps {
  logos: {
    imgSrc: string;
    imgClassname?: string;
    alt?: string;
  }[];
  direction?: 'left' | 'right';
  speed?: 'fast' | 'normal' | 'slow';
  pauseOnHover?: boolean;
  className?: string;
}

export const InfiniteMovingCards: React.FC<InfiniteMovingCardsProps> = ({
  logos,
  direction = 'left',
  speed = 'fast',
  pauseOnHover = true,
  className,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollerRef = useRef<HTMLUListElement>(null);
  const [start, setStart] = useState(false);

  useEffect(() => {
    // Set animation properties immediately
    if (containerRef.current) {
      const animationDirection = direction === 'left' ? 'forwards' : 'reverse';
      const duration = speed === 'fast' ? '20s' : speed === 'normal' ? '40s' : '80s';

      containerRef.current.style.setProperty('--animation-direction', animationDirection);
      containerRef.current.style.setProperty('--animation-duration', duration);
    }

    // Small delay to ensure DOM is fully ready
    const timer = setTimeout(() => {
      initializeCarousel();
    }, 10);

    return () => clearTimeout(timer);
  }, [logos, direction, speed]);

  const initializeCarousel = () => {
    if (!scrollerRef.current) return;

    // Clear any existing cloned elements first
    const originalItems = Array.from(scrollerRef.current.children).slice(0, logos.length);
    scrollerRef.current.innerHTML = '';

    // Add original items back
    originalItems.forEach(item => {
      scrollerRef.current?.appendChild(item);
    });

    // Then add clones
    originalItems.forEach(item => {
      const clone = item.cloneNode(true) as HTMLElement;
      scrollerRef.current?.appendChild(clone);
    });

    // Make sure we have enough content to scroll
    // If needed, add another set of clones for longer content
    if (scrollerRef.current.scrollWidth < window.innerWidth * 2) {
      originalItems.forEach(item => {
        const clone = item.cloneNode(true) as HTMLElement;
        scrollerRef.current?.appendChild(clone);
      });
    }

    // Start animation and make visible
    setStart(true);
    if (containerRef.current) {
      containerRef.current.style.visibility = 'visible';
    }
  };

  return (
    <section className='mb-16 mt-8 lg:mt-20 2xl:mt-0'>
      <div className='flex flex-col'>
        <h2 className='z-50 mx-auto mb-3 max-w-xs text-center text-xs font-normal text-white/70 sm:mb-4 sm:max-w-sm sm:text-sm md:mb-5 md:max-w-md md:text-base lg:max-w-lg lg:text-lg xl:text-xl'>
          Invisible to all Screen Sharing platforms and Browser Based tests.
        </h2>

        <div>
          <div
            ref={containerRef}
            style={{ visibility: 'hidden' }}
            className={cn(
              'scroller relative z-20 max-w-6xl overflow-hidden [mask-image:linear-gradient(to_right,transparent,white_20%,white_80%,transparent)]',
              className
            )}
          >
            <ul
              ref={scrollerRef}
              className={cn(
                'flex min-w-full shrink-0 gap-6 py-12 w-max flex-nowrap',
                start && 'animate-scroll',
                pauseOnHover && 'hover:[animation-play-state:paused]'
              )}
            >
              {logos.map((item, idx) => (
                <li key={idx} className='flex items-center justify-center'>
                  <div className='relative mx-8'>
                    <Image
                      src={item.imgSrc}
                      alt={item.alt || 'company logo'}
                      aria-description='company logos'
                      className='user-select-none pointer-events-none h-16 w-[160px] object-contain grayscale'
                      width={160}
                      height={64}
                      priority={true}
                      loading='eager'
                    />
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
};

export default InfiniteMovingCards;