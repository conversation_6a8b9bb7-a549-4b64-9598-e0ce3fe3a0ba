import { getAuth } from "@clerk/nextjs/server";
import { NextResponse, NextRequest } from "next/server";

import connectDB from "@/lib/mongodb";
import { User } from "@/model/User";

export async function DELETE(request: NextRequest) {
  try {
    // Get the authenticated user's ID from Clerk
    const { userId } = getAuth(request);

    if (!userId) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Ensure MongoDB connection is established
    await connectDB();
    
    console.log(`Attempting to delete user with clerkId: ${userId}`);
    
    // Verify user exists before deletion
    const userExists = await User.findOne({ clerkId: userId });
    
    if (!userExists) {
      console.log(`No user found with clerkId: ${userId}`);
      return NextResponse.json({ message: "User not found in database" }, { status: 404 });
    }
    
    console.log(`Found user to delete: ${userExists._id} (clerkId: ${userId})`);
    
    // Perform deletion with force option
    const deletedUser = await User.findOneAndDelete({ clerkId: userId });
    
    if (!deletedUser) {
      console.error(`Failed to delete user with clerkId: ${userId} despite finding them`);
      return NextResponse.json({ message: "Failed to delete user" }, { status: 500 });
    }

    console.log(`Successfully deleted user with ID: ${deletedUser._id} and clerkId: ${userId}`);
    
    // Confirm deletion by checking if user still exists
    const confirmDeletion = await User.findOne({ clerkId: userId });
    if (confirmDeletion) {
      console.error(`User still exists after deletion attempt: ${userId}`);
      return NextResponse.json({ message: "Failed to delete user from database" }, { status: 500 });
    }
    
    return NextResponse.json({ message: "Account deleted successfully" }, { status: 200 });
  } catch (error) {
    console.error("Delete account error:", error);
    return NextResponse.json({ 
      message: "Internal server error", 
      error: error instanceof Error ? error.message : String(error) 
    }, { status: 500 });
  }
}