"use client";

import { useAuth, useSignIn } from '@clerk/nextjs';
import { motion } from 'framer-motion';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect } from 'react';
import { FiArrowRight, FiLock } from 'react-icons/fi';

export default function ResetPasswordPage() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const { isSignedIn } = useAuth();
    const { isLoaded } = useSignIn();

    // Get code and email from URL parameters
    const code = searchParams?.get('code') || '';
    const email = searchParams?.get('email') || '';

    // Redirect if user is already signed in
    useEffect(() => {
        if (isSignedIn) {
            router.push('/dashboard');
        }
    }, [isSignedIn, router]);

    // If code and email are in the URL, redirect to the forgot-password page with the parameters
    useEffect(() => {
        if (code && email && isLoaded) {
            // Navigate to forgot-password page with the verification code pre-filled
            router.push(`/forgot-password?code=${code}&email=${email}`);
        }
    }, [code, email, isLoaded, router]);

    if (!isLoaded) {
        return (
            <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-900">
                <div className="size-16 animate-spin rounded-full border-4 border-blue-600 border-t-transparent"></div>
            </div>
        );
    }

    return (
        <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-900 px-4 text-white">
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="relative w-full max-w-md overflow-hidden"
            >
                {/* Abstract design elements */}
                <div className="absolute right-0 top-0 -z-10 size-64 -translate-y-1/2 translate-x-1/2 rounded-full bg-blue-500/10 blur-3xl"></div>
                <div className="bg-purple-500/10 absolute bottom-0 left-0 -z-10 size-64 -translate-x-1/2 translate-y-1/2 rounded-full blur-3xl"></div>

                <div className="overflow-hidden rounded-2xl border border-gray-800 bg-black/60 shadow-2xl backdrop-blur-sm">
                    <div className="p-8">
                        <div className="mb-8 text-center">
                            <h2 className="mb-2 text-2xl font-bold text-white">Reset Password</h2>
                            <p className="text-sm text-gray-400">
                                {code && email ? "Redirecting to reset password form..." : "To reset your password, you'll need a verification code"}
                            </p>
                        </div>

                        {!code || !email ? (
                            <div className="space-y-6 text-center">
                                <div className="rounded-lg border border-gray-700/50 bg-gray-800/50 p-6">
                                    <FiLock className="mx-auto mb-4 size-12 text-blue-400" />
                                    <p className="mb-4 text-gray-300">
                                        To reset your password, please go to the forgot password page where you can request a verification code.
                                    </p>
                                    <a
                                        href="/forgot-password"
                                        className="inline-flex items-center justify-center rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 px-5 py-3 font-medium text-white transition duration-200 hover:-translate-y-px hover:from-blue-700 hover:to-indigo-700"
                                    >
                                        Go to forgot password <FiArrowRight className="ml-2" />
                                    </a>
                                </div>
                            </div>
                        ) : (
                            <div className="flex justify-center">
                                <div className="inline-flex animate-pulse items-center">
                                    <div className="mr-1 size-2.5 rounded-full bg-blue-400"></div>
                                    <div className="animation-delay-200 mr-1 size-2.5 rounded-full bg-blue-400"></div>
                                    <div className="animation-delay-500 size-2.5 rounded-full bg-blue-400"></div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                <p className="mt-6 text-center text-xs text-gray-500">
                    © {new Date().getFullYear()} Interview Cracker. All rights reserved.
                </p>
            </motion.div>
        </div>
    );
}