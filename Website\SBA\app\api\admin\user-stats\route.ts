import { getAuth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";

import connectDB from "@/lib/mongodb";
import { User } from "@/model/User";

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  try {
    const auth = getAuth(req);
    const { userId } = auth;
    
    if (!userId) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    await connectDB();
    
    const adminUser = await User.findOne({ clerkId: userId });
    if (!adminUser || !adminUser.isAdmin) {
      return NextResponse.json({ message: "Unauthorized: Admin access required" }, { status: 403 });
    }
    
    // Get user statistics
    const totalUsers = await User.countDocuments();
    const premiumUsers = await User.countDocuments({ subscriptionType: "premium" });
    const freeUsers = totalUsers - premiumUsers;
    
    // Get plan breakdown for premium users
    const monthlyPlanUsers = await User.countDocuments({ 
      subscriptionType: "premium",
      subscriptionPlan: "monthly"
    });
    
    const yearlyPlanUsers = await User.countDocuments({ 
      subscriptionType: "premium",
      subscriptionPlan: "yearly"
    });
    
    const weeklyPlanUsers = await User.countDocuments({ 
      subscriptionType: "premium",
      subscriptionPlan: "weekly"
    });

    return NextResponse.json({
      totalUsers,
      premiumUsers,
      freeUsers,
      monthlyPlanUsers,
      yearlyPlanUsers,
      weeklyPlanUsers,
      status: 'success'
    });
  } catch (error) {
    console.error("Admin stats error:", error);
    return NextResponse.json({ 
      message: "Internal server error", 
      error: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 });
  }
}