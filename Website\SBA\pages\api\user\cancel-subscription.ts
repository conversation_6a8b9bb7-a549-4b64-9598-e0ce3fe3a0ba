import { getAuth } from '@clerk/nextjs/server';
import { NextApiRequest, NextApiResponse } from 'next';

import { sendCancellationConfirmation } from '@/lib/email';
import connectDB from '@/lib/mongodb';
import { Payment } from '@/model/Payment';
import { User } from '@/model/User';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { userId } = getAuth(req);
  if (!userId) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    await connectDB();
    const user = await User.findOne({ clerkId: userId });
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const currentPlan = user.subscriptionPlan;
    user.subscriptionType = 'free';
    user.subscriptionPlan = '';
    user.promptsUsed = 0; // Reset prompts used to 0
    // Remove dates instead of updating them
    user.subscriptionStartDate = undefined;
    user.subscriptionEndDate = undefined;
    await user.save();

    // Create a cancellation record in the payment history
    const timestamp = Date.now();
    const cancellationRecord = new Payment({
      user: userId,
      paymentId: `cancel-${timestamp}`,
      orderId: `cancel-${timestamp}`,
      amount: 0, // No charge for cancellation
      currency: "USD", // Default currency
      status: "cancelled", 
      method: "cancellation",
      plan: currentPlan || "subscription",
      createdAt: new Date()
    });
    
    await cancellationRecord.save();

    try {
      await sendCancellationConfirmation(user.email, user.name || 'Valued Customer', currentPlan || 'premium', new Date());
    } catch (emailError) {
      console.error('Failed to send cancellation confirmation email:', emailError);
    }

    return res.status(200).json({
      success: true,
      message: 'Subscription cancelled successfully',
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        subscriptionEndDate: user.subscriptionEndDate,
        subscriptionType: user.subscriptionType,
        subscriptionPlan: user.subscriptionPlan,
        promptsUsed: user.promptsUsed || 0,
      }
    });
  } catch (error) {
    console.error('Error cancelling subscription:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}