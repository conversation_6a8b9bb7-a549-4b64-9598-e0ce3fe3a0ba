"use client";

import { useAuth } from "@clerk/nextjs";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Card, CardBody } from "@nextui-org/react";
import { AlertTriangle } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

export default function AccountDeletionSection() {
    const [isOpen, setIsOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const router = useRouter();
    const { signOut } = useAuth();

    const handleDelete = async () => {
        setIsLoading(true);
        try {
            const response = await fetch("/api/user/delete-account", {
                method: "DELETE",
                headers: { "Content-Type": "application/json" },
            });

            if (response.ok) {
                toast.success("Account deleted successfully");
                await signOut(() => {
                    router.push("/");
                    router.refresh();
                });
            } else {
                const errorData = await response.json();
                toast.error(errorData.message || "Failed to delete account");
                setIsLoading(false);
            }
        } catch (error) {
            console.error("Delete account error:", error);
            toast.error("An unexpected error occurred");
            setIsLoading(false);
        }
    };

    return (
        <Card className="border border-slate-800/60 bg-[#0f172a] shadow-xl">
            <CardBody className="p-6">
                <h2 className="text-18-bold mb-4 flex items-center gap-2 text-red-400">
                    <span className="text-red-400">
                        <AlertTriangle size={18} />
                    </span>
                    Danger Zone
                </h2>

                <div className="mb-5 rounded-md border border-red-900/40 bg-[#1a141f] p-4">
                    <p className="text-14-medium mb-2 text-gray-300">
                        Deleting your account will permanently remove all your data from our system.
                    </p>
                    <p className="text-14-regular text-gray-400">
                        This action is irreversible and you will lose access to all your saved information.
                    </p>
                </div>

                <Button
                    color="danger"
                    onClick={() => setIsOpen(true)}
                    className="bg-red-700/20 text-red-400 transition-all duration-200 hover:bg-red-700/40"
                >
                    Delete My Account
                </Button>

                <Modal
                    isOpen={isOpen}
                    onClose={() => setIsOpen(false)}
                    backdrop="blur"
                    classNames={{
                        base: "bg-[#0f172a] border border-slate-800/60 shadow-xl",
                        header: "border-b border-slate-800/60",
                        footer: "border-t border-slate-800/60",
                    }}
                >
                    <ModalContent>
                        <ModalHeader className="text-white">
                            <div className="flex items-center gap-2">
                                <AlertTriangle className="text-red-400" size={20} />
                                <span>Confirm Account Deletion</span>
                            </div>
                        </ModalHeader>
                        <ModalBody>
                            <div className="py-4">
                                <p className="text-14-medium mb-3 text-gray-300">
                                    Are you sure you want to delete your account? This action cannot be undone.
                                </p>
                                <p className="text-14-regular text-gray-400">
                                    All your data, including profile information, payment history, and account details will be permanently removed.
                                </p>
                            </div>
                        </ModalBody>
                        <ModalFooter>
                            <Button
                                color="default"
                                onClick={() => setIsOpen(false)}
                                className="bg-[#181f33] text-white transition-all duration-200 hover:bg-[#232a40]"
                            >
                                Cancel
                            </Button>
                            <Button
                                color="danger"
                                onClick={handleDelete}
                                isLoading={isLoading}
                                className="bg-red-600 text-white transition-all duration-200 hover:bg-red-700"
                                spinner={
                                    <div className="size-5 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                                }
                            >
                                Delete Permanently
                            </Button>
                        </ModalFooter>
                    </ModalContent>
                </Modal>
            </CardBody>
        </Card>
    );
}