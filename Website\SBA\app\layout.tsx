import type { <PERSON>ada<PERSON>, Viewport } from 'next';
// eslint-disable-next-line camelcase
import { DM_Sans, Inter } from 'next/font/google';
import Script from 'next/script';
import { Toaster } from 'sonner';

import FooterExp from '@/components/FooterExp';
import NavbarComponent from '@/components/ui/Navbar';
import { UserContextProvider } from '@/context/UserContext';

import './globals.css';
import { Providers } from './nextuiProvider';
import { ThemeProvider } from './provider';

// eslint-disable-next-line import/order
import { GoogleAnalytics } from '@next/third-parties/google';
// eslint-disable-next-line import/order
import { ClerkProvider } from '@clerk/nextjs';

const dmSans = DM_Sans({ subsets: ['latin'], weight: ['400', '500', '700'] });
const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
});

export const metadata: Metadata = {
  metadataBase: new URL('https://www.interviewcracker.in'),
  title: {
    template: '%s | Interview Cracker',
    default: 'Interview Cracker - Ace Your Technical Interviews',
  },
  description:
    'Interview Cracker provides AI-powered tool to help you excel in technical interviews for software engineering and developer positions.',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://www.interviewcracker.in',
    siteName: 'Interview Cracker',
    title: 'Interview Cracker - Ace Your Technical Interviews',
    description:
      'Interview Cracker provides AI-powered tool to help you excel in technical interviews for software engineering and developer positions.',
    images: [
      {
        url: '/main.png',
        width: 1200,
        height: 627,
        alt: 'Interview Cracker - Ace Your Technical Interviews',
        type: 'image/png',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@Interview_Cracker',
    creator: '@Interview_Cracker',
    title: 'Interview Cracker - Ace Your Technical Interviews',
    description: 'Interview Cracker provides AI-powered tool to help you excel in technical interviews for software engineering and developer positions.',
    images: '/main.png',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: 'https://www.interviewcracker.in',
    languages: {
      'en-US': 'https://www.interviewcracker.in',
      'en-IN': 'https://www.interviewcracker.in',
    },
  },
  icons: {
    icon: [
      { url: '/favicon.ico' },
      { url: '/icon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/icon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-icon.png' },
      { url: '/apple-icon-180x180.png', sizes: '180x180', type: 'image/png' },
    ],
  },
  category: 'technology',
};

// Export viewport configuration correctly
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
      <html lang="en" className={`${dmSans.className} ${inter.variable}`} suppressHydrationWarning>
        <head>
          {/* Google Tag Manager */}
          <Script id="google-tag-manager" strategy="afterInteractive">
            {`(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
          new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
          j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
          'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','GTM-WX84V8GZ');`}
          </Script>
          <meta name="yandex-verification" content="4612fa7caf7ddff6" />
          <link rel="icon" href="/logo.png" sizes="any" />
          <link rel="manifest" href="/manifest.json" />
          <meta name="theme-color" content="#121212" />

          <link rel="preconnect" href="https://fonts.googleapis.com" />
          <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

          <Script
            id="twitter-conversion-tracking"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `
              !function(e,t,n,s,u,a){e.twq||(s=e.twq=function(){s.exe?s.exe.apply(s,arguments):s.queue.push(arguments);
              },s.version='1.1',s.queue=[],u=t.createElement(n),u.async=!0,u.src='https://static.ads-twitter.com/uwt.js',
              a=t.getElementsByTagName(n)[0],a.parentNode.insertBefore(u,a))}(window,document,'script');
              twq('config','p9l1d');
            `,
            }}
          />
          <Script id="schema-structured-data" type="application/ld+json">
            {`
            {
              "@context": "https://schema.org",
              "@type": "WebSite",
              "name": "Interview Cracker",
              "url": "https://www.interviewcracker.in",
              "description": "Interview Cracker provides AI-powered tools and resources to help you excel in technical interviews.",
              "potentialAction": {
                "@type": "SearchAction",
                "target": "https://www.interviewcracker.in/search?q={search_term_string}",
                "query-input": "required name=search_term_string"
              }
            }
          `}
          </Script>
          <Script id="organization-structured-data" type="application/ld+json">
            {`
            {
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "Interview Cracker",
              "url": "https://www.interviewcracker.in",
              "logo": "https://www.interviewcracker.in/logo.png",
              "sameAs": [
                "https://twitter.com/Interview_Cracker",
                "https://www.linkedin.com/company/interview-cracker",
                "https://www.github.com/interview-cracker"
              ]
            }
          `}
          </Script>

        </head>
        <body className="antialiased" suppressHydrationWarning>
          {/* Google Tag Manager (noscript) */}
          <noscript>
            <iframe
              src="https://www.googletagmanager.com/ns.html?id=GTM-WX84V8GZ"
              height="0"
              width="0"
              style={{ display: 'none', visibility: 'hidden' }}
            ></iframe>
          </noscript>
          <ThemeProvider
            attribute="class"
            defaultTheme="dark"
            forcedTheme="dark"
            disableTransitionOnChange
          >
            <UserContextProvider>
              <Providers>
                <NavbarComponent />
                {children}
                <GoogleAnalytics gaId="G-41XE35T7MP" />
                <Toaster />
                <FooterExp />
              </Providers>
            </UserContextProvider>
          </ThemeProvider>
        </body>
      </html>
    </ClerkProvider>
  );
}