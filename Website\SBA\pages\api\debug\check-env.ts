import { NextApiRequest, NextApiResponse } from "next";

// This endpoint should only be accessible in development mode
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (process.env.NODE_ENV !== "development") {
    return res.status(403).json({ message: "This endpoint is only available in development mode" });
  }

  // Check essential environment variables
  const envStatus = {
    BREVO_API_KEY: !!process.env.BREVO_API_KEY ? "✅ Set" : "❌ Missing",
    BREVO_SENDER_EMAIL: !!process.env.BREVO_SENDER_EMAIL ? "✅ Set" : "❌ Missing", 
    MONGODB_URI: !!process.env.MONGODB_URI ? "✅ Set" : "❌ Missing",
    NEXTAUTH_SECRET: !!process.env.NEXTAUTH_SECRET ? "✅ Set" : "❌ Missing",
    NEXTAUTH_URL: !!process.env.NEXTAUTH_URL ? "✅ Set" : "❌ Missing",
  };

  return res.status(200).json({ 
    message: "Environment Variable Status",
    status: envStatus
  });
}