'use client';
import { Accordion, AccordionItem } from '@nextui-org/react';
import Image from 'next/image';
import Link from 'next/link';

export default function FAQ() {
  const faqItems = [
    {
      question: "Does the app really work with current Zoom versions?",
      answer: "Yes, absolutely! All you have to do is enable Advanced capture with window filtering in Zoom settings.",
      image: "/guide.png", // Adjust the path to your image
    },
    {
      question: "Will you ever leak my information to companies?",
      answer: "No, we prioritize your privacy and do not share your information with any companies.",
    },
    {
      question: "Is your service free?",
      answer: "We offer both free and premium plans. Check our <a href='/pricing'>pricing page</a> for more details.",
    },
    {
      question: "How is your app undetectable?",
      answer: "Our app is undetectable because it is always on top of your windows, but never in focus. Additionally, undetectable keyboard shortcuts for moving the app window around and being able to generate solutions using shortcuts allows our app to stay undetected.",
    },
    {
      question: "What types of interviews do you support?",
      answer: "Currently we're all in on technical interviews for software engineers, data scientists, data analysts, data engineers. As demand grows, we will consider more interview types.",
    },
    {
      question: "Why does this app exist?",
      answer: "We created this app to help candidates perform better in technical interviews by providing real-time assistance.",
    },
  ];

  return (
    <div className='my-12 bg-black pb-14 md:mb-20 md:mt-16 md:py-16 lg:my-24 lg:mt-20'>
      <h2 className='text-pretty text-center text-4xl font-semibold leading-relaxed text-white lg:text-5xl'>
        FAQs
      </h2>
      <Accordion
        className='mt-12 md:mt-16 lg:mt-20 xl:mt-24'
        variant='light'
        defaultExpandedKeys={['1']}
        itemClasses={{
          base: 'border-b border-gray-700 py-2', // Subtle border between items
          title: 'text-white font-semibold text-lg', // Bold white titles
          content: 'text-gray-300 text-base', // Lighter gray content
          trigger: 'py-3', // Adjust padding for trigger
        }}
      >
        {faqItems.map((item, index) => (
          <AccordionItem
            key={index + 1}
            aria-label={`Accordion ${index + 1}`}
            title={item.question}
            className='text-base'
          >
            <div>
              {/* Render the answer with links */}
              <p
                className='text-base'
                dangerouslySetInnerHTML={{
                  __html: item.answer.replace(
                    /<a href='(.*?)'>(.*?)<\/a>/g,
                    (match, href, text) =>
                      `<a href="${href}" class="text-blue-400 hover:underline">${text}</a>`
                  ),
                }}
              />
              {/* Render the image if it exists for this item */}
              {item.image && (
                <div className='mt-4'>
                  <Image
                    src={item.image}
                    alt='Zoom settings window showing Advanced capture with window filtering'
                    width={600}
                    height={400}
                    className='h-auto w-full rounded-md'
                  />
                </div>
              )}
            </div>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
}