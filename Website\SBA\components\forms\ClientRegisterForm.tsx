"use client";

import { useSignUp } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useState, useEffect, useRef } from "react";
import { toast } from "sonner";

interface ClientRegisterFormProps {
  onLoading: (loading: boolean) => void;
}

export const ClientRegisterForm = ({ onLoading }: ClientRegisterFormProps) => {
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();
  const { signUp } = useSignUp();
  const captchaRef = useRef<HTMLDivElement>(null);

  // Add effect to ensure the CAPTCHA container is ready when Clerk tries to find it
  useEffect(() => {
    // Give Clerk some time to initialize before trying to use CAPTCHA
    const timer = setTimeout(() => {
      // Force Clerk to check for the CAPTCHA element again
      if (window.Clerk) {
        try {
          window.Clerk.mountCaptcha && window.Clerk.mountCaptcha(captchaRef.current);
        } catch (e) {
          console.error("Failed to mount CAPTCHA:", e);
        }
      }
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!signUp) return; // Guard against undefined signUp
    onLoading(true);
    setError("");

    try {
      // Create the signup with only the required fields - email and password
      // No attempts to update user attributes that might cause API errors
      await signUp.create({
        emailAddress: email,
        password,
      });

      // Prepare email verification
      await signUp.prepareEmailAddressVerification({
        strategy: "email_code",
      });

      // Pass fullName as a URL parameter to be handled after verification
      toast.success("Please check your email for the verification code.");
      router.push(`/verify-email?email=${encodeURIComponent(email)}&fullName=${encodeURIComponent(fullName)}`);
    } catch (err: any) {
      console.error("Registration error:", err);
      setError(err.errors?.[0]?.message || "An error occurred during registration");
      onLoading(false);
      toast.error(err.errors?.[0]?.message || "An error occurred during registration");
    }
  };

  const handleGoogleSignUp = async () => {
    if (!signUp) return;
    onLoading(true);
    try {
      await signUp.authenticateWithRedirect({
        strategy: "oauth_google",
        redirectUrl: "/sso-callback",
        redirectUrlComplete: "/dashboard",
      });
    } catch (err: any) {
      setError(err.errors?.[0]?.message || "Failed to sign up with Google");
      onLoading(false);
      toast.error(err.errors?.[0]?.message || "Failed to sign up with Google");
    }
  };

  return (
    <div className="space-y-4">
      <form onSubmit={handleSubmit} className="space-y-4">
        <input
          type="text"
          value={fullName}
          onChange={(e) => setFullName(e.target.value)}
          placeholder="Full Name"
          className="w-full rounded border p-2"
          required
          id="fullName"
          name="fullName"
          autoComplete="name"
        />
        <input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Email"
          className="w-full rounded border p-2"
          required
          id="email"
          name="email"
          autoComplete="email"
        />
        <div className="relative">
          <input
            type={showPassword ? "text" : "password"}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Password"
            className="w-full rounded border p-2 pr-10"
            required
            id="password"
            name="password"
            autoComplete="new-password"
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500"
            aria-label={showPassword ? "Hide password" : "Show password"}
          >
            {showPassword ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="size-5"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="size-5"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                <line x1="1" y1="1" x2="23" y2="23"></line>
              </svg>
            )}
          </button>
        </div>

        {/* Clerk CAPTCHA container - with proper ref and ID */}
        <div
          id="clerk-captcha"
          ref={captchaRef}
          data-cl-theme="light"
          className="my-4"
        ></div>

        <button
          type="submit"
          className="w-full rounded bg-green-500 p-2 text-white transition-colors hover:bg-green-600"
        >
          Register
        </button>
      </form>

      <div className="flex items-center gap-4">
        <hr className="grow border-gray-300" />
        <span className="text-gray-500">OR</span>
        <hr className="grow border-gray-300" />
      </div>

      <button
        onClick={handleGoogleSignUp}
        className="flex w-full items-center justify-center gap-2 rounded border border-gray-200 bg-white p-2 text-gray-800 transition-all duration-150 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-300 active:translate-y-0.5 active:bg-gray-200"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 48 48"
          className="size-6"
        >
          <circle cx="24" cy="24" r="20" fill="white" />
          <path
            fill="#FFC107"
            d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"
          />
          <path
            fill="#FF3D00"
            d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"
          />
          <path
            fill="#4CAF50"
            d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"
          />
          <path
            fill="#1976D2"
            d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"
          />
        </svg>
        Sign up with Google
      </button>

      {error && <p className="text-center text-red-500">{error}</p>}

      <p className="text-center text-gray-600">
        Already have an account?{" "}
        <a href="/login" className="text-blue-500 hover:underline">
          Log in
        </a>
      </p>
    </div>
  );
};