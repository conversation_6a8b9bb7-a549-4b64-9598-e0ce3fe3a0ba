// eslint-disable-next-line no-unused-vars
import mongoose, { Schema, model, models } from "mongoose";

const PaymentSchema = new Schema({
  user: { type: String, required: true }, // Changed to String to support Clerk user IDs
  paymentId: { type: String, required: true },
  orderId: { type: String, required: true },
  amount: { type: Number, required: true },
  currency: { type: String, required: true },
  status: { type: String, required: true },
  method: { type: String, required: true },
  plan: { type: String, required: true },
  createdAt: { type: Date, default: Date.now },
  metadata: { type: Schema.Types.Mixed }, // Add metadata field to match your usage
});

delete mongoose.models.Payment;

export const Payment = mongoose.model("Payment", PaymentSchema);