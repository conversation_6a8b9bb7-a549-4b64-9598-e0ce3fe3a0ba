import { getAuth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";

import connectDB from "@/lib/mongodb";
import Announcement from "@/model/Announcement";

// GET /api/announcements/[id] - Get a specific announcement
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB();
    
    const announcement = await Announcement.findById(params.id);
    
    if (!announcement) {
      return NextResponse.json({ error: "Announcement not found" }, { status: 404 });
    }
    
    return NextResponse.json(announcement);
  } catch (error) {
    console.error("Error fetching announcement:", error);
    return NextResponse.json({ error: "Failed to fetch announcement" }, { status: 500 });
  }
}

// PUT /api/announcements/[id] - Update an announcement (admin only)
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const auth = getAuth(req);
    if (!auth?.userId) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }
    
    await connectDB();
    
    // Check if user is admin
    const response = await fetch(`${req.nextUrl.origin}/api/user/me`, {
      headers: {
        cookie: req.headers.get("cookie") || "",
      },
    });
    
    const userData = await response.json();
    if (!userData.isAdmin) {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 });
    }
    
    // Parse request body
    const data = await req.json();
    
    // Add updater information
    data.lastUpdatedBy = auth.userId;
    
    // Update announcement
    const announcement = await Announcement.findByIdAndUpdate(
      params.id,
      { $set: data },
      { new: true, runValidators: true }
    );
    
    if (!announcement) {
      return NextResponse.json({ error: "Announcement not found" }, { status: 404 });
    }
    
    return NextResponse.json(announcement);
  } catch (error: any) {
    console.error("Error updating announcement:", error);
    return NextResponse.json({ 
      error: "Failed to update announcement", 
      details: error.message 
    }, { status: 500 });
  }
}

// DELETE /api/announcements/[id] - Delete an announcement (admin only)
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const auth = getAuth(req);
    if (!auth?.userId) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }
    
    await connectDB();
    
    // Check if user is admin
    const response = await fetch(`${req.nextUrl.origin}/api/user/me`, {
      headers: {
        cookie: req.headers.get("cookie") || "",
      },
    });
    
    const userData = await response.json();
    if (!userData.isAdmin) {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 });
    }
    
    // Delete announcement
    const announcement = await Announcement.findByIdAndDelete(params.id);
    
    if (!announcement) {
      return NextResponse.json({ error: "Announcement not found" }, { status: 404 });
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting announcement:", error);
    return NextResponse.json({ error: "Failed to delete announcement" }, { status: 500 });
  }
}