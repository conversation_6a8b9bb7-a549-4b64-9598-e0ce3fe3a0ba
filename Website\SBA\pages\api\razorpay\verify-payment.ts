import crypto from 'crypto';

import { getAuth } from '@clerk/nextjs/server';
import { NextApiRequest, NextApiResponse } from 'next';
import Razorpay from 'razorpay';

import { sendSubscriptionConfirmation } from '@/lib/email';
import connectDB from '@/lib/mongodb';
import { Payment } from '@/model/Payment';
import { User } from '@/model/User';



const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID!,
  key_secret: process.env.RAZORPAY_KEY_SECRET!,
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  const { userId } = getAuth(req);
  if (!userId) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  try {
    await connectDB();
    const { razorpay_order_id: razorpayOrderId, razorpay_payment_id: razorpayPaymentId, razorpay_signature: razorpaySignature, plan, os } = req.body;
    if (!razorpayOrderId || !razorpayPaymentId || !razorpaySignature || !plan) {
      return res.status(400).json({ success: false, message: 'Missing required payment details' });
    }

    const generatedSignature = crypto.createHmac('sha256', process.env.RAZORPAY_KEY_SECRET!).update(`${razorpayOrderId}|${razorpayPaymentId}`).digest('hex');
    if (generatedSignature !== razorpaySignature) {
      return res.status(400).json({ success: false, message: 'Invalid signature' });
    }

    const payment = await razorpay.payments.fetch(razorpayPaymentId);
    if (payment.status !== 'captured') {
      return res.status(400).json({ success: false, message: 'Payment not captured' });
    }

    const usdToInr = 85.26;
    // Get the user's country code to determine currency
    const user = await User.findOne({ clerkId: userId });
    const isIndianUser = user?.countryCode === 'IN';

    console.log('User country code:', user?.countryCode, 'isIndianUser:', isIndianUser);

    // For Indian users, store in INR, for others convert to USD
    const amount = isIndianUser
      ? Number(payment.amount)
      : Math.round((Number(payment.amount) / 100) / usdToInr * 100);
    const currency = isIndianUser ? 'INR' : 'USD';

    const newPayment = new Payment({
      user: userId,
      paymentId: payment.id,
      orderId: payment.order_id,
      amount,
      currency,
      status: payment.status,
      method: payment.method,
      plan,
      metadata: { os: os || 'Windows' },
    });
    await newPayment.save();

    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    const now = new Date();
    let endDate: Date;
    switch (plan) {
      case 'weekly':
        endDate = new Date(now.setDate(now.getDate() + 7));
        break;
      case 'monthly':
        endDate = new Date(now.setMonth(now.getMonth() + 1));
        break;
      case 'yearly':
        endDate = new Date(now.setFullYear(now.getFullYear() + 1));
        break;
      default:
        return res.status(400).json({ success: false, message: 'Invalid plan' });
    }

    user.subscriptionType = 'premium';
    user.subscriptionPlan = plan;
    user.subscriptionStartDate = new Date();
    user.subscriptionEndDate = endDate;
    user.promptsUsed = 0;
    await user.save();

    try {
      await sendSubscriptionConfirmation(user.email, user.name || 'Valued Customer', plan, amount, currency, endDate, os);
    } catch (emailError) {
      console.error('Failed to send subscription confirmation email:', emailError);
    }

    return res.status(200).json({
      success: true,
      message: 'Payment verified and subscription updated',
      payment: {
        amount: (amount / 100).toFixed(2),
        currency,
        plan,
        os: os || 'Windows'
      },
    });
  } catch (error) {
    console.error('Verify payment error:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
}