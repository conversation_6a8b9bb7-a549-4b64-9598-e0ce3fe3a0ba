"use client";
import { useUser, useClerk } from "@clerk/nextjs";
import {
  Navbar,
  NavbarBrand,
  NavbarContent,
  NavbarItem,
  NavbarMenu,
  NavbarMenuItem,
  NavbarMenuToggle,
  Button,
} from "@nextui-org/react";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import { useState } from "react";

import { navItems } from "@/data";

import NavigationMenuContents from "./NavigationMenuDemo";

export default function NavbarComponent() {
  const { isLoaded, isSignedIn, user } = useUser();
  const { signOut } = useClerk();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  // Hide the navbar on pages starting with /dashboard
  if (pathname && pathname.startsWith("/dashboard")) {
    return null;
  }

  const handleLogout = async () => {
    try {
      await signOut();
      // No need to specify redirect as Clerk will handle this
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  return (
    <header role="banner">
      <Navbar
        maxWidth="lg"
        isBordered
        onMenuOpenChange={setIsMenuOpen}
        className="custom-navbar fixed"
        disableAnimation
      >
        <NavbarContent>
          <NavbarMenuToggle
            aria-label={isMenuOpen ? "Close menu" : "Open menu"}
            className="sm:hidden"
          />
          <NavbarBrand>
            <div
              onClick={() => router.push("/")}
              className="flex items-center justify-center gap-2 hover:cursor-pointer"
              role="link"
              aria-label="Navigate to homepage"
            >
              <Image
                src="/logo.png"
                width={30}
                height={30}
                priority
                className="lg:transition-all lg:duration-100 lg:ease-in-out lg:hover:animate-hovereffect"
                alt="Company logo: Interview Cracker"
              />
              <h1 className="text-l select-none font-semibold sm:text-[20px]">
                InterviewCracker
              </h1>
            </div>
          </NavbarBrand>
        </NavbarContent>

        <NavbarContent
          className="ml-8 hidden gap-4 sm:flex"
          justify="center"
          role="navigation"
          aria-label="Primary navigation"
        >
          <NavigationMenuContents user={user} isSignedIn={isSignedIn} />
        </NavbarContent>

        <NavbarContent justify="end">
          {!isLoaded ? (
            <div className="animate-pulse">Loading...</div>
          ) : isSignedIn ? (
            <NavbarItem>
              <Button
                color="primary"
                variant="shadow"
                radius="full"
                onClick={handleLogout}
                aria-label="Logout"
              >
                Logout
              </Button>
            </NavbarItem>
          ) : (
            <NavbarItem>
              <Button
                color="primary"
                variant="shadow"
                radius="full"
                onClick={() => router.push("/login")}
                aria-label="Login"
              >
                Login
              </Button>
            </NavbarItem>
          )}
        </NavbarContent>

        {/* Mobile Menu */}
        <NavbarMenu className="z-50">
          {navItems.map((link, index) => (
            <NavbarMenuItem className="mt-5" key={index}>
              <a
                href={link.path}
                className="w-full text-3xl md:text-4xl"
                aria-label={`Navigate to ${link.display}`}
              >
                {link.display}
              </a>
            </NavbarMenuItem>
          ))}
          <NavbarMenuItem className="mt-5">
            <a
              href="/pricing"
              className="w-full text-3xl md:text-4xl"
              aria-label="Navigate to Pricing"
            >
              Pricing
            </a>
          </NavbarMenuItem>
          <NavbarMenuItem className="mt-5">
            <a
              href="/documentation"
              className="w-full text-3xl md:text-4xl"
              aria-label="Navigate to Documentation"
            >
              Documentation
            </a>
          </NavbarMenuItem>
          {isSignedIn && (
            <NavbarMenuItem className="mt-5">
              <a
                href="/dashboard"
                className="w-full text-3xl md:text-4xl"
                aria-label="Navigate to Dashboard"
              >
                Dashboard
              </a>
            </NavbarMenuItem>
          )}
        </NavbarMenu>
      </Navbar>
    </header>
  );
}