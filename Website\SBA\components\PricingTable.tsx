import Link from 'next/link';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import {
    HoverCard,
    HoverCardContent,
    HoverCardTrigger,
} from '@/components/ui/hover-card';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';


const PricingTable = () => {
    return (
        <div className="my-20 border-none">
            <Card className="border-gradient-to-r overflow-hidden rounded-3xl border bg-black/85 from-green-500/50 to-blue-500/50 shadow-2xl shadow-green-500/20 backdrop-blur-2xl transition-all duration-700 hover:shadow-blue-500/40">
                <CardContent className="p-0">
                    <Table className="w-full overflow-hidden border-none">
                        <TableHeader>
                            <TableRow className="w-full rounded-t-3xl border-b-4 border-green-500/70 bg-gradient-to-r from-[#141414] to-[#1a1a1a] transition-all duration-500">
                                <TableHead className="hover:text-shadow-[0_0_8px_rgba(0,255,153,0.5)] w-[120px] bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text px-4 py-8 text-center text-base font-bold capitalize tracking-normal text-transparent drop-shadow-[0_2px_4px_rgba(0,255,153,0.3)] transition-all duration-300">
                                    Plan Type
                                </TableHead>
                                <TableHead className="hover:text-shadow-[0_0_8px_rgba(0,255,153,0.5)] w-[150px] bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text px-4 py-8 text-center text-base font-bold capitalize tracking-normal text-transparent drop-shadow-[0_2px_4px_rgba(0,255,153,0.3)] transition-all duration-300">
                                    InterviewCracker Pricing
                                </TableHead>
                                <TableHead className="hover:text-shadow-[0_0_8px_rgba(0,255,153,0.5)] w-[200px] bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text py-8 pl-2 text-left text-base font-bold capitalize tracking-normal text-transparent drop-shadow-[0_2px_4px_rgba(0,255,153,0.3)] transition-all duration-300">
                                    InterviewCracker Features
                                </TableHead>
                                <TableHead className="hover:text-shadow-[0_0_8px_rgba(0,255,153,0.5)] w-[150px] bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text px-4 py-8 text-center text-base font-bold capitalize tracking-normal text-transparent drop-shadow-[0_2px_4px_rgba(0,255,153,0.3)] transition-all duration-300">
                                    InterviewCoder Pricing
                                </TableHead>
                                <TableHead className="hover:text-shadow-[0_0_8px_rgba(0,255,153,0.5)] w-[200px] bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text px-4 py-8 text-center text-base font-bold capitalize tracking-normal text-transparent drop-shadow-[0_2px_4px_rgba(0,255,153,0.3)] transition-all duration-300">
                                    InterviewCoder Features
                                </TableHead>
                                <TableHead className="hover:text-shadow-[0_0_8px_rgba(0,255,153,0.5)] w-[160px] bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text px-4 py-8 text-center text-base font-bold capitalize tracking-normal text-transparent drop-shadow-[0_2px_4px_rgba(0,255,153,0.3)] transition-all duration-300">
                                    Savings with InterviewCracker
                                </TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody className="border-none">
                            {/* Free Plan */}
                            <TableRow className="border-t border-green-500/40 transition-all duration-500 hover:bg-gradient-to-r hover:from-[#1a1a1a]/90 hover:to-[#2a2a2a]/90">
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[120px] whitespace-nowrap px-4 py-8 text-center text-base font-semibold text-white transition-all duration-300">
                                    Free
                                </TableCell>
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[150px] whitespace-nowrap px-4 py-8 text-center text-base font-semibold text-white transition-all duration-300">
                                    <span className="bg-gradient-to-r from-gray-200 to-white bg-clip-text text-transparent">
                                        $0/month
                                    </span>
                                </TableCell>
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[200px] py-8 pl-2 text-left text-white transition-all duration-300">
                                    <ul className="list-none space-y-2">
                                        <li className="flex items-center space-x-2">
                                            <span className="text-green-400">✔</span>
                                            <span>
                                                <HoverCard>
                                                    <HoverCardTrigger className="cursor-pointer text-gray-200 underline transition-colors hover:text-green-300">
                                                        Basic Code Generation
                                                    </HoverCardTrigger>
                                                    <HoverCardContent className="rounded-lg border-green-500/50 bg-[#1a1a1a]/95 p-4 text-white shadow-lg backdrop-blur-lg">
                                                        Generate code snippets for simple problems with limited customization.
                                                    </HoverCardContent>
                                                </HoverCard>
                                            </span>
                                        </li>
                                        <li className="flex items-center space-x-2">
                                            <span className="text-green-400">✔</span>
                                            <span className="text-gray-200">5 Prompts (Lifetime)</span>
                                        </li>
                                        <li className="flex items-center space-x-2">
                                            <span className="text-green-400">✔</span>
                                            <span className="text-gray-200">Community Support</span>
                                        </li>
                                        <li className="flex items-center space-x-2">
                                            <span className="text-green-400">✔</span>
                                            <span className="text-gray-200">Unlimited Access</span>
                                        </li>
                                    </ul>
                                </TableCell>
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[150px] whitespace-nowrap px-4 py-8 text-center text-base font-semibold text-white transition-all duration-300">
                                    <span className="bg-gradient-to-r from-gray-200 to-white bg-clip-text text-transparent">
                                        Not Offered
                                    </span>
                                </TableCell>
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[200px] whitespace-nowrap px-4 py-8 text-center text-white transition-all duration-300">
                                    <span className="text-gray-400">-</span>
                                </TableCell>
                                <TableCell className="w-[160px] whitespace-nowrap px-4 py-8 text-center">
                                    <span className="text-gray-400">-</span>
                                </TableCell>
                            </TableRow>
                            {/* Daily Plan */}
                            <TableRow className="border-t border-green-500/40 transition-all duration-500 hover:bg-gradient-to-r hover:from-[#1a1a1a]/90 hover:to-[#2a2a2a]/90">
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[120px] whitespace-nowrap px-4 py-8 text-center text-base font-semibold text-white transition-all duration-300">
                                    Daily
                                </TableCell>
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[150px] whitespace-nowrap px-4 py-8 text-center text-base font-semibold text-white transition-all duration-300">
                                    <span className="bg-gradient-to-r from-gray-200 to-white bg-clip-text text-transparent">
                                        $5/day
                                    </span>
                                </TableCell>
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[200px] py-8 pl-2 text-left text-white transition-all duration-300">
                                    <ul className="list-none space-y-2">
                                        <li className="flex items-center space-x-2">
                                            <span className="text-green-400">✔</span>
                                            <span>
                                                <HoverCard>
                                                    <HoverCardTrigger className="cursor-pointer text-gray-200 underline transition-colors hover:text-green-300">
                                                        Advanced Code Generation
                                                    </HoverCardTrigger>
                                                    <HoverCardContent className="rounded-lg border-green-500/50 bg-[#1a1a1a]/95 p-4 text-white shadow-lg backdrop-blur-lg">
                                                        Generate complex code with multiple languages and optimizations.
                                                    </HoverCardContent>
                                                </HoverCard>
                                            </span>
                                        </li>
                                        <li className="flex items-center space-x-2">
                                            <span className="text-green-400">✔</span>
                                            <span className="text-gray-200">35 Prompts/Day</span>
                                        </li>
                                        <li className="flex items-center space-x-2">
                                            <span className="text-green-400">✔</span>
                                            <span className="text-gray-200">Priority Support</span>
                                        </li>
                                    </ul>
                                </TableCell>
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[150px] whitespace-nowrap px-4 py-8 text-center text-base font-semibold text-white transition-all duration-300">
                                    <span className="bg-gradient-to-r from-gray-200 to-white bg-clip-text text-transparent">
                                        Not Offered
                                    </span>
                                </TableCell>
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[200px] whitespace-nowrap px-4 py-8 text-center text-white transition-all duration-300">
                                    <span className="text-gray-400">-</span>
                                </TableCell>
                                <TableCell className="w-[160px] whitespace-nowrap px-4 py-8 text-center">
                                    <span className="text-gray-400">-</span>
                                </TableCell>
                            </TableRow>
                            {/* Monthly Plan */}
                            <TableRow className="border-t border-green-500/40 transition-all duration-500 hover:bg-gradient-to-r hover:from-[#1a1a1a]/90 hover:to-[#2a2a2a]/90">
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[120px] whitespace-nowrap px-4 py-8 text-center text-base font-semibold text-white transition-all duration-300">
                                    Monthly
                                </TableCell>
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[150px] whitespace-nowrap px-4 py-8 text-center text-base font-semibold text-white transition-all duration-300">
                                    <span className="bg-gradient-to-r from-gray-200 to-white bg-clip-text text-transparent">
                                        $10/month
                                    </span>
                                </TableCell>
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[200px] py-8 pl-2 text-left text-white transition-all duration-300">
                                    <ul className="list-none space-y-2">
                                        <li className="flex items-center space-x-2">
                                            <span className="text-green-400">✔</span>
                                            <span>
                                                <HoverCard>
                                                    <HoverCardTrigger className="cursor-pointer text-gray-200 underline transition-colors hover:text-green-300">
                                                        Advanced Code Generation
                                                    </HoverCardTrigger>
                                                    <HoverCardContent className="rounded-lg border-green-500/50 bg-[#1a1a1a]/95 p-4 text-white shadow-lg backdrop-blur-lg">
                                                        Generate complex code with multiple languages and optimizations.
                                                    </HoverCardContent>
                                                </HoverCard>
                                            </span>
                                        </li>
                                        <li className="flex items-center space-x-2">
                                            <span className="text-green-400">✔</span>
                                            <span className="text-gray-200">250 Prompts/Month</span>
                                        </li>
                                        <li className="flex items-center space-x-2">
                                            <span className="text-green-400">✔</span>
                                            <span className="text-gray-200">Priority Support</span>
                                        </li>
                                    </ul>
                                </TableCell>
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[150px] whitespace-nowrap px-4 py-8 text-center text-base font-semibold text-white transition-all duration-300">
                                    <span className="bg-gradient-to-r from-gray-200 to-white bg-clip-text text-transparent">
                                        $60/month
                                    </span>
                                </TableCell>
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[200px] px-4 py-8 text-left text-white transition-all duration-300">
                                    <ul className="list-none space-y-2">
                                        <li className="flex items-center space-x-2">
                                            <span className="text-green-400">✔</span>
                                            <span className="text-gray-200">Advanced Code Generation</span>
                                        </li>
                                        <li className="flex items-center space-x-2">
                                            <span className="text-green-400">✔</span>
                                            <span className="text-gray-200">Email Support</span>
                                        </li>
                                    </ul>
                                </TableCell>
                                <TableCell className="w-[160px] px-4 py-8 text-center">
                                    <Link
                                        href="/pricing"
                                        className="no-underline outline-none focus:outline-none focus:ring-0 focus:ring-offset-0 focus:ring-offset-transparent"
                                    >
                                        <div className="transition-all duration-300 hover:scale-105 hover:shadow-[0_0-15px_rgba(0,255,153,0.5)]">
                                            <Badge
                                                variant="default"
                                                className="border-gradient-to-r rounded-full border bg-white from-gray-200 to-gray-400 px-6 py-3 text-base text-black shadow-lg outline-none hover:outline-none hover:ring-0 focus:outline-none focus:ring-0 focus:ring-offset-0 focus:ring-offset-transparent"
                                            >
                                                Save $50/month
                                            </Badge>
                                        </div>
                                    </Link>
                                </TableCell>
                            </TableRow>
                            {/* Yearly Plan */}
                            <TableRow className="border-t border-green-500/40 transition-all duration-500 hover:bg-gradient-to-r hover:from-[#1a1a1a]/90 hover:to-[#2a2a2a]/90">
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[120px] whitespace-nowrap px-4 py-8 text-center text-base font-semibold text-white transition-all duration-300">
                                    Yearly
                                </TableCell>
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[150px] whitespace-nowrap px-4 py-8 text-center text-base font-semibold text-white transition-all duration-300">
                                    <span className="bg-gradient-to-r from-gray-200 to-white bg-clip-text text-transparent">
                                        $80/year
                                    </span>
                                </TableCell>
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[200px] py-8 pl-2 text-left text-white transition-all duration-300">
                                    <ul className="list-none space-y-2">
                                        <li className="flex items-center space-x-2">
                                            <span className="text-green-400">✔</span>
                                            <span>
                                                <HoverCard>
                                                    <HoverCardTrigger className="cursor-pointer text-gray-200 underline transition-colors hover:text-green-300">
                                                        Advanced Code Generation
                                                    </HoverCardTrigger>
                                                    <HoverCardContent className="rounded-lg border-green-500/50 bg-[#1a1a1a]/95 p-4 text-white shadow-lg backdrop-blur-lg">
                                                        Generate complex code with multiple languages and optimizations.
                                                    </HoverCardContent>
                                                </HoverCard>
                                            </span>
                                        </li>
                                        <li className="flex items-center space-x-2">
                                            <span className="text-green-400">✔</span>
                                            <span className="text-gray-200">250 Prompts/Week</span>
                                        </li>
                                        <li className="flex items-center space-x-2">
                                            <span className="text-green-400">✔</span>
                                            <span className="text-gray-200">Priority Support</span>
                                        </li>
                                    </ul>
                                </TableCell>
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[150px] whitespace-nowrap px-4 py-8 text-center text-base font-semibold text-white transition-all duration-300">
                                    <span className="bg-gradient-to-r from-gray-200 to-white bg-clip-text text-transparent">
                                        $300/year
                                    </span>
                                </TableCell>
                                <TableCell className="hover:text-shadow-[0_0_6px_rgba(0,255,153,0.3)] w-[200px] px-4 py-8 text-left text-white transition-all duration-300">
                                    <ul className="list-none space-y-2">
                                        <li className="flex items-center space-x-2">
                                            <span className="text-green-400">✔</span>
                                            <span className="text-gray-200">Advanced Code Generation</span>
                                        </li>
                                        <li className="flex items-center space-x-2">
                                            <span className="text-green-400">✔</span>
                                            <span className="text-gray-200">Email Support</span>
                                        </li>
                                    </ul>
                                </TableCell>
                                <TableCell className="w-[160px] px-4 py-8 text-center">
                                    <Link
                                        href="/pricing"
                                        className="no-underline outline-none focus:outline-none focus:ring-0 focus:ring-offset-0 focus:ring-offset-transparent"
                                    >
                                        <div className="transition-all duration-300 hover:scale-105 hover:shadow-[0_0-15px_rgba(0,255,153,0.5)]">
                                            <Badge
                                                variant="default"
                                                className="border-gradient-to-r rounded-full border bg-white from-gray-200 to-gray-400 px-6 py-3 text-base text-black shadow-lg outline-none hover:outline-none hover:ring-0 focus:outline-none focus:ring-0 focus:ring-offset-0 focus:ring-offset-transparent"
                                            >
                                                Save $220/year
                                            </Badge>
                                        </div>
                                    </Link>
                                </TableCell>
                            </TableRow>
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
        </div>
    );
};

export default PricingTable;