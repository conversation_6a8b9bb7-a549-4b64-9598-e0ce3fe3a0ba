"use client";

import { motion } from "framer-motion";
import React from "react";

// Timeline Data
const timelineData = [
    { date: "2025-01-01", users: 0, price: "$0", features: ["Launch Date"] },
    {
        date: "2025-01-20",
        users: 30,
        price: "$7",
        features: ["Remains Undetectable", "Can send screenshots", "Use top models"],
    },
    {
        date: "2025-02-10",
        users: 50,
        price: "$10",
        features: [
            "Remains Undetectable",
            "Can send screenshots",
            "Select LLM model",
            "Get system design chat",
        ],
    },
    {
        date: "2025-04-11",
        users: 100,
        price: "$40",
        features: [
            "Remains Undetectable",
            "Can send screenshots",
            "Select LLM model",
            "Get system design chat",
            "Real-time audio transcription",
        ],
    },
];

// Current user count - hardcoded to 39
const currentUserCount = 39;

interface PricingDisplayProps {
    className?: string;
}

const PricingTimelineDisplay = ({ className }: PricingDisplayProps) => {
    // Get current price tier based on user count
    const getCurrentTierIndex = () => {
        if (currentUserCount <= 30) {
            return 0;
        } else if (currentUserCount <= 50) {
            return 1;
        } else {
            return 2;
        }
    };

    const currentTierIndex = getCurrentTierIndex();
    const currentPrice = timelineData[currentTierIndex + 1]?.price || "$10";

    return (
        <div className={`relative flex w-full flex-col items-center py-12 ${className}`}>
            {/* Background with dot pattern and radial gradient similar to Hero */}
            <div className='absolute inset-0 size-full bg-white bg-dot-black/[0.05] dark:bg-black dark:bg-dot-white/[0.2]'>
                <div className='pointer-events-none absolute inset-0 flex items-center justify-center bg-white [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)] dark:bg-black'></div>
            </div>

            {/* Content with z-index to appear above background */}
            <div className="relative z-10 flex w-full flex-col items-center">
                {/* Horizontal Timeline Component */}
                <motion.div
                    className="mx-auto mb-8 w-full max-w-6xl px-4"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 1.0 }}
                >                    {/* Price Increase Timeline and Current Users removed */}

                    {/* Timeline Display */}
                    <div className="relative mb-16 flex w-full items-center">
                        {/* Timeline Line */}
                        <motion.div
                            className="from-purple-600 absolute inset-x-0 h-1 bg-gradient-to-r to-blue-400"
                            initial={{ width: 0 }}
                            animate={{ width: "100%" }}
                            transition={{ duration: 1.5, ease: "easeOut" }}
                        />

                        {/* Timeline Points */}
                        <div className="relative flex w-full justify-between">
                            {timelineData.map((item, index) => (
                                <motion.div
                                    key={index}
                                    className="relative flex flex-col items-center"
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: index * 0.2, duration: 0.6 }}
                                >
                                    {/* User Count Marker - Only show if we're at a specific point */}
                                    {index > 0 && index - 1 === currentTierIndex && (
                                        <motion.div
                                            className="absolute -top-12 h-16 w-5 bg-gradient-to-b from-yellow-400 to-transparent"
                                            initial={{ height: 0, opacity: 0 }}
                                            animate={{ height: 60, opacity: 0.6 }}
                                            transition={{ duration: 1.5, repeat: Infinity, repeatType: "reverse" }}
                                        />
                                    )}

                                    {/* Marker */}
                                    <div className={`size-6 rounded-full border-2 ${index > 0 && index - 1 === currentTierIndex
                                        ? "border-yellow-600 bg-yellow-400"
                                        : "border-gray-600 bg-gray-400"
                                        }`} />

                                    {/* Date Label */}
                                    <p className="mt-3 text-sm font-medium text-white">
                                        {new Date(item.date).toLocaleDateString("en-US", { month: "short", day: "numeric" })}
                                    </p>

                                    {/* Price */}
                                    <p className="text-lg font-bold text-yellow-400">
                                        {item.price}
                                    </p>
                                </motion.div>
                            ))}
                        </div>
                    </div>
                </motion.div>

                {/* Pricing Cards */}
                <motion.div
                    className="mx-auto flex w-full max-w-6xl justify-center px-4"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 1.2, ease: "easeOut", delay: 0.2 }}
                >
                    <div className="w-full rounded-2xl border border-gray-800/40 p-8 shadow-2xl backdrop-blur-md">
                        <h2 className="mb-8 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-center text-4xl font-extrabold text-transparent">
                            Current Price: <span className="animate-pulse">{currentPrice}</span>
                        </h2>

                        <div className="flex flex-col items-stretch justify-between space-x-0 md:flex-row md:space-x-6">
                            {timelineData.slice(1).map((tier, index) => (
                                <motion.div
                                    key={tier.date}
                                    className={`flex-1 p-6 text-center ${index === currentTierIndex
                                        ? "border-yellow-500/30 bg-black/70"
                                        : "bg-black/50"
                                        } rounded-xl border border-gray-700/40 shadow-lg backdrop-blur-sm ${index === currentTierIndex ? "my-0" : "my-2"
                                        } ${index > 0 ? "mt-8 md:mt-0" : ""}`}
                                    whileHover={{ scale: 1.05, boxShadow: "0 0 20px rgba(255, 215, 0, 0.3)" }}
                                    whileTap={{ scale: 0.98 }}
                                    transition={{ duration: 0.4, type: "spring" }}
                                >
                                    <p className="text-sm font-medium text-gray-300">First {tier.users} Users</p>
                                    <p className="my-3 text-3xl font-bold text-white">{tier.price}</p>
                                    <p className="mb-4 text-xs text-gray-400">
                                        {new Date(tier.date).toLocaleDateString("en-US", { month: "long", day: "numeric", year: "numeric" })}
                                    </p>
                                    <ul className="space-y-2 text-xs text-gray-400">
                                        {tier.features.map((feature, i) => (
                                            <motion.li
                                                key={i}
                                                className="flex items-center justify-center"
                                                initial={{ opacity: 0, x: -10 }}
                                                animate={{ opacity: 1, x: 0 }}
                                                transition={{ delay: i * 0.1, duration: 0.5 }}
                                            >
                                                <span className="mr-1 text-green-400">✓</span> {feature}
                                            </motion.li>
                                        ))}
                                    </ul>

                                    {/* Highlight Current Tier */}
                                    {index === currentTierIndex && (
                                        <motion.div
                                            className="mt-4 inline-block rounded-full bg-gradient-to-r from-yellow-400 to-amber-500 px-3 py-1 text-xs font-bold text-black"
                                            initial={{ scale: 0.8, opacity: 0 }}
                                            animate={{ scale: 1, opacity: 1 }}
                                            transition={{ delay: 1, duration: 0.5 }}
                                        >
                                            CURRENT
                                        </motion.div>
                                    )}

                                    {/* Arrow connecting to next tier */}
                                    {index < timelineData.slice(1).length - 1 && (
                                        <div className="absolute -right-3 top-1/2 z-10 hidden -translate-y-1/2 items-center md:flex">
                                            <motion.div
                                                className="text-purple-500 text-xl"
                                                initial={{ opacity: 0 }}
                                                animate={{ opacity: 1 }}
                                                transition={{ delay: 1 + index * 0.2, duration: 0.5 }}
                                            >
                                                ▶
                                            </motion.div>
                                        </div>
                                    )}
                                </motion.div>
                            ))}
                        </div>

                        <motion.div
                            className="from-purple-600 mt-8 h-2 w-full rounded-full bg-gradient-to-r to-blue-500"
                            initial={{ width: 0 }}
                            animate={{ width: "100%" }}
                            transition={{ duration: 2.5, ease: "easeInOut", repeat: Infinity, repeatType: "reverse" }}
                        />
                    </div>
                </motion.div>            </div>
        </div>
    );
};

// Make sure to export as a named constant
export default PricingTimelineDisplay;
