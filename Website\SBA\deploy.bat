@echo off
echo Deploying to Google Cloud Run...

:: Set your variables
set PROJECT_ID=basic-curve-453309-a9
set SERVICE_NAME=interviewcracker-frontend
set REGION=us-central1
set IMAGE=gcr.io/%PROJECT_ID%/%SERVICE_NAME%
set DOMAIN=interviewcracker.in

:: Build and push the Docker image
gcloud builds submit --tag %IMAGE%

:: Deploy the new image to Cloud Run
gcloud run deploy %SERVICE_NAME% ^
  --image %IMAGE% ^
  --platform managed ^
  --region %REGION% ^
  --allow-unauthenticated ^
  --port 3000

:: Update domain mapping (if needed)
gcloud beta run domain-mappings create ^
  --service=%SERVICE_NAME% ^
  --domain=%DOMAIN%

echo Deployment completed!
pause
