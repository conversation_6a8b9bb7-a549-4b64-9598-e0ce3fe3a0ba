import bcrypt from "bcryptjs";
import { NextApiRequest, NextApiResponse } from "next";

import connectDB from "@/lib/mongodb";
import { User } from "@/model/User";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "POST") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  await connectDB();

  const { email, code, newPassword, confirmPassword } = req.body;
  if (!email || !code || !newPassword || !confirmPassword) {
    return res.status(400).json({ message: "All fields are required" });
  }

  if (newPassword !== confirmPassword) {
    return res.status(400).json({ message: "Passwords do not match" });
  }

  try {
    // Find user by email without provider restriction
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(400).json({ message: "Invalid reset code or email" });
    }

    console.log(`Processing password reset for user: ${user._id}, Provider: ${user.provider}`);

    // Validate the reset code
    if (user.resetCode !== code || !user.resetCodeExpires || user.resetCodeExpires < new Date()) {
      return res.status(400).json({ message: "Invalid or expired reset code" });
    }

    // Hash the new password with a stronger salt (12 rounds)
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    
    // Check if this is a Clerk user that should maintain Clerk authentication
    const isClerkUser = user.provider === "clerk";
    
    // Update user with new password
    user.password = hashedPassword;
    
    // Update the provider to allow password-based login
    // For Clerk users, we allow both methods but keep track of their origin
    if (isClerkUser) {
      console.log("Setting up hybrid authentication for Clerk user");
      // Allow email login while preserving Clerk ID
      user.provider = "email";
    }
    
    // Clear reset code fields
    user.resetCode = undefined;
    user.resetCodeExpires = undefined;
    
    // Save the user with updated password
    await user.save();
    
    console.log(`Password successfully reset for user: ${user._id}`);

    // Return appropriate response
    return res.status(200).json({ 
      message: "Password reset successfully",
      // Let the client know if this was a Clerk user so it can handle auth flow
      wasClerkUser: isClerkUser
    });
  } catch (error) {
    console.error("Reset password error:", error);
    return res.status(500).json({ 
      message: "Internal server error",
      error: process.env.NODE_ENV === 'development' ? 
        (error instanceof Error ? error.message : String(error)) : 
        undefined
    });
  }
}