// lib/subscription-utils.ts
import { User } from "@/model/User";
import { Payment } from "@/model/Payment";

/**
 * Checks if a user's premium subscription has expired and reverts them to free if needed
 * @param user The user document to check
 * @returns True if the user was updated, false otherwise
 */
export async function checkAndUpdateExpiredSubscription(user: any): Promise<boolean> {
  // If the user is not premium or doesn't have an end date, no action needed
  if (user.subscriptionType !== 'premium' || !user.subscriptionEndDate) {
    return false;
  }

  const now = new Date();
  const endDate = new Date(user.subscriptionEndDate);

  // Check if subscription has expired
  if (endDate < now) {
    console.log(`Subscription expired for user ${user._id}. Reverting to free plan.`);
    
    // Store the previous plan for the cancellation record
    const previousPlan = user.subscriptionPlan;
    
    // Update user to free plan
    user.subscriptionType = 'free';
    user.subscriptionPlan = '';
    user.promptsUsed = 0;
    user.subscriptionStartDate = undefined;
    user.subscriptionEndDate = undefined;
    
    // Save the updated user
    await user.save();
    
    // Create a record in payment history for the automatic cancellation
    const timestamp = Date.now();
    const cancellationRecord = new Payment({
      user: user.clerkId || user._id,
      paymentId: `auto-expire-${timestamp}`,
      orderId: `auto-expire-${timestamp}`,
      amount: 0,
      currency: "USD",
      status: "expired",
      method: "expiration",
      plan: previousPlan || "subscription",
      createdAt: new Date()
    });
    
    await cancellationRecord.save();
    
    return true;
  }
  
  return false;
}
