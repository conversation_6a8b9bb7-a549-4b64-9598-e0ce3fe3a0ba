"use client";

import { useUser } from "@clerk/nextjs";
import { <PERSON><PERSON><PERSON>hadow, <PERSON>lt<PERSON> } from "@nextui-org/react";
import { motion } from "framer-motion";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React, { useState, useEffect } from "react";
import { FiHome, FiUser, FiCreditCard, FiDollarSign, FiKey, FiTag, FiBook, FiShield, FiBell } from "react-icons/fi";

// Define navigation items
const navItems = [
    { title: "Dashboard", href: "/dashboard", icon: <FiHome className="text-lg" /> },
    { title: "Profile", href: "/dashboard/profile", icon: <FiUser className="text-lg" /> },
    { title: "Account Type", href: "/dashboard/account-type", icon: <FiCreditCard className="text-lg" /> },
    { title: "Payment History", href: "/dashboard/payment-history", icon: <FiDollarSign className="text-lg" /> },
    { title: "Secret Key", href: "/dashboard/secret-key", icon: <FiKey className="text-lg" /> },
    { title: "Pricing", href: "/pricing", icon: <FiTag className="text-lg" /> },
    { title: "Announcements", href: "/services", icon: <FiBell className="text-lg" /> },
    { title: "Documentation", href: "/documentation", icon: <FiBook className="text-lg" /> },
];

// Admin navigation items
const adminItems = [
    {
        title: "Admin Dashboard",
        href: "/dashboard/admin",
        icon: <FiShield className="text-lg" />,
    },
    {
        title: "Manage Announcements",
        href: "/dashboard/announcements",
        icon: <FiBell className="text-lg" />,
    }
];

export function SidebarNav() {
    const { isLoaded, user } = useUser();
    const pathname = usePathname();
    const [isMounted, setIsMounted] = useState(false);
    const [isAdmin, setIsAdmin] = useState(false);

    useEffect(() => {
        const checkAdminStatus = async () => {
            if (!isLoaded || !user) return;

            try {
                const response = await fetch(`/api/user/me?t=${Date.now()}`, {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache',
                    },
                    credentials: 'include'
                });

                if (response.ok) {
                    const userData = await response.json();
                    setIsAdmin(userData?.isAdmin === true);
                }
            } catch (error) {
                console.error("Error checking admin status:", error);
            }
        };

        checkAdminStatus();
    }, [isLoaded, user]);

    useEffect(() => {
        setIsMounted(true);
    }, []);

    if (!isMounted) return null;

    // Create final nav items array based on admin status
    const finalNavItems = isAdmin
        ? [...adminItems, ...navItems]
        : navItems;

    return (
        <aside className="sticky top-0 hidden h-screen w-64 lg:block">
            <ScrollShadow className="h-full">
                <div className="flex h-full flex-col border-r border-slate-800/40 bg-[#0f172a]/60 px-4 py-8 backdrop-blur-md">
                    <div className="mb-4 px-2">
                        <Link href="/" className="flex items-center">
                            <img
                                src="/logo.png"
                                alt="Logo"
                                className="mr-2 h-auto w-8"
                            />
                            <span className="text-lg font-bold text-white">InterviewCracker</span>
                        </Link>
                    </div>

                    <nav className="mt-4 flex-1">
                        <ul className="space-y-2">
                            {finalNavItems.map((item, index) => {
                                const isActive = pathname === item.href;
                                return (
                                    <motion.li
                                        key={item.href}
                                        initial={{ opacity: 0, x: -5 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: index * 0.05 }}
                                    >
                                        <Tooltip
                                            content={item.title}
                                            placement="right"
                                            delay={500}
                                            offset={10}
                                        >
                                            <Link
                                                href={item.href}
                                                className={`flex items-center rounded-lg px-4 py-3 transition-colors ${isActive
                                                    ? "bg-[#24AE7C]/20 font-medium text-[#24AE7C]"
                                                    : "text-gray-300 hover:bg-white/5 hover:text-white"
                                                    }`}
                                            >
                                                <span className="mr-3">{item.icon}</span>
                                                <span>{item.title}</span>
                                            </Link>
                                        </Tooltip>
                                    </motion.li>
                                );
                            })}
                        </ul>
                    </nav>

                    <div className="mt-auto border-t border-slate-800/40 pt-4">
                        <div className="px-4 py-2">
                            <p className="text-xs text-gray-500">
                                © {new Date().getFullYear()} InterviewCracker
                            </p>
                        </div>
                    </div>
                </div>
            </ScrollShadow>
        </aside>
    );
}