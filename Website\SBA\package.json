{"name": "interviewcracker", "version": "1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "vercel --prod", "email": "email dev"}, "browser": {"fs": false, "path": false, "os": false}, "dependencies": {"@clerk/clerk-sdk-node": "^5.1.6", "@clerk/nextjs": "^6.12.12", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@hookform/resolvers": "^3.3.4", "@mui/icons-material": "^5.15.21", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.21", "@next/third-parties": "^15.2.2", "@nextui-org/button": "^2.0.34", "@nextui-org/link": "^2.0.32", "@nextui-org/navbar": "^2.0.33", "@nextui-org/react": "^2.4.2", "@nextui-org/system": "^2.2.2", "@nextui-org/theme": "^2.2.6", "@paypal/checkout-server-sdk": "^1.0.3", "@pmndrs/vanilla": "^1.14.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@react-email/components": "0.0.19", "@react-email/img": "0.0.8", "@react-email/tailwind": "0.0.18", "@react-three/drei": "^9.105.4", "@react-three/fiber": "^8.16.2", "@sentry/nextjs": "^9.9.0", "@tabler/icons-react": "^3.1.0", "@tanstack/react-table": "^8.17.0", "@types/lodash": "^4.17.6", "@types/three": "^0.163.0", "@vercel/analytics": "^1.4.1", "@vercel/speed-insights": "^1.0.12", "animejs": "^3.2.2", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "crisp-sdk-web": "^1.0.25", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "embla-carousel-autoplay": "^8.1.5", "embla-carousel-react": "^8.1.5", "framer-motion": "^11.2.13", "fs": "^0.0.1-security", "input-otp": "^1.2.4", "leva": "^0.9.35", "lottie-react": "^2.4.0", "lucide-react": "^0.378.0", "mini-svg-data-uri": "^1.4.4", "mongoose": "^8.12.1", "next": "^14.2.24", "next-auth": "^4.24.11", "next-themes": "^0.3.0", "node-appwrite": "^13.0.0", "nodemailer": "^6.10.0", "razorpay": "^2.9.6", "react": "^18.3.1", "react-datepicker": "^6.9.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-email": "^1.2.0", "react-hook-form": "^7.51.4", "react-icons": "^5.0.1", "react-lottie": "^1.2.10", "react-phone-number-input": "^3.4.1", "react-spinners": "^0.15.0", "resend": "^3.5.0", "sharp": "^0.33.5", "sib-api-v3-sdk": "^8.5.0", "sonner": "^1.5.0", "styled-components": "^6.1.11", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three": "^0.163.0", "three-globe": "^2.31.0", "typewriter-effect": "^2.21.0", "vercel": "^41.3.2", "zod": "^3.23.8"}, "devDependencies": {"@playwright/test": "^1.51.1", "@types/bcrypt": "^5.0.2", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^18", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^18", "@types/react-lottie": "^1.2.10", "autoprefixer": "^10.0.1", "eslint": "^8.57.1", "eslint-config-next": "^14.2.24", "eslint-config-prettier": "^10.1.1", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-tailwindcss": "^3.18.0", "postcss": "^8", "prettier": "^3.5.3", "tailwindcss": "^3.4.1", "typescript": "^5"}}