"use client";

import { motion } from "framer-motion";
import React from "react";

import { getTotalUserCount } from "../lib/actions/user-actions";

// Timeline Data
const timelineData = [
    { date: "2025-01-01", users: 0, price: "$0", features: ["Launch Date"] },
    {
        date: "2025-01-20",
        users: 5,
        price: "$7/Week",
        features: ["Remains Undetectable", "Can send screenshots", "Use top models"],
    },
    {
        date: "2025-02-10",
        users: 25,
        price: "$10/Week",
        features: [
            "Remains Undetectable",
            "Can send screenshots",
            "Select LLM model",
            "Get system design chat",
        ],
    },
    {
        date: "2025-03-15",
        users: 50,
        price: "$30/Week",
        features: [
            "Remains Undetectable",
            "Can send screenshots",
            "Select LLM model",
            "Get system design chat",
            "Real-time audio transcription",
        ],
    },
    {
        date: "2025-04-11",
        users: 100,
        price: "$30/Week",
        features: ["Upcoming features on the way"],
        isUpcoming: true,
    },
];

// Component for displaying price information on hover
function PriceTooltip({ item, visible }) {
    if (!visible || !item) return null; return (
        <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`absolute ${item.users === 100 ? "-top-28" : "-top-60"} z-50 w-64 rounded-xl border border-yellow-500/30 bg-black/90 p-4 shadow-lg shadow-amber-500/20 backdrop-blur-lg`}
        ><p className="mb-2 text-lg font-bold text-white">
                {item.isUpcoming ? "Upcoming" : `Above ${item.users} Users`}
            </p>
            <p className="mb-3 text-2xl font-bold text-yellow-400">{item.price}</p>
            <ul className="space-y-2">
                {item.features.map((feature, i) => (
                    <li key={i} className="flex items-center text-sm">
                        <span className="mr-2 text-amber-400">✓</span>
                        <span className="text-gray-300">{feature}</span>
                    </li>
                ))}
            </ul>
        </motion.div>
    );
}

// Separate TimelinePoint component to fix the hooks error
function TimelinePoint({ item, index, currentUserCount }) {
    const [isHovering, setIsHovering] = React.useState(false);

    return (
        <div
            className="relative flex flex-col items-center"
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
        >
            {/* Tooltip appears on hover */}
            {index > 0 && <PriceTooltip item={item} visible={isHovering} />}            {/* Larger dot to cover the height of timeline */}
            <div className="flex h-[80px] items-center justify-center">
                <div className="absolute top-[40px]" style={{ marginTop: "0px" }}>
                    <motion.div
                        className={`z-30 size-6 cursor-pointer rounded-full border-2 ${currentUserCount >= item.users && index > 0
                            ? "border-yellow-600 bg-yellow-400"
                            : "border-gray-600 bg-gray-400 hover:border-amber-500 hover:bg-amber-300"
                            }`}
                        animate={
                            currentUserCount >= item.users && index > 0
                                ? { scale: [1, 1.2, 1] } // Pulse only for dots where line has crossed
                                : {}
                        }
                        transition={{
                            duration: 1.5,
                            repeat: currentUserCount >= item.users && index > 0 ? Infinity : 0
                        }}
                        whileHover={{ scale: 1.2 }}
                    />
                </div>                {/* Text label below timeline */}
                <p className="absolute top-[70px] bg-gradient-to-b from-yellow-300 to-amber-500 bg-clip-text text-lg font-bold text-transparent">
                    {index === 0 ? "Start" : `${item.users}`}
                </p>
            </div>
        </div>
    );
}

export default function PriceTimeline({ className }) {
    const [currentUserCount, setCurrentUserCount] = React.useState(0);
    const [isLoading, setIsLoading] = React.useState(true);

    // Fetch user count on component mount with retry logic
    React.useEffect(() => {
        const fetchUserCount = async (retries = 3, delayMs = 1000) => {
            for (let i = 0; i < retries; i++) {
                try {
                    const count = await getTotalUserCount();
                    setCurrentUserCount(count > 0 ? count : 39);
                    break;
                } catch (error) {
                    console.error(`Error fetching user count (attempt ${i + 1}):`, error);
                    if (i === retries - 1) {
                        setCurrentUserCount(39); // Fallback after all retries
                    }
                    await new Promise((resolve) => setTimeout(resolve, delayMs));
                }
            }
            setIsLoading(false);
        };

        fetchUserCount();
    }, []);

    // Get current price tier based on user count
    const getCurrentTierIndex = () => {
        for (let i = timelineData.length - 1; i > 0; i--) {
            if (currentUserCount >= timelineData[i].users) return i;
        }
        return 0;
    };

    const currentTierIndex = getCurrentTierIndex();
    // eslint-disable-next-line no-unused-vars
    const currentPrice = timelineData[currentTierIndex]?.price || "$0";

    // Calculate progress based on user count for static line position
    const calculateLinePosition = () => {
        // Find the position based on the actual user count
        let lastCrossedIndex = 0;
        for (let i = 1; i < timelineData.length; i++) {
            if (currentUserCount >= timelineData[i].users) {
                lastCrossedIndex = i;
            }
        }

        if (lastCrossedIndex === 0) {
            // If we're before the first milestone, calculate percentage within first segment
            return (currentUserCount / timelineData[1].users) * 25;
        }

        // If beyond first milestone, calculate exact position
        const currentMilestone = timelineData[lastCrossedIndex].users;
        const nextMilestone = timelineData[lastCrossedIndex + 1]?.users || currentMilestone * 2;
        const segmentWidth = 25; // Each segment is 25% of the timeline

        const basePercentage = lastCrossedIndex * segmentWidth;
        const extraPercentage = (currentUserCount - currentMilestone) / (nextMilestone - currentMilestone) * segmentWidth;

        return Math.min(basePercentage + extraPercentage, 100);
    };

    const linePosition = calculateLinePosition();

    if (isLoading) {
        return <div className="text-center text-white">Loading user count...</div>;
    } return (
        <div className={`flex w-full flex-col items-center py-12 ${className} relative`}>
            {/* Radial gradient background - exact same structure as Hero component */}
            <div className='absolute left-0 top-0 flex size-full items-center justify-center bg-white bg-dot-black/[0.05] dark:bg-black dark:bg-dot-white/[0.2]'>
                <div className='pointer-events-none absolute inset-0 flex items-center justify-center bg-white [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)] dark:bg-black'></div>
            </div>
            <div className="relative z-10 flex w-full flex-col items-center"><motion.div
                className="mx-auto mb-8 w-full max-w-5xl px-4" // Increased max-width from 3xl to 5xl
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1.0 }}
            >
                {/* Timeline Display with increased width and decreased height */}
                <div className="relative mb-16 w-full" style={{ height: "80px" }}> {/* Decreased height from 100px to 80px */}
                    {/* Timeline container */}
                    <div className="absolute inset-x-0 mx-auto w-[95%]" style={{ top: "40px" }}> {/* Increased width from 80% to 95% */}
                        {/* Base Timeline Line - Decreased height */}
                        <div className="absolute left-0 h-6 w-full rounded-xl bg-gray-700/50"></div> {/* Decreased height from 8 to 6 */}

                        {/* Static Progress Line - Decreased height */}
                        <div
                            className="via-purple-500 absolute left-0 h-6 rounded-xl bg-gradient-to-r from-blue-500 to-amber-600 shadow-lg shadow-amber-500/30"
                            style={{ width: `${linePosition}%` }}
                        />
                    </div>

                    {/* Timeline Points - Positioned within timeline */}
                    <div className="absolute inset-x-0 mx-auto flex w-[90%] justify-between">
                        {timelineData.map((item, index) => (
                            <TimelinePoint
                                key={index}
                                item={item}
                                index={index}
                                currentUserCount={currentUserCount}
                            />
                        ))}
                    </div>
                </div>
            </motion.div>
            </div>
        </div>
    );
}