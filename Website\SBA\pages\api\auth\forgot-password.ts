import crypto from "crypto";

import { NextApiRequest, NextApiResponse } from "next";

import { isBrevoConfigured } from "@/lib/brevo";
import { sendResetCode } from "@/lib/email";
import connectDB from "@/lib/mongodb";
import { User } from "@/model/User";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "POST") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  await connectDB();

  const { email } = req.body;
  if (!email) {
    return res.status(400).json({ message: "Email is required" });
  }

  // First check if <PERSON>re<PERSON> is properly configured
  if (!isBrevoConfigured()) {
    console.error("Brevo API is not configured. Email sending will fail.");
    return res.status(500).json({ 
      message: "Email service is not configured properly. Contact the administrator." 
    });
  }

  try {
    // Log for debugging
    console.log(`Attempting to find user with email: ${email}`);
    
    // Modified to find users with any provider
    const user = await User.findOne({ email });
    
    if (!user) {
      console.log(`No user found with email: ${email}`);
      // Don't reveal user existence but log it for debugging
      return res.status(200).json({ message: "If an account exists for that email, a reset code has been sent." });
    }

    console.log(`User found: ${user._id}, Provider: ${user.provider}. Generating reset code...`);
    
    const resetCode = crypto.randomBytes(4).toString("hex").toUpperCase();
    const resetCodeExpires = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes

    // Store the reset code in user document
    user.resetCode = resetCode;
    user.resetCodeExpires = resetCodeExpires;
    
    // Save user with reset code
    await user.save();

    console.log(`Reset code generated: ${resetCode}. Attempting to send email...`);

    try {
      // Send reset code email
      await sendResetCode(email, resetCode);
      console.log(`Reset code email sent successfully to ${email}`);
    } catch (emailError) {
      console.error("Error sending reset code email:", emailError);
      // Return error for debugging purposes (remove in production)
      return res.status(500).json({ 
        message: "Failed to send reset email", 
        error: process.env.NODE_ENV === 'development' && emailError instanceof Error ? emailError.message : undefined 
      });
    }

    // If this is a Clerk user, we'll let the client side handle the flow differently
    if (user.provider === "clerk") {
      return res.status(200).json({ 
        message: "If an account exists for that email, a reset code has been sent.",
        usingClerk: true
      });
    }

    // Return success
    return res.status(200).json({ message: "If an account exists for that email, a reset code has been sent." });
  } catch (error) {
    console.error("Forgot password error:", error);
    return res.status(500).json({ 
      message: "Internal server error",
      error: process.env.NODE_ENV === 'development' && error instanceof Error ? error.message : undefined
    });
  }
}