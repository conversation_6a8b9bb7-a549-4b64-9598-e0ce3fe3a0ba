/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./app/globals.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(121 181 236 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(121 181 236 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.14 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

:root {
  --inherit: inherit;
  --current: currentColor;
  --transparent: transparent;
  --black-100: rgb(0 0 0);;
  --black-200: rgb(0 0 0);;
  --black-300: rgba(255, 255, 255, 0.125);
  --black: #000;
  --white-100: #BEC1DD;
  --white-200: #C1C2D3;
  --white: #FFF;
  --slate-50: #f8fafc;
  --slate-100: #f1f5f9;
  --slate-200: #e2e8f0;
  --slate-300: #cbd5e1;
  --slate-400: #94a3b8;
  --slate-500: #64748b;
  --slate-600: #475569;
  --slate-700: #334155;
  --slate-800: #1e293b;
  --slate-900: #0f172a;
  --slate-950: #020617;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --gray-950: #030712;
  --zinc-50: #fafafa;
  --zinc-100: #f4f4f5;
  --zinc-200: #e4e4e7;
  --zinc-300: #d4d4d8;
  --zinc-400: #a1a1aa;
  --zinc-500: #71717a;
  --zinc-600: #52525b;
  --zinc-700: #3f3f46;
  --zinc-800: #27272a;
  --zinc-900: #18181b;
  --zinc-950: #09090b;
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;
  --neutral-950: #0a0a0a;
  --stone-50: #fafaf9;
  --stone-100: #f5f5f4;
  --stone-200: #e7e5e4;
  --stone-300: #d6d3d1;
  --stone-400: #a8a29e;
  --stone-500: #78716c;
  --stone-600: #57534e;
  --stone-700: #44403c;
  --stone-800: #292524;
  --stone-900: #1c1917;
  --stone-950: #0c0a09;
  --red-50: #fef2f2;
  --red-100: #fee2e2;
  --red-200: #fecaca;
  --red-300: #fca5a5;
  --red-400: #f87171;
  --red-500: #F37877;
  --red-600: #3E1716;
  --red-700: #F24E43;
  --red-800: #991b1b;
  --red-900: #7f1d1d;
  --red-950: #450a0a;
  --orange-50: #fff7ed;
  --orange-100: #ffedd5;
  --orange-200: #fed7aa;
  --orange-300: #fdba74;
  --orange-400: #fb923c;
  --orange-500: #f97316;
  --orange-600: #ea580c;
  --orange-700: #c2410c;
  --orange-800: #9a3412;
  --orange-900: #7c2d12;
  --orange-950: #431407;
  --amber-50: #fffbeb;
  --amber-100: #fef3c7;
  --amber-200: #fde68a;
  --amber-300: #fcd34d;
  --amber-400: #fbbf24;
  --amber-500: #f59e0b;
  --amber-600: #d97706;
  --amber-700: #b45309;
  --amber-800: #92400e;
  --amber-900: #78350f;
  --amber-950: #451a03;
  --yellow-50: #fefce8;
  --yellow-100: #fef9c3;
  --yellow-200: #fef08a;
  --yellow-300: #fde047;
  --yellow-400: #facc15;
  --yellow-500: #eab308;
  --yellow-600: #ca8a04;
  --yellow-700: #a16207;
  --yellow-800: #854d0e;
  --yellow-900: #713f12;
  --yellow-950: #422006;
  --lime-50: #f7fee7;
  --lime-100: #ecfccb;
  --lime-200: #d9f99d;
  --lime-300: #bef264;
  --lime-400: #a3e635;
  --lime-500: #84cc16;
  --lime-600: #65a30d;
  --lime-700: #4d7c0f;
  --lime-800: #3f6212;
  --lime-900: #365314;
  --lime-950: #1a2e05;
  --green-50: #f0fdf4;
  --green-100: #dcfce7;
  --green-200: #bbf7d0;
  --green-300: #86efac;
  --green-400: #4ade80;
  --green-500: #24AE7C;
  --green-600: #16a34a;
  --green-700: #0D2A1F;
  --green-800: #166534;
  --green-900: #14532d;
  --green-950: #052e16;
  --emerald-50: #ecfdf5;
  --emerald-100: #d1fae5;
  --emerald-200: #a7f3d0;
  --emerald-300: #6ee7b7;
  --emerald-400: #34d399;
  --emerald-500: #10b981;
  --emerald-600: #059669;
  --emerald-700: #047857;
  --emerald-800: #065f46;
  --emerald-900: #064e3b;
  --emerald-950: #022c22;
  --teal-50: #f0fdfa;
  --teal-100: #ccfbf1;
  --teal-200: #99f6e4;
  --teal-300: #5eead4;
  --teal-400: #2dd4bf;
  --teal-500: #14b8a6;
  --teal-600: #0d9488;
  --teal-700: #0f766e;
  --teal-800: #115e59;
  --teal-900: #134e4a;
  --teal-950: #042f2e;
  --cyan-50: #ecfeff;
  --cyan-100: #cffafe;
  --cyan-200: #a5f3fc;
  --cyan-300: #67e8f9;
  --cyan-400: #22d3ee;
  --cyan-500: #06b6d4;
  --cyan-600: #0891b2;
  --cyan-700: #0e7490;
  --cyan-800: #155e75;
  --cyan-900: #164e63;
  --cyan-950: #083344;
  --sky-50: #f0f9ff;
  --sky-100: #e0f2fe;
  --sky-200: #bae6fd;
  --sky-300: #7dd3fc;
  --sky-400: #38bdf8;
  --sky-500: #0ea5e9;
  --sky-600: #0284c7;
  --sky-700: #0369a1;
  --sky-800: #075985;
  --sky-900: #0c4a6e;
  --sky-950: #082f49;
  --blue-50: #eff6ff;
  --blue-100: #E4ECFF;
  --blue-200: #bfdbfe;
  --blue-300: #93c5fd;
  --blue-400: #60a5fa;
  --blue-500: #79B5EC;
  --blue-600: #152432;
  --blue-700: #1d4ed8;
  --blue-800: #1e40af;
  --blue-900: #1e3a8a;
  --blue-950: #172554;
  --indigo-50: #eef2ff;
  --indigo-100: #e0e7ff;
  --indigo-200: #c7d2fe;
  --indigo-300: #a5b4fc;
  --indigo-400: #818cf8;
  --indigo-500: #6366f1;
  --indigo-600: #4f46e5;
  --indigo-700: #4338ca;
  --indigo-800: #3730a3;
  --indigo-900: #312e81;
  --indigo-950: #1e1b4b;
  --violet-50: #f5f3ff;
  --violet-100: #ede9fe;
  --violet-200: #ddd6fe;
  --violet-300: #c4b5fd;
  --violet-400: #a78bfa;
  --violet-500: #8b5cf6;
  --violet-600: #7c3aed;
  --violet-700: #6d28d9;
  --violet-800: #5b21b6;
  --violet-900: #4c1d95;
  --violet-950: #2e1065;
  --purple: #CBACF9;
  --fuchsia-50: #fdf4ff;
  --fuchsia-100: #fae8ff;
  --fuchsia-200: #f5d0fe;
  --fuchsia-300: #f0abfc;
  --fuchsia-400: #e879f9;
  --fuchsia-500: #d946ef;
  --fuchsia-600: #c026d3;
  --fuchsia-700: #a21caf;
  --fuchsia-800: #86198f;
  --fuchsia-900: #701a75;
  --fuchsia-950: #4a044e;
  --pink-50: #fdf2f8;
  --pink-100: #fce7f3;
  --pink-200: #fbcfe8;
  --pink-300: #f9a8d4;
  --pink-400: #f472b6;
  --pink-500: #ec4899;
  --pink-600: #db2777;
  --pink-700: #be185d;
  --pink-800: #9d174d;
  --pink-900: #831843;
  --pink-950: #500724;
  --rose-50: #fff1f2;
  --rose-100: #ffe4e6;
  --rose-200: #fecdd3;
  --rose-300: #fda4af;
  --rose-400: #fb7185;
  --rose-500: #f43f5e;
  --rose-600: #e11d48;
  --rose-700: #be123c;
  --rose-800: #9f1239;
  --rose-900: #881337;
  --rose-950: #4c0519;
  --background: hsl(var(--background));
  --foreground-50: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --foreground-100: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --foreground-200: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --foreground-300: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --foreground-400: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --foreground-500: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --foreground-600: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --foreground-700: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --foreground-800: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --foreground-900: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --foreground: hsl(var(--foreground));
  --divider: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --focus: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --overlay: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --content1: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --content1-foreground: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --content2: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --content2-foreground: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --content3: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --content3-foreground: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --content4: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --content4-foreground: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --default-50: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --default-100: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --default-200: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --default-300: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --default-400: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --default-500: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --default-600: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --default-700: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --default-800: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --default-900: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --default-foreground: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --default: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --primary-50: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --primary-100: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --primary-200: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --primary-300: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --primary-400: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --primary-500: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --primary-600: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --primary-700: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --primary-800: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --primary-900: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --primary-foreground: hsl(var(--primary-foreground));
  --primary: hsl(var(--primary));
  --secondary-50: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --secondary-100: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --secondary-200: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --secondary-300: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --secondary-400: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --secondary-500: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --secondary-600: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --secondary-700: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --secondary-800: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --secondary-900: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --secondary-foreground: hsl(var(--secondary-foreground));
  --secondary: hsl(var(--secondary));
  --success-50: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --success-100: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --success-200: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --success-300: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --success-400: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --success-500: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --success-600: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --success-700: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --success-800: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --success-900: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --success-foreground: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --success: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --warning-50: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --warning-100: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --warning-200: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --warning-300: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --warning-400: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --warning-500: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --warning-600: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --warning-700: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --warning-800: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --warning-900: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --warning-foreground: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --warning: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --danger-50: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --danger-100: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --danger-200: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --danger-300: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --danger-400: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --danger-500: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --danger-600: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --danger-700: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --danger-800: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --danger-900: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --danger-foreground: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --danger: ({ opacityVariable, opacityValue }) => {
          if (!isNaN(+opacityValue)) {
            return `hsl(var(${nextuiColorVariable}) / ${opacityValue})`;
          }
          if (opacityVariable) {
            return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, var(${opacityVariable})))`;
          }
          return `hsl(var(${nextuiColorVariable}) / var(${nextuiOpacityVariable}, 1))`;
        };
  --dark-200: #0D0F10;
  --dark-300: #131619;
  --dark-400: #1A1D21;
  --dark-500: #363A3D;
  --dark-600: #76828D;
  --dark-700: #ABB8C4;
  --light-200: #E8E9E9;
  --border: hsl(var(--border));
  --input: hsl(var(--input));
  --ring: hsl(var(--ring));
  --destructive: hsl(var(--destructive));
  --destructive-foreground: hsl(var(--destructive-foreground));
  --muted: hsl(var(--muted));
  --muted-foreground: hsl(var(--muted-foreground));
  --accent: hsl(var(--accent));
  --accent-foreground: hsl(var(--accent-foreground));
  --popover: hsl(var(--popover));
  --popover-foreground: hsl(var(--popover-foreground));
  --card: hsl(var(--card));
  --card-foreground: hsl(var(--card-foreground));
}

:root, [data-theme] {
  color: hsl(var(--nextui-foreground));
  background-color: hsl(var(--nextui-background));
}
  /* Remove scrollbar */
  .remove-scrollbar::-webkit-scrollbar {
    width: 0px;
    height: 0px;
    border-radius: 0px;
  }

  .remove-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .remove-scrollbar::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 0px;
  }

  .remove-scrollbar::-webkit-scrollbar-thumb:hover {
    /* background: #1e2238; */
    background: transparent;
  }
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;

    --radius: 0.5rem;

    --radial-gradient-background: 250, 250, 250;
    --solid-color-background: 15, 15, 15;
    --overlay-color: 255, 255, 255;
  }
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-5: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-2: 340 75% 55%;

    --radial-gradient-background: 250, 250, 250;
    --solid-color-background: 15, 15, 15;
    --overlay-color: 255, 255, 255;
  }
  * {
  scroll-behavior: smooth !important;
  border-color: hsl(var(--border));
}
  body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
    transition-property: opacity, transform, filter, background;
    transition-timing-function: ease-in-out;
    transition-duration: 500ms;
}
  button:active {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1rem;
  padding-left: 1rem;
}
@media (min-width: 1400px) {

  .container {
    max-width: 1400px;
  }
}
.page-transition {
    opacity: 0;
    background: black;
    transform: translateY(40px);
    filter: blur(12px);
  }
.no-visible-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
    -webkit-overflow-scrolling: touch;
  }
.no-visible-scrollbar::-webkit-scrollbar {
    display: none;
  }
.radial {
    background: #1c1c1c;
    background: radial-gradient(at center top, #1c1c1c, #06081c);
  }
.radial-2 {
    background: radial-gradient(at center, #1c1c1c, #06081c);
  }
.glassmorphism {
    background: rgba(255, 255, 255, 0.05);
    /* box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37); */
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
  }
.cardglass {
    /* background: rgba(255, 255, 255, 0.05);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border-radius: 10px; */
    background: rgba(255, 255, 255, 0.05);
    /* box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37); */
    backdrop-filter: blur(0.5px);
    -webkit-backdrop-filter: blur(0.5px);
  }
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.pointer-events-auto {
  pointer-events: auto;
}
.\!visible {
  visibility: visible !important;
}
.visible {
  visibility: visible;
}
.invisible {
  visibility: hidden;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.sticky {
  position: sticky;
}
.-inset-0\.5 {
  inset: -0.125rem;
}
.-inset-px {
  inset: -1px;
}
.inset-0 {
  inset: 0px;
}
.inset-\[-1000\%\] {
  inset: -1000%;
}
.inset-auto {
  inset: auto;
}
.inset-x-0 {
  left: 0px;
  right: 0px;
}
.inset-x-10 {
  left: 2.5rem;
  right: 2.5rem;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.inset-y-1 {
  top: 0.25rem;
  bottom: 0.25rem;
}
.inset-y-9 {
  top: 2.25rem;
  bottom: 2.25rem;
}
.-bottom-12 {
  bottom: -3rem;
}
.-bottom-20 {
  bottom: -5rem;
}
.-bottom-24 {
  bottom: -6rem;
}
.-bottom-5 {
  bottom: -1.25rem;
}
.-bottom-px {
  bottom: -1px;
}
.-left-1\/2 {
  left: -50%;
}
.-left-12 {
  left: -3rem;
}
.-left-16 {
  left: -4rem;
}
.-left-20 {
  left: -5rem;
}
.-left-5 {
  left: -1.25rem;
}
.-left-\[22px\] {
  left: -22px;
}
.-right-1\/4 {
  right: -25%;
}
.-right-10 {
  right: -2.5rem;
}
.-right-12 {
  right: -3rem;
}
.-right-20 {
  right: -5rem;
}
.-right-3 {
  right: -0.75rem;
}
.-top-1\/2 {
  top: -50%;
}
.-top-1\/4 {
  top: -25%;
}
.-top-10 {
  top: -2.5rem;
}
.-top-12 {
  top: -3rem;
}
.-top-16 {
  top: -4rem;
}
.-top-20 {
  top: -5rem;
}
.-top-28 {
  top: -7rem;
}
.-top-3 {
  top: -0.75rem;
}
.-top-60 {
  top: -15rem;
}
.-top-\[14px\] {
  top: -14px;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-10 {
  bottom: 2.5rem;
}
.bottom-3 {
  bottom: 0.75rem;
}
.bottom-6 {
  bottom: 1.5rem;
}
.bottom-\[10\%\] {
  bottom: 10%;
}
.bottom-\[5\%\] {
  bottom: 5%;
}
.end-3 {
  inset-inline-end: 0.75rem;
}
.end-auto {
  inset-inline-end: auto;
}
.left-0 {
  left: 0px;
}
.left-1\.5 {
  left: 0.375rem;
}
.left-1\/2 {
  left: 50%;
}
.left-1\/3 {
  left: 33.333333%;
}
.left-1\/4 {
  left: 25%;
}
.left-10 {
  left: 2.5rem;
}
.left-2 {
  left: 0.5rem;
}
.left-\[10\%\] {
  left: 10%;
}
.left-\[5\%\] {
  left: 5%;
}
.left-\[50\%\] {
  left: 50%;
}
.left-\[calc\(50\%-var\(--size\)\/2\)\] {
  left: calc(50% - var(--size) / 2);
}
.right-0 {
  right: 0px;
}
.right-1 {
  right: 0.25rem;
}
.right-1\.5 {
  right: 0.375rem;
}
.right-1\/2 {
  right: 50%;
}
.right-10 {
  right: 2.5rem;
}
.right-3 {
  right: 0.75rem;
}
.right-4 {
  right: 1rem;
}
.right-5 {
  right: 1.25rem;
}
.right-\[10\%\] {
  right: 10%;
}
.right-\[5\%\] {
  right: 5%;
}
.start-2 {
  inset-inline-start: 0.5rem;
}
.start-3 {
  inset-inline-start: 0.75rem;
}
.start-auto {
  inset-inline-start: auto;
}
.top-0 {
  top: 0px;
}
.top-1 {
  top: 0.25rem;
}
.top-1\/2 {
  top: 50%;
}
.top-16 {
  top: 4rem;
}
.top-20 {
  top: 5rem;
}
.top-3 {
  top: 0.75rem;
}
.top-3\/4 {
  top: 75%;
}
.top-36 {
  top: 9rem;
}
.top-4 {
  top: 1rem;
}
.top-5 {
  top: 1.25rem;
}
.top-\[10\%\] {
  top: 10%;
}
.top-\[40px\] {
  top: 40px;
}
.top-\[5\%\] {
  top: 5%;
}
.top-\[50\%\] {
  top: 50%;
}
.top-\[60\%\] {
  top: 60%;
}
.top-\[70px\] {
  top: 70px;
}
.top-\[calc\(100\%_\+_2px\)\] {
  top: calc(100% + 2px);
}
.top-\[calc\(50\%-var\(--size\)\/2\)\] {
  top: calc(50% - var(--size) / 2);
}
.top-\[var\(--navbar-height\)\] {
  top: var(--navbar-height);
}
.top-full {
  top: 100%;
}
.top-px {
  top: 1px;
}
.isolate {
  isolation: isolate;
}
.-z-0 {
  z-index: 0;
}
.-z-10 {
  z-index: -10;
}
.-z-30 {
  z-index: -30;
}
.z-0 {
  z-index: 0;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-30 {
  z-index: 30;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.z-\[1000\] {
  z-index: 1000;
}
.z-\[1\] {
  z-index: 1;
}
.z-\[60\] {
  z-index: 60;
}
.z-\[70\] {
  z-index: 70;
}
.order-1 {
  order: 1;
}
.order-2 {
  order: 2;
}
.order-3 {
  order: 3;
}
.\!m-0 {
  margin: 0px !important;
}
.-m-2 {
  margin: -0.5rem;
}
.-m-2\.5 {
  margin: -0.625rem;
}
.m-0 {
  margin: 0px;
}
.m-2 {
  margin: 0.5rem;
}
.m-auto {
  margin: auto;
}
.m-px {
  margin: 1px;
}
.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
.-my-2 {
  margin-top: -0.5rem;
  margin-bottom: -0.5rem;
}
.mx-0 {
  margin-left: 0px;
  margin-right: 0px;
}
.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.mx-8 {
  margin-left: 2rem;
  margin-right: 2rem;
}
.mx-\[calc\(\(theme\(spacing\.5\)-theme\(spacing\.1\)\)\/2\)\] {
  margin-left: calc((1.25rem - 0.25rem) / 2);
  margin-right: calc((1.25rem - 0.25rem) / 2);
}
.mx-\[calc\(\(theme\(spacing\.6\)-theme\(spacing\.3\)\)\/2\)\] {
  margin-left: calc((1.5rem - 0.75rem) / 2);
  margin-right: calc((1.5rem - 0.75rem) / 2);
}
.mx-\[calc\(\(theme\(spacing\.7\)-theme\(spacing\.5\)\)\/2\)\] {
  margin-left: calc((1.75rem - 1.25rem) / 2);
  margin-right: calc((1.75rem - 1.25rem) / 2);
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-0 {
  margin-top: 0px;
  margin-bottom: 0px;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-1\.5 {
  margin-top: 0.375rem;
  margin-bottom: 0.375rem;
}
.my-10 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}
.my-12 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.my-16 {
  margin-top: 4rem;
  margin-bottom: 4rem;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-2\.5 {
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
}
.my-20 {
  margin-top: 5rem;
  margin-bottom: 5rem;
}
.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-5 {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}
.my-\[calc\(\(theme\(spacing\.5\)-theme\(spacing\.1\)\)\/2\)\] {
  margin-top: calc((1.25rem - 0.25rem) / 2);
  margin-bottom: calc((1.25rem - 0.25rem) / 2);
}
.my-\[calc\(\(theme\(spacing\.6\)-theme\(spacing\.3\)\)\/2\)\] {
  margin-top: calc((1.5rem - 0.75rem) / 2);
  margin-bottom: calc((1.5rem - 0.75rem) / 2);
}
.my-\[calc\(\(theme\(spacing\.7\)-theme\(spacing\.5\)\)\/2\)\] {
  margin-top: calc((1.75rem - 1.25rem) / 2);
  margin-bottom: calc((1.75rem - 1.25rem) / 2);
}
.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}
.-ml-1 {
  margin-left: -0.25rem;
}
.-ml-4 {
  margin-left: -1rem;
}
.-mr-2 {
  margin-right: -0.5rem;
}
.-mr-4 {
  margin-right: -1rem;
}
.-ms-2 {
  margin-inline-start: -0.5rem;
}
.-mt-16 {
  margin-top: -4rem;
}
.-mt-4 {
  margin-top: -1rem;
}
.-mt-48 {
  margin-top: -12rem;
}
.mb-0 {
  margin-bottom: 0px;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-1\.5 {
  margin-bottom: 0.375rem;
}
.mb-10 {
  margin-bottom: 2.5rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.mb-16 {
  margin-bottom: 4rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-20 {
  margin-bottom: 5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-7 {
  margin-bottom: 1.75rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.mb-px {
  margin-bottom: 1px;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-12 {
  margin-left: 3rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-8 {
  margin-left: 2rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-10 {
  margin-right: 2.5rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.ms-2 {
  margin-inline-start: 0.5rem;
}
.mt-0\.5 {
  margin-top: 0.125rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-1\.5 {
  margin-top: 0.375rem;
}
.mt-10 {
  margin-top: 2.5rem;
}
.mt-12 {
  margin-top: 3rem;
}
.mt-16 {
  margin-top: 4rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-20 {
  margin-top: 5rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-5 {
  margin-top: 1.25rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-7 {
  margin-top: 1.75rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-auto {
  margin-top: auto;
}
.box-border {
  box-sizing: border-box;
}
.box-content {
  box-sizing: content-box;
}
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.\!table {
  display: table !important;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.inline-grid {
  display: inline-grid;
}
.contents {
  display: contents;
}
.hidden {
  display: none;
}
.aspect-video {
  aspect-ratio: 16 / 9;
}
.size-1 {
  width: 0.25rem;
  height: 0.25rem;
}
.size-1\.5 {
  width: 0.375rem;
  height: 0.375rem;
}
.size-1\/3 {
  width: 33.333333%;
  height: 33.333333%;
}
.size-10 {
  width: 2.5rem;
  height: 2.5rem;
}
.size-12 {
  width: 3rem;
  height: 3rem;
}
.size-14 {
  width: 3.5rem;
  height: 3.5rem;
}
.size-16 {
  width: 4rem;
  height: 4rem;
}
.size-2 {
  width: 0.5rem;
  height: 0.5rem;
}
.size-2\.5 {
  width: 0.625rem;
  height: 0.625rem;
}
.size-24 {
  width: 6rem;
  height: 6rem;
}
.size-3 {
  width: 0.75rem;
  height: 0.75rem;
}
.size-3\.5 {
  width: 0.875rem;
  height: 0.875rem;
}
.size-32 {
  width: 8rem;
  height: 8rem;
}
.size-4 {
  width: 1rem;
  height: 1rem;
}
.size-40 {
  width: 10rem;
  height: 10rem;
}
.size-48 {
  width: 12rem;
  height: 12rem;
}
.size-5 {
  width: 1.25rem;
  height: 1.25rem;
}
.size-6 {
  width: 1.5rem;
  height: 1.5rem;
}
.size-64 {
  width: 16rem;
  height: 16rem;
}
.size-8 {
  width: 2rem;
  height: 2rem;
}
.size-96 {
  width: 24rem;
  height: 24rem;
}
.size-full {
  width: 100%;
  height: 100%;
}
.\!h-auto {
  height: auto !important;
}
.h-0\.5 {
  height: 0.125rem;
}
.h-1 {
  height: 0.25rem;
}
.h-1\.5 {
  height: 0.375rem;
}
.h-1\/2 {
  height: 50%;
}
.h-10 {
  height: 2.5rem;
}
.h-11 {
  height: 2.75rem;
}
.h-12 {
  height: 3rem;
}
.h-14 {
  height: 3.5rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-2\.5 {
  height: 0.625rem;
}
.h-20 {
  height: 5rem;
}
.h-24 {
  height: 6rem;
}
.h-3 {
  height: 0.75rem;
}
.h-3\.5 {
  height: 0.875rem;
}
.h-3\/6 {
  height: 50%;
}
.h-36 {
  height: 9rem;
}
.h-4 {
  height: 1rem;
}
.h-40 {
  height: 10rem;
}
.h-44 {
  height: 11rem;
}
.h-48 {
  height: 12rem;
}
.h-5 {
  height: 1.25rem;
}
.h-56 {
  height: 14rem;
}
.h-6 {
  height: 1.5rem;
}
.h-7 {
  height: 1.75rem;
}
.h-72 {
  height: 18rem;
}
.h-8 {
  height: 2rem;
}
.h-80 {
  height: 20rem;
}
.h-9 {
  height: 2.25rem;
}
.h-96 {
  height: 24rem;
}
.h-\[--visual-viewport-height\] {
  height: var(--visual-viewport-height);
}
.h-\[100dvh\] {
  height: 100dvh;
}
.h-\[169\%\] {
  height: 169%;
}
.h-\[200px\] {
  height: 200px;
}
.h-\[2px\] {
  height: 2px;
}
.h-\[3px\] {
  height: 3px;
}
.h-\[80px\] {
  height: 80px;
}
.h-\[calc\(100dvh_-_var\(--navbar-height\)\)\] {
  height: calc(100dvh - var(--navbar-height));
}
.h-\[var\(--navbar-height\)\] {
  height: var(--navbar-height);
}
.h-\[var\(--picker-height\)\] {
  height: var(--picker-height);
}
.h-\[var\(--radix-navigation-menu-viewport-height\)\] {
  height: var(--radix-navigation-menu-viewport-height);
}
.h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}
.h-\[var\(--size\)\] {
  height: var(--size);
}
.h-auto {
  height: auto;
}
.h-divider {
  height: var(--nextui-divider-weight);
}
.h-fit {
  height: -moz-fit-content;
  height: fit-content;
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.h-screen {
  height: 100vh;
}
.max-h-64 {
  max-height: 16rem;
}
.max-h-96 {
  max-height: 24rem;
}
.max-h-\[300px\] {
  max-height: 300px;
}
.max-h-\[80vh\] {
  max-height: 80vh;
}
.max-h-\[85vh\] {
  max-height: 85vh;
}
.max-h-\[90\%\] {
  max-height: 90%;
}
.max-h-\[calc\(100\%_-_8rem\)\] {
  max-height: calc(100% - 8rem);
}
.min-h-10 {
  min-height: 2.5rem;
}
.min-h-12 {
  min-height: 3rem;
}
.min-h-14 {
  min-height: 3.5rem;
}
.min-h-16 {
  min-height: 4rem;
}
.min-h-3 {
  min-height: 0.75rem;
}
.min-h-3\.5 {
  min-height: 0.875rem;
}
.min-h-4 {
  min-height: 1rem;
}
.min-h-40 {
  min-height: 10rem;
}
.min-h-5 {
  min-height: 1.25rem;
}
.min-h-6 {
  min-height: 1.5rem;
}
.min-h-7 {
  min-height: 1.75rem;
}
.min-h-8 {
  min-height: 2rem;
}
.min-h-96 {
  min-height: 24rem;
}
.min-h-\[100dvh\] {
  min-height: 100dvh;
}
.min-h-\[100px\] {
  min-height: 100px;
}
.min-h-\[32px\] {
  min-height: 32px;
}
.min-h-\[50vh\] {
  min-height: 50vh;
}
.min-h-\[80vh\] {
  min-height: 80vh;
}
.min-h-screen {
  min-height: 100vh;
}
.w-1 {
  width: 0.25rem;
}
.w-1\.5 {
  width: 0.375rem;
}
.w-1\/5 {
  width: 20%;
}
.w-10 {
  width: 2.5rem;
}
.w-12 {
  width: 3rem;
}
.w-14 {
  width: 3.5rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-2\.5 {
  width: 0.625rem;
}
.w-2\/5 {
  width: 40%;
}
.w-20 {
  width: 5rem;
}
.w-24 {
  width: 6rem;
}
.w-28 {
  width: 7rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\.5 {
  width: 0.875rem;
}
.w-3\/4 {
  width: 75%;
}
.w-32 {
  width: 8rem;
}
.w-4 {
  width: 1rem;
}
.w-40 {
  width: 10rem;
}
.w-48 {
  width: 12rem;
}
.w-5 {
  width: 1.25rem;
}
.w-56 {
  width: 14rem;
}
.w-6 {
  width: 1.5rem;
}
.w-64 {
  width: 16rem;
}
.w-7 {
  width: 1.75rem;
}
.w-72 {
  width: 18rem;
}
.w-8 {
  width: 2rem;
}
.w-9 {
  width: 2.25rem;
}
.w-\[120px\] {
  width: 120px;
}
.w-\[138\%\] {
  width: 138%;
}
.w-\[150px\] {
  width: 150px;
}
.w-\[160px\] {
  width: 160px;
}
.w-\[165px\] {
  width: 165px;
}
.w-\[200px\] {
  width: 200px;
}
.w-\[28px\] {
  width: 28px;
}
.w-\[28rem\] {
  width: 28rem;
}
.w-\[30rem\] {
  width: 30rem;
}
.w-\[35px\] {
  width: 35px;
}
.w-\[5\%\] {
  width: 5%;
}
.w-\[50\%\] {
  width: 50%;
}
.w-\[80\%\] {
  width: 80%;
}
.w-\[90\%\] {
  width: 90%;
}
.w-\[95\%\] {
  width: 95%;
}
.w-\[calc\(100\%_-_16px\)\] {
  width: calc(100% - 16px);
}
.w-\[calc\(100\%_-_theme\(spacing\.6\)\)\] {
  width: calc(100% - 1.5rem);
}
.w-\[calc\(var\(--visible-months\)_\*_var\(--calendar-width\)\)\] {
  width: calc(var(--visible-months) * var(--calendar-width));
}
.w-\[var\(--size\)\] {
  width: var(--size);
}
.w-auto {
  width: auto;
}
.w-divider {
  width: var(--nextui-divider-weight);
}
.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}
.w-full {
  width: 100%;
}
.w-max {
  width: -moz-max-content;
  width: max-content;
}
.w-px {
  width: 1px;
}
.w-screen {
  width: 100vw;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-10 {
  min-width: 2.5rem;
}
.min-w-12 {
  min-width: 3rem;
}
.min-w-16 {
  min-width: 4rem;
}
.min-w-20 {
  min-width: 5rem;
}
.min-w-24 {
  min-width: 6rem;
}
.min-w-3 {
  min-width: 0.75rem;
}
.min-w-3\.5 {
  min-width: 0.875rem;
}
.min-w-4 {
  min-width: 1rem;
}
.min-w-5 {
  min-width: 1.25rem;
}
.min-w-6 {
  min-width: 1.5rem;
}
.min-w-7 {
  min-width: 1.75rem;
}
.min-w-8 {
  min-width: 2rem;
}
.min-w-9 {
  min-width: 2.25rem;
}
.min-w-\[200px\] {
  min-width: 200px;
}
.min-w-\[8rem\] {
  min-width: 8rem;
}
.min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}
.min-w-full {
  min-width: 100%;
}
.min-w-max {
  min-width: -moz-max-content;
  min-width: max-content;
}
.min-w-min {
  min-width: -moz-min-content;
  min-width: min-content;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-5xl {
  max-width: 64rem;
}
.max-w-60 {
  max-width: 15rem;
}
.max-w-6xl {
  max-width: 72rem;
}
.max-w-7xl {
  max-width: 80rem;
}
.max-w-96 {
  max-width: 24rem;
}
.max-w-\[100vw\] {
  max-width: 100vw;
}
.max-w-\[1024px\] {
  max-width: 1024px;
}
.max-w-\[1280px\] {
  max-width: 1280px;
}
.max-w-\[1536px\] {
  max-width: 1536px;
}
.max-w-\[180px\] {
  max-width: 180px;
}
.max-w-\[270px\] {
  max-width: 270px;
}
.max-w-\[280px\] {
  max-width: 280px;
}
.max-w-\[496px\] {
  max-width: 496px;
}
.max-w-\[640px\] {
  max-width: 640px;
}
.max-w-\[75\%\] {
  max-width: 75%;
}
.max-w-\[768px\] {
  max-width: 768px;
}
.max-w-\[80vw\] {
  max-width: 80vw;
}
.max-w-fit {
  max-width: -moz-fit-content;
  max-width: fit-content;
}
.max-w-full {
  max-width: 100%;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-max {
  max-width: -moz-max-content;
  max-width: max-content;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-screen-2xl {
  max-width: 1536px;
}
.max-w-sm {
  max-width: 24rem;
}
.max-w-xl {
  max-width: 36rem;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-auto {
  flex: 1 1 auto;
}
.flex-initial {
  flex: 0 1 auto;
}
.flex-none {
  flex: none;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.shrink-0 {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}
.grow {
  flex-grow: 1;
}
.grow-0 {
  flex-grow: 0;
}
.basis-0 {
  flex-basis: 0px;
}
.basis-full {
  flex-basis: 100%;
}
.table-auto {
  table-layout: auto;
}
.table-fixed {
  table-layout: fixed;
}
.caption-bottom {
  caption-side: bottom;
}
.border-collapse {
  border-collapse: collapse;
}
.origin-center {
  transform-origin: center;
}
.origin-left {
  transform-origin: left;
}
.origin-right {
  transform-origin: right;
}
.origin-top {
  transform-origin: top;
}
.origin-top-left {
  transform-origin: top left;
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-24 {
  --tw-translate-y: -6rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-28 {
  --tw-translate-y: -7rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-56 {
  --tw-translate-y: -14rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-\[50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-1\/2 {
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-\[-50\%\] {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-1 {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-1\/2 {
  --tw-translate-y: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-12 {
  --tw-translate-y: 3rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-5 {
  --tw-translate-y: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[-50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-1 {
  --tw-rotate: -1deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-0 {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-1 {
  --tw-rotate: 1deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-0 {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-50 {
  --tw-scale-x: .5;
  --tw-scale-y: .5;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-\[1\.03\] {
  --tw-scale-x: 1.03;
  --tw-scale-y: 1.03;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-x-150 {
  --tw-scale-x: 1.5;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-y-125 {
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-\[pulse_1\.5s_ease-in-out_infinite_0\.2s\] {
  animation: pulse 1.5s ease-in-out infinite 0.2s;
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-\[pulse_1\.5s_ease-in-out_infinite_0\.4s\] {
  animation: pulse 1.5s ease-in-out infinite 0.4s;
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-\[pulse_1\.5s_ease-in-out_infinite_0\.6s\] {
  animation: pulse 1.5s ease-in-out infinite 0.6s;
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-\[spin_2s_linear_infinite\] {
  animation: spin 2s linear infinite;
}
@keyframes drip-expand {

  0% {
    opacity: 0.2;
    transform: scale(0);
  }

  100% {
    opacity: 0;
    transform: scale(2);
  }
}
.animate-drip-expand {
  animation: drip-expand 420ms linear;
}
@keyframes moveInCircle {

  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
.animate-fifth {
  animation: moveInCircle 20s ease infinite;
}
@keyframes moveVertical {

  0% {
    transform: translateY(-50%);
  }

  50% {
    transform: translateY(50%);
  }

  100% {
    transform: translateY(-50%);
  }
}
.animate-first {
  animation: moveVertical 30s ease infinite;
}
@keyframes moveHorizontal {

  0% {
    transform: translateX(-50%) translateY(-10%);
  }

  50% {
    transform: translateX(50%) translateY(10%);
  }

  100% {
    transform: translateX(-50%) translateY(-10%);
  }
}
.animate-fourth {
  animation: moveHorizontal 40s ease infinite;
}
@keyframes indeterminate-bar {

  0% {
    transform: translateX(-50%) scaleX(0.2);
  }

  100% {
    transform: translateX(100%) scaleX(1);
  }
}
.animate-indeterminate-bar {
  animation: indeterminate-bar 1.5s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite normal none running;
}
.animate-none {
  animation: none;
}
@keyframes ping {

  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes ripple {

  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
  }

  50% {
    transform: translate(-50%, -50%) scale(0.9);
  }
}
.animate-ripple {
  animation: ripple var(--duration,2s) ease calc(var(--i, 0)*.2s) infinite;
}
@keyframes scroll {

  to {
    transform: translate(calc(-50% - 0.5rem));
  }
}
.animate-scroll {
  animation: scroll var(--animation-duration, 40s) var(--animation-direction, forwards) linear infinite;
}
@keyframes moveInCircle {

  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
.animate-second {
  animation: moveInCircle 20s reverse infinite;
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
@keyframes spinner-spin {

  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
.animate-spinner-ease-spin {
  animation: spinner-spin 0.8s ease infinite;
}
@keyframes spinner-spin {

  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
.animate-spinner-linear-spin {
  animation: spinner-spin 0.8s linear infinite;
}
@keyframes spotlight {

  0% {
    opacity: 0;
    transform: translate(-72%, -62%) scale(0.5);
  }

  100% {
    opacity: 1;
    transform: translate(-50%,-40%) scale(1);
  }
}
.animate-spotlight {
  animation: spotlight 2s ease .75s 1 forwards;
}
@keyframes moveInCircle {

  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(180deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
.animate-third {
  animation: moveInCircle 40s linear infinite;
}
.cursor-crosshair {
  cursor: crosshair;
}
.cursor-default {
  cursor: default;
}
.cursor-grab {
  cursor: grab;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.cursor-text {
  cursor: text;
}
.touch-none {
  touch-action: none;
}
.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.resize-none {
  resize: none;
}
.resize {
  resize: both;
}
.snap-y {
  scroll-snap-type: y var(--tw-scroll-snap-strictness);
}
.snap-mandatory {
  --tw-scroll-snap-strictness: mandatory;
}
.snap-center {
  scroll-snap-align: center;
}
.scroll-py-6 {
  scroll-padding-top: 1.5rem;
  scroll-padding-bottom: 1.5rem;
}
.list-inside {
  list-style-position: inside;
}
.list-decimal {
  list-style-type: decimal;
}
.list-disc {
  list-style-type: disc;
}
.list-none {
  list-style-type: none;
}
.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.flex-row {
  flex-direction: row;
}
.flex-row-reverse {
  flex-direction: row-reverse;
}
.flex-col {
  flex-direction: column;
}
.flex-col-reverse {
  flex-direction: column-reverse;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
.place-content-center {
  place-content: center;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.items-baseline {
  align-items: baseline;
}
.items-stretch {
  align-items: stretch;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-around {
  justify-content: space-around;
}
.justify-items-end {
  justify-items: end;
}
.\!gap-0 {
  gap: 0px !important;
}
.gap-0 {
  gap: 0px;
}
.gap-0\.5 {
  gap: 0.125rem;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-1\.5 {
  gap: 0.375rem;
}
.gap-10 {
  gap: 2.5rem;
}
.gap-12 {
  gap: 3rem;
}
.gap-16 {
  gap: 4rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-20 {
  gap: 5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-5 {
  gap: 1.25rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-7 {
  gap: 1.75rem;
}
.gap-8 {
  gap: 2rem;
}
.gap-9 {
  gap: 2.25rem;
}
.gap-x-0\.5 {
  -moz-column-gap: 0.125rem;
       column-gap: 0.125rem;
}
.gap-x-2 {
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}
.gap-x-6 {
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}
.gap-y-1\.5 {
  row-gap: 0.375rem;
}
.gap-y-2 {
  row-gap: 0.5rem;
}
.-space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0px * var(--tw-space-x-reverse));
  margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.125rem * var(--tw-space-x-reverse));
  margin-left: calc(0.125rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.375rem * var(--tw-space-x-reverse));
  margin-left: calc(0.375rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.self-center {
  align-self: center;
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-visible {
  overflow: visible;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-x-hidden {
  overflow-x: hidden;
}
.overflow-y-hidden {
  overflow-y: hidden;
}
.overflow-x-scroll {
  overflow-x: scroll;
}
.overflow-y-scroll {
  overflow-y: scroll;
}
.overscroll-x-auto {
  overscroll-behavior-x: auto;
}
.scroll-smooth {
  scroll-behavior: smooth;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text-ellipsis {
  text-overflow: ellipsis;
}
.whitespace-normal {
  white-space: normal;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.whitespace-pre-line {
  white-space: pre-line;
}
.text-wrap {
  text-wrap: wrap;
}
.text-pretty {
  text-wrap: pretty;
}
.break-words {
  overflow-wrap: break-word;
}
.\!rounded-none {
  border-radius: 0px !important;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-3xl {
  border-radius: 1.5rem;
}
.rounded-\[calc\(theme\(borderRadius\.large\)\/1\.5\)\] {
  border-radius: calc(var(--nextui-radius-large) / 1.5);
}
.rounded-\[calc\(theme\(borderRadius\.medium\)\*0\.5\)\] {
  border-radius: calc(var(--nextui-radius-medium) * 0.5);
}
.rounded-\[calc\(theme\(borderRadius\.medium\)\*0\.6\)\] {
  border-radius: calc(var(--nextui-radius-medium) * 0.6);
}
.rounded-\[calc\(theme\(borderRadius\.medium\)\*0\.7\)\] {
  border-radius: calc(var(--nextui-radius-medium) * 0.7);
}
.rounded-\[calc\(theme\(borderRadius\.medium\)\/2\)\] {
  border-radius: calc(var(--nextui-radius-medium) / 2);
}
.rounded-\[calc\(theme\(borderRadius\.small\)\/2\)\] {
  border-radius: calc(var(--nextui-radius-small) / 2);
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-large {
  border-radius: var(--nextui-radius-large);
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-medium {
  border-radius: var(--nextui-radius-medium);
}
.rounded-none {
  border-radius: 0px;
}
.rounded-sm {
  border-radius: 0.125rem;
}
.rounded-small {
  border-radius: var(--nextui-radius-small);
}
.rounded-xl {
  border-radius: 0.75rem;
}
.\!rounded-e-none {
  border-start-end-radius: 0px !important;
  border-end-end-radius: 0px !important;
}
.\!rounded-s-none {
  border-start-start-radius: 0px !important;
  border-end-start-radius: 0px !important;
}
.rounded-b-large {
  border-bottom-right-radius: var(--nextui-radius-large);
  border-bottom-left-radius: var(--nextui-radius-large);
}
.rounded-b-medium {
  border-bottom-right-radius: var(--nextui-radius-medium);
  border-bottom-left-radius: var(--nextui-radius-medium);
}
.rounded-b-small {
  border-bottom-right-radius: var(--nextui-radius-small);
  border-bottom-left-radius: var(--nextui-radius-small);
}
.rounded-b-xl {
  border-bottom-right-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}
.rounded-l-xl {
  border-top-left-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}
.rounded-r-xl {
  border-top-right-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}
.rounded-t-3xl {
  border-top-left-radius: 1.5rem;
  border-top-right-radius: 1.5rem;
}
.rounded-t-large {
  border-top-left-radius: var(--nextui-radius-large);
  border-top-right-radius: var(--nextui-radius-large);
}
.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}
.rounded-t-md {
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
}
.rounded-t-medium {
  border-top-left-radius: var(--nextui-radius-medium);
  border-top-right-radius: var(--nextui-radius-medium);
}
.rounded-t-small {
  border-top-left-radius: var(--nextui-radius-small);
  border-top-right-radius: var(--nextui-radius-small);
}
.rounded-t-xl {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}
.rounded-tl-sm {
  border-top-left-radius: 0.125rem;
}
.border {
  border-width: 1px;
}
.border-0 {
  border-width: 0px;
}
.border-2 {
  border-width: 2px;
}
.border-3 {
  border-width: 3px;
}
.border-4 {
  border-width: 4px;
}
.border-\[0\.5px\] {
  border-width: 0.5px;
}
.border-\[0\.9px\] {
  border-width: 0.9px;
}
.border-medium {
  border-width: var(--nextui-border-width-medium);
}
.border-small {
  border-width: var(--nextui-border-width-small);
}
.border-x-\[calc\(theme\(spacing\.5\)\/2\)\] {
  border-left-width: calc(1.25rem / 2);
  border-right-width: calc(1.25rem / 2);
}
.border-x-\[calc\(theme\(spacing\.6\)\/2\)\] {
  border-left-width: calc(1.5rem / 2);
  border-right-width: calc(1.5rem / 2);
}
.border-x-\[calc\(theme\(spacing\.7\)\/2\)\] {
  border-left-width: calc(1.75rem / 2);
  border-right-width: calc(1.75rem / 2);
}
.border-y {
  border-top-width: 1px;
  border-bottom-width: 1px;
}
.border-y-\[calc\(theme\(spacing\.5\)\/2\)\] {
  border-top-width: calc(1.25rem / 2);
  border-bottom-width: calc(1.25rem / 2);
}
.border-y-\[calc\(theme\(spacing\.6\)\/2\)\] {
  border-top-width: calc(1.5rem / 2);
  border-bottom-width: calc(1.5rem / 2);
}
.border-y-\[calc\(theme\(spacing\.7\)\/2\)\] {
  border-top-width: calc(1.75rem / 2);
  border-bottom-width: calc(1.75rem / 2);
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-b-4 {
  border-bottom-width: 4px;
}
.border-b-medium {
  border-bottom-width: var(--nextui-border-width-medium);
}
.border-l {
  border-left-width: 1px;
}
.border-r {
  border-right-width: 1px;
}
.border-t {
  border-top-width: 1px;
}
.border-solid {
  border-style: solid;
}
.border-dotted {
  border-style: dotted;
}
.border-none {
  border-style: none;
}
.\!border-danger {
  --tw-border-opacity: 1 !important;
  border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity))) !important;
}
.border-\[\#0f172a\] {
  --tw-border-opacity: 1;
  border-color: rgb(15 23 42 / var(--tw-border-opacity));
}
.border-\[\#353535\] {
  --tw-border-opacity: 1;
  border-color: rgb(53 53 53 / var(--tw-border-opacity));
}
.border-amber-800\/50 {
  border-color: rgb(146 64 14 / 0.5);
}
.border-background {
  border-color: hsl(var(--background));
}
.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(121 181 236 / var(--tw-border-opacity));
}
.border-blue-500\/30 {
  border-color: rgb(121 181 236 / 0.3);
}
.border-blue-500\/50 {
  border-color: rgb(121 181 236 / 0.5);
}
.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgb(21 36 50 / var(--tw-border-opacity));
}
.border-blue-800\/50 {
  border-color: rgb(30 64 175 / 0.5);
}
.border-danger {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));
}
.border-dark-500 {
  --tw-border-opacity: 1;
  border-color: rgb(54 58 61 / var(--tw-border-opacity));
}
.border-default {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-border-opacity)));
}
.border-default-200 {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-default-200) / var(--nextui-default-200-opacity, var(--tw-border-opacity)));
}
.border-default-300 {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-default-300) / var(--nextui-default-300-opacity, var(--tw-border-opacity)));
}
.border-divider {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-divider) / var(--nextui-divider-opacity, var(--tw-border-opacity)));
}
.border-foreground {
  border-color: hsl(var(--foreground));
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}
.border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity));
}
.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity));
}
.border-gray-700\/40 {
  border-color: rgb(55 65 81 / 0.4);
}
.border-gray-700\/50 {
  border-color: rgb(55 65 81 / 0.5);
}
.border-gray-800 {
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity));
}
.border-gray-800\/30 {
  border-color: rgb(31 41 55 / 0.3);
}
.border-gray-800\/40 {
  border-color: rgb(31 41 55 / 0.4);
}
.border-gray-800\/50 {
  border-color: rgb(31 41 55 / 0.5);
}
.border-green-500\/40 {
  border-color: rgb(36 174 124 / 0.4);
}
.border-green-500\/50 {
  border-color: rgb(36 174 124 / 0.5);
}
.border-green-500\/70 {
  border-color: rgb(36 174 124 / 0.7);
}
.border-green-800\/50 {
  border-color: rgb(22 101 52 / 0.5);
}
.border-indigo-700\/50 {
  border-color: rgb(67 56 202 / 0.5);
}
.border-indigo-800\/50 {
  border-color: rgb(55 48 163 / 0.5);
}
.border-neutral-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 229 229 / var(--tw-border-opacity));
}
.border-primary {
  border-color: hsl(var(--primary));
}
.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(243 120 119 / var(--tw-border-opacity));
}
.border-red-500\/30 {
  border-color: rgb(243 120 119 / 0.3);
}
.border-red-500\/50 {
  border-color: rgb(243 120 119 / 0.5);
}
.border-red-800\/50 {
  border-color: rgb(153 27 27 / 0.5);
}
.border-red-900\/40 {
  border-color: rgb(127 29 29 / 0.4);
}
.border-secondary {
  border-color: hsl(var(--secondary));
}
.border-slate-200 {
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity));
}
.border-slate-700\/40 {
  border-color: rgb(51 65 85 / 0.4);
}
.border-slate-700\/50 {
  border-color: rgb(51 65 85 / 0.5);
}
.border-slate-800 {
  --tw-border-opacity: 1;
  border-color: rgb(30 41 59 / var(--tw-border-opacity));
}
.border-slate-800\/40 {
  border-color: rgb(30 41 59 / 0.4);
}
.border-slate-800\/50 {
  border-color: rgb(30 41 59 / 0.5);
}
.border-slate-800\/60 {
  border-color: rgb(30 41 59 / 0.6);
}
.border-slate-900 {
  --tw-border-opacity: 1;
  border-color: rgb(15 23 42 / var(--tw-border-opacity));
}
.border-slate-950 {
  --tw-border-opacity: 1;
  border-color: rgb(2 6 23 / var(--tw-border-opacity));
}
.border-success {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));
}
.border-transparent {
  border-color: transparent;
}
.border-warning {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));
}
.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}
.border-yellow-500 {
  --tw-border-opacity: 1;
  border-color: rgb(234 179 8 / var(--tw-border-opacity));
}
.border-yellow-500\/30 {
  border-color: rgb(234 179 8 / 0.3);
}
.border-yellow-600 {
  --tw-border-opacity: 1;
  border-color: rgb(202 138 4 / var(--tw-border-opacity));
}
.border-x-transparent {
  border-left-color: transparent;
  border-right-color: transparent;
}
.border-y-transparent {
  border-top-color: transparent;
  border-bottom-color: transparent;
}
.border-b-current {
  border-bottom-color: currentColor;
}
.border-b-danger {
  --tw-border-opacity: 1;
  border-bottom-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));
}
.border-b-default {
  --tw-border-opacity: 1;
  border-bottom-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-border-opacity)));
}
.border-b-foreground {
  border-bottom-color: hsl(var(--foreground));
}
.border-b-primary {
  border-bottom-color: hsl(var(--primary));
}
.border-b-secondary {
  border-bottom-color: hsl(var(--secondary));
}
.border-b-success {
  --tw-border-opacity: 1;
  border-bottom-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));
}
.border-b-warning {
  --tw-border-opacity: 1;
  border-bottom-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));
}
.border-b-white {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(255 255 255 / var(--tw-border-opacity));
}
.border-l-transparent {
  border-left-color: transparent;
}
.border-r-transparent {
  border-right-color: transparent;
}
.border-s-danger {
  --tw-border-opacity: 1;
  border-inline-start-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));
}
.border-s-foreground {
  border-inline-start-color: hsl(var(--foreground));
}
.border-s-primary {
  border-inline-start-color: hsl(var(--primary));
}
.border-s-secondary {
  border-inline-start-color: hsl(var(--secondary));
}
.border-s-success {
  --tw-border-opacity: 1;
  border-inline-start-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));
}
.border-s-warning {
  --tw-border-opacity: 1;
  border-inline-start-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));
}
.border-t-\[\#24AE7C\] {
  --tw-border-opacity: 1;
  border-top-color: rgb(36 174 124 / var(--tw-border-opacity));
}
.border-t-blue-500 {
  --tw-border-opacity: 1;
  border-top-color: rgb(121 181 236 / var(--tw-border-opacity));
}
.border-t-gray-600 {
  --tw-border-opacity: 1;
  border-top-color: rgb(75 85 99 / var(--tw-border-opacity));
}
.border-t-green-500 {
  --tw-border-opacity: 1;
  border-top-color: rgb(36 174 124 / var(--tw-border-opacity));
}
.border-t-transparent {
  border-top-color: transparent;
}
.border-t-white\/10 {
  border-top-color: rgb(255 255 255 / 0.1);
}
.\!bg-\[\#161A31\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(22 26 49 / var(--tw-bg-opacity)) !important;
}
.\!bg-danger-50 {
  --tw-bg-opacity: 1 !important;
  background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity))) !important;
}
.bg-\[\#06081c\] {
  --tw-bg-opacity: 1;
  background-color: rgb(6 8 28 / var(--tw-bg-opacity));
}
.bg-\[\#0B879C\]\/90 {
  background-color: rgb(11 135 156 / 0.9);
}
.bg-\[\#0f172a\] {
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}
.bg-\[\#0f172a\]\/60 {
  background-color: rgb(15 23 42 / 0.6);
}
.bg-\[\#0f172a\]\/95 {
  background-color: rgb(15 23 42 / 0.95);
}
.bg-\[\#181f33\] {
  --tw-bg-opacity: 1;
  background-color: rgb(24 31 51 / var(--tw-bg-opacity));
}
.bg-\[\#181f33\]\/80 {
  background-color: rgb(24 31 51 / 0.8);
}
.bg-\[\#1a141f\] {
  --tw-bg-opacity: 1;
  background-color: rgb(26 20 31 / var(--tw-bg-opacity));
}
.bg-\[\#1a1a1a\]\/95 {
  background-color: rgb(26 26 26 / 0.95);
}
.bg-\[\#24AE7C\] {
  --tw-bg-opacity: 1;
  background-color: rgb(36 174 124 / var(--tw-bg-opacity));
}
.bg-\[\#24AE7C\]\/10 {
  background-color: rgb(36 174 124 / 0.1);
}
.bg-\[\#24AE7C\]\/20 {
  background-color: rgb(36 174 124 / 0.2);
}
.bg-\[\#24AE7C\]\/5 {
  background-color: rgb(36 174 124 / 0.05);
}
.bg-\[\#F5F5F7\] {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 247 / var(--tw-bg-opacity));
}
.bg-amber-900\/20 {
  background-color: rgb(120 53 15 / 0.2);
}
.bg-background {
  background-color: hsl(var(--background));
}
.bg-background\/10 {
  background-color: hsl(var(--background) / 0.1);
}
.bg-background\/70 {
  background-color: hsl(var(--background) / 0.7);
}
.bg-background\/80 {
  background-color: hsl(var(--background) / 0.8);
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}
.bg-black-100 {
  background-color: rgb(0 0 0);;
}
.bg-black\/30 {
  background-color: rgb(0 0 0 / 0.3);
}
.bg-black\/40 {
  background-color: rgb(0 0 0 / 0.4);
}
.bg-black\/50 {
  background-color: rgb(0 0 0 / 0.5);
}
.bg-black\/60 {
  background-color: rgb(0 0 0 / 0.6);
}
.bg-black\/70 {
  background-color: rgb(0 0 0 / 0.7);
}
.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}
.bg-black\/85 {
  background-color: rgb(0 0 0 / 0.85);
}
.bg-black\/90 {
  background-color: rgb(0 0 0 / 0.9);
}
.bg-blue-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity));
}
.bg-blue-500\/10 {
  background-color: rgb(121 181 236 / 0.1);
}
.bg-blue-500\/20 {
  background-color: rgb(121 181 236 / 0.2);
}
.bg-blue-500\/5 {
  background-color: rgb(121 181 236 / 0.05);
}
.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(21 36 50 / var(--tw-bg-opacity));
}
.bg-blue-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity));
}
.bg-blue-700\/40 {
  background-color: rgb(29 78 216 / 0.4);
}
.bg-blue-900\/10 {
  background-color: rgb(30 58 138 / 0.1);
}
.bg-blue-900\/50 {
  background-color: rgb(30 58 138 / 0.5);
}
.bg-card {
  background-color: hsl(var(--card));
}
.bg-content1 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-content1) / var(--nextui-content1-opacity, var(--tw-bg-opacity)));
}
.bg-content3 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-content3) / var(--nextui-content3-opacity, var(--tw-bg-opacity)));
}
.bg-current {
  background-color: currentColor;
}
.bg-cyan-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 211 238 / var(--tw-bg-opacity));
}
.bg-cyan-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity));
}
.bg-danger {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));
}
.bg-danger-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger-100) / var(--nextui-danger-100-opacity, var(--tw-bg-opacity)));
}
.bg-danger-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity)));
}
.bg-danger\/20 {
  background-color: hsl(var(--nextui-danger) / 0.2);
}
.bg-dark-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(13 15 16 / var(--tw-bg-opacity));
}
.bg-dark-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(26 29 33 / var(--tw-bg-opacity));
}
.bg-default {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-bg-opacity)));
}
.bg-default-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));
}
.bg-default-200 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-200) / var(--nextui-default-200-opacity, var(--tw-bg-opacity)));
}
.bg-default-300\/50 {
  background-color: hsl(var(--nextui-default-300) / 0.5);
}
.bg-default-400 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-400) / var(--nextui-default-400-opacity, var(--tw-bg-opacity)));
}
.bg-default-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-50) / var(--nextui-default-50-opacity, var(--tw-bg-opacity)));
}
.bg-default-500 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-500) / var(--nextui-default-500-opacity, var(--tw-bg-opacity)));
}
.bg-default\/40 {
  background-color: hsl(var(--nextui-default) / 0.4);
}
.bg-divider {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-divider) / var(--nextui-divider-opacity, var(--tw-bg-opacity)));
}
.bg-foreground {
  background-color: hsl(var(--foreground));
}
.bg-foreground\/10 {
  background-color: hsl(var(--foreground) / 0.1);
}
.bg-foreground\/25 {
  background-color: hsl(var(--foreground) / 0.25);
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}
.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}
.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}
.bg-gray-700\/50 {
  background-color: rgb(55 65 81 / 0.5);
}
.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}
.bg-gray-800\/30 {
  background-color: rgb(31 41 55 / 0.3);
}
.bg-gray-800\/40 {
  background-color: rgb(31 41 55 / 0.4);
}
.bg-gray-800\/50 {
  background-color: rgb(31 41 55 / 0.5);
}
.bg-gray-800\/80 {
  background-color: rgb(31 41 55 / 0.8);
}
.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}
.bg-gray-900\/50 {
  background-color: rgb(17 24 39 / 0.5);
}
.bg-gray-900\/60 {
  background-color: rgb(17 24 39 / 0.6);
}
.bg-gray-900\/80 {
  background-color: rgb(17 24 39 / 0.8);
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(36 174 124 / var(--tw-bg-opacity));
}
.bg-green-500\/10 {
  background-color: rgb(36 174 124 / 0.1);
}
.bg-green-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(13 42 31 / var(--tw-bg-opacity));
}
.bg-green-700\/40 {
  background-color: rgb(13 42 31 / 0.4);
}
.bg-green-900\/60 {
  background-color: rgb(20 83 45 / 0.6);
}
.bg-indigo-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity));
}
.bg-indigo-700\/40 {
  background-color: rgb(67 56 202 / 0.4);
}
.bg-neutral-800\/80 {
  background-color: rgb(38 38 38 / 0.8);
}
.bg-overlay\/30 {
  background-color: hsl(var(--nextui-overlay) / 0.3);
}
.bg-overlay\/50 {
  background-color: hsl(var(--nextui-overlay) / 0.5);
}
.bg-primary {
  background-color: hsl(var(--primary));
}
.bg-primary-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-primary-100) / var(--nextui-primary-100-opacity, var(--tw-bg-opacity)));
}
.bg-primary-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-primary-50) / var(--nextui-primary-50-opacity, var(--tw-bg-opacity)));
}
.bg-primary\/10 {
  background-color: hsl(var(--primary) / 0.1);
}
.bg-primary\/20 {
  background-color: hsl(var(--primary) / 0.2);
}
.bg-red-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--tw-bg-opacity));
}
.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 120 119 / var(--tw-bg-opacity));
}
.bg-red-500\/10 {
  background-color: rgb(243 120 119 / 0.1);
}
.bg-red-500\/20 {
  background-color: rgb(243 120 119 / 0.2);
}
.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(62 23 22 / var(--tw-bg-opacity));
}
.bg-red-600\/80 {
  background-color: rgb(62 23 22 / 0.8);
}
.bg-red-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(242 78 67 / var(--tw-bg-opacity));
}
.bg-red-700\/20 {
  background-color: rgb(242 78 67 / 0.2);
}
.bg-red-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(153 27 27 / var(--tw-bg-opacity));
}
.bg-red-900\/20 {
  background-color: rgb(127 29 29 / 0.2);
}
.bg-secondary {
  background-color: hsl(var(--secondary));
}
.bg-secondary-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-secondary-100) / var(--nextui-secondary-100-opacity, var(--tw-bg-opacity)));
}
.bg-secondary-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-secondary-50) / var(--nextui-secondary-50-opacity, var(--tw-bg-opacity)));
}
.bg-secondary\/20 {
  background-color: hsl(var(--secondary) / 0.2);
}
.bg-slate-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}
.bg-slate-100\/50 {
  background-color: rgb(241 245 249 / 0.5);
}
.bg-slate-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity));
}
.bg-slate-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
}
.bg-slate-800\/60 {
  background-color: rgb(30 41 59 / 0.6);
}
.bg-slate-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}
.bg-slate-900\/\[0\.\] {
  background-color: rgb(15 23 42 / 0.);
}
.bg-slate-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(2 6 23 / var(--tw-bg-opacity));
}
.bg-success {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));
}
.bg-success-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success-100) / var(--nextui-success-100-opacity, var(--tw-bg-opacity)));
}
.bg-success-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success-50) / var(--nextui-success-50-opacity, var(--tw-bg-opacity)));
}
.bg-success\/20 {
  background-color: hsl(var(--nextui-success) / 0.2);
}
.bg-teal-500\/10 {
  background-color: rgb(20 184 166 / 0.1);
}
.bg-teal-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(13 148 136 / var(--tw-bg-opacity));
}
.bg-transparent {
  background-color: transparent;
}
.bg-warning {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));
}
.bg-warning-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning-100) / var(--nextui-warning-100-opacity, var(--tw-bg-opacity)));
}
.bg-warning-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning-50) / var(--nextui-warning-50-opacity, var(--tw-bg-opacity)));
}
.bg-warning\/20 {
  background-color: hsl(var(--nextui-warning) / 0.2);
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}
.bg-white\/5 {
  background-color: rgb(255 255 255 / 0.05);
}
.bg-yellow-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 204 21 / var(--tw-bg-opacity));
}
.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
}
.bg-zinc-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(39 39 42 / var(--tw-bg-opacity));
}
.bg-zinc-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(9 9 11 / var(--tw-bg-opacity));
}
.bg-opacity-70 {
  --tw-bg-opacity: 0.7;
}
.bg-opacity-80 {
  --tw-bg-opacity: 0.8;
}
.bg-\[conic-gradient\(from_90deg_at_50\%_50\%\2c \#E2CBFF_0\%\2c \#393BB2_50\%\2c \#E2CBFF_100\%\)\] {
  background-image: conic-gradient(from 90deg at 50% 50%,#E2CBFF 0%,#393BB2 50%,#E2CBFF 100%);
}
.bg-\[linear-gradient\(40deg\2c var\(--gradient-background-start\)\2c var\(--gradient-background-end\)\)\] {
  background-image: linear-gradient(40deg,var(--gradient-background-start),var(--gradient-background-end));
}
.bg-\[radial-gradient\(\#CBACF9_40\%\2c transparent_60\%\)\] {
  background-image: radial-gradient(#CBACF9 40%,transparent 60%);
}
.bg-appointments {
  background-image: url('/assets/images/appointments-bg.png');
}
.bg-cancelled {
  background-image: url('/assets/images/cancelled-bg.png');
}
.bg-gradient-conic {
  background-image: conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops));
}
.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-l {
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}
.bg-pending {
  background-image: url('/assets/images/pending-bg.png');
}
.bg-stripe-gradient {
  background-image: linear-gradient(45deg, rgba(0, 0, 0, 0.1) 25%, transparent 25%, transparent 50%, rgba(0, 0, 0, 0.1) 50%, rgba(0, 0, 0, 0.1) 75%, transparent 75%, transparent);
}
.from-\[\#141414\] {
  --tw-gradient-from: #141414 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(20 20 20 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#24AE7C\] {
  --tw-gradient-from: #24AE7C var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(36 174 124 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#24AE7C\]\/20 {
  --tw-gradient-from: rgb(36 174 124 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(36 174 124 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-900\/30 {
  --tw-gradient-from: rgb(120 53 15 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(120 53 15 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black {
  --tw-gradient-from: #000 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/50 {
  --tw-gradient-from: rgb(0 0 0 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-400 {
  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500 {
  --tw-gradient-from: #79B5EC var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(121 181 236 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-600 {
  --tw-gradient-from: #152432 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(21 36 50 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-900\/70 {
  --tw-gradient-from: rgb(30 58 138 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-cyan-400 {
  --tw-gradient-from: #22d3ee var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-cyan-500 {
  --tw-gradient-from: #06b6d4 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(6 182 212 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-200 {
  --tw-gradient-from: #e5e7eb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(229 231 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-800\/50 {
  --tw-gradient-from: rgb(31 41 55 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-800\/80 {
  --tw-gradient-from: rgb(31 41 55 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-900 {
  --tw-gradient-from: #111827 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-900\/90 {
  --tw-gradient-from: rgb(17 24 39 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-950 {
  --tw-gradient-from: #030712 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(3 7 18 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-400 {
  --tw-gradient-from: #4ade80 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(74 222 128 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500\/50 {
  --tw-gradient-from: rgb(36 174 124 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(36 174 124 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-900\/70 {
  --tw-gradient-from: rgb(20 83 45 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(20 83 45 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-900\/70 {
  --tw-gradient-from: rgb(49 46 129 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(49 46 129 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary {
  --tw-gradient-from: hsl(var(--primary)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-400 {
  --tw-gradient-from: #f87171 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(248 113 113 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-500\/20 {
  --tw-gradient-from: rgb(243 120 119 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(243 120 119 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-600 {
  --tw-gradient-from: #3E1716 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(62 23 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-300 {
  --tw-gradient-from: #cbd5e1 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(203 213 225 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-900 {
  --tw-gradient-from: #0f172a var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(15 23 42 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-transparent {
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white {
  --tw-gradient-from: #FFF var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-300 {
  --tw-gradient-from: #fde047 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(253 224 71 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-400 {
  --tw-gradient-from: #facc15 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 204 21 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-black {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #000 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-blue-400 {
  --tw-gradient-to: rgb(96 165 250 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #60a5fa var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-blue-500 {
  --tw-gradient-to: rgb(121 181 236 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #79B5EC var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-cyan-400 {
  --tw-gradient-to: rgb(34 211 238 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #22d3ee var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-cyan-500 {
  --tw-gradient-to: rgb(6 182 212 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #06b6d4 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-emerald-500 {
  --tw-gradient-to: rgb(16 185 129 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #10b981 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-gray-800 {
  --tw-gradient-to: rgb(31 41 55 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #1f2937 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-red-500 {
  --tw-gradient-to: rgb(243 120 119 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #F37877 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-sky-500 {
  --tw-gradient-to: rgb(14 165 233 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #0ea5e9 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-slate-900\/80 {
  --tw-gradient-to: rgb(15 23 42 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(15 23 42 / 0.8) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-transparent {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-\[\#1a1a1a\] {
  --tw-gradient-to: #1a1a1a var(--tw-gradient-to-position);
}
.to-\[\#24AE7C\]\/10 {
  --tw-gradient-to: rgb(36 174 124 / 0.1) var(--tw-gradient-to-position);
}
.to-\[\#24AE7C\]\/5 {
  --tw-gradient-to: rgb(36 174 124 / 0.05) var(--tw-gradient-to-position);
}
.to-amber-500 {
  --tw-gradient-to: #f59e0b var(--tw-gradient-to-position);
}
.to-amber-600 {
  --tw-gradient-to: #d97706 var(--tw-gradient-to-position);
}
.to-black {
  --tw-gradient-to: #000 var(--tw-gradient-to-position);
}
.to-black\/60 {
  --tw-gradient-to: rgb(0 0 0 / 0.6) var(--tw-gradient-to-position);
}
.to-blue-400 {
  --tw-gradient-to: #60a5fa var(--tw-gradient-to-position);
}
.to-blue-500 {
  --tw-gradient-to: #79B5EC var(--tw-gradient-to-position);
}
.to-blue-500\/50 {
  --tw-gradient-to: rgb(121 181 236 / 0.5) var(--tw-gradient-to-position);
}
.to-blue-800\/40 {
  --tw-gradient-to: rgb(30 64 175 / 0.4) var(--tw-gradient-to-position);
}
.to-blue-900\/10 {
  --tw-gradient-to: rgb(30 58 138 / 0.1) var(--tw-gradient-to-position);
}
.to-cyan-300 {
  --tw-gradient-to: #67e8f9 var(--tw-gradient-to-position);
}
.to-cyan-400 {
  --tw-gradient-to: #22d3ee var(--tw-gradient-to-position);
}
.to-cyan-500 {
  --tw-gradient-to: #06b6d4 var(--tw-gradient-to-position);
}
.to-gray-300 {
  --tw-gradient-to: #d1d5db var(--tw-gradient-to-position);
}
.to-gray-400 {
  --tw-gradient-to: #9ca3af var(--tw-gradient-to-position);
}
.to-gray-700 {
  --tw-gradient-to: #374151 var(--tw-gradient-to-position);
}
.to-gray-800 {
  --tw-gradient-to: #1f2937 var(--tw-gradient-to-position);
}
.to-gray-800\/60 {
  --tw-gradient-to: rgb(31 41 55 / 0.6) var(--tw-gradient-to-position);
}
.to-gray-900 {
  --tw-gradient-to: #111827 var(--tw-gradient-to-position);
}
.to-gray-900\/70 {
  --tw-gradient-to: rgb(17 24 39 / 0.7) var(--tw-gradient-to-position);
}
.to-gray-900\/90 {
  --tw-gradient-to: rgb(17 24 39 / 0.9) var(--tw-gradient-to-position);
}
.to-green-800\/40 {
  --tw-gradient-to: rgb(22 101 52 / 0.4) var(--tw-gradient-to-position);
}
.to-indigo-600 {
  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);
}
.to-indigo-800\/40 {
  --tw-gradient-to: rgb(55 48 163 / 0.4) var(--tw-gradient-to-position);
}
.to-orange-500 {
  --tw-gradient-to: #f97316 var(--tw-gradient-to-position);
}
.to-red-500\/20 {
  --tw-gradient-to: rgb(243 120 119 / 0.2) var(--tw-gradient-to-position);
}
.to-red-700 {
  --tw-gradient-to: #F24E43 var(--tw-gradient-to-position);
}
.to-secondary {
  --tw-gradient-to: hsl(var(--secondary)) var(--tw-gradient-to-position);
}
.to-slate-500 {
  --tw-gradient-to: #64748b var(--tw-gradient-to-position);
}
.to-teal-300 {
  --tw-gradient-to: #5eead4 var(--tw-gradient-to-position);
}
.to-teal-600 {
  --tw-gradient-to: #0d9488 var(--tw-gradient-to-position);
}
.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}
.to-white {
  --tw-gradient-to: #FFF var(--tw-gradient-to-position);
}
.to-\[84\%\] {
  --tw-gradient-to-position: 84%;
}
.bg-\[length\:1\.25rem_1\.25rem\] {
  background-size: 1.25rem 1.25rem;
}
.bg-clip-text {
  -webkit-background-clip: text;
          background-clip: text;
}
.fill-green-600 {
  fill: #16a34a;
}
.fill-red-500 {
  fill: #F37877;
}
.stroke-current {
  stroke: currentColor;
}
.stroke-default-300\/50 {
  stroke: hsl(var(--nextui-default-300) / 0.5);
}
.stroke-\[1px\] {
  stroke-width: 1px;
}
.stroke-\[3px\] {
  stroke-width: 3px;
}
.object-contain {
  -o-object-fit: contain;
     object-fit: contain;
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.object-center {
  -o-object-position: center;
     object-position: center;
}
.object-top {
  -o-object-position: top;
     object-position: top;
}
.\!p-0 {
  padding: 0px !important;
}
.p-0 {
  padding: 0px;
}
.p-0\.5 {
  padding: 0.125rem;
}
.p-1 {
  padding: 0.25rem;
}
.p-10 {
  padding: 2.5rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-2\.5 {
  padding: 0.625rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-7 {
  padding: 1.75rem;
}
.p-8 {
  padding: 2rem;
}
.p-\[1px\] {
  padding: 1px;
}
.p-px {
  padding: 1px;
}
.\!px-1 {
  padding-left: 0.25rem !important;
  padding-right: 0.25rem !important;
}
.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}
.px-0\.5 {
  padding-left: 0.125rem;
  padding-right: 0.125rem;
}
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}
.px-12 {
  padding-left: 3rem;
  padding-right: 3rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.px-\[9px\] {
  padding-left: 9px;
  padding-right: 9px;
}
.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-14 {
  padding-top: 3.5rem;
  padding-bottom: 3.5rem;
}
.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-24 {
  padding-top: 6rem;
  padding-bottom: 6rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-7 {
  padding-top: 1.75rem;
  padding-bottom: 1.75rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.\!pb-0 {
  padding-bottom: 0px !important;
}
.pb-0 {
  padding-bottom: 0px;
}
.pb-0\.5 {
  padding-bottom: 0.125rem;
}
.pb-1 {
  padding-bottom: 0.25rem;
}
.pb-1\.5 {
  padding-bottom: 0.375rem;
}
.pb-10 {
  padding-bottom: 2.5rem;
}
.pb-14 {
  padding-bottom: 3.5rem;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pb-20 {
  padding-bottom: 5rem;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pb-7 {
  padding-bottom: 1.75rem;
}
.pe-2 {
  padding-inline-end: 0.5rem;
}
.pl-0 {
  padding-left: 0px;
}
.pl-0\.5 {
  padding-left: 0.125rem;
}
.pl-1 {
  padding-left: 0.25rem;
}
.pl-10 {
  padding-left: 2.5rem;
}
.pl-2 {
  padding-left: 0.5rem;
}
.pl-3 {
  padding-left: 0.75rem;
}
.pl-4 {
  padding-left: 1rem;
}
.pl-5 {
  padding-left: 1.25rem;
}
.pl-8 {
  padding-left: 2rem;
}
.pr-0\.5 {
  padding-right: 0.125rem;
}
.pr-1 {
  padding-right: 0.25rem;
}
.pr-10 {
  padding-right: 2.5rem;
}
.pr-12 {
  padding-right: 3rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pr-3 {
  padding-right: 0.75rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pr-6 {
  padding-right: 1.5rem;
}
.ps-2 {
  padding-inline-start: 0.5rem;
}
.pt-0 {
  padding-top: 0px;
}
.pt-10 {
  padding-top: 2.5rem;
}
.pt-16 {
  padding-top: 4rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-20 {
  padding-top: 5rem;
}
.pt-24 {
  padding-top: 6rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-6 {
  padding-top: 1.5rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-start {
  text-align: start;
}
.text-end {
  text-align: end;
}
.align-middle {
  vertical-align: middle;
}
.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.font-sans {
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-5xl {
  font-size: 3rem;
  line-height: 1;
}
.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}
.text-7xl {
  font-size: 4.5rem;
  line-height: 1;
}
.text-\[0\.55rem\] {
  font-size: 0.55rem;
}
.text-\[0\.5rem\] {
  font-size: 0.5rem;
}
.text-\[0\.6rem\] {
  font-size: 0.6rem;
}
.text-\[42px\] {
  font-size: 42px;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-large {
  font-size: var(--nextui-font-size-large);
  line-height: var(--nextui-line-height-large);
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-medium {
  font-size: var(--nextui-font-size-medium);
  line-height: var(--nextui-line-height-medium);
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-small {
  font-size: var(--nextui-font-size-small);
  line-height: var(--nextui-line-height-small);
}
.text-tiny {
  font-size: var(--nextui-font-size-tiny);
  line-height: var(--nextui-line-height-tiny);
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-black {
  font-weight: 900;
}
.font-bold {
  font-weight: 700;
}
.font-extrabold {
  font-weight: 800;
}
.font-extralight {
  font-weight: 200;
}
.font-light {
  font-weight: 300;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.capitalize {
  text-transform: capitalize;
}
.tabular-nums {
  --tw-numeric-spacing: tabular-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}
.leading-6 {
  line-height: 1.5rem;
}
.leading-7 {
  line-height: 1.75rem;
}
.leading-\[32px\] {
  line-height: 32px;
}
.leading-none {
  line-height: 1;
}
.leading-relaxed {
  line-height: 1.625;
}
.leading-snug {
  line-height: 1.375;
}
.leading-tight {
  line-height: 1.25;
}
.tracking-\[0\.3em\] {
  letter-spacing: 0.3em;
}
.tracking-normal {
  letter-spacing: 0em;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.tracking-wide {
  letter-spacing: 0.025em;
}
.tracking-wider {
  letter-spacing: 0.05em;
}
.tracking-widest {
  letter-spacing: 0.1em;
}
.\!text-danger {
  --tw-text-opacity: 1 !important;
  color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity))) !important;
}
.\!text-danger-foreground {
  --tw-text-opacity: 1 !important;
  color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity))) !important;
}
.text-\[\#24AE7C\] {
  --tw-text-opacity: 1;
  color: rgb(36 174 124 / var(--tw-text-opacity));
}
.text-\[\#C1C2D3\] {
  --tw-text-opacity: 1;
  color: rgb(193 194 211 / var(--tw-text-opacity));
}
.text-\[\#CBACF9\] {
  --tw-text-opacity: 1;
  color: rgb(203 172 249 / var(--tw-text-opacity));
}
.text-\[\#ED5F5F\] {
  --tw-text-opacity: 1;
  color: rgb(237 95 95 / var(--tw-text-opacity));
}
.text-\[\#EF4444\] {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
}
.text-\[\#f8f9fa\] {
  --tw-text-opacity: 1;
  color: rgb(248 249 250 / var(--tw-text-opacity));
}
.text-amber-200 {
  --tw-text-opacity: 1;
  color: rgb(253 230 138 / var(--tw-text-opacity));
}
.text-amber-400 {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity));
}
.text-amber-500 {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity));
}
.text-background {
  color: hsl(var(--background));
}
.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}
.text-blue-300 {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity));
}
.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity));
}
.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(121 181 236 / var(--tw-text-opacity));
}
.text-card-foreground {
  color: hsl(var(--card-foreground));
}
.text-current {
  color: currentColor;
}
.text-danger {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));
}
.text-danger-300 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-300) / var(--nextui-danger-300-opacity, var(--tw-text-opacity)));
}
.text-danger-800 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-800) / var(--nextui-danger-800-opacity, var(--tw-text-opacity)));
}
.text-danger-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));
}
.text-danger\/80 {
  color: hsl(var(--nextui-danger) / 0.8);
}
.text-dark-600 {
  --tw-text-opacity: 1;
  color: rgb(118 130 141 / var(--tw-text-opacity));
}
.text-default-400 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-400) / var(--nextui-default-400-opacity, var(--tw-text-opacity)));
}
.text-default-500 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-500) / var(--nextui-default-500-opacity, var(--tw-text-opacity)));
}
.text-default-600 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-600) / var(--nextui-default-600-opacity, var(--tw-text-opacity)));
}
.text-default-700 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-700) / var(--nextui-default-700-opacity, var(--tw-text-opacity)));
}
.text-default-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-text-opacity)));
}
.text-foreground {
  color: hsl(var(--foreground));
}
.text-foreground-400 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-foreground-400) / var(--nextui-foreground-400-opacity, var(--tw-text-opacity)));
}
.text-foreground-500 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-foreground-500) / var(--nextui-foreground-500-opacity, var(--tw-text-opacity)));
}
.text-foreground-600 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-foreground-600) / var(--nextui-foreground-600-opacity, var(--tw-text-opacity)));
}
.text-foreground\/50 {
  color: hsl(var(--foreground) / 0.5);
}
.text-gray-100 {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}
.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}
.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}
.text-green-300 {
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity));
}
.text-green-400 {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity));
}
.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(36 174 124 / var(--tw-text-opacity));
}
.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity));
}
.text-indigo-300 {
  --tw-text-opacity: 1;
  color: rgb(165 180 252 / var(--tw-text-opacity));
}
.text-inherit {
  color: inherit;
}
.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}
.text-neutral-100 {
  --tw-text-opacity: 1;
  color: rgb(245 245 245 / var(--tw-text-opacity));
}
.text-neutral-300 {
  --tw-text-opacity: 1;
  color: rgb(212 212 212 / var(--tw-text-opacity));
}
.text-neutral-400 {
  --tw-text-opacity: 1;
  color: rgb(163 163 163 / var(--tw-text-opacity));
}
.text-neutral-600 {
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity));
}
.text-neutral-700 {
  --tw-text-opacity: 1;
  color: rgb(64 64 64 / var(--tw-text-opacity));
}
.text-neutral-800 {
  --tw-text-opacity: 1;
  color: rgb(38 38 38 / var(--tw-text-opacity));
}
.text-neutral-900 {
  --tw-text-opacity: 1;
  color: rgb(23 23 23 / var(--tw-text-opacity));
}
.text-primary {
  color: hsl(var(--primary));
}
.text-primary-300 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-primary-300) / var(--nextui-primary-300-opacity, var(--tw-text-opacity)));
}
.text-primary-700 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-primary-700) / var(--nextui-primary-700-opacity, var(--tw-text-opacity)));
}
.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}
.text-primary\/80 {
  color: hsl(var(--primary) / 0.8);
}
.text-red-200 {
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity));
}
.text-red-300 {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity));
}
.text-red-400 {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(243 120 119 / var(--tw-text-opacity));
}
.text-secondary {
  color: hsl(var(--secondary));
}
.text-secondary-300 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-secondary-300) / var(--nextui-secondary-300-opacity, var(--tw-text-opacity)));
}
.text-secondary-700 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-secondary-700) / var(--nextui-secondary-700-opacity, var(--tw-text-opacity)));
}
.text-secondary-foreground {
  color: hsl(var(--secondary-foreground));
}
.text-secondary\/80 {
  color: hsl(var(--secondary) / 0.8);
}
.text-slate-300 {
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity));
}
.text-slate-50 {
  --tw-text-opacity: 1;
  color: rgb(248 250 252 / var(--tw-text-opacity));
}
.text-slate-500 {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}
.text-slate-900 {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.text-slate-950 {
  --tw-text-opacity: 1;
  color: rgb(2 6 23 / var(--tw-text-opacity));
}
.text-success {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-text-opacity)));
}
.text-success-400 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-400) / var(--nextui-success-400-opacity, var(--tw-text-opacity)));
}
.text-success-600 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-600) / var(--nextui-success-600-opacity, var(--tw-text-opacity)));
}
.text-success-800 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-800) / var(--nextui-success-800-opacity, var(--tw-text-opacity)));
}
.text-success-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));
}
.text-success\/80 {
  color: hsl(var(--nextui-success) / 0.8);
}
.text-teal-600 {
  --tw-text-opacity: 1;
  color: rgb(13 148 136 / var(--tw-text-opacity));
}
.text-transparent {
  color: transparent;
}
.text-warning {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-text-opacity)));
}
.text-warning-400 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-400) / var(--nextui-warning-400-opacity, var(--tw-text-opacity)));
}
.text-warning-600 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-600) / var(--nextui-warning-600-opacity, var(--tw-text-opacity)));
}
.text-warning-800 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-800) / var(--nextui-warning-800-opacity, var(--tw-text-opacity)));
}
.text-warning-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));
}
.text-warning\/80 {
  color: hsl(var(--nextui-warning) / 0.8);
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.text-white-100 {
  --tw-text-opacity: 1;
  color: rgb(190 193 221 / var(--tw-text-opacity));
}
.text-white-200 {
  --tw-text-opacity: 1;
  color: rgb(193 194 211 / var(--tw-text-opacity));
}
.text-white\/40 {
  color: rgb(255 255 255 / 0.4);
}
.text-white\/60 {
  color: rgb(255 255 255 / 0.6);
}
.text-white\/70 {
  color: rgb(255 255 255 / 0.7);
}
.text-white\/80 {
  color: rgb(255 255 255 / 0.8);
}
.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity));
}
.text-zinc-600 {
  --tw-text-opacity: 1;
  color: rgb(82 82 91 / var(--tw-text-opacity));
}
.underline {
  text-decoration-line: underline;
}
.no-underline {
  text-decoration-line: none;
}
.underline-offset-4 {
  text-underline-offset: 4px;
}
.underline-offset-8 {
  text-underline-offset: 8px;
}
.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.subpixel-antialiased {
  -webkit-font-smoothing: auto;
  -moz-osx-font-smoothing: auto;
}
.opacity-0 {
  opacity: 0;
}
.opacity-10 {
  opacity: 0.1;
}
.opacity-100 {
  opacity: 1;
}
.opacity-25 {
  opacity: 0.25;
}
.opacity-30 {
  opacity: 0.3;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-60 {
  opacity: 0.6;
}
.opacity-70 {
  opacity: 0.7;
}
.opacity-75 {
  opacity: 0.75;
}
.opacity-80 {
  opacity: 0.8;
}
.opacity-\[0\.8\] {
  opacity: 0.8;
}
.opacity-\[value\] {
  opacity: value;
}
.opacity-disabled {
  opacity: var(--nextui-disabled-opacity);
}
.mix-blend-overlay {
  mix-blend-mode: overlay;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_1px_0px_0_rgba\(0\2c 0\2c 0\2c 0\.05\)\] {
  --tw-shadow: 0 1px 0px 0 rgba(0,0,0,0.05);
  --tw-shadow-colored: 0 1px 0px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0px_20px_20px_0px_rgb\(0_0_0\/0\.05\)\] {
  --tw-shadow: 0px 20px 20px 0px rgb(0 0 0/0.05);
  --tw-shadow-colored: 0px 20px 20px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-large {
  --tw-shadow: var(--nextui-box-shadow-large);
  --tw-shadow-colored: var(--nextui-box-shadow-large);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-medium {
  --tw-shadow: var(--nextui-box-shadow-medium);
  --tw-shadow-colored: var(--nextui-box-shadow-medium);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-small {
  --tw-shadow: var(--nextui-box-shadow-small);
  --tw-shadow-colored: var(--nextui-box-shadow-small);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-amber-500\/20 {
  --tw-shadow-color: rgb(245 158 11 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-amber-500\/30 {
  --tw-shadow-color: rgb(245 158 11 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-amber-900\/10 {
  --tw-shadow-color: rgb(120 53 15 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-black\/5 {
  --tw-shadow-color: rgb(0 0 0 / 0.05);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-blue-900\/20 {
  --tw-shadow-color: rgb(30 58 138 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-danger\/40 {
  --tw-shadow-color: hsl(var(--nextui-danger) / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-default\/50 {
  --tw-shadow-color: hsl(var(--nextui-default) / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-foreground\/40 {
  --tw-shadow-color: hsl(var(--foreground) / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-green-500\/20 {
  --tw-shadow-color: rgb(36 174 124 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-input {
  --tw-shadow-color: hsl(var(--input));
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-primary\/40 {
  --tw-shadow-color: hsl(var(--primary) / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-secondary\/40 {
  --tw-shadow-color: hsl(var(--secondary) / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-success\/40 {
  --tw-shadow-color: hsl(var(--nextui-success) / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-warning\/40 {
  --tw-shadow-color: hsl(var(--nextui-warning) / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-yellow-400\/50 {
  --tw-shadow-color: rgb(250 204 21 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-yellow-500\/20 {
  --tw-shadow-color: rgb(234 179 8 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.\!outline-none {
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline {
  outline-style: solid;
}
.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-background {
  --tw-ring-color: hsl(var(--background));
}
.ring-danger {
  --tw-ring-opacity: 1;
  --tw-ring-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-ring-opacity)));
}
.ring-default {
  --tw-ring-opacity: 1;
  --tw-ring-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-ring-opacity)));
}
.ring-focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: hsl(var(--nextui-focus) / var(--nextui-focus-opacity, var(--tw-ring-opacity)));
}
.ring-primary {
  --tw-ring-color: hsl(var(--primary));
}
.ring-secondary {
  --tw-ring-color: hsl(var(--secondary));
}
.ring-slate-950 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(2 6 23 / var(--tw-ring-opacity));
}
.ring-success {
  --tw-ring-opacity: 1;
  --tw-ring-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-ring-opacity)));
}
.ring-transparent {
  --tw-ring-color: transparent;
}
.ring-warning {
  --tw-ring-opacity: 1;
  --tw-ring-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-ring-opacity)));
}
.ring-offset-2 {
  --tw-ring-offset-width: 2px;
}
.ring-offset-background {
  --tw-ring-offset-color: hsl(var(--background));
}
.ring-offset-white {
  --tw-ring-offset-color: #FFF;
}
.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-0 {
  --tw-blur: blur(0);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-2xl {
  --tw-blur: blur(40px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-3xl {
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-\[100px\] {
  --tw-blur: blur(100px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-\[30px\] {
  --tw-blur: blur(30px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-\[50px\] {
  --tw-blur: blur(50px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-\[60px\] {
  --tw-blur: blur(60px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-lg {
  --tw-blur: blur(16px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-md {
  --tw-blur: blur(12px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-none {
  --tw-blur:  ;
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-sm {
  --tw-blur: blur(4px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow {
  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-\[0_2px_4px_rgba\(0\2c 255\2c 153\2c 0\.3\)\] {
  --tw-drop-shadow: drop-shadow(0 2px 4px rgba(0,255,153,0.3));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-lg {
  --tw-drop-shadow: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.saturate-150 {
  --tw-saturate: saturate(1.5);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur {
  --tw-backdrop-blur: blur(8px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-2xl {
  --tw-backdrop-blur: blur(40px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-3xl {
  --tw-backdrop-blur: blur(64px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-lg {
  --tw-backdrop-blur: blur(16px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-xl {
  --tw-backdrop-blur: blur(24px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-opacity-disabled {
  --tw-backdrop-opacity: opacity(var(--nextui-disabled-opacity));
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-saturate-150 {
  --tw-backdrop-saturate: saturate(1.5);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.\!transition-none {
  transition-property: none !important;
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}
.transition-\[color\2c opacity\] {
  transition-property: color,opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}
.transition-\[transform\2c background-color\2c color\] {
  transition-property: transform,background-color,color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}
.transition-\[transform\2c color\2c left\2c opacity\] {
  transition-property: transform,color,left,opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}
.transition-none {
  transition-property: none;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}
.\!duration-100 {
  transition-duration: 100ms !important;
}
.\!duration-150 {
  transition-duration: 150ms !important;
}
.\!duration-200 {
  transition-duration: 200ms !important;
}
.\!duration-250 {
  transition-duration: 250ms !important;
}
.\!duration-300 {
  transition-duration: 300ms !important;
}
.\!duration-500 {
  transition-duration: 500ms !important;
}
.duration-1000 {
  transition-duration: 1000ms;
}
.duration-150 {
  transition-duration: 150ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-500 {
  transition-duration: 500ms;
}
.duration-700 {
  transition-duration: 700ms;
}
.duration-75 {
  transition-duration: 75ms;
}
.\!ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1) !important;
}
.\!ease-soft-spring {
  transition-timing-function: cubic-bezier(0.155, 1.105, 0.295, 1.12) !important;
}
.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear {
  transition-timing-function: linear;
}
.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.will-change-auto {
  will-change: auto;
}
.will-change-transform {
  will-change: transform;
}
@keyframes enter {

  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}
@keyframes exit {

  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}
.fade-in-0 {
  --tw-enter-opacity: 0;
}
.zoom-in-95 {
  --tw-enter-scale: .95;
}
.\!duration-100 {
  animation-duration: 100ms !important;
}
.\!duration-150 {
  animation-duration: 150ms !important;
}
.\!duration-200 {
  animation-duration: 200ms !important;
}
.\!duration-250 {
  animation-duration: 250ms !important;
}
.\!duration-300 {
  animation-duration: 300ms !important;
}
.\!duration-500 {
  animation-duration: 500ms !important;
}
.duration-1000 {
  animation-duration: 1000ms;
}
.duration-150 {
  animation-duration: 150ms;
}
.duration-200 {
  animation-duration: 200ms;
}
.duration-300 {
  animation-duration: 300ms;
}
.duration-500 {
  animation-duration: 500ms;
}
.duration-700 {
  animation-duration: 700ms;
}
.duration-75 {
  animation-duration: 75ms;
}
.\!ease-out {
  animation-timing-function: cubic-bezier(0, 0, 0.2, 1) !important;
}
.\!ease-soft-spring {
  animation-timing-function: cubic-bezier(0.155, 1.105, 0.295, 1.12) !important;
}
.ease-in {
  animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear {
  animation-timing-function: linear;
}
.ease-out {
  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.running {
  animation-play-state: running;
}
.bg-dot-black\/\[0\.05\] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='16' height='16' fill='none'%3e%3ccircle fill='rgb(0 0 0 / 0.05)' id='pattern-circle' cx='10' cy='10' r='1.6257413380501518'%3e%3c/circle%3e%3c/svg%3e");
}
:root,.light,[data-theme="light"] {
  color-scheme: light;
  --nextui-background: 0 0% 100%;
  --nextui-foreground-50: 0 0% 98.04%;
  --nextui-foreground-100: 240 4.76% 95.88%;
  --nextui-foreground-200: 240 5.88% 90%;
  --nextui-foreground-300: 240 4.88% 83.92%;
  --nextui-foreground-400: 240 5.03% 64.9%;
  --nextui-foreground-500: 240 3.83% 46.08%;
  --nextui-foreground-600: 240 5.2% 33.92%;
  --nextui-foreground-700: 240 5.26% 26.08%;
  --nextui-foreground-800: 240 3.7% 15.88%;
  --nextui-foreground-900: 240 5.88% 10%;
  --nextui-foreground: 201.81999999999994 24.44% 8.82%;
  --nextui-divider: 0 0% 6.67%;
  --nextui-divider-opacity: 0.15;
  --nextui-focus: 212.01999999999998 100% 46.67%;
  --nextui-overlay: 0 0% 0%;
  --nextui-content1: 0 0% 100%;
  --nextui-content1-foreground: 201.81999999999994 24.44% 8.82%;
  --nextui-content2: 240 4.76% 95.88%;
  --nextui-content2-foreground: 240 3.7% 15.88%;
  --nextui-content3: 240 5.88% 90%;
  --nextui-content3-foreground: 240 5.26% 26.08%;
  --nextui-content4: 240 4.88% 83.92%;
  --nextui-content4-foreground: 240 5.2% 33.92%;
  --nextui-default-50: 0 0% 98.04%;
  --nextui-default-100: 240 4.76% 95.88%;
  --nextui-default-200: 240 5.88% 90%;
  --nextui-default-300: 240 4.88% 83.92%;
  --nextui-default-400: 240 5.03% 64.9%;
  --nextui-default-500: 240 3.83% 46.08%;
  --nextui-default-600: 240 5.2% 33.92%;
  --nextui-default-700: 240 5.26% 26.08%;
  --nextui-default-800: 240 3.7% 15.88%;
  --nextui-default-900: 240 5.88% 10%;
  --nextui-default-foreground: 0 0% 0%;
  --nextui-default: 240 4.88% 83.92%;
  --nextui-primary-50: 212.5 92.31% 94.9%;
  --nextui-primary-100: 211.84000000000003 92.45% 89.61%;
  --nextui-primary-200: 211.84000000000003 92.45% 79.22%;
  --nextui-primary-300: 212.24 92.45% 68.82%;
  --nextui-primary-400: 212.14 92.45% 58.43%;
  --nextui-primary-500: 212.01999999999998 100% 46.67%;
  --nextui-primary-600: 212.14 100% 38.43%;
  --nextui-primary-700: 212.24 100% 28.82%;
  --nextui-primary-800: 211.84000000000003 100% 19.22%;
  --nextui-primary-900: 211.84000000000003 100% 9.61%;
  --nextui-primary-foreground: 0 0% 100%;
  --nextui-primary: 212.01999999999998 100% 46.67%;
  --nextui-secondary-50: 270 61.54% 94.9%;
  --nextui-secondary-100: 270 59.26% 89.41%;
  --nextui-secondary-200: 270 59.26% 78.82%;
  --nextui-secondary-300: 270 59.26% 68.24%;
  --nextui-secondary-400: 270 59.26% 57.65%;
  --nextui-secondary-500: 270 66.67% 47.06%;
  --nextui-secondary-600: 270 66.67% 37.65%;
  --nextui-secondary-700: 270 66.67% 28.24%;
  --nextui-secondary-800: 270 66.67% 18.82%;
  --nextui-secondary-900: 270 66.67% 9.41%;
  --nextui-secondary-foreground: 0 0% 100%;
  --nextui-secondary: 270 66.67% 47.06%;
  --nextui-success-50: 146.66999999999996 64.29% 94.51%;
  --nextui-success-100: 145.71000000000004 61.4% 88.82%;
  --nextui-success-200: 146.2 61.74% 77.45%;
  --nextui-success-300: 145.78999999999996 62.57% 66.47%;
  --nextui-success-400: 146.01 62.45% 55.1%;
  --nextui-success-500: 145.96000000000004 79.46% 43.92%;
  --nextui-success-600: 146.01 79.89% 35.1%;
  --nextui-success-700: 145.78999999999996 79.26% 26.47%;
  --nextui-success-800: 146.2 79.78% 17.45%;
  --nextui-success-900: 145.71000000000004 77.78% 8.82%;
  --nextui-success-foreground: 0 0% 0%;
  --nextui-success: 145.96000000000004 79.46% 43.92%;
  --nextui-warning-50: 54.55000000000001 91.67% 95.29%;
  --nextui-warning-100: 37.139999999999986 91.3% 90.98%;
  --nextui-warning-200: 37.139999999999986 91.3% 81.96%;
  --nextui-warning-300: 36.95999999999998 91.24% 73.14%;
  --nextui-warning-400: 37.00999999999999 91.26% 64.12%;
  --nextui-warning-500: 37.02999999999997 91.27% 55.1%;
  --nextui-warning-600: 37.00999999999999 74.22% 44.12%;
  --nextui-warning-700: 36.95999999999998 73.96% 33.14%;
  --nextui-warning-800: 37.139999999999986 75% 21.96%;
  --nextui-warning-900: 37.139999999999986 75% 10.98%;
  --nextui-warning-foreground: 0 0% 0%;
  --nextui-warning: 37.02999999999997 91.27% 55.1%;
  --nextui-danger-50: 339.13 92% 95.1%;
  --nextui-danger-100: 340 91.84% 90.39%;
  --nextui-danger-200: 339.3299999999999 90% 80.39%;
  --nextui-danger-300: 339.11 90.6% 70.78%;
  --nextui-danger-400: 339 90% 60.78%;
  --nextui-danger-500: 339.20000000000005 90.36% 51.18%;
  --nextui-danger-600: 339 86.54% 40.78%;
  --nextui-danger-700: 339.11 85.99% 30.78%;
  --nextui-danger-800: 339.3299999999999 86.54% 20.39%;
  --nextui-danger-900: 340 84.91% 10.39%;
  --nextui-danger-foreground: 0 0% 100%;
  --nextui-danger: 339.20000000000005 90.36% 51.18%;
  --nextui-divider-weight: 1px;
  --nextui-disabled-opacity: .5;
  --nextui-font-size-tiny: 0.75rem;
  --nextui-font-size-small: 0.875rem;
  --nextui-font-size-medium: 1rem;
  --nextui-font-size-large: 1.125rem;
  --nextui-line-height-tiny: 1rem;
  --nextui-line-height-small: 1.25rem;
  --nextui-line-height-medium: 1.5rem;
  --nextui-line-height-large: 1.75rem;
  --nextui-radius-small: 8px;
  --nextui-radius-medium: 12px;
  --nextui-radius-large: 14px;
  --nextui-border-width-small: 1px;
  --nextui-border-width-medium: 2px;
  --nextui-border-width-large: 3px;
  --nextui-box-shadow-small: 0px 0px 5px 0px rgb(0 0 0 / 0.02), 0px 2px 10px 0px rgb(0 0 0 / 0.06), 0px 0px 1px 0px rgb(0 0 0 / 0.3);
  --nextui-box-shadow-medium: 0px 0px 15px 0px rgb(0 0 0 / 0.03), 0px 2px 30px 0px rgb(0 0 0 / 0.08), 0px 0px 1px 0px rgb(0 0 0 / 0.3);
  --nextui-box-shadow-large: 0px 0px 30px 0px rgb(0 0 0 / 0.04), 0px 30px 60px 0px rgb(0 0 0 / 0.12), 0px 0px 1px 0px rgb(0 0 0 / 0.3);
  --nextui-hover-opacity: .8;
}
.dark,[data-theme="dark"] {
  color-scheme: dark;
  --nextui-background: 0 0% 0%;
  --nextui-foreground-50: 240 5.88% 10%;
  --nextui-foreground-100: 240 3.7% 15.88%;
  --nextui-foreground-200: 240 5.26% 26.08%;
  --nextui-foreground-300: 240 5.2% 33.92%;
  --nextui-foreground-400: 240 3.83% 46.08%;
  --nextui-foreground-500: 240 5.03% 64.9%;
  --nextui-foreground-600: 240 4.88% 83.92%;
  --nextui-foreground-700: 240 5.88% 90%;
  --nextui-foreground-800: 240 4.76% 95.88%;
  --nextui-foreground-900: 0 0% 98.04%;
  --nextui-foreground: 210 5.56% 92.94%;
  --nextui-focus: 212.01999999999998 100% 46.67%;
  --nextui-overlay: 0 0% 0%;
  --nextui-divider: 0 0% 100%;
  --nextui-divider-opacity: 0.15;
  --nextui-content1: 240 5.88% 10%;
  --nextui-content1-foreground: 0 0% 98.04%;
  --nextui-content2: 240 3.7% 15.88%;
  --nextui-content2-foreground: 240 4.76% 95.88%;
  --nextui-content3: 240 5.26% 26.08%;
  --nextui-content3-foreground: 240 5.88% 90%;
  --nextui-content4: 240 5.2% 33.92%;
  --nextui-content4-foreground: 240 4.88% 83.92%;
  --nextui-default-50: 240 5.88% 10%;
  --nextui-default-100: 240 3.7% 15.88%;
  --nextui-default-200: 240 5.26% 26.08%;
  --nextui-default-300: 240 5.2% 33.92%;
  --nextui-default-400: 240 3.83% 46.08%;
  --nextui-default-500: 240 5.03% 64.9%;
  --nextui-default-600: 240 4.88% 83.92%;
  --nextui-default-700: 240 5.88% 90%;
  --nextui-default-800: 240 4.76% 95.88%;
  --nextui-default-900: 0 0% 98.04%;
  --nextui-default-foreground: 0 0% 100%;
  --nextui-default: 240 5.26% 26.08%;
  --nextui-primary-50: 211.84000000000003 100% 9.61%;
  --nextui-primary-100: 211.84000000000003 100% 19.22%;
  --nextui-primary-200: 212.24 100% 28.82%;
  --nextui-primary-300: 212.14 100% 38.43%;
  --nextui-primary-400: 212.01999999999998 100% 46.67%;
  --nextui-primary-500: 212.14 92.45% 58.43%;
  --nextui-primary-600: 212.24 92.45% 68.82%;
  --nextui-primary-700: 211.84000000000003 92.45% 79.22%;
  --nextui-primary-800: 211.84000000000003 92.45% 89.61%;
  --nextui-primary-900: 212.5 92.31% 94.9%;
  --nextui-primary-foreground: 0 0% 100%;
  --nextui-primary: 212.01999999999998 100% 46.67%;
  --nextui-secondary-50: 270 66.67% 9.41%;
  --nextui-secondary-100: 270 66.67% 18.82%;
  --nextui-secondary-200: 270 66.67% 28.24%;
  --nextui-secondary-300: 270 66.67% 37.65%;
  --nextui-secondary-400: 270 66.67% 47.06%;
  --nextui-secondary-500: 270 59.26% 57.65%;
  --nextui-secondary-600: 270 59.26% 68.24%;
  --nextui-secondary-700: 270 59.26% 78.82%;
  --nextui-secondary-800: 270 59.26% 89.41%;
  --nextui-secondary-900: 270 61.54% 94.9%;
  --nextui-secondary-foreground: 0 0% 100%;
  --nextui-secondary: 270 59.26% 57.65%;
  --nextui-success-50: 145.71000000000004 77.78% 8.82%;
  --nextui-success-100: 146.2 79.78% 17.45%;
  --nextui-success-200: 145.78999999999996 79.26% 26.47%;
  --nextui-success-300: 146.01 79.89% 35.1%;
  --nextui-success-400: 145.96000000000004 79.46% 43.92%;
  --nextui-success-500: 146.01 62.45% 55.1%;
  --nextui-success-600: 145.78999999999996 62.57% 66.47%;
  --nextui-success-700: 146.2 61.74% 77.45%;
  --nextui-success-800: 145.71000000000004 61.4% 88.82%;
  --nextui-success-900: 146.66999999999996 64.29% 94.51%;
  --nextui-success-foreground: 0 0% 0%;
  --nextui-success: 145.96000000000004 79.46% 43.92%;
  --nextui-warning-50: 37.139999999999986 75% 10.98%;
  --nextui-warning-100: 37.139999999999986 75% 21.96%;
  --nextui-warning-200: 36.95999999999998 73.96% 33.14%;
  --nextui-warning-300: 37.00999999999999 74.22% 44.12%;
  --nextui-warning-400: 37.02999999999997 91.27% 55.1%;
  --nextui-warning-500: 37.00999999999999 91.26% 64.12%;
  --nextui-warning-600: 36.95999999999998 91.24% 73.14%;
  --nextui-warning-700: 37.139999999999986 91.3% 81.96%;
  --nextui-warning-800: 37.139999999999986 91.3% 90.98%;
  --nextui-warning-900: 54.55000000000001 91.67% 95.29%;
  --nextui-warning-foreground: 0 0% 0%;
  --nextui-warning: 37.02999999999997 91.27% 55.1%;
  --nextui-danger-50: 340 84.91% 10.39%;
  --nextui-danger-100: 339.3299999999999 86.54% 20.39%;
  --nextui-danger-200: 339.11 85.99% 30.78%;
  --nextui-danger-300: 339 86.54% 40.78%;
  --nextui-danger-400: 339.20000000000005 90.36% 51.18%;
  --nextui-danger-500: 339 90% 60.78%;
  --nextui-danger-600: 339.11 90.6% 70.78%;
  --nextui-danger-700: 339.3299999999999 90% 80.39%;
  --nextui-danger-800: 340 91.84% 90.39%;
  --nextui-danger-900: 339.13 92% 95.1%;
  --nextui-danger-foreground: 0 0% 100%;
  --nextui-danger: 339.20000000000005 90.36% 51.18%;
  --nextui-divider-weight: 1px;
  --nextui-disabled-opacity: .5;
  --nextui-font-size-tiny: 0.75rem;
  --nextui-font-size-small: 0.875rem;
  --nextui-font-size-medium: 1rem;
  --nextui-font-size-large: 1.125rem;
  --nextui-line-height-tiny: 1rem;
  --nextui-line-height-small: 1.25rem;
  --nextui-line-height-medium: 1.5rem;
  --nextui-line-height-large: 1.75rem;
  --nextui-radius-small: 8px;
  --nextui-radius-medium: 12px;
  --nextui-radius-large: 14px;
  --nextui-border-width-small: 1px;
  --nextui-border-width-medium: 2px;
  --nextui-border-width-large: 3px;
  --nextui-box-shadow-small: 0px 0px 5px 0px rgb(0 0 0 / 0.05), 0px 2px 10px 0px rgb(0 0 0 / 0.2), inset 0px 0px 1px 0px rgb(255 255 255 / 0.15);
  --nextui-box-shadow-medium: 0px 0px 15px 0px rgb(0 0 0 / 0.06), 0px 2px 30px 0px rgb(0 0 0 / 0.22), inset 0px 0px 1px 0px rgb(255 255 255 / 0.15);
  --nextui-box-shadow-large: 0px 0px 30px 0px rgb(0 0 0 / 0.07), 0px 30px 60px 0px rgb(0 0 0 / 0.26), inset 0px 0px 1px 0px rgb(255 255 255 / 0.15);
  --nextui-hover-opacity: .9;
}
.leading-inherit {
  line-height: inherit;
}
.bg-img-inherit {
  background-image: inherit;
}
.bg-clip-inherit {
  background-clip: inherit;
}
.text-fill-inherit {
  -webkit-text-fill-color: inherit;
}
.tap-highlight-transparent {
  -webkit-tap-highlight-color: transparent;
}
.transition-background {
  transition-property: background;
  transition-timing-function: ease;
  transition-duration: 250ms;
}
.transition-colors-opacity {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity;
  transition-timing-function: ease;
  transition-duration: 250ms;
}
.transition-width {
  transition-property: width;
  transition-timing-function: ease;
  transition-duration: 250ms;
}
.transition-height {
  transition-property: height;
  transition-timing-function: ease;
  transition-duration: 250ms;
}
.transition-size {
  transition-property: width, height;
  transition-timing-function: ease;
  transition-duration: 250ms;
}
.transition-left {
  transition-property: left;
  transition-timing-function: ease;
  transition-duration: 250ms;
}
.transition-transform-opacity {
  transition-property: transform, opacity;
  transition-timing-function: ease;
  transition-duration: 250ms;
}
.transition-transform-background {
  transition-property: transform, background;
  transition-timing-function: ease;
  transition-duration: 250ms;
}
.transition-transform-colors {
  transition-property: transform, color, background, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: ease;
  transition-duration: 250ms;
}
.transition-transform-colors-opacity {
  transition-property: transform, color, background, background-color, border-color, text-decoration-color, fill, stroke, opacity;
  transition-timing-function: ease;
  transition-duration: 250ms;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-default {
  -ms-overflow-style: auto;
  scrollbar-width: auto;
}
.scrollbar-default::-webkit-scrollbar {
  display: block;
}
/* ===== UTILITIES */
.header {
  font-size: 32px;
  font-weight: 700;
  line-height: 36px;
}
@media (min-width: 768px) {
  .header {
    font-size: 36px;
    font-weight: 700;
    line-height: 40px;
  }
}
.container {
  position: relative;
  flex: 1 1 0%;
  overflow-y: auto;
  padding-left: 5%;
  padding-right: 5%;
}
.sub-container {
  margin-left: auto;
  margin-right: auto;
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.side-img {
  display: none;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
@media (min-width: 768px) {

  .side-img {
    display: block;
  }
}
.copyright {
  justify-items: end;
  text-align: center;
  --tw-text-opacity: 1;
  color: rgb(118 130 141 / var(--tw-text-opacity));
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
}
@media (min-width: 1280px) {

  .copyright {
    text-align: left;
  }
}
/* ==== SUCCESS */
/* ===== ADMIN */
/* ==== FORM */
.checkbox-label {
  cursor: pointer;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(171 184 196 / var(--tw-text-opacity));
}
.peer:disabled ~ .checkbox-label {
  cursor: not-allowed;
  opacity: 0.7;
}
@media (min-width: 768px) {

  .checkbox-label {
    line-height: 1;
  }
}
/* ==== File Upload */
/* ==== Stat Card */
.stat-card {
  display: flex;
  flex: 1 1 0%;
  flex-direction: column;
  gap: 1.5rem;
  border-radius: 1rem;
  background-size: cover;
  padding: 1.5rem;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
/* ==== Status Badge */
.status-badge {
  display: flex;
  width: -moz-fit-content;
  width: fit-content;
  align-items: center;
  gap: 0.5rem;
  border-radius: 9999px;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
/* Data Table */
.data-table {
  z-index: 10;
  width: 100%;
  overflow: hidden;
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(26 29 33 / var(--tw-border-opacity));
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.table-actions {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
}
.table-actions > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.table-actions {
  padding: 1rem;
}
/* ===== ALIGNMENTS */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
/* ===== TYPOGRAPHY */
.text-24-bold {
  font-size: 24px;
  font-weight: 700;
  line-height: 28px;
}
.text-32-bold {
  font-size: 32px;
  font-weight: 700;
  line-height: 36px;
}
.text-18-bold {
  font-size: 18px;
  font-weight: 700;
  line-height: 24px;
}
.text-16-semibold {
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
}
.text-14-medium {
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
}
.text-14-regular {
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
}
.text-12-semibold {
  font-size: 12px;
  font-weight: 600;
  line-height: 16px;
}
/* =====  SHADCN OVERRIDES */
.shad-primary-btn {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(36 174 124 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}
.shad-gray-btn {
  cursor: pointer !important;
  border-width: 1px !important;
  --tw-border-opacity: 1 !important;
  border-color: rgb(54 58 61 / var(--tw-border-opacity)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(26 29 33 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}
.shad-input-label {
  --tw-text-opacity: 1 !important;
  color: rgb(171 184 196 / var(--tw-text-opacity)) !important;
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
}
.shad-input {
  height: 2.75rem !important;
  --tw-border-opacity: 1 !important;
  border-color: rgb(54 58 61 / var(--tw-border-opacity)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(26 29 33 / var(--tw-bg-opacity)) !important;
}
.shad-input::-moz-placeholder {
  --tw-text-opacity: 1 !important;
  color: rgb(118 130 141 / var(--tw-text-opacity)) !important;
}
.shad-input::placeholder {
  --tw-text-opacity: 1 !important;
  color: rgb(118 130 141 / var(--tw-text-opacity)) !important;
}
.shad-input:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}
.shad-textArea {
  --tw-border-opacity: 1 !important;
  border-color: rgb(54 58 61 / var(--tw-border-opacity)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(26 29 33 / var(--tw-bg-opacity)) !important;
}
.shad-textArea::-moz-placeholder {
  --tw-text-opacity: 1 !important;
  color: rgb(118 130 141 / var(--tw-text-opacity)) !important;
}
.shad-textArea::placeholder {
  --tw-text-opacity: 1 !important;
  color: rgb(118 130 141 / var(--tw-text-opacity)) !important;
}
.shad-textArea:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}
.shad-select-trigger {
  height: 2.75rem !important;
  --tw-border-opacity: 1 !important;
  border-color: rgb(54 58 61 / var(--tw-border-opacity)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(26 29 33 / var(--tw-bg-opacity)) !important;
}
.shad-select-trigger::-moz-placeholder {
  --tw-text-opacity: 1 !important;
  color: rgb(118 130 141 / var(--tw-text-opacity)) !important;
}
.shad-select-trigger::placeholder {
  --tw-text-opacity: 1 !important;
  color: rgb(118 130 141 / var(--tw-text-opacity)) !important;
}
.shad-select-trigger:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-offset-width: 0px !important;
}
.shad-select-content {
  --tw-border-opacity: 1 !important;
  border-color: rgb(54 58 61 / var(--tw-border-opacity)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(26 29 33 / var(--tw-bg-opacity)) !important;
}
.shad-error {
  --tw-text-opacity: 1 !important;
  color: rgb(248 113 113 / var(--tw-text-opacity)) !important;
}
.shad-table {
  overflow: hidden !important;
  border-radius: 0.5rem !important;
}
.shad-table-row-header {
  border-bottom-width: 1px !important;
  --tw-border-opacity: 1 !important;
  border-color: rgb(26 29 33 / var(--tw-border-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(232 233 233 / var(--tw-text-opacity)) !important;
}
.shad-table-row-header:hover {
  background-color: transparent !important;
}
.shad-table-row {
  border-bottom-width: 1px !important;
  --tw-border-opacity: 1 !important;
  border-color: rgb(26 29 33 / var(--tw-border-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(232 233 233 / var(--tw-text-opacity)) !important;
}
.shad-otp {
  display: flex !important;
  width: 100% !important;
  justify-content: space-between !important;
}
.shad-otp-slot {
  display: flex !important;
  width: 4rem !important;
  height: 4rem !important;
  justify-content: center !important;
  gap: 1rem !important;
  border-radius: 0.5rem !important;
  border-width: 1px !important;
  --tw-border-opacity: 1 !important;
  border-color: rgb(54 58 61 / var(--tw-border-opacity)) !important;
  font-size: 36px;
  font-weight: 700;
  line-height: 40px;
}
.shad-alert-dialog > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0 !important;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse))) !important;
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse)) !important;
}
.shad-alert-dialog {
  --tw-border-opacity: 1 !important;
  border-color: rgb(54 58 61 / var(--tw-border-opacity)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(26 29 33 / var(--tw-bg-opacity)) !important;
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
}
/* =====  REACT PHONE NUMBER INPUT OVERRIDES */
.input-phone {
  margin-top: 0.5rem !important;
  height: 2.75rem !important;
  border-radius: 0.375rem !important;
  border-width: 1px !important;
  --tw-border-opacity: 1 !important;
  border-color: rgb(54 58 61 / var(--tw-border-opacity)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(26 29 33 / var(--tw-bg-opacity)) !important;
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}
.input-phone::-moz-placeholder {
  --tw-text-opacity: 1 !important;
  color: rgb(118 130 141 / var(--tw-text-opacity)) !important;
}
.input-phone::placeholder {
  --tw-text-opacity: 1 !important;
  color: rgb(118 130 141 / var(--tw-text-opacity)) !important;
}
/* =====  REACT DATE PICKER OVERRIDES */
.date-picker {
  height: 2.75rem !important;
  width: 100% !important;
  overflow: hidden !important;
  border-radius: 0.375rem !important;
  border-color: transparent !important;
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
}
.date-picker::-moz-placeholder {
  --tw-text-opacity: 1 !important;
  color: rgb(118 130 141 / var(--tw-text-opacity)) !important;
}
.date-picker::placeholder {
  --tw-text-opacity: 1 !important;
  color: rgb(118 130 141 / var(--tw-text-opacity)) !important;
}
.\[--conic-position\:from_290deg_at_center_top\] {
  --conic-position: from 290deg at center top;
}
.\[--conic-position\:from_70deg_at_center_top\] {
  --conic-position: from 70deg at center top;
}
.\[--picker-height\:224px\] {
  --picker-height: 224px;
}
.\[--scale-enter\:100\%\] {
  --scale-enter: 100%;
}
.\[--scale-exit\:100\%\] {
  --scale-exit: 100%;
}
.\[--scroll-shadow-size\:100px\] {
  --scroll-shadow-size: 100px;
}
.\[--slide-enter\:0px\] {
  --slide-enter: 0px;
}
.\[--slide-exit\:80px\] {
  --slide-exit: 80px;
}
.\[background\:radial-gradient\(circle_at_center\2c _rgba\(var\(--fifth-color\)\2c _0\.8\)_0\2c _rgba\(var\(--fifth-color\)\2c _0\)_50\%\)_no-repeat\] {
  background: radial-gradient(circle at center, rgba(var(--fifth-color), 0.8) 0, rgba(var(--fifth-color), 0) 50%) no-repeat;
}
.\[background\:radial-gradient\(circle_at_center\2c _rgba\(var\(--fourth-color\)\2c _0\.8\)_0\2c _rgba\(var\(--fourth-color\)\2c _0\)_50\%\)_no-repeat\] {
  background: radial-gradient(circle at center, rgba(var(--fourth-color), 0.8) 0, rgba(var(--fourth-color), 0) 50%) no-repeat;
}
.\[background\:radial-gradient\(circle_at_center\2c _rgba\(var\(--pointer-color\)\2c _0\.8\)_0\2c _rgba\(var\(--pointer-color\)\2c _0\)_50\%\)_no-repeat\] {
  background: radial-gradient(circle at center, rgba(var(--pointer-color), 0.8) 0, rgba(var(--pointer-color), 0) 50%) no-repeat;
}
.\[background\:radial-gradient\(circle_at_center\2c _rgba\(var\(--second-color\)\2c _0\.8\)_0\2c _rgba\(var\(--second-color\)\2c _0\)_50\%\)_no-repeat\] {
  background: radial-gradient(circle at center, rgba(var(--second-color), 0.8) 0, rgba(var(--second-color), 0) 50%) no-repeat;
}
.\[background\:radial-gradient\(circle_at_center\2c _rgba\(var\(--third-color\)\2c _0\.8\)_0\2c _rgba\(var\(--third-color\)\2c _0\)_50\%\)_no-repeat\] {
  background: radial-gradient(circle at center, rgba(var(--third-color), 0.8) 0, rgba(var(--third-color), 0) 50%) no-repeat;
}
.\[background\:radial-gradient\(circle_at_center\2c _var\(--first-color\)_0\2c _var\(--first-color\)_50\%\)_no-repeat\] {
  background: radial-gradient(circle at center, var(--first-color) 0, var(--first-color) 50%) no-repeat;
}
.\[filter\:url\(\#blurMe\)_blur\(40px\)\] {
  filter: url(#blurMe) blur(40px);
}
.\[mask-image\:linear-gradient\(\#000\2c \#000\2c transparent_0\2c \#000_var\(--scroll-shadow-size\)\2c \#000_calc\(100\%_-_var\(--scroll-shadow-size\)\)\2c transparent\)\] {
  -webkit-mask-image: linear-gradient(#000,#000,transparent 0,#000 var(--scroll-shadow-size),#000 calc(100% - var(--scroll-shadow-size)),transparent);
          mask-image: linear-gradient(#000,#000,transparent 0,#000 var(--scroll-shadow-size),#000 calc(100% - var(--scroll-shadow-size)),transparent);
}
.\[mask-image\:linear-gradient\(to_bottom\2c white\2c transparent\)\] {
  -webkit-mask-image: linear-gradient(to bottom,white,transparent);
          mask-image: linear-gradient(to bottom,white,transparent);
}
.\[mask-image\:linear-gradient\(to_left\2c white\2c transparent\)\] {
  -webkit-mask-image: linear-gradient(to left,white,transparent);
          mask-image: linear-gradient(to left,white,transparent);
}
.\[mask-image\:linear-gradient\(to_right\2c transparent\2c white_20\%\2c white_80\%\2c transparent\)\] {
  -webkit-mask-image: linear-gradient(to right,transparent,white 20%,white 80%,transparent);
          mask-image: linear-gradient(to right,transparent,white 20%,white 80%,transparent);
}
.\[mask-image\:linear-gradient\(to_right\2c white\2c transparent\)\] {
  -webkit-mask-image: linear-gradient(to right,white,transparent);
          mask-image: linear-gradient(to right,white,transparent);
}
.\[mask-image\:linear-gradient\(to_top\2c white\2c transparent\)\] {
  -webkit-mask-image: linear-gradient(to top,white,transparent);
          mask-image: linear-gradient(to top,white,transparent);
}
.\[mask-image\:radial-gradient\(ellipse_at_center\2c transparent_20\%\2c black\)\] {
  -webkit-mask-image: radial-gradient(ellipse at center,transparent 20%,black);
          mask-image: radial-gradient(ellipse at center,transparent 20%,black);
}
.\[mask-repeat\:no-repeat\] {
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
}
.\[mask-size\:40px\] {
  -webkit-mask-size: 40px;
          mask-size: 40px;
}
.\[mix-blend-mode\:var\(--blending-value\)\] {
  mix-blend-mode: var(--blending-value);
}
.\[perspective\:700px\] {
  perspective: 700px;
}
.\[scrollbar-width\:none\] {
  scrollbar-width: none;
}
.\[text-wrap\:balance\] {
  text-wrap: balance;
}
.\[transform-origin\:calc\(50\%\+400px\)\] {
  transform-origin: calc(50% + 400px);
}
.\[transform-origin\:calc\(50\%-200px\)\] {
  transform-origin: calc(50% - 200px);
}
.\[transform-origin\:calc\(50\%-400px\)\] {
  transform-origin: calc(50% - 400px);
}
.\[transform-origin\:calc\(50\%-800px\)_calc\(50\%\+800px\)\] {
  transform-origin: calc(50% - 800px) calc(50% + 800px);
}
.\[transform-origin\:center_center\] {
  transform-origin: center center;
}

::-moz-selection {
  /* Code for Firefox */
  color: #fff;
  background: #000;
}

::selection {
  color: #cdfff2d3;
  background: #18181858;
}

.word-rotate-text {
  line-height: 1.2; /* Increase line height */
  transition: opacity 0.1s ease-in-out;
  opacity: 1;
  -webkit-font-smoothing: antialiased; /* Enable font smoothing for WebKit */
  -moz-osx-font-smoothing: grayscale; /* Enable font smoothing for Firefox */
}

/* width */
::-webkit-scrollbar {
  width: 2px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #222a35;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #626970;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #767676;
}

html {
  scroll-behavior: smooth;
}
:root {
  --radial-gradient-background: 250, 250, 250;
  --solid-color-background: 0, 0, 0;
  --overlay-color: 255, 255, 255;
}
.radial-gradient {
  background: radial-gradient(
      circle at 50% 0%,
      rgba(var(--radial-gradient-background), 0.05) 0%,
      transparent 60%
    )
    rgba(var(--solid-color-background), 1);
}
.custom-navbar {
  -webkit-backdrop-filter: blur(7px);
          backdrop-filter: blur(7px); /* Adjust this value to change blur */
}
.linear-mask {
  mask-image: linear-gradient(
    -75deg,
    white calc(var(--x) + 20%),
    transparent calc(var(--x) + 30%),
    white calc(var(--x) + 100%)
  );
  -webkit-mask-image: linear-gradient(
    -75deg,
    white calc(var(--x) + 20%),
    transparent calc(var(--x) + 30%),
    white calc(var(--x) + 100%)
  );
}

.linear-overlay {
  background-image: linear-gradient(
    -75deg,
    rgba(var(--overlay-color), 0.1) calc(var(--x) + 20%),
    rgba(var(--overlay-color), 0.5) calc(var(--x) + 25%),
    rgba(var(--overlay-color), 0.1) calc(var(--x) + 100%)
  );
  mask:
    linear-gradient(black, black) content-box,
    linear-gradient(black, black);
  -webkit-mask:
    linear-gradient(black, black) content-box,
    linear-gradient(black, black);
  mask-composite: exclude;
  -webkit-mask-composite: xor;
}

/* ========================================== TAILWIND STYLES */

/* =====  REACT-DATEPICKER OVERRIDES */
.react-datepicker-wrapper.date-picker {
  display: flex;
  align-items: center;
}

.react-datepicker,
.react-datepicker__time,
.react-datepicker__header,
.react-datepicker__current-month,
.react-datepicker__day-name,
.react-datepicker__day,
.react-datepicker-time__header {
  background-color: #1a1d21 !important;
  border-color: #363a3d !important;
  color: #abb8c4 !important;
}

.react-datepicker__current-month,
.react-datepicker__day-name,
.react-datepicker-time__header {
  color: #ffffff !important;
}

.react-datepicker__triangle {
  fill: #1a1d21 !important;
  color: #1a1d21 !important;
  stroke: #1a1d21 !important;
}

.react-datepicker__time-list-item:hover {
  background-color: #363a3d !important;
}

.react-datepicker__input-container input {
  background-color: #1a1d21 !important;
  width: 100%;
  outline: none;
}

.react-datepicker__day--selected {
  background-color: #24ae7c !important;
  color: #ffffff !important;
  border-radius: 4px;
}

.react-datepicker__time-list-item--selected {
  background-color: #24ae7c !important;
}

.react-datepicker__time-container {
  border-left: 1px solid #363a3d !important;
}

.react-datepicker__time-list-item {
  display: flex !important;
  align-items: center !important;
}

/* =====  REACT PHONE NUMBER INPUT OVERRIDES */
.PhoneInputInput {
  outline: none;
  margin-left: 4px;
  background: #1a1d21;
  font-size: 14px;
  font-weight: 500;
}

.PhoneInputInput::-moz-placeholder {
  color: #1a1d21;
}

.PhoneInputInput::placeholder {
  color: #1a1d21;
}
/* styles/globals.css */
@keyframes pulseOutline {
  0% {
    transform: scale(1);
    opacity: 1;
    border-color: rgba(239, 68, 68, 1); /* Red-500 */
  }
  50% {
    transform: scale(1.05);
    opacity: 0.5;
    border-color: rgba(239, 68, 68, 0.5);
  }
  100% {
    transform: scale(1);
    opacity: 1;
    border-color: rgba(239, 68, 68, 1);
  }
}

.animate-pulse-outline {
  animation: pulseOutline 2s infinite ease-in-out;
}

.file\:cursor-pointer::file-selector-button {
  cursor: pointer;
}

.file\:border-0::file-selector-button {
  border-width: 0px;
}

.file\:bg-transparent::file-selector-button {
  background-color: transparent;
}

.file\:text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.file\:font-medium::file-selector-button {
  font-weight: 500;
}

.placeholder\:text-danger::-moz-placeholder {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));
}

.placeholder\:text-danger::placeholder {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));
}

.placeholder\:text-foreground-500::-moz-placeholder {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-foreground-500) / var(--nextui-foreground-500-opacity, var(--tw-text-opacity)));
}

.placeholder\:text-foreground-500::placeholder {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-foreground-500) / var(--nextui-foreground-500-opacity, var(--tw-text-opacity)));
}

.placeholder\:text-gray-400::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.placeholder\:text-gray-400::placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.placeholder\:text-primary::-moz-placeholder {
  color: hsl(var(--primary));
}

.placeholder\:text-primary::placeholder {
  color: hsl(var(--primary));
}

.placeholder\:text-secondary::-moz-placeholder {
  color: hsl(var(--secondary));
}

.placeholder\:text-secondary::placeholder {
  color: hsl(var(--secondary));
}

.placeholder\:text-slate-500::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}

.placeholder\:text-slate-500::placeholder {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}

.placeholder\:text-success-600::-moz-placeholder {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-600) / var(--nextui-success-600-opacity, var(--tw-text-opacity)));
}

.placeholder\:text-success-600::placeholder {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-600) / var(--nextui-success-600-opacity, var(--tw-text-opacity)));
}

.placeholder\:text-warning-600::-moz-placeholder {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-600) / var(--nextui-warning-600-opacity, var(--tw-text-opacity)));
}

.placeholder\:text-warning-600::placeholder {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-600) / var(--nextui-warning-600-opacity, var(--tw-text-opacity)));
}

.before\:absolute::before {
  content: var(--tw-content);
  position: absolute;
}

.before\:inset-0::before {
  content: var(--tw-content);
  inset: 0px;
}

.before\:z-0::before {
  content: var(--tw-content);
  z-index: 0;
}

.before\:z-\[-1\]::before {
  content: var(--tw-content);
  z-index: -1;
}

.before\:box-border::before {
  content: var(--tw-content);
  box-sizing: border-box;
}

.before\:block::before {
  content: var(--tw-content);
  display: block;
}

.before\:hidden::before {
  content: var(--tw-content);
  display: none;
}

.before\:h-0\.5::before {
  content: var(--tw-content);
  height: 0.125rem;
}

.before\:h-11::before {
  content: var(--tw-content);
  height: 2.75rem;
}

.before\:h-2\.5::before {
  content: var(--tw-content);
  height: 0.625rem;
}

.before\:h-px::before {
  content: var(--tw-content);
  height: 1px;
}

.before\:w-0::before {
  content: var(--tw-content);
  width: 0px;
}

.before\:w-11::before {
  content: var(--tw-content);
  width: 2.75rem;
}

.before\:w-2\.5::before {
  content: var(--tw-content);
  width: 0.625rem;
}

.before\:w-6::before {
  content: var(--tw-content);
  width: 1.5rem;
}

.before\:-translate-x-full::before {
  content: var(--tw-content);
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:-translate-y-1::before {
  content: var(--tw-content);
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:rotate-0::before {
  content: var(--tw-content);
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:rotate-45::before {
  content: var(--tw-content);
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes shimmer {

  100% {
    content: var(--tw-content);
    transform: translateX(100%);
  }

  from {
    content: var(--tw-content);
    background-position: 0 0;
  }

  to {
    content: var(--tw-content);
    background-position: -200% 0;
  }
}

.before\:animate-\[shimmer_2s_infinite\]::before {
  content: var(--tw-content);
  animation: shimmer 2s infinite;
}

.before\:animate-none::before {
  content: var(--tw-content);
  animation: none;
}

.before\:rounded-\[calc\(theme\(borderRadius\.medium\)\*0\.5\)\]::before {
  content: var(--tw-content);
  border-radius: calc(var(--nextui-radius-medium) * 0.5);
}

.before\:rounded-\[calc\(theme\(borderRadius\.medium\)\*0\.6\)\]::before {
  content: var(--tw-content);
  border-radius: calc(var(--nextui-radius-medium) * 0.6);
}

.before\:rounded-\[calc\(theme\(borderRadius\.medium\)\*0\.7\)\]::before {
  content: var(--tw-content);
  border-radius: calc(var(--nextui-radius-medium) * 0.7);
}

.before\:rounded-full::before {
  content: var(--tw-content);
  border-radius: 9999px;
}

.before\:rounded-none::before {
  content: var(--tw-content);
  border-radius: 0px;
}

.before\:rounded-sm::before {
  content: var(--tw-content);
  border-radius: 0.125rem;
}

.before\:border-2::before {
  content: var(--tw-content);
  border-width: 2px;
}

.before\:border-t::before {
  content: var(--tw-content);
  border-top-width: 1px;
}

.before\:border-solid::before {
  content: var(--tw-content);
  border-style: solid;
}

.before\:border-content4\/30::before {
  content: var(--tw-content);
  border-color: hsl(var(--nextui-content4) / 0.3);
}

.before\:border-danger::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));
}

.before\:border-default::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-border-opacity)));
}

.before\:bg-content1::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-content1) / var(--nextui-content1-opacity, var(--tw-bg-opacity)));
}

.before\:bg-current::before {
  content: var(--tw-content);
  background-color: currentColor;
}

.before\:bg-danger::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));
}

.before\:bg-danger\/20::before {
  content: var(--tw-content);
  background-color: hsl(var(--nextui-danger) / 0.2);
}

.before\:bg-default\/60::before {
  content: var(--tw-content);
  background-color: hsl(var(--nextui-default) / 0.6);
}

.before\:bg-foreground::before {
  content: var(--tw-content);
  background-color: hsl(var(--foreground));
}

.before\:bg-primary::before {
  content: var(--tw-content);
  background-color: hsl(var(--primary));
}

.before\:bg-primary\/20::before {
  content: var(--tw-content);
  background-color: hsl(var(--primary) / 0.2);
}

.before\:bg-secondary::before {
  content: var(--tw-content);
  background-color: hsl(var(--secondary));
}

.before\:bg-secondary\/20::before {
  content: var(--tw-content);
  background-color: hsl(var(--secondary) / 0.2);
}

.before\:bg-success::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));
}

.before\:bg-success\/20::before {
  content: var(--tw-content);
  background-color: hsl(var(--nextui-success) / 0.2);
}

.before\:bg-warning::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));
}

.before\:bg-warning\/20::before {
  content: var(--tw-content);
  background-color: hsl(var(--nextui-warning) / 0.2);
}

.before\:bg-gradient-to-r::before {
  content: var(--tw-content);
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.before\:from-transparent::before {
  content: var(--tw-content);
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.before\:via-content4::before {
  content: var(--tw-content);
  --tw-gradient-to: hsl(var(--nextui-content4) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--nextui-content4) / var(--nextui-content4-opacity, 1)) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.before\:to-transparent::before {
  content: var(--tw-content);
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.before\:opacity-0::before {
  content: var(--tw-content);
  opacity: 0;
}

.before\:opacity-100::before {
  content: var(--tw-content);
  opacity: 1;
}

.before\:shadow-small::before {
  content: var(--tw-content);
  --tw-shadow: var(--nextui-box-shadow-small);
  --tw-shadow-colored: var(--nextui-box-shadow-small);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.before\:transition-colors::before {
  content: var(--tw-content);
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}

.before\:transition-none::before {
  content: var(--tw-content);
  transition-property: none;
}

.before\:transition-transform::before {
  content: var(--tw-content);
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}

.before\:duration-150::before {
  content: var(--tw-content);
  transition-duration: 150ms;
}

.before\:content-\[\'\'\]::before {
  --tw-content: '';
  content: var(--tw-content);
}

.before\:duration-150::before {
  content: var(--tw-content);
  animation-duration: 150ms;
}

.before\:transition-width::before {
  content: var(--tw-content);
  transition-property: width;
  transition-timing-function: ease;
  transition-duration: 250ms;
}

.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}

.after\:inset-0::after {
  content: var(--tw-content);
  inset: 0px;
}

.after\:-bottom-1::after {
  content: var(--tw-content);
  bottom: -0.25rem;
}

.after\:-bottom-\[2px\]::after {
  content: var(--tw-content);
  bottom: -2px;
}

.after\:bottom-0::after {
  content: var(--tw-content);
  bottom: 0px;
}

.after\:left-0::after {
  content: var(--tw-content);
  left: 0px;
}

.after\:left-1\/2::after {
  content: var(--tw-content);
  left: 50%;
}

.after\:right-0::after {
  content: var(--tw-content);
  right: 0px;
}

.after\:top-0::after {
  content: var(--tw-content);
  top: 0px;
}

.after\:-z-10::after {
  content: var(--tw-content);
  z-index: -10;
}

.after\:z-0::after {
  content: var(--tw-content);
  z-index: 0;
}

.after\:ml-0\.5::after {
  content: var(--tw-content);
  margin-left: 0.125rem;
}

.after\:ms-0\.5::after {
  content: var(--tw-content);
  margin-inline-start: 0.125rem;
}

.after\:block::after {
  content: var(--tw-content);
  display: block;
}

.after\:h-0::after {
  content: var(--tw-content);
  height: 0px;
}

.after\:h-4::after {
  content: var(--tw-content);
  height: 1rem;
}

.after\:h-5::after {
  content: var(--tw-content);
  height: 1.25rem;
}

.after\:h-\[2px\]::after {
  content: var(--tw-content);
  height: 2px;
}

.after\:h-divider::after {
  content: var(--tw-content);
  height: var(--nextui-divider-weight);
}

.after\:h-full::after {
  content: var(--tw-content);
  height: 100%;
}

.after\:h-px::after {
  content: var(--tw-content);
  height: 1px;
}

.after\:w-0::after {
  content: var(--tw-content);
  width: 0px;
}

.after\:w-4::after {
  content: var(--tw-content);
  width: 1rem;
}

.after\:w-5::after {
  content: var(--tw-content);
  width: 1.25rem;
}

.after\:w-6::after {
  content: var(--tw-content);
  width: 1.5rem;
}

.after\:w-\[80\%\]::after {
  content: var(--tw-content);
  width: 80%;
}

.after\:w-full::after {
  content: var(--tw-content);
  width: 100%;
}

.after\:origin-center::after {
  content: var(--tw-content);
  transform-origin: center;
}

.after\:-translate-x-1\/2::after {
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:translate-y-1::after {
  content: var(--tw-content);
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:rotate-0::after {
  content: var(--tw-content);
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:scale-50::after {
  content: var(--tw-content);
  --tw-scale-x: .5;
  --tw-scale-y: .5;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:rounded-\[calc\(theme\(borderRadius\.large\)\/2\)\]::after {
  content: var(--tw-content);
  border-radius: calc(var(--nextui-radius-large) / 2);
}

.after\:rounded-\[calc\(theme\(borderRadius\.medium\)\*0\.5\)\]::after {
  content: var(--tw-content);
  border-radius: calc(var(--nextui-radius-medium) * 0.5);
}

.after\:rounded-\[calc\(theme\(borderRadius\.medium\)\*0\.6\)\]::after {
  content: var(--tw-content);
  border-radius: calc(var(--nextui-radius-medium) * 0.6);
}

.after\:rounded-\[calc\(theme\(borderRadius\.medium\)\*0\.7\)\]::after {
  content: var(--tw-content);
  border-radius: calc(var(--nextui-radius-medium) * 0.7);
}

.after\:rounded-\[calc\(theme\(borderRadius\.medium\)\/3\)\]::after {
  content: var(--tw-content);
  border-radius: calc(var(--nextui-radius-medium) / 3);
}

.after\:rounded-\[calc\(theme\(borderRadius\.small\)\/3\)\]::after {
  content: var(--tw-content);
  border-radius: calc(var(--nextui-radius-small) / 3);
}

.after\:rounded-full::after {
  content: var(--tw-content);
  border-radius: 9999px;
}

.after\:rounded-none::after {
  content: var(--tw-content);
  border-radius: 0px;
}

.after\:rounded-xl::after {
  content: var(--tw-content);
  border-radius: 0.75rem;
}

.after\:\!bg-danger::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1 !important;
  background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity))) !important;
}

.after\:bg-background::after {
  content: var(--tw-content);
  background-color: hsl(var(--background));
}

.after\:bg-content1::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-content1) / var(--nextui-content1-opacity, var(--tw-bg-opacity)));
}

.after\:bg-content3::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-content3) / var(--nextui-content3-opacity, var(--tw-bg-opacity)));
}

.after\:bg-current::after {
  content: var(--tw-content);
  background-color: currentColor;
}

.after\:bg-danger::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));
}

.after\:bg-default::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-bg-opacity)));
}

.after\:bg-default-foreground::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-bg-opacity)));
}

.after\:bg-divider::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-divider) / var(--nextui-divider-opacity, var(--tw-bg-opacity)));
}

.after\:bg-primary::after {
  content: var(--tw-content);
  background-color: hsl(var(--primary));
}

.after\:bg-secondary::after {
  content: var(--tw-content);
  background-color: hsl(var(--secondary));
}

.after\:bg-success::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));
}

.after\:bg-warning::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));
}

.after\:text-danger::after {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));
}

.after\:text-danger-foreground::after {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));
}

.after\:text-default-foreground::after {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-text-opacity)));
}

.after\:text-primary-foreground::after {
  content: var(--tw-content);
  color: hsl(var(--primary-foreground));
}

.after\:text-secondary-foreground::after {
  content: var(--tw-content);
  color: hsl(var(--secondary-foreground));
}

.after\:text-success-foreground::after {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));
}

.after\:text-warning-foreground::after {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));
}

.after\:opacity-0::after {
  content: var(--tw-content);
  opacity: 0;
}

.after\:opacity-100::after {
  content: var(--tw-content);
  opacity: 1;
}

.after\:shadow-\[0_1px_0px_0_rgba\(0\2c 0\2c 0\2c 0\.05\)\]::after {
  content: var(--tw-content);
  --tw-shadow: 0 1px 0px 0 rgba(0,0,0,0.05);
  --tw-shadow-colored: 0 1px 0px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.after\:shadow-small::after {
  content: var(--tw-content);
  --tw-shadow: var(--nextui-box-shadow-small);
  --tw-shadow-colored: var(--nextui-box-shadow-small);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.after\:transition-all::after {
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}

.after\:transition-none::after {
  content: var(--tw-content);
  transition-property: none;
}

.after\:transition-transform::after {
  content: var(--tw-content);
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}

.after\:\!duration-200::after {
  content: var(--tw-content);
  transition-duration: 200ms !important;
}

.after\:duration-150::after {
  content: var(--tw-content);
  transition-duration: 150ms;
}

.after\:\!ease-linear::after {
  content: var(--tw-content);
  transition-timing-function: linear !important;
}

.after\:content-\[\'\'\]::after {
  --tw-content: '';
  content: var(--tw-content);
}

.after\:content-\[\'\*\'\]::after {
  --tw-content: '*';
  content: var(--tw-content);
}

.after\:\!duration-200::after {
  content: var(--tw-content);
  animation-duration: 200ms !important;
}

.after\:duration-150::after {
  content: var(--tw-content);
  animation-duration: 150ms;
}

.after\:\!ease-linear::after {
  content: var(--tw-content);
  animation-timing-function: linear !important;
}

.after\:transition-background::after {
  content: var(--tw-content);
  transition-property: background;
  transition-timing-function: ease;
  transition-duration: 250ms;
}

.after\:transition-width::after {
  content: var(--tw-content);
  transition-property: width;
  transition-timing-function: ease;
  transition-duration: 250ms;
}

.after\:transition-height::after {
  content: var(--tw-content);
  transition-property: height;
  transition-timing-function: ease;
  transition-duration: 250ms;
}

.after\:transition-transform-opacity::after {
  content: var(--tw-content);
  transition-property: transform, opacity;
  transition-timing-function: ease;
  transition-duration: 250ms;
}

.first\:-ml-0\.5:first-child {
  margin-left: -0.125rem;
}

.first\:mt-2:first-child {
  margin-top: 0.5rem;
}

.first\:rounded-l-md:first-child {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.first\:rounded-s-full:first-child {
  border-start-start-radius: 9999px;
  border-end-start-radius: 9999px;
}

.first\:rounded-s-large:first-child {
  border-start-start-radius: var(--nextui-radius-large);
  border-end-start-radius: var(--nextui-radius-large);
}

.first\:rounded-s-lg:first-child {
  border-start-start-radius: 0.5rem;
  border-end-start-radius: 0.5rem;
}

.first\:rounded-s-medium:first-child {
  border-start-start-radius: var(--nextui-radius-medium);
  border-end-start-radius: var(--nextui-radius-medium);
}

.first\:rounded-s-none:first-child {
  border-start-start-radius: 0px;
  border-end-start-radius: 0px;
}

.first\:rounded-s-small:first-child {
  border-start-start-radius: var(--nextui-radius-small);
  border-end-start-radius: var(--nextui-radius-small);
}

.first\:border-l:first-child {
  border-left-width: 1px;
}

.first\:before\:rounded-s-lg:first-child::before {
  content: var(--tw-content);
  border-start-start-radius: 0.5rem;
  border-end-start-radius: 0.5rem;
}

.last\:rounded-e-full:last-child {
  border-start-end-radius: 9999px;
  border-end-end-radius: 9999px;
}

.last\:rounded-e-large:last-child {
  border-start-end-radius: var(--nextui-radius-large);
  border-end-end-radius: var(--nextui-radius-large);
}

.last\:rounded-e-lg:last-child {
  border-start-end-radius: 0.5rem;
  border-end-end-radius: 0.5rem;
}

.last\:rounded-e-medium:last-child {
  border-start-end-radius: var(--nextui-radius-medium);
  border-end-end-radius: var(--nextui-radius-medium);
}

.last\:rounded-e-none:last-child {
  border-start-end-radius: 0px;
  border-end-end-radius: 0px;
}

.last\:rounded-e-small:last-child {
  border-start-end-radius: var(--nextui-radius-small);
  border-end-end-radius: var(--nextui-radius-small);
}

.last\:rounded-r-md:last-child {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.last\:pr-\[5\%\]:last-child {
  padding-right: 5%;
}

.last\:before\:rounded-e-lg:last-child::before {
  content: var(--tw-content);
  border-start-end-radius: 0.5rem;
  border-end-end-radius: 0.5rem;
}

.first-of-type\:rounded-e-none:first-of-type {
  border-start-end-radius: 0px;
  border-end-end-radius: 0px;
}

.last-of-type\:rounded-s-none:last-of-type {
  border-start-start-radius: 0px;
  border-end-start-radius: 0px;
}

.autofill\:bg-transparent:-webkit-autofill {
  background-color: transparent;
}

.autofill\:bg-transparent:autofill {
  background-color: transparent;
}

.focus-within\:border-danger:focus-within {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));
}

.focus-within\:border-default-foreground:focus-within {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-border-opacity)));
}

.focus-within\:border-primary:focus-within {
  border-color: hsl(var(--primary));
}

.focus-within\:border-secondary:focus-within {
  border-color: hsl(var(--secondary));
}

.focus-within\:border-success:focus-within {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));
}

.focus-within\:border-warning:focus-within {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));
}

.focus-within\:bg-danger-50:focus-within {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity)));
}

.focus-within\:bg-primary-50:focus-within {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-primary-50) / var(--nextui-primary-50-opacity, var(--tw-bg-opacity)));
}

.focus-within\:bg-secondary-50:focus-within {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-secondary-50) / var(--nextui-secondary-50-opacity, var(--tw-bg-opacity)));
}

.focus-within\:bg-success-50:focus-within {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success-50) / var(--nextui-success-50-opacity, var(--tw-bg-opacity)));
}

.focus-within\:bg-warning-50:focus-within {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning-50) / var(--nextui-warning-50-opacity, var(--tw-bg-opacity)));
}

.focus-within\:after\:w-full:focus-within::after {
  content: var(--tw-content);
  width: 100%;
}

.hover\:border-collapse:hover {
  border-collapse: collapse;
}

.hover\:-translate-x-0:hover {
  --tw-translate-x: -0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-px:hover {
  --tw-translate-y: -1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-110:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-125:hover {
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:cursor-grab:hover {
  cursor: grab;
}

.hover\:cursor-pointer:hover {
  cursor: pointer;
}

.hover\:border-\[\#24AE7C\]:hover {
  --tw-border-opacity: 1;
  border-color: rgb(36 174 124 / var(--tw-border-opacity));
}

.hover\:border-\[\#24AE7C\]\/20:hover {
  border-color: rgb(36 174 124 / 0.2);
}

.hover\:border-\[\#24AE7C\]\/30:hover {
  border-color: rgb(36 174 124 / 0.3);
}

.hover\:border-amber-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity));
}

.hover\:border-default:hover {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-border-opacity)));
}

.hover\:border-default-300:hover {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-default-300) / var(--nextui-default-300-opacity, var(--tw-border-opacity)));
}

.hover\:border-default-400:hover {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-default-400) / var(--nextui-default-400-opacity, var(--tw-border-opacity)));
}

.hover\:\!bg-foreground:hover {
  background-color: hsl(var(--foreground)) !important;
}

.hover\:bg-\[\#131313\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(19 19 19 / var(--tw-bg-opacity));
}

.hover\:bg-\[\#141414\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(20 20 20 / var(--tw-bg-opacity));
}

.hover\:bg-\[\#1F1F1F\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(31 31 31 / var(--tw-bg-opacity));
}

.hover\:bg-\[\#232a40\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(35 42 64 / var(--tw-bg-opacity));
}

.hover\:bg-\[\#24AE7C\]\/20:hover {
  background-color: rgb(36 174 124 / 0.2);
}

.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}

.hover\:bg-amber-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(252 211 77 / var(--tw-bg-opacity));
}

.hover\:bg-blue-500\/10:hover {
  background-color: rgb(121 181 236 / 0.1);
}

.hover\:bg-danger-100:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger-100) / var(--nextui-danger-100-opacity, var(--tw-bg-opacity)));
}

.hover\:bg-default-100:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));
}

.hover\:bg-default-200:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-200) / var(--nextui-default-200-opacity, var(--tw-bg-opacity)));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.hover\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.hover\:bg-gray-700\/70:hover {
  background-color: rgb(55 65 81 / 0.7);
}

.hover\:bg-gray-700\/80:hover {
  background-color: rgb(55 65 81 / 0.8);
}

.hover\:bg-gray-800\/20:hover {
  background-color: rgb(31 41 55 / 0.2);
}

.hover\:bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity));
}

.hover\:bg-primary-100:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-primary-100) / var(--nextui-primary-100-opacity, var(--tw-bg-opacity)));
}

.hover\:bg-red-500\/80:hover {
  background-color: rgb(243 120 119 / 0.8);
}

.hover\:bg-red-500\/90:hover {
  background-color: rgb(243 120 119 / 0.9);
}

.hover\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(242 78 67 / var(--tw-bg-opacity));
}

.hover\:bg-red-700\/40:hover {
  background-color: rgb(242 78 67 / 0.4);
}

.hover\:bg-secondary-100:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-secondary-100) / var(--nextui-secondary-100-opacity, var(--tw-bg-opacity)));
}

.hover\:bg-slate-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}

.hover\:bg-slate-100\/50:hover {
  background-color: rgb(241 245 249 / 0.5);
}

.hover\:bg-slate-100\/80:hover {
  background-color: rgb(241 245 249 / 0.8);
}

.hover\:bg-slate-900\/80:hover {
  background-color: rgb(15 23 42 / 0.8);
}

.hover\:bg-slate-900\/90:hover {
  background-color: rgb(15 23 42 / 0.9);
}

.hover\:bg-success-100:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success-100) / var(--nextui-success-100-opacity, var(--tw-bg-opacity)));
}

.hover\:bg-warning-100:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning-100) / var(--nextui-warning-100-opacity, var(--tw-bg-opacity)));
}

.hover\:bg-white\/10:hover {
  background-color: rgb(255 255 255 / 0.1);
}

.hover\:bg-white\/5:hover {
  background-color: rgb(255 255 255 / 0.05);
}

.hover\:bg-gradient-to-r:hover {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.hover\:from-\[\#1a1a1a\]\/90:hover {
  --tw-gradient-from: rgb(26 26 26 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(26 26 26 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-blue-700:hover {
  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:to-\[\#2a2a2a\]\/90:hover {
  --tw-gradient-to: rgb(42 42 42 / 0.9) var(--tw-gradient-to-position);
}

.hover\:to-indigo-700:hover {
  --tw-gradient-to: #4338ca var(--tw-gradient-to-position);
}

.hover\:text-\[\#24AE7C\]:hover {
  --tw-text-opacity: 1;
  color: rgb(36 174 124 / var(--tw-text-opacity));
}

.hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}

.hover\:text-blue-300:hover {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity));
}

.hover\:text-blue-400:hover {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity));
}

.hover\:text-gray-200:hover {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}

.hover\:text-gray-300:hover {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.hover\:text-gray-700\/75:hover {
  color: rgb(55 65 81 / 0.75);
}

.hover\:text-green-300:hover {
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity));
}

.hover\:text-green-500:hover {
  --tw-text-opacity: 1;
  color: rgb(36 174 124 / var(--tw-text-opacity));
}

.hover\:text-neutral-100:hover {
  --tw-text-opacity: 1;
  color: rgb(245 245 245 / var(--tw-text-opacity));
}

.hover\:text-red-300:hover {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity));
}

.hover\:text-slate-900:hover {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.hover\:text-slate-950:hover {
  --tw-text-opacity: 1;
  color: rgb(2 6 23 / var(--tw-text-opacity));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:underline-offset-4:hover {
  text-underline-offset: 4px;
}

.hover\:\!opacity-100:hover {
  opacity: 1 !important;
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:opacity-40:hover {
  opacity: 0.4;
}

.hover\:opacity-80:hover {
  opacity: 0.8;
}

.hover\:opacity-90:hover {
  opacity: 0.9;
}

.hover\:shadow-\[0_0-15px_rgba\(0\2c 255\2c 153\2c 0\.5\)\]:hover {
  --tw-shadow: 0 0-15px rgba(0,255,153,0.5);
  --tw-shadow-colored: 0 0-15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-sm:hover {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-blue-500\/40:hover {
  --tw-shadow-color: rgb(121 181 236 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:outline-none:hover {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.hover\:ring-0:hover {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.hover\:blur-3xl:hover {
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.hover\:\[animation-play-state\:paused\]:hover {
  animation-play-state: paused;
}

.hover\:after\:bg-danger\/20:hover::after {
  content: var(--tw-content);
  background-color: hsl(var(--nextui-danger) / 0.2);
}

.hover\:after\:bg-foreground\/10:hover::after {
  content: var(--tw-content);
  background-color: hsl(var(--foreground) / 0.1);
}

.hover\:after\:bg-primary\/20:hover::after {
  content: var(--tw-content);
  background-color: hsl(var(--primary) / 0.2);
}

.hover\:after\:bg-secondary\/20:hover::after {
  content: var(--tw-content);
  background-color: hsl(var(--secondary) / 0.2);
}

.hover\:after\:bg-success\/20:hover::after {
  content: var(--tw-content);
  background-color: hsl(var(--nextui-success) / 0.2);
}

.hover\:after\:bg-warning\/20:hover::after {
  content: var(--tw-content);
  background-color: hsl(var(--nextui-warning) / 0.2);
}

.hover\:after\:opacity-100:hover::after {
  content: var(--tw-content);
  opacity: 1;
}

.focus-within\:hover\:border-danger:hover:focus-within {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));
}

.focus-within\:hover\:border-default-foreground:hover:focus-within {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-border-opacity)));
}

.focus-within\:hover\:border-primary:hover:focus-within {
  border-color: hsl(var(--primary));
}

.focus-within\:hover\:border-secondary:hover:focus-within {
  border-color: hsl(var(--secondary));
}

.focus-within\:hover\:border-success:hover:focus-within {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));
}

.focus-within\:hover\:border-warning:hover:focus-within {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));
}

.focus-within\:hover\:bg-default-100:hover:focus-within {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));
}

.focus\:border-blue-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(121 181 236 / var(--tw-border-opacity));
}

.focus\:bg-accent:focus {
  background-color: hsl(var(--accent));
}

.focus\:bg-danger-400\/50:focus {
  background-color: hsl(var(--nextui-danger-400) / 0.5);
}

.focus\:bg-default-400\/50:focus {
  background-color: hsl(var(--nextui-default-400) / 0.5);
}

.focus\:bg-primary-400\/50:focus {
  background-color: hsl(var(--nextui-primary-400) / 0.5);
}

.focus\:bg-secondary-400\/50:focus {
  background-color: hsl(var(--nextui-secondary-400) / 0.5);
}

.focus\:bg-slate-100:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}

.focus\:bg-success-400\/50:focus {
  background-color: hsl(var(--nextui-success-400) / 0.5);
}

.focus\:bg-warning-400\/50:focus {
  background-color: hsl(var(--nextui-warning-400) / 0.5);
}

.focus\:text-accent-foreground:focus {
  color: hsl(var(--accent-foreground));
}

.focus\:text-slate-900:focus {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.focus\:underline:focus {
  text-decoration-line: underline;
}

.focus\:shadow-sm:focus {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(121 181 236 / var(--tw-ring-opacity));
}

.focus\:ring-gray-300:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity));
}

.focus\:ring-ring:focus {
  --tw-ring-color: hsl(var(--ring));
}

.focus\:ring-slate-400:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(148 163 184 / var(--tw-ring-opacity));
}

.focus\:ring-slate-950:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(2 6 23 / var(--tw-ring-opacity));
}

.focus\:ring-offset-0:focus {
  --tw-ring-offset-width: 0px;
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus\:ring-offset-slate-50:focus {
  --tw-ring-offset-color: #f8fafc;
}

.focus\:ring-offset-transparent:focus {
  --tw-ring-offset-color: transparent;
}

.focus-visible\:z-10:focus-visible {
  z-index: 10;
}

.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-visible\:outline-2:focus-visible {
  outline-width: 2px;
}

.focus-visible\:outline-offset-2:focus-visible {
  outline-offset: 2px;
}

.focus-visible\:outline-focus:focus-visible {
  outline-color: hsl(var(--nextui-focus) / var(--nextui-focus-opacity, 1));
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-slate-950:focus-visible {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(2 6 23 / var(--tw-ring-opacity));
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}

.active\:border-collapse:active {
  border-collapse: collapse;
}

.active\:translate-y-0\.5:active {
  --tw-translate-y: 0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.active\:bg-\[\#1F1F1F\]:active {
  --tw-bg-opacity: 1;
  background-color: rgb(31 31 31 / var(--tw-bg-opacity));
}

.active\:bg-default-200:active {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-200) / var(--nextui-default-200-opacity, var(--tw-bg-opacity)));
}

.active\:bg-default-300:active {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-300) / var(--nextui-default-300-opacity, var(--tw-bg-opacity)));
}

.active\:bg-gray-200:active {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.active\:underline:active {
  text-decoration-line: underline;
}

.active\:\!opacity-70:active {
  opacity: 0.7 !important;
}

.active\:opacity-disabled:active {
  opacity: var(--nextui-disabled-opacity);
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:transform-none:disabled {
  transform: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.disabled\:opacity-60:disabled {
  opacity: 0.6;
}

.group:hover .group-hover\:z-30 {
  z-index: 30;
}

.group:hover .group-hover\:block {
  display: block;
}

.group:hover .group-hover\:hidden {
  display: none;
}

.group\/bento:hover .group-hover\/bento\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-\[1\.05\] {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:border-current {
  border-color: currentColor;
}

.group\/bento:hover .group-hover\/bento\:bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:from-indigo-600 {
  --tw-gradient-from: #4f46e5 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(79 70 229 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.group:hover .group-hover\:to-white {
  --tw-gradient-to: #FFF var(--tw-gradient-to-position);
}

.group:hover .group-hover\:text-\[\#24AE7C\] {
  --tw-text-opacity: 1;
  color: rgb(36 174 124 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-current {
  color: currentColor;
}

.group\/spotlight:hover .group-hover\/spotlight\:opacity-100 {
  opacity: 1;
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.group:hover .group-hover\:opacity-50 {
  opacity: 0.5;
}

.group.toaster .group-\[\.toaster\]\:border-slate-200 {
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity));
}

.group.toast .group-\[\.toast\]\:bg-slate-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}

.group.toast .group-\[\.toast\]\:bg-slate-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}

.group.toaster .group-\[\.toaster\]\:bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.group.toast .group-\[\.toast\]\:text-slate-50 {
  --tw-text-opacity: 1;
  color: rgb(248 250 252 / var(--tw-text-opacity));
}

.group.toast .group-\[\.toast\]\:text-slate-500 {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}

.group.toaster .group-\[\.toaster\]\:text-slate-950 {
  --tw-text-opacity: 1;
  color: rgb(2 6 23 / var(--tw-text-opacity));
}

.group.toaster .group-\[\.toaster\]\:shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}

.peer:disabled ~ .peer-disabled\:opacity-70 {
  opacity: 0.7;
}

.has-\[\:disabled\]\:opacity-50:has(:disabled) {
  opacity: 0.5;
}

.aria-expanded\:scale-\[0\.97\][aria-expanded="true"] {
  --tw-scale-x: 0.97;
  --tw-scale-y: 0.97;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.aria-expanded\:opacity-70[aria-expanded="true"] {
  opacity: 0.7;
}

.aria-selected\:bg-slate-100[aria-selected="true"] {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}

.aria-selected\:text-slate-900[aria-selected="true"] {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.data-\[disabled\=true\]\:pointer-events-none[data-disabled="true"] {
  pointer-events: none;
}

.data-\[disabled\]\:pointer-events-none[data-disabled] {
  pointer-events: none;
}

.data-\[loaded\=true\]\:pointer-events-auto[data-loaded="true"] {
  pointer-events: auto;
}

.data-\[visible\=true\]\:pointer-events-auto[data-visible="true"] {
  pointer-events: auto;
}

.data-\[focus-visible\=true\]\:z-10[data-focus-visible="true"] {
  z-index: 10;
}

.data-\[focused\=true\]\:z-10[data-focused="true"] {
  z-index: 10;
}

.data-\[has-label\=true\]\:mt-\[calc\(theme\(fontSize\.small\)_\+_10px\)\][data-has-label="true"] {
  margin-top: calc(var(--nextui-font-size-small) + 10px);
}

.data-\[has-label\=true\]\:mt-\[calc\(theme\(fontSize\.small\)_\+_12px\)\][data-has-label="true"] {
  margin-top: calc(var(--nextui-font-size-small) + 12px);
}

.data-\[has-label\=true\]\:mt-\[calc\(theme\(fontSize\.small\)_\+_8px\)\][data-has-label="true"] {
  margin-top: calc(var(--nextui-font-size-small) + 8px);
}

.data-\[open\=true\]\:block[data-open="true"] {
  display: block;
}

.data-\[open\=true\]\:flex[data-open="true"] {
  display: flex;
}

.data-\[hidden\=true\]\:hidden[data-hidden="true"] {
  display: none;
}

.data-\[inert\=true\]\:hidden[data-inert="true"] {
  display: none;
}

.data-\[justify\=end\]\:flex-grow[data-justify="end"] {
  flex-grow: 1;
}

.data-\[justify\=start\]\:flex-grow[data-justify="start"] {
  flex-grow: 1;
}

.data-\[justify\=end\]\:basis-0[data-justify="end"] {
  flex-basis: 0px;
}

.data-\[justify\=start\]\:basis-0[data-justify="start"] {
  flex-basis: 0px;
}

.data-\[focus-visible\=true\]\:-translate-x-3[data-focus-visible="true"] {
  --tw-translate-x: -0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[hover\=true\]\:-translate-x-3[data-hover="true"] {
  --tw-translate-x: -0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[hover\=true\]\:translate-x-0[data-hover="true"] {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=right\]\:translate-x-1[data-side="right"] {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[before\=true\]\:rotate-180[data-before="true"] {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[direction\=ascending\]\:rotate-180[data-direction="ascending"] {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[open\=true\]\:-rotate-90[data-open="true"] {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[open\=true\]\:rotate-180[data-open="true"] {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[invisible\=true\]\:scale-0[data-invisible="true"] {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[pressed\=true\]\:scale-100[data-pressed="true"] {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[pressed\=true\]\:scale-\[0\.97\][data-pressed="true"] {
  --tw-scale-x: 0.97;
  --tw-scale-y: 0.97;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[disabled\=true\]\:cursor-default[data-disabled="true"] {
  cursor: default;
}

.data-\[disabled\=true\]\:cursor-not-allowed[data-disabled="true"] {
  cursor: not-allowed;
}

.data-\[dragging\=true\]\:cursor-grabbing[data-dragging="true"] {
  cursor: grabbing;
}

.data-\[readonly\=true\]\:cursor-default[data-readonly="true"] {
  cursor: default;
}

.data-\[sortable\=true\]\:cursor-pointer[data-sortable="true"] {
  cursor: pointer;
}

.data-\[unavailable\=true\]\:cursor-default[data-unavailable="true"] {
  cursor: default;
}

.data-\[visible\=true\]\:cursor-pointer[data-visible="true"] {
  cursor: pointer;
}

.data-\[orientation\=horizontal\]\:flex-row[data-orientation="horizontal"] {
  flex-direction: row;
}

.data-\[has-helper\=true\]\:items-start[data-has-helper="true"] {
  align-items: flex-start;
}

.data-\[justify\=start\]\:justify-start[data-justify="start"] {
  justify-content: flex-start;
}

.data-\[justify\=end\]\:justify-end[data-justify="end"] {
  justify-content: flex-end;
}

.data-\[justify\=center\]\:justify-center[data-justify="center"] {
  justify-content: center;
}

.data-\[loaded\=true\]\:overflow-visible[data-loaded="true"] {
  overflow: visible;
}

.data-\[has-multiple-rows\=true\]\:rounded-large[data-has-multiple-rows="true"] {
  border-radius: var(--nextui-radius-large);
}

.data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:rounded-full[data-range-selection="true"][data-selection-end="true"][data-selected="true"] {
  border-radius: 9999px;
}

.data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:rounded-full[data-range-selection="true"][data-selection-start="true"][data-selected="true"] {
  border-radius: 9999px;
}

.data-\[selected\=true\]\:border-b-2[data-selected="true"] {
  border-bottom-width: 2px;
}

.data-\[menu-open\=true\]\:border-none[data-menu-open="true"] {
  border-style: none;
}

.data-\[active\=true\]\:border-danger[data-active="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));
}

.data-\[active\=true\]\:border-default-400[data-active="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-default-400) / var(--nextui-default-400-opacity, var(--tw-border-opacity)));
}

.data-\[active\=true\]\:border-primary[data-active="true"] {
  border-color: hsl(var(--primary));
}

.data-\[active\=true\]\:border-secondary[data-active="true"] {
  border-color: hsl(var(--secondary));
}

.data-\[active\=true\]\:border-success[data-active="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));
}

.data-\[active\=true\]\:border-warning[data-active="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));
}

.data-\[focus\=true\]\:border-danger[data-focus="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));
}

.data-\[focus\=true\]\:border-default-foreground[data-focus="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-border-opacity)));
}

.data-\[focus\=true\]\:border-primary[data-focus="true"] {
  border-color: hsl(var(--primary));
}

.data-\[focus\=true\]\:border-secondary[data-focus="true"] {
  border-color: hsl(var(--secondary));
}

.data-\[focus\=true\]\:border-success[data-focus="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));
}

.data-\[focus\=true\]\:border-warning[data-focus="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));
}

.data-\[hover\=true\]\:border-danger[data-hover="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));
}

.data-\[hover\=true\]\:border-default[data-hover="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-border-opacity)));
}

.data-\[hover\=true\]\:border-default-400[data-hover="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-default-400) / var(--nextui-default-400-opacity, var(--tw-border-opacity)));
}

.data-\[hover\=true\]\:border-primary[data-hover="true"] {
  border-color: hsl(var(--primary));
}

.data-\[hover\=true\]\:border-secondary[data-hover="true"] {
  border-color: hsl(var(--secondary));
}

.data-\[hover\=true\]\:border-success[data-hover="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));
}

.data-\[hover\=true\]\:border-warning[data-hover="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));
}

.data-\[open\=true\]\:border-danger[data-open="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));
}

.data-\[open\=true\]\:border-default-foreground[data-open="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-border-opacity)));
}

.data-\[open\=true\]\:border-primary[data-open="true"] {
  border-color: hsl(var(--primary));
}

.data-\[open\=true\]\:border-secondary[data-open="true"] {
  border-color: hsl(var(--secondary));
}

.data-\[open\=true\]\:border-success[data-open="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));
}

.data-\[open\=true\]\:border-warning[data-open="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));
}

.data-\[selected\=true\]\:border-\[\#24AE7C\][data-selected="true"] {
  --tw-border-opacity: 1;
  border-color: rgb(36 174 124 / var(--tw-border-opacity));
}

.data-\[active\=true\]\:bg-danger[data-active="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));
}

.data-\[active\=true\]\:bg-default-400[data-active="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-400) / var(--nextui-default-400-opacity, var(--tw-bg-opacity)));
}

.data-\[active\=true\]\:bg-primary[data-active="true"] {
  background-color: hsl(var(--primary));
}

.data-\[active\=true\]\:bg-secondary[data-active="true"] {
  background-color: hsl(var(--secondary));
}

.data-\[active\=true\]\:bg-success[data-active="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));
}

.data-\[active\=true\]\:bg-warning[data-active="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));
}

.data-\[active\]\:bg-accent\/50[data-active] {
  background-color: hsl(var(--accent) / 0.5);
}

.data-\[hover\=true\]\:\!bg-danger[data-hover="true"] {
  --tw-bg-opacity: 1 !important;
  background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity))) !important;
}

.data-\[hover\=true\]\:\!bg-danger-100[data-hover="true"] {
  --tw-bg-opacity: 1 !important;
  background-color: hsl(var(--nextui-danger-100) / var(--nextui-danger-100-opacity, var(--tw-bg-opacity))) !important;
}

.data-\[hover\=true\]\:\!bg-default[data-hover="true"] {
  --tw-bg-opacity: 1 !important;
  background-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-bg-opacity))) !important;
}

.data-\[hover\=true\]\:\!bg-primary[data-hover="true"] {
  background-color: hsl(var(--primary)) !important;
}

.data-\[hover\=true\]\:\!bg-secondary[data-hover="true"] {
  background-color: hsl(var(--secondary)) !important;
}

.data-\[hover\=true\]\:\!bg-success[data-hover="true"] {
  --tw-bg-opacity: 1 !important;
  background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity))) !important;
}

.data-\[hover\=true\]\:\!bg-warning[data-hover="true"] {
  --tw-bg-opacity: 1 !important;
  background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity))) !important;
}

.data-\[hover\=true\]\:bg-content2[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-content2) / var(--nextui-content2-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-danger[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-danger-100[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger-100) / var(--nextui-danger-100-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-danger-50[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-danger\/20[data-hover="true"] {
  background-color: hsl(var(--nextui-danger) / 0.2);
}

.data-\[hover\=true\]\:bg-default[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-default-100[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-default-200[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-200) / var(--nextui-default-200-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-default-50[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-50) / var(--nextui-default-50-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-default\/40[data-hover="true"] {
  background-color: hsl(var(--nextui-default) / 0.4);
}

.data-\[hover\=true\]\:bg-foreground-200[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-foreground-200) / var(--nextui-foreground-200-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-primary[data-hover="true"] {
  background-color: hsl(var(--primary));
}

.data-\[hover\=true\]\:bg-primary-100[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-primary-100) / var(--nextui-primary-100-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-primary-50[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-primary-50) / var(--nextui-primary-50-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-primary\/20[data-hover="true"] {
  background-color: hsl(var(--primary) / 0.2);
}

.data-\[hover\=true\]\:bg-secondary[data-hover="true"] {
  background-color: hsl(var(--secondary));
}

.data-\[hover\=true\]\:bg-secondary-100[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-secondary-100) / var(--nextui-secondary-100-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-secondary-50[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-secondary-50) / var(--nextui-secondary-50-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-secondary\/20[data-hover="true"] {
  background-color: hsl(var(--secondary) / 0.2);
}

.data-\[hover\=true\]\:bg-success[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-success-100[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success-100) / var(--nextui-success-100-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-success-50[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success-50) / var(--nextui-success-50-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-success\/20[data-hover="true"] {
  background-color: hsl(var(--nextui-success) / 0.2);
}

.data-\[hover\=true\]\:bg-transparent[data-hover="true"] {
  background-color: transparent;
}

.data-\[hover\=true\]\:bg-warning[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-warning-100[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning-100) / var(--nextui-warning-100-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-warning-50[data-hover="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning-50) / var(--nextui-warning-50-opacity, var(--tw-bg-opacity)));
}

.data-\[hover\=true\]\:bg-warning\/20[data-hover="true"] {
  background-color: hsl(var(--nextui-warning) / 0.2);
}

.data-\[in-range\=false\]\:bg-default-200[data-in-range="false"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-200) / var(--nextui-default-200-opacity, var(--tw-bg-opacity)));
}

.data-\[in-range\=true\]\:bg-background\/50[data-in-range="true"] {
  background-color: hsl(var(--background) / 0.5);
}

.data-\[in-range\=true\]\:bg-danger[data-in-range="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));
}

.data-\[in-range\=true\]\:bg-foreground[data-in-range="true"] {
  background-color: hsl(var(--foreground));
}

.data-\[in-range\=true\]\:bg-primary[data-in-range="true"] {
  background-color: hsl(var(--primary));
}

.data-\[in-range\=true\]\:bg-secondary[data-in-range="true"] {
  background-color: hsl(var(--secondary));
}

.data-\[in-range\=true\]\:bg-success[data-in-range="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));
}

.data-\[in-range\=true\]\:bg-warning[data-in-range="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));
}

.data-\[loaded\=true\]\:\!bg-transparent[data-loaded="true"] {
  background-color: transparent !important;
}

.data-\[selected\=true\]\:bg-danger[data-selected="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));
}

.data-\[selected\=true\]\:bg-default[data-selected="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-bg-opacity)));
}

.data-\[selected\=true\]\:bg-foreground[data-selected="true"] {
  background-color: hsl(var(--foreground));
}

.data-\[selected\=true\]\:bg-primary[data-selected="true"] {
  background-color: hsl(var(--primary));
}

.data-\[selected\=true\]\:bg-secondary[data-selected="true"] {
  background-color: hsl(var(--secondary));
}

.data-\[selected\=true\]\:bg-success[data-selected="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));
}

.data-\[selected\=true\]\:bg-transparent[data-selected="true"] {
  background-color: transparent;
}

.data-\[selected\=true\]\:bg-warning[data-selected="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));
}

.data-\[selected\=true\]\:data-\[hover\=true\]\:bg-danger[data-hover="true"][data-selected="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));
}

.data-\[selected\=true\]\:data-\[hover\=true\]\:bg-foreground[data-hover="true"][data-selected="true"] {
  background-color: hsl(var(--foreground));
}

.data-\[selected\=true\]\:data-\[hover\=true\]\:bg-primary[data-hover="true"][data-selected="true"] {
  background-color: hsl(var(--primary));
}

.data-\[selected\=true\]\:data-\[hover\=true\]\:bg-secondary[data-hover="true"][data-selected="true"] {
  background-color: hsl(var(--secondary));
}

.data-\[selected\=true\]\:data-\[hover\=true\]\:bg-success[data-hover="true"][data-selected="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));
}

.data-\[selected\=true\]\:data-\[hover\=true\]\:bg-warning[data-hover="true"][data-selected="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));
}

.data-\[selected\=true\]\:data-\[range-selection\=true\]\:data-\[outside-month\=true\]\:bg-transparent[data-outside-month="true"][data-range-selection="true"][data-selected="true"] {
  background-color: transparent;
}

.data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:bg-danger[data-range-selection="true"][data-selection-end="true"][data-selected="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));
}

.data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:bg-foreground[data-range-selection="true"][data-selection-end="true"][data-selected="true"] {
  background-color: hsl(var(--foreground));
}

.data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:bg-primary[data-range-selection="true"][data-selection-end="true"][data-selected="true"] {
  background-color: hsl(var(--primary));
}

.data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:bg-secondary[data-range-selection="true"][data-selection-end="true"][data-selected="true"] {
  background-color: hsl(var(--secondary));
}

.data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:bg-success[data-range-selection="true"][data-selection-end="true"][data-selected="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));
}

.data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:bg-warning[data-range-selection="true"][data-selection-end="true"][data-selected="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));
}

.data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:bg-danger[data-range-selection="true"][data-selection-start="true"][data-selected="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));
}

.data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:bg-foreground[data-range-selection="true"][data-selection-start="true"][data-selected="true"] {
  background-color: hsl(var(--foreground));
}

.data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:bg-primary[data-range-selection="true"][data-selection-start="true"][data-selected="true"] {
  background-color: hsl(var(--primary));
}

.data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:bg-secondary[data-range-selection="true"][data-selection-start="true"][data-selected="true"] {
  background-color: hsl(var(--secondary));
}

.data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:bg-success[data-range-selection="true"][data-selection-start="true"][data-selected="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));
}

.data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:bg-warning[data-range-selection="true"][data-selection-start="true"][data-selected="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));
}

.data-\[state\=checked\]\:bg-slate-900[data-state="checked"] {
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}

.data-\[state\=open\]\:bg-accent\/50[data-state="open"] {
  background-color: hsl(var(--accent) / 0.5);
}

.data-\[state\=open\]\:bg-secondary[data-state="open"] {
  background-color: hsl(var(--secondary));
}

.data-\[state\=selected\]\:bg-slate-100[data-state="selected"] {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}

.data-\[has-end-content\=true\]\:pe-1\.5[data-has-end-content="true"] {
  padding-inline-end: 0.375rem;
}

.data-\[has-helper\=true\]\:pb-\[calc\(theme\(fontSize\.tiny\)_\+8px\)\][data-has-helper="true"] {
  padding-bottom: calc(var(--nextui-font-size-tiny) + 8px);
}

.data-\[has-helper\=true\]\:pb-\[calc\(theme\(fontSize\.tiny\)_\+_8px\)\][data-has-helper="true"] {
  padding-bottom: calc(var(--nextui-font-size-tiny) + 8px);
}

.data-\[has-start-content\=true\]\:ps-1\.5[data-has-start-content="true"] {
  padding-inline-start: 0.375rem;
}

.data-\[has-title\=true\]\:pt-1[data-has-title="true"] {
  padding-top: 0.25rem;
}

.data-\[active\=true\]\:font-semibold[data-active="true"] {
  font-weight: 600;
}

.data-\[selected\=true\]\:font-medium[data-selected="true"] {
  font-weight: 500;
}

.data-\[active\=true\]\:text-danger-foreground[data-active="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[active\=true\]\:text-default-foreground[data-active="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[active\=true\]\:text-primary-foreground[data-active="true"] {
  color: hsl(var(--primary-foreground));
}

.data-\[active\=true\]\:text-secondary-foreground[data-active="true"] {
  color: hsl(var(--secondary-foreground));
}

.data-\[active\=true\]\:text-success-foreground[data-active="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[active\=true\]\:text-warning-foreground[data-active="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[disabled\=true\]\:text-default-300[data-disabled="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-300) / var(--nextui-default-300-opacity, var(--tw-text-opacity)));
}

.data-\[editable\=true\]\:data-\[placeholder\=true\]\:text-danger-300[data-placeholder="true"][data-editable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-300) / var(--nextui-danger-300-opacity, var(--tw-text-opacity)));
}

.data-\[editable\=true\]\:data-\[placeholder\=true\]\:text-foreground-500[data-placeholder="true"][data-editable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-foreground-500) / var(--nextui-foreground-500-opacity, var(--tw-text-opacity)));
}

.data-\[editable\=true\]\:data-\[placeholder\=true\]\:text-primary-300[data-placeholder="true"][data-editable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-primary-300) / var(--nextui-primary-300-opacity, var(--tw-text-opacity)));
}

.data-\[editable\=true\]\:data-\[placeholder\=true\]\:text-secondary-300[data-placeholder="true"][data-editable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-secondary-300) / var(--nextui-secondary-300-opacity, var(--tw-text-opacity)));
}

.data-\[editable\=true\]\:data-\[placeholder\=true\]\:text-success-400[data-placeholder="true"][data-editable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-400) / var(--nextui-success-400-opacity, var(--tw-text-opacity)));
}

.data-\[editable\=true\]\:data-\[placeholder\=true\]\:text-warning-400[data-placeholder="true"][data-editable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-400) / var(--nextui-warning-400-opacity, var(--tw-text-opacity)));
}

.data-\[editable\=true\]\:text-danger[data-editable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));
}

.data-\[editable\=true\]\:text-foreground[data-editable="true"] {
  color: hsl(var(--foreground));
}

.data-\[editable\=true\]\:text-primary[data-editable="true"] {
  color: hsl(var(--primary));
}

.data-\[editable\=true\]\:text-secondary[data-editable="true"] {
  color: hsl(var(--secondary));
}

.data-\[editable\=true\]\:text-success-600[data-editable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-600) / var(--nextui-success-600-opacity, var(--tw-text-opacity)));
}

.data-\[editable\=true\]\:text-warning-600[data-editable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-600) / var(--nextui-warning-600-opacity, var(--tw-text-opacity)));
}

.data-\[hover\=true\]\:\!text-primary-foreground[data-hover="true"] {
  color: hsl(var(--primary-foreground)) !important;
}

.data-\[hover\=true\]\:\!text-secondary-foreground[data-hover="true"] {
  color: hsl(var(--secondary-foreground)) !important;
}

.data-\[hover\=true\]\:\!text-success-foreground[data-hover="true"] {
  --tw-text-opacity: 1 !important;
  color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity))) !important;
}

.data-\[hover\=true\]\:\!text-warning-foreground[data-hover="true"] {
  --tw-text-opacity: 1 !important;
  color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity))) !important;
}

.data-\[hover\=true\]\:text-danger[data-hover="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));
}

.data-\[hover\=true\]\:text-danger-500[data-hover="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-500) / var(--nextui-danger-500-opacity, var(--tw-text-opacity)));
}

.data-\[hover\=true\]\:text-danger-foreground[data-hover="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[hover\=true\]\:text-default-500[data-hover="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-500) / var(--nextui-default-500-opacity, var(--tw-text-opacity)));
}

.data-\[hover\=true\]\:text-default-foreground[data-hover="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[hover\=true\]\:text-foreground-400[data-hover="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-foreground-400) / var(--nextui-foreground-400-opacity, var(--tw-text-opacity)));
}

.data-\[hover\=true\]\:text-foreground-600[data-hover="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-foreground-600) / var(--nextui-foreground-600-opacity, var(--tw-text-opacity)));
}

.data-\[hover\=true\]\:text-primary[data-hover="true"] {
  color: hsl(var(--primary));
}

.data-\[hover\=true\]\:text-primary-400[data-hover="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-primary-400) / var(--nextui-primary-400-opacity, var(--tw-text-opacity)));
}

.data-\[hover\=true\]\:text-primary-foreground[data-hover="true"] {
  color: hsl(var(--primary-foreground));
}

.data-\[hover\=true\]\:text-secondary[data-hover="true"] {
  color: hsl(var(--secondary));
}

.data-\[hover\=true\]\:text-secondary-400[data-hover="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-secondary-400) / var(--nextui-secondary-400-opacity, var(--tw-text-opacity)));
}

.data-\[hover\=true\]\:text-secondary-foreground[data-hover="true"] {
  color: hsl(var(--secondary-foreground));
}

.data-\[hover\=true\]\:text-success[data-hover="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-text-opacity)));
}

.data-\[hover\=true\]\:text-success-600[data-hover="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-600) / var(--nextui-success-600-opacity, var(--tw-text-opacity)));
}

.data-\[hover\=true\]\:text-success-foreground[data-hover="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[hover\=true\]\:text-warning[data-hover="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-text-opacity)));
}

.data-\[hover\=true\]\:text-warning-600[data-hover="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-600) / var(--nextui-warning-600-opacity, var(--tw-text-opacity)));
}

.data-\[hover\=true\]\:text-warning-foreground[data-hover="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[invalid\=true\]\:data-\[editable\=true\]\:text-danger[data-editable="true"][data-invalid="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));
}

.data-\[invalid\=true\]\:text-danger-300[data-invalid="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-300) / var(--nextui-danger-300-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:data-\[hover\=true\]\:text-background[data-hover="true"][data-selected="true"] {
  color: hsl(var(--background));
}

.data-\[selected\=true\]\:data-\[hover\=true\]\:text-danger-foreground[data-hover="true"][data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:data-\[hover\=true\]\:text-primary-foreground[data-hover="true"][data-selected="true"] {
  color: hsl(var(--primary-foreground));
}

.data-\[selected\=true\]\:data-\[hover\=true\]\:text-secondary-foreground[data-hover="true"][data-selected="true"] {
  color: hsl(var(--secondary-foreground));
}

.data-\[selected\=true\]\:data-\[hover\=true\]\:text-success-foreground[data-hover="true"][data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:data-\[hover\=true\]\:text-warning-foreground[data-hover="true"][data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:data-\[range-selection\=true\]\:data-\[outside-month\=true\]\:text-default-300[data-outside-month="true"][data-range-selection="true"][data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-300) / var(--nextui-default-300-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:data-\[range-selection\=true\]\:text-danger-500[data-range-selection="true"][data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-500) / var(--nextui-danger-500-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:data-\[range-selection\=true\]\:text-foreground[data-range-selection="true"][data-selected="true"] {
  color: hsl(var(--foreground));
}

.data-\[selected\=true\]\:data-\[range-selection\=true\]\:text-primary[data-range-selection="true"][data-selected="true"] {
  color: hsl(var(--primary));
}

.data-\[selected\=true\]\:data-\[range-selection\=true\]\:text-secondary[data-range-selection="true"][data-selected="true"] {
  color: hsl(var(--secondary));
}

.data-\[selected\=true\]\:data-\[range-selection\=true\]\:text-success-600[data-range-selection="true"][data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-600) / var(--nextui-success-600-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:data-\[range-selection\=true\]\:text-warning-500[data-range-selection="true"][data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-500) / var(--nextui-warning-500-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:text-background[data-range-selection="true"][data-selection-end="true"][data-selected="true"] {
  color: hsl(var(--background));
}

.data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:text-danger-foreground[data-range-selection="true"][data-selection-end="true"][data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:text-primary-foreground[data-range-selection="true"][data-selection-end="true"][data-selected="true"] {
  color: hsl(var(--primary-foreground));
}

.data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:text-secondary-foreground[data-range-selection="true"][data-selection-end="true"][data-selected="true"] {
  color: hsl(var(--secondary-foreground));
}

.data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:text-success-foreground[data-range-selection="true"][data-selection-end="true"][data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:text-warning-foreground[data-range-selection="true"][data-selection-end="true"][data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:text-background[data-range-selection="true"][data-selection-start="true"][data-selected="true"] {
  color: hsl(var(--background));
}

.data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:text-danger-foreground[data-range-selection="true"][data-selection-start="true"][data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:text-primary-foreground[data-range-selection="true"][data-selection-start="true"][data-selected="true"] {
  color: hsl(var(--primary-foreground));
}

.data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:text-secondary-foreground[data-range-selection="true"][data-selection-start="true"][data-selected="true"] {
  color: hsl(var(--secondary-foreground));
}

.data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:text-success-foreground[data-range-selection="true"][data-selection-start="true"][data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:text-warning-foreground[data-range-selection="true"][data-selection-start="true"][data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:text-\[\#24AE7C\][data-selected="true"] {
  --tw-text-opacity: 1;
  color: rgb(36 174 124 / var(--tw-text-opacity));
}

.data-\[selected\=true\]\:text-background[data-selected="true"] {
  color: hsl(var(--background));
}

.data-\[selected\=true\]\:text-danger[data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:text-danger-foreground[data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:text-default-foreground[data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:text-primary[data-selected="true"] {
  color: hsl(var(--primary));
}

.data-\[selected\=true\]\:text-primary-foreground[data-selected="true"] {
  color: hsl(var(--primary-foreground));
}

.data-\[selected\=true\]\:text-secondary[data-selected="true"] {
  color: hsl(var(--secondary));
}

.data-\[selected\=true\]\:text-secondary-foreground[data-selected="true"] {
  color: hsl(var(--secondary-foreground));
}

.data-\[selected\=true\]\:text-success-600[data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-600) / var(--nextui-success-600-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:text-success-foreground[data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:text-warning-600[data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-600) / var(--nextui-warning-600-opacity, var(--tw-text-opacity)));
}

.data-\[selected\=true\]\:text-warning-foreground[data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[state\=checked\]\:text-slate-50[data-state="checked"] {
  --tw-text-opacity: 1;
  color: rgb(248 250 252 / var(--tw-text-opacity));
}

.data-\[unavailable\=true\]\:text-default-300[data-unavailable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-300) / var(--nextui-default-300-opacity, var(--tw-text-opacity)));
}

.data-\[unavailable\=true\]\:line-through[data-unavailable="true"] {
  text-decoration-line: line-through;
}

.data-\[disabled\=true\]\:data-\[outside-month\=true\]\:opacity-0[data-outside-month="true"][data-disabled="true"] {
  opacity: 0;
}

.data-\[disabled\=true\]\:opacity-30[data-disabled="true"] {
  opacity: 0.3;
}

.data-\[disabled\=true\]\:opacity-50[data-disabled="true"] {
  opacity: 0.5;
}

.data-\[disabled\]\:opacity-50[data-disabled] {
  opacity: 0.5;
}

.data-\[hover-unselected\=true\]\:opacity-disabled[data-hover-unselected="true"] {
  opacity: var(--nextui-disabled-opacity);
}

.data-\[hover\=true\]\:opacity-hover[data-hover="true"] {
  opacity: var(--nextui-hover-opacity);
}

.data-\[in-range\=true\]\:opacity-100[data-in-range="true"] {
  opacity: 1;
}

.data-\[invisible\=true\]\:opacity-0[data-invisible="true"] {
  opacity: 0;
}

.data-\[loaded\=true\]\:opacity-100[data-loaded="true"] {
  opacity: 1;
}

.data-\[moving\]\:opacity-100[data-moving] {
  opacity: 1;
}

.data-\[pressed\=true\]\:opacity-50[data-pressed="true"] {
  opacity: 0.5;
}

.data-\[pressed\=true\]\:opacity-70[data-pressed="true"] {
  opacity: 0.7;
}

.data-\[visible\=true\]\:opacity-100[data-visible="true"] {
  opacity: 1;
}

.data-\[active\=true\]\:shadow-md[data-active="true"] {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.data-\[hover\=true\]\:shadow-lg[data-hover="true"] {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.data-\[selected\=true\]\:data-\[selection-end\=true\]\:shadow-md[data-selection-end="true"][data-selected="true"] {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.data-\[selected\=true\]\:data-\[selection-start\=true\]\:shadow-md[data-selection-start="true"][data-selected="true"] {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.data-\[selected\=true\]\:shadow-md[data-selected="true"] {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.data-\[selected\=true\]\:shadow-none[data-selected="true"] {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.data-\[active\=true\]\:shadow-danger\/40[data-active="true"] {
  --tw-shadow-color: hsl(var(--nextui-danger) / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[active\=true\]\:shadow-default\/50[data-active="true"] {
  --tw-shadow-color: hsl(var(--nextui-default) / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[active\=true\]\:shadow-primary\/40[data-active="true"] {
  --tw-shadow-color: hsl(var(--primary) / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[active\=true\]\:shadow-secondary\/40[data-active="true"] {
  --tw-shadow-color: hsl(var(--secondary) / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[active\=true\]\:shadow-success\/40[data-active="true"] {
  --tw-shadow-color: hsl(var(--nextui-success) / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[active\=true\]\:shadow-warning\/40[data-active="true"] {
  --tw-shadow-color: hsl(var(--nextui-warning) / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[hover\=true\]\:shadow-danger\/30[data-hover="true"] {
  --tw-shadow-color: hsl(var(--nextui-danger) / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[hover\=true\]\:shadow-default\/50[data-hover="true"] {
  --tw-shadow-color: hsl(var(--nextui-default) / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[hover\=true\]\:shadow-primary\/30[data-hover="true"] {
  --tw-shadow-color: hsl(var(--primary) / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[hover\=true\]\:shadow-secondary\/30[data-hover="true"] {
  --tw-shadow-color: hsl(var(--secondary) / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[hover\=true\]\:shadow-success\/30[data-hover="true"] {
  --tw-shadow-color: hsl(var(--nextui-success) / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[hover\=true\]\:shadow-warning\/30[data-hover="true"] {
  --tw-shadow-color: hsl(var(--nextui-warning) / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[selected\=true\]\:shadow-danger\/40[data-selected="true"] {
  --tw-shadow-color: hsl(var(--nextui-danger) / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[selected\=true\]\:shadow-foreground\/40[data-selected="true"] {
  --tw-shadow-color: hsl(var(--foreground) / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[selected\=true\]\:shadow-primary\/40[data-selected="true"] {
  --tw-shadow-color: hsl(var(--primary) / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[selected\=true\]\:shadow-secondary\/40[data-selected="true"] {
  --tw-shadow-color: hsl(var(--secondary) / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[selected\=true\]\:shadow-success\/40[data-selected="true"] {
  --tw-shadow-color: hsl(var(--nextui-success) / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[selected\=true\]\:shadow-warning\/40[data-selected="true"] {
  --tw-shadow-color: hsl(var(--nextui-warning) / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[focus-visible\=true\]\:outline-2[data-focus-visible="true"] {
  outline-width: 2px;
}

.data-\[focus-visible\=true\]\:outline-offset-2[data-focus-visible="true"] {
  outline-offset: 2px;
}

.data-\[focus-visible\=true\]\:outline-focus[data-focus-visible="true"] {
  outline-color: hsl(var(--nextui-focus) / var(--nextui-focus-opacity, 1));
}

.data-\[focus-visible\]\:outline-danger-foreground[data-focus-visible] {
  outline-color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, 1));
}

.data-\[focus-visible\]\:outline-default-foreground[data-focus-visible] {
  outline-color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, 1));
}

.data-\[focus-visible\]\:outline-primary-foreground[data-focus-visible] {
  outline-color: hsl(var(--primary-foreground));
}

.data-\[focus-visible\]\:outline-secondary-foreground[data-focus-visible] {
  outline-color: hsl(var(--secondary-foreground));
}

.data-\[focus-visible\]\:outline-success-foreground[data-focus-visible] {
  outline-color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, 1));
}

.data-\[focus-visible\]\:outline-warning-foreground[data-focus-visible] {
  outline-color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, 1));
}

.data-\[menu-open\=true\]\:backdrop-blur-xl[data-menu-open="true"] {
  --tw-backdrop-blur: blur(24px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.data-\[disabled\=true\]\:transition-none[data-disabled="true"] {
  transition-property: none;
}

.data-\[hover\=true\]\:transition-colors[data-hover="true"] {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}

.data-\[moving\=true\]\:transition-transform[data-moving="true"] {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}

.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  transition-duration: 300ms;
}

.data-\[state\=open\]\:duration-500[data-state="open"] {
  transition-duration: 500ms;
}

.data-\[motion\^\=from-\]\:animate-in[data-motion^="from-"] {
  animation-name: enter;
  animation-duration: 250ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[state\=open\]\:animate-in[data-state="open"] {
  animation-name: enter;
  animation-duration: 250ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[state\=visible\]\:animate-in[data-state="visible"] {
  animation-name: enter;
  animation-duration: 250ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[motion\^\=to-\]\:animate-out[data-motion^="to-"] {
  animation-name: exit;
  animation-duration: 250ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[state\=closed\]\:animate-out[data-state="closed"] {
  animation-name: exit;
  animation-duration: 250ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[state\=hidden\]\:animate-out[data-state="hidden"] {
  animation-name: exit;
  animation-duration: 250ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[motion\^\=from-\]\:fade-in[data-motion^="from-"] {
  --tw-enter-opacity: 0;
}

.data-\[motion\^\=to-\]\:fade-out[data-motion^="to-"] {
  --tw-exit-opacity: 0;
}

.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}

.data-\[state\=hidden\]\:fade-out[data-state="hidden"] {
  --tw-exit-opacity: 0;
}

.data-\[state\=open\]\:fade-in-0[data-state="open"] {
  --tw-enter-opacity: 0;
}

.data-\[state\=visible\]\:fade-in[data-state="visible"] {
  --tw-enter-opacity: 0;
}

.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
  --tw-exit-scale: .95;
}

.data-\[state\=open\]\:zoom-in-90[data-state="open"] {
  --tw-enter-scale: .9;
}

.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
  --tw-enter-scale: .95;
}

.data-\[motion\=from-end\]\:slide-in-from-right-52[data-motion="from-end"] {
  --tw-enter-translate-x: 13rem;
}

.data-\[motion\=from-start\]\:slide-in-from-left-52[data-motion="from-start"] {
  --tw-enter-translate-x: -13rem;
}

.data-\[motion\=to-end\]\:slide-out-to-right-52[data-motion="to-end"] {
  --tw-exit-translate-x: 13rem;
}

.data-\[motion\=to-start\]\:slide-out-to-left-52[data-motion="to-start"] {
  --tw-exit-translate-x: -13rem;
}

.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
  --tw-enter-translate-y: -0.5rem;
}

.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
  --tw-enter-translate-x: 0.5rem;
}

.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
  --tw-enter-translate-x: -0.5rem;
}

.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
  --tw-enter-translate-y: 0.5rem;
}

.data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"] {
  --tw-exit-translate-y: 100%;
}

.data-\[state\=closed\]\:slide-out-to-left[data-state="closed"] {
  --tw-exit-translate-x: -100%;
}

.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"] {
  --tw-exit-translate-x: -50%;
}

.data-\[state\=closed\]\:slide-out-to-right[data-state="closed"] {
  --tw-exit-translate-x: 100%;
}

.data-\[state\=closed\]\:slide-out-to-top[data-state="closed"] {
  --tw-exit-translate-y: -100%;
}

.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"] {
  --tw-exit-translate-y: -48%;
}

.data-\[state\=open\]\:slide-in-from-bottom[data-state="open"] {
  --tw-enter-translate-y: 100%;
}

.data-\[state\=open\]\:slide-in-from-left[data-state="open"] {
  --tw-enter-translate-x: -100%;
}

.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"] {
  --tw-enter-translate-x: -50%;
}

.data-\[state\=open\]\:slide-in-from-right[data-state="open"] {
  --tw-enter-translate-x: 100%;
}

.data-\[state\=open\]\:slide-in-from-top[data-state="open"] {
  --tw-enter-translate-y: -100%;
}

.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"] {
  --tw-enter-translate-y: -48%;
}

.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  animation-duration: 300ms;
}

.data-\[state\=open\]\:duration-500[data-state="open"] {
  animation-duration: 500ms;
}

.data-\[hide-scroll\=true\]\:scrollbar-hide[data-hide-scroll="true"] {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.data-\[hide-scroll\=true\]\:scrollbar-hide[data-hide-scroll="true"]::-webkit-scrollbar {
  display: none;
}

.data-\[top-bottom-scroll\=true\]\:\[mask-image\:linear-gradient\(\#000\2c \#000\2c transparent_0\2c \#000_var\(--scroll-shadow-size\)\2c \#000_calc\(100\%_-_var\(--scroll-shadow-size\)\)\2c transparent\)\][data-top-bottom-scroll="true"] {
  -webkit-mask-image: linear-gradient(#000,#000,transparent 0,#000 var(--scroll-shadow-size),#000 calc(100% - var(--scroll-shadow-size)),transparent);
          mask-image: linear-gradient(#000,#000,transparent 0,#000 var(--scroll-shadow-size),#000 calc(100% - var(--scroll-shadow-size)),transparent);
}

.data-\[top-scroll\=true\]\:\[mask-image\:linear-gradient\(0deg\2c \#000_calc\(100\%_-_var\(--scroll-shadow-size\)\)\2c transparent\)\][data-top-scroll="true"] {
  -webkit-mask-image: linear-gradient(0deg,#000 calc(100% - var(--scroll-shadow-size)),transparent);
          mask-image: linear-gradient(0deg,#000 calc(100% - var(--scroll-shadow-size)),transparent);
}

.data-\[bottom-scroll\=true\]\:\[mask-image\:linear-gradient\(180deg\2c \#000_calc\(100\%_-_var\(--scroll-shadow-size\)\)\2c transparent\)\][data-bottom-scroll="true"] {
  -webkit-mask-image: linear-gradient(180deg,#000 calc(100% - var(--scroll-shadow-size)),transparent);
          mask-image: linear-gradient(180deg,#000 calc(100% - var(--scroll-shadow-size)),transparent);
}

.data-\[left-scroll\=true\]\:\[mask-image\:linear-gradient\(270deg\2c \#000_calc\(100\%_-_var\(--scroll-shadow-size\)\)\2c transparent\)\][data-left-scroll="true"] {
  -webkit-mask-image: linear-gradient(270deg,#000 calc(100% - var(--scroll-shadow-size)),transparent);
          mask-image: linear-gradient(270deg,#000 calc(100% - var(--scroll-shadow-size)),transparent);
}

.data-\[right-scroll\=true\]\:\[mask-image\:linear-gradient\(90deg\2c \#000_calc\(100\%_-_var\(--scroll-shadow-size\)\)\2c transparent\)\][data-right-scroll="true"] {
  -webkit-mask-image: linear-gradient(90deg,#000 calc(100% - var(--scroll-shadow-size)),transparent);
          mask-image: linear-gradient(90deg,#000 calc(100% - var(--scroll-shadow-size)),transparent);
}

.data-\[left-right-scroll\=true\]\:\[mask-image\:linear-gradient\(to_right\2c \#000\2c \#000\2c transparent_0\2c \#000_var\(--scroll-shadow-size\)\2c \#000_calc\(100\%_-_var\(--scroll-shadow-size\)\)\2c transparent\)\][data-left-right-scroll="true"] {
  -webkit-mask-image: linear-gradient(to right,#000,#000,transparent 0,#000 var(--scroll-shadow-size),#000 calc(100% - var(--scroll-shadow-size)),transparent);
          mask-image: linear-gradient(to right,#000,#000,transparent 0,#000 var(--scroll-shadow-size),#000 calc(100% - var(--scroll-shadow-size)),transparent);
}

.data-\[placement\=bottom-end\]\:before\:-top-\[calc\(theme\(spacing\.5\)\/4_-_1\.5px\)\][data-placement="bottom-end"]::before {
  content: var(--tw-content);
  top: calc(calc(1.25rem / 4 - 1.5px) * -1);
}

.data-\[placement\=bottom-end\]\:before\:right-3[data-placement="bottom-end"]::before {
  content: var(--tw-content);
  right: 0.75rem;
}

.data-\[placement\=bottom-start\]\:before\:-top-\[calc\(theme\(spacing\.5\)\/4_-_1\.5px\)\][data-placement="bottom-start"]::before {
  content: var(--tw-content);
  top: calc(calc(1.25rem / 4 - 1.5px) * -1);
}

.data-\[placement\=bottom-start\]\:before\:left-3[data-placement="bottom-start"]::before {
  content: var(--tw-content);
  left: 0.75rem;
}

.data-\[placement\=bottom\]\:before\:-top-\[calc\(theme\(spacing\.5\)\/4_-_1\.5px\)\][data-placement="bottom"]::before {
  content: var(--tw-content);
  top: calc(calc(1.25rem / 4 - 1.5px) * -1);
}

.data-\[placement\=bottom\]\:before\:left-1\/2[data-placement="bottom"]::before {
  content: var(--tw-content);
  left: 50%;
}

.data-\[placement\=left-end\]\:before\:-right-\[calc\(theme\(spacing\.5\)\/4_-_3px\)\][data-placement="left-end"]::before {
  content: var(--tw-content);
  right: calc(calc(1.25rem / 4 - 3px) * -1);
}

.data-\[placement\=left-end\]\:before\:bottom-1\/4[data-placement="left-end"]::before {
  content: var(--tw-content);
  bottom: 25%;
}

.data-\[placement\=left-start\]\:before\:-right-\[calc\(theme\(spacing\.5\)\/4_-_3px\)\][data-placement="left-start"]::before {
  content: var(--tw-content);
  right: calc(calc(1.25rem / 4 - 3px) * -1);
}

.data-\[placement\=left-start\]\:before\:top-1\/4[data-placement="left-start"]::before {
  content: var(--tw-content);
  top: 25%;
}

.data-\[placement\=left\]\:before\:-right-\[calc\(theme\(spacing\.5\)\/4_-_2px\)\][data-placement="left"]::before {
  content: var(--tw-content);
  right: calc(calc(1.25rem / 4 - 2px) * -1);
}

.data-\[placement\=left\]\:before\:top-1\/2[data-placement="left"]::before {
  content: var(--tw-content);
  top: 50%;
}

.data-\[placement\=right-end\]\:before\:-left-\[calc\(theme\(spacing\.5\)\/4_-_3px\)\][data-placement="right-end"]::before {
  content: var(--tw-content);
  left: calc(calc(1.25rem / 4 - 3px) * -1);
}

.data-\[placement\=right-end\]\:before\:bottom-1\/4[data-placement="right-end"]::before {
  content: var(--tw-content);
  bottom: 25%;
}

.data-\[placement\=right-start\]\:before\:-left-\[calc\(theme\(spacing\.5\)\/4_-_3px\)\][data-placement="right-start"]::before {
  content: var(--tw-content);
  left: calc(calc(1.25rem / 4 - 3px) * -1);
}

.data-\[placement\=right-start\]\:before\:top-1\/4[data-placement="right-start"]::before {
  content: var(--tw-content);
  top: 25%;
}

.data-\[placement\=right\]\:before\:-left-\[calc\(theme\(spacing\.5\)\/4_-_2px\)\][data-placement="right"]::before {
  content: var(--tw-content);
  left: calc(calc(1.25rem / 4 - 2px) * -1);
}

.data-\[placement\=right\]\:before\:top-1\/2[data-placement="right"]::before {
  content: var(--tw-content);
  top: 50%;
}

.data-\[placement\=top-end\]\:before\:-bottom-\[calc\(theme\(spacing\.5\)\/4_-_1\.5px\)\][data-placement="top-end"]::before {
  content: var(--tw-content);
  bottom: calc(calc(1.25rem / 4 - 1.5px) * -1);
}

.data-\[placement\=top-end\]\:before\:right-3[data-placement="top-end"]::before {
  content: var(--tw-content);
  right: 0.75rem;
}

.data-\[placement\=top-start\]\:before\:-bottom-\[calc\(theme\(spacing\.5\)\/4_-_1\.5px\)\][data-placement="top-start"]::before {
  content: var(--tw-content);
  bottom: calc(calc(1.25rem / 4 - 1.5px) * -1);
}

.data-\[placement\=top-start\]\:before\:left-3[data-placement="top-start"]::before {
  content: var(--tw-content);
  left: 0.75rem;
}

.data-\[placement\=top\]\:before\:-bottom-\[calc\(theme\(spacing\.5\)\/4_-_1\.5px\)\][data-placement="top"]::before {
  content: var(--tw-content);
  bottom: calc(calc(1.25rem / 4 - 1.5px) * -1);
}

.data-\[placement\=top\]\:before\:left-1\/2[data-placement="top"]::before {
  content: var(--tw-content);
  left: 50%;
}

.data-\[loaded\=true\]\:before\:-z-10[data-loaded="true"]::before {
  content: var(--tw-content);
  z-index: -10;
}

.data-\[arrow\=true\]\:before\:block[data-arrow="true"]::before {
  content: var(--tw-content);
  display: block;
}

.data-\[outside-month\=true\]\:before\:hidden[data-outside-month="true"]::before {
  content: var(--tw-content);
  display: none;
}

.data-\[placement\=bottom\]\:before\:-translate-x-1\/2[data-placement="bottom"]::before {
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[placement\=left\]\:before\:-translate-y-1\/2[data-placement="left"]::before {
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[placement\=right\]\:before\:-translate-y-1\/2[data-placement="right"]::before {
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[placement\=top\]\:before\:-translate-x-1\/2[data-placement="top"]::before {
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[loaded\=true\]\:before\:animate-none[data-loaded="true"]::before {
  content: var(--tw-content);
  animation: none;
}

.data-\[range-end\=true\]\:before\:rounded-r-full[data-range-end="true"]::before {
  content: var(--tw-content);
  border-top-right-radius: 9999px;
  border-bottom-right-radius: 9999px;
}

.data-\[range-start\=true\]\:before\:rounded-l-full[data-range-start="true"]::before {
  content: var(--tw-content);
  border-top-left-radius: 9999px;
  border-bottom-left-radius: 9999px;
}

.data-\[selection-end\=true\]\:before\:rounded-r-full[data-selection-end="true"]::before {
  content: var(--tw-content);
  border-top-right-radius: 9999px;
  border-bottom-right-radius: 9999px;
}

.data-\[selection-start\=true\]\:before\:rounded-l-full[data-selection-start="true"]::before {
  content: var(--tw-content);
  border-top-left-radius: 9999px;
  border-bottom-left-radius: 9999px;
}

.data-\[selected\=true\]\:data-\[range-selection\=true\]\:before\:bg-danger-50[data-range-selection="true"][data-selected="true"]::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity)));
}

.data-\[selected\=true\]\:data-\[range-selection\=true\]\:before\:bg-foreground\/10[data-range-selection="true"][data-selected="true"]::before {
  content: var(--tw-content);
  background-color: hsl(var(--foreground) / 0.1);
}

.data-\[selected\=true\]\:data-\[range-selection\=true\]\:before\:bg-primary-50[data-range-selection="true"][data-selected="true"]::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-primary-50) / var(--nextui-primary-50-opacity, var(--tw-bg-opacity)));
}

.data-\[selected\=true\]\:data-\[range-selection\=true\]\:before\:bg-secondary-50[data-range-selection="true"][data-selected="true"]::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-secondary-50) / var(--nextui-secondary-50-opacity, var(--tw-bg-opacity)));
}

.data-\[selected\=true\]\:data-\[range-selection\=true\]\:before\:bg-success-100[data-range-selection="true"][data-selected="true"]::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success-100) / var(--nextui-success-100-opacity, var(--tw-bg-opacity)));
}

.data-\[selected\=true\]\:data-\[range-selection\=true\]\:before\:bg-warning-100[data-range-selection="true"][data-selected="true"]::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning-100) / var(--nextui-warning-100-opacity, var(--tw-bg-opacity)));
}

.data-\[loaded\=true\]\:before\:opacity-0[data-loaded="true"]::before {
  content: var(--tw-content);
  opacity: 0;
}

.data-\[selected\=true\]\:before\:opacity-100[data-selected="true"]::before {
  content: var(--tw-content);
  opacity: 1;
}

.data-\[focus\=true\]\:after\:w-full[data-focus="true"]::after {
  content: var(--tw-content);
  width: 100%;
}

.data-\[open\=true\]\:after\:w-full[data-open="true"]::after {
  content: var(--tw-content);
  width: 100%;
}

.data-\[dragging\=true\]\:after\:scale-100[data-dragging="true"]::after {
  content: var(--tw-content);
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[dragging\=true\]\:after\:scale-80[data-dragging="true"]::after {
  content: var(--tw-content);
  --tw-scale-x: 0.8;
  --tw-scale-y: 0.8;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[selected\=true\]\:after\:bg-danger[data-selected="true"]::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));
}

.data-\[selected\=true\]\:after\:bg-foreground[data-selected="true"]::after {
  content: var(--tw-content);
  background-color: hsl(var(--foreground));
}

.data-\[selected\=true\]\:after\:bg-primary[data-selected="true"]::after {
  content: var(--tw-content);
  background-color: hsl(var(--primary));
}

.data-\[selected\=true\]\:after\:bg-secondary[data-selected="true"]::after {
  content: var(--tw-content);
  background-color: hsl(var(--secondary));
}

.data-\[selected\=true\]\:after\:bg-success[data-selected="true"]::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));
}

.data-\[selected\=true\]\:after\:bg-warning[data-selected="true"]::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));
}

.data-\[loaded\=true\]\:after\:opacity-0[data-loaded="true"]::after {
  content: var(--tw-content);
  opacity: 0;
}

.data-\[selected\=true\]\:after\:opacity-100[data-selected="true"]::after {
  content: var(--tw-content);
  opacity: 1;
}

.data-\[selectable\=true\]\:focus\:border-danger:focus[data-selectable="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));
}

.data-\[selectable\=true\]\:focus\:border-default:focus[data-selectable="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-border-opacity)));
}

.data-\[selectable\=true\]\:focus\:border-primary:focus[data-selectable="true"] {
  border-color: hsl(var(--primary));
}

.data-\[selectable\=true\]\:focus\:border-secondary:focus[data-selectable="true"] {
  border-color: hsl(var(--secondary));
}

.data-\[selectable\=true\]\:focus\:border-success:focus[data-selectable="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));
}

.data-\[selectable\=true\]\:focus\:border-warning:focus[data-selectable="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));
}

.data-\[invalid\=true\]\:focus\:bg-danger-400\/50:focus[data-invalid="true"] {
  background-color: hsl(var(--nextui-danger-400) / 0.5);
}

.data-\[selectable\=true\]\:focus\:bg-danger:focus[data-selectable="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));
}

.data-\[selectable\=true\]\:focus\:bg-danger\/20:focus[data-selectable="true"] {
  background-color: hsl(var(--nextui-danger) / 0.2);
}

.data-\[selectable\=true\]\:focus\:bg-default:focus[data-selectable="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-bg-opacity)));
}

.data-\[selectable\=true\]\:focus\:bg-default-100:focus[data-selectable="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));
}

.data-\[selectable\=true\]\:focus\:bg-default\/40:focus[data-selectable="true"] {
  background-color: hsl(var(--nextui-default) / 0.4);
}

.data-\[selectable\=true\]\:focus\:bg-primary:focus[data-selectable="true"] {
  background-color: hsl(var(--primary));
}

.data-\[selectable\=true\]\:focus\:bg-primary\/20:focus[data-selectable="true"] {
  background-color: hsl(var(--primary) / 0.2);
}

.data-\[selectable\=true\]\:focus\:bg-secondary:focus[data-selectable="true"] {
  background-color: hsl(var(--secondary));
}

.data-\[selectable\=true\]\:focus\:bg-secondary\/20:focus[data-selectable="true"] {
  background-color: hsl(var(--secondary) / 0.2);
}

.data-\[selectable\=true\]\:focus\:bg-success:focus[data-selectable="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));
}

.data-\[selectable\=true\]\:focus\:bg-success\/20:focus[data-selectable="true"] {
  background-color: hsl(var(--nextui-success) / 0.2);
}

.data-\[selectable\=true\]\:focus\:bg-warning:focus[data-selectable="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));
}

.data-\[selectable\=true\]\:focus\:bg-warning\/20:focus[data-selectable="true"] {
  background-color: hsl(var(--nextui-warning) / 0.2);
}

.data-\[editable\=true\]\:focus\:text-danger:focus[data-editable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));
}

.data-\[editable\=true\]\:focus\:text-default-foreground:focus[data-editable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[editable\=true\]\:focus\:text-primary:focus[data-editable="true"] {
  color: hsl(var(--primary));
}

.data-\[editable\=true\]\:focus\:text-secondary:focus[data-editable="true"] {
  color: hsl(var(--secondary));
}

.data-\[editable\=true\]\:focus\:text-success:focus[data-editable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-text-opacity)));
}

.data-\[editable\=true\]\:focus\:text-success-600:focus[data-editable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-600) / var(--nextui-success-600-opacity, var(--tw-text-opacity)));
}

.data-\[editable\=true\]\:focus\:text-warning:focus[data-editable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-text-opacity)));
}

.data-\[editable\=true\]\:focus\:text-warning-600:focus[data-editable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-600) / var(--nextui-warning-600-opacity, var(--tw-text-opacity)));
}

.data-\[invalid\=true\]\:data-\[editable\=true\]\:focus\:text-danger:focus[data-editable="true"][data-invalid="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));
}

.data-\[selectable\=true\]\:focus\:text-danger:focus[data-selectable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));
}

.data-\[selectable\=true\]\:focus\:text-danger-foreground:focus[data-selectable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[selectable\=true\]\:focus\:text-default-500:focus[data-selectable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-500) / var(--nextui-default-500-opacity, var(--tw-text-opacity)));
}

.data-\[selectable\=true\]\:focus\:text-default-foreground:focus[data-selectable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[selectable\=true\]\:focus\:text-primary:focus[data-selectable="true"] {
  color: hsl(var(--primary));
}

.data-\[selectable\=true\]\:focus\:text-primary-foreground:focus[data-selectable="true"] {
  color: hsl(var(--primary-foreground));
}

.data-\[selectable\=true\]\:focus\:text-secondary:focus[data-selectable="true"] {
  color: hsl(var(--secondary));
}

.data-\[selectable\=true\]\:focus\:text-secondary-foreground:focus[data-selectable="true"] {
  color: hsl(var(--secondary-foreground));
}

.data-\[selectable\=true\]\:focus\:text-success:focus[data-selectable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-text-opacity)));
}

.data-\[selectable\=true\]\:focus\:text-success-foreground:focus[data-selectable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[selectable\=true\]\:focus\:text-warning:focus[data-selectable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-text-opacity)));
}

.data-\[selectable\=true\]\:focus\:text-warning-foreground:focus[data-selectable="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));
}

.data-\[selectable\=true\]\:focus\:shadow-danger\/30:focus[data-selectable="true"] {
  --tw-shadow-color: hsl(var(--nextui-danger) / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[selectable\=true\]\:focus\:shadow-default\/50:focus[data-selectable="true"] {
  --tw-shadow-color: hsl(var(--nextui-default) / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[selectable\=true\]\:focus\:shadow-primary\/30:focus[data-selectable="true"] {
  --tw-shadow-color: hsl(var(--primary) / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[selectable\=true\]\:focus\:shadow-secondary\/30:focus[data-selectable="true"] {
  --tw-shadow-color: hsl(var(--secondary) / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[selectable\=true\]\:focus\:shadow-success\/30:focus[data-selectable="true"] {
  --tw-shadow-color: hsl(var(--nextui-success) / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.data-\[selectable\=true\]\:focus\:shadow-warning\/30:focus[data-selectable="true"] {
  --tw-shadow-color: hsl(var(--nextui-warning) / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.group[data-filled-within="true"] .group-data-\[filled-within\=true\]\:pointer-events-auto {
  pointer-events: auto;
}

.group[data-filled-within="true"] .group-data-\[filled-within\=true\]\:start-0 {
  inset-inline-start: 0px;
}

.group[data-filled="true"] .group-data-\[filled\=true\]\:start-0 {
  inset-inline-start: 0px;
}

.group[data-focus-visible="true"] .group-data-\[focus-visible\=true\]\:z-10 {
  z-index: 10;
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:ml-4 {
  margin-left: 1rem;
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:ml-5 {
  margin-left: 1.25rem;
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:ml-6 {
  margin-left: 1.5rem;
}

.group[data-selected][data-pressed] .group-data-\[selected\]\:group-data-\[pressed\]\:ml-3 {
  margin-left: 0.75rem;
}

.group[data-selected][data-pressed] .group-data-\[selected\]\:group-data-\[pressed\]\:ml-4 {
  margin-left: 1rem;
}

.group[data-selected][data-pressed] .group-data-\[selected\]\:group-data-\[pressed\]\:ml-5 {
  margin-left: 1.25rem;
}

.group[data-focus-visible="true"] .group-data-\[focus-visible\=true\]\:block {
  display: block;
}

.group[data-has-helper="true"] .group-data-\[has-helper\=true\]\:flex {
  display: flex;
}

.group[data-focus-visible="true"] .group-data-\[focus-visible\=true\]\:hidden {
  display: none;
}

.group[data-pressed="true"] .group-data-\[pressed\=true\]\:w-5 {
  width: 1.25rem;
}

.group[data-pressed="true"] .group-data-\[pressed\=true\]\:w-6 {
  width: 1.5rem;
}

.group[data-pressed="true"] .group-data-\[pressed\=true\]\:w-7 {
  width: 1.75rem;
}

.group[data-filled-within="true"] .group-data-\[filled-within\=true\]\:-translate-y-\[calc\(100\%_\+_theme\(fontSize\.small\)\/2_\+_20px\)\] {
  --tw-translate-y: calc(calc(100% + var(--nextui-font-size-small) / 2 + 20px) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled-within="true"] .group-data-\[filled-within\=true\]\:-translate-y-\[calc\(100\%_\+_theme\(fontSize\.small\)\/2_\+_24px\)\] {
  --tw-translate-y: calc(calc(100% + var(--nextui-font-size-small) / 2 + 24px) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled-within="true"] .group-data-\[filled-within\=true\]\:-translate-y-\[calc\(100\%_\+_theme\(fontSize\.tiny\)\/2_\+_16px\)\] {
  --tw-translate-y: calc(calc(100% + var(--nextui-font-size-tiny) / 2 + 16px) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled-within="true"] .group-data-\[filled-within\=true\]\:-translate-y-\[calc\(50\%_\+_theme\(fontSize\.small\)\/2_-_3\.5px\)\] {
  --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 3.5px) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled-within="true"] .group-data-\[filled-within\=true\]\:-translate-y-\[calc\(50\%_\+_theme\(fontSize\.small\)\/2_-_4px\)\] {
  --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 4px) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled-within="true"] .group-data-\[filled-within\=true\]\:-translate-y-\[calc\(50\%_\+_theme\(fontSize\.small\)\/2_-_6px\)\] {
  --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 6px) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled-within="true"] .group-data-\[filled-within\=true\]\:-translate-y-\[calc\(50\%_\+_theme\(fontSize\.small\)\/2_-_6px_-_theme\(borderWidth\.medium\)\)\] {
  --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 6px - var(--nextui-border-width-medium)) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled-within="true"] .group-data-\[filled-within\=true\]\:-translate-y-\[calc\(50\%_\+_theme\(fontSize\.small\)\/2_-_8px\)\] {
  --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 8px) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled-within="true"] .group-data-\[filled-within\=true\]\:-translate-y-\[calc\(50\%_\+_theme\(fontSize\.small\)\/2_-_8px_-_theme\(borderWidth\.medium\)\)\] {
  --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 8px - var(--nextui-border-width-medium)) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled-within="true"] .group-data-\[filled-within\=true\]\:-translate-y-\[calc\(50\%_\+_theme\(fontSize\.tiny\)\/2_-_5px\)\] {
  --tw-translate-y: calc(calc(50% + var(--nextui-font-size-tiny) / 2 - 5px) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled-within="true"] .group-data-\[filled-within\=true\]\:-translate-y-\[calc\(50\%_\+_theme\(fontSize\.tiny\)\/2_-_8px\)\] {
  --tw-translate-y: calc(calc(50% + var(--nextui-font-size-tiny) / 2 - 8px) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled-within="true"] .group-data-\[filled-within\=true\]\:-translate-y-\[calc\(50\%_\+_theme\(fontSize\.tiny\)\/2_-_8px_-_theme\(borderWidth\.medium\)\)\] {
  --tw-translate-y: calc(calc(50% + var(--nextui-font-size-tiny) / 2 - 8px - var(--nextui-border-width-medium)) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled="true"] .group-data-\[filled\=true\]\:-translate-y-\[calc\(100\%_\+_theme\(fontSize\.small\)\/2_\+_20px\)\] {
  --tw-translate-y: calc(calc(100% + var(--nextui-font-size-small) / 2 + 20px) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled="true"] .group-data-\[filled\=true\]\:-translate-y-\[calc\(100\%_\+_theme\(fontSize\.small\)\/2_\+_24px\)\] {
  --tw-translate-y: calc(calc(100% + var(--nextui-font-size-small) / 2 + 24px) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled="true"] .group-data-\[filled\=true\]\:-translate-y-\[calc\(100\%_\+_theme\(fontSize\.tiny\)\/2_\+_16px\)\] {
  --tw-translate-y: calc(calc(100% + var(--nextui-font-size-tiny) / 2 + 16px) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled="true"] .group-data-\[filled\=true\]\:-translate-y-\[calc\(50\%_\+_theme\(fontSize\.small\)\/2_-_3\.5px\)\] {
  --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 3.5px) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled="true"] .group-data-\[filled\=true\]\:-translate-y-\[calc\(50\%_\+_theme\(fontSize\.small\)\/2_-_4px\)\] {
  --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 4px) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled="true"] .group-data-\[filled\=true\]\:-translate-y-\[calc\(50\%_\+_theme\(fontSize\.small\)\/2_-_6px\)\] {
  --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 6px) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled="true"] .group-data-\[filled\=true\]\:-translate-y-\[calc\(50\%_\+_theme\(fontSize\.small\)\/2_-_6px_-_theme\(borderWidth\.medium\)\)\] {
  --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 6px - var(--nextui-border-width-medium)) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled="true"] .group-data-\[filled\=true\]\:-translate-y-\[calc\(50\%_\+_theme\(fontSize\.small\)\/2_-_8px\)\] {
  --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 8px) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled="true"] .group-data-\[filled\=true\]\:-translate-y-\[calc\(50\%_\+_theme\(fontSize\.small\)\/2_-_8px_-_theme\(borderWidth\.medium\)\)\] {
  --tw-translate-y: calc(calc(50% + var(--nextui-font-size-small) / 2 - 8px - var(--nextui-border-width-medium)) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled="true"] .group-data-\[filled\=true\]\:-translate-y-\[calc\(50\%_\+_theme\(fontSize\.tiny\)\/2_-_5px\)\] {
  --tw-translate-y: calc(calc(50% + var(--nextui-font-size-tiny) / 2 - 5px) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled="true"] .group-data-\[filled\=true\]\:-translate-y-\[calc\(50\%_\+_theme\(fontSize\.tiny\)\/2_-_8px\)\] {
  --tw-translate-y: calc(calc(50% + var(--nextui-font-size-tiny) / 2 - 8px) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled="true"] .group-data-\[filled\=true\]\:-translate-y-\[calc\(50\%_\+_theme\(fontSize\.tiny\)\/2_-_8px_-_theme\(borderWidth\.medium\)\)\] {
  --tw-translate-y: calc(calc(50% + var(--nextui-font-size-tiny) / 2 - 8px - var(--nextui-border-width-medium)) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:translate-x-3 {
  --tw-translate-x: 0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-state="open"] .group-data-\[state\=open\]\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-copied="true"] .group-data-\[copied\=true\]\:scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-copied="true"] .group-data-\[copied\=true\]\:scale-50 {
  --tw-scale-x: .5;
  --tw-scale-y: .5;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled-within="true"] .group-data-\[filled-within\=true\]\:scale-85 {
  --tw-scale-x: 0.85;
  --tw-scale-y: 0.85;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-filled="true"] .group-data-\[filled\=true\]\:scale-85 {
  --tw-scale-x: 0.85;
  --tw-scale-y: 0.85;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-pressed="true"] .group-data-\[pressed\=true\]\:scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-disabled="true"] .group-data-\[disabled\=true\]\:cursor-not-allowed {
  cursor: not-allowed;
}

.group[data-has-multiple-months="true"] .group-data-\[has-multiple-months\=true\]\:flex-row {
  flex-direction: row;
}

.group[data-has-label="true"] .group-data-\[has-label\=true\]\:items-start {
  align-items: flex-start;
}

.group[data-has-label="true"] .group-data-\[has-label\=true\]\:items-end {
  align-items: flex-end;
}

.group[data-focus="true"] .group-data-\[focus\=true\]\:\!border-danger {
  --tw-border-opacity: 1 !important;
  border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity))) !important;
}

.group[data-focus="true"] .group-data-\[focus\=true\]\:border-danger {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));
}

.group[data-focus="true"] .group-data-\[focus\=true\]\:border-default-foreground {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-border-opacity)));
}

.group[data-focus="true"] .group-data-\[focus\=true\]\:border-primary {
  border-color: hsl(var(--primary));
}

.group[data-focus="true"] .group-data-\[focus\=true\]\:border-secondary {
  border-color: hsl(var(--secondary));
}

.group[data-focus="true"] .group-data-\[focus\=true\]\:border-success {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));
}

.group[data-focus="true"] .group-data-\[focus\=true\]\:border-warning {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));
}

.group[data-invalid="true"] .group-data-\[invalid\=true\]\:border-danger {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:border-danger {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:border-default-500 {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-default-500) / var(--nextui-default-500-opacity, var(--tw-border-opacity)));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:border-primary {
  border-color: hsl(var(--primary));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:border-secondary {
  border-color: hsl(var(--secondary));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:border-success {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-border-opacity)));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:border-warning {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-border-opacity)));
}

.group[data-focus="true"] .group-data-\[focus\=true\]\:\!bg-danger-50 {
  --tw-bg-opacity: 1 !important;
  background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity))) !important;
}

.group[data-focus="true"] .group-data-\[focus\=true\]\:bg-danger-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity)));
}

.group[data-focus="true"] .group-data-\[focus\=true\]\:bg-default-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));
}

.group[data-focus="true"] .group-data-\[focus\=true\]\:bg-primary-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-primary-50) / var(--nextui-primary-50-opacity, var(--tw-bg-opacity)));
}

.group[data-focus="true"] .group-data-\[focus\=true\]\:bg-secondary-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-secondary-50) / var(--nextui-secondary-50-opacity, var(--tw-bg-opacity)));
}

.group[data-focus="true"] .group-data-\[focus\=true\]\:bg-success-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success-50) / var(--nextui-success-50-opacity, var(--tw-bg-opacity)));
}

.group[data-focus="true"] .group-data-\[focus\=true\]\:bg-warning-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning-50) / var(--nextui-warning-50-opacity, var(--tw-bg-opacity)));
}

.group[data-hover-unselected="true"] .group-data-\[hover-unselected\=true\]\:bg-default-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));
}

.group[data-invalid="true"] .group-data-\[invalid\=true\]\:bg-danger-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity)));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:bg-danger {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:bg-default-400 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-400) / var(--nextui-default-400-opacity, var(--tw-bg-opacity)));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:bg-primary {
  background-color: hsl(var(--primary));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:bg-secondary {
  background-color: hsl(var(--secondary));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:bg-success {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:bg-warning {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));
}

.group[data-has-helper="true"] .group-data-\[has-helper\=true\]\:pt-2 {
  padding-top: 0.5rem;
}

.group[data-has-helper="true"] .group-data-\[has-helper\=true\]\:pt-3 {
  padding-top: 0.75rem;
}

.group[data-has-helper="true"] .group-data-\[has-helper\=true\]\:pt-4 {
  padding-top: 1rem;
}

.group[data-has-label="true"] .group-data-\[has-label\=true\]\:pt-4 {
  padding-top: 1rem;
}

.group[data-has-label="true"] .group-data-\[has-label\=true\]\:pt-5 {
  padding-top: 1.25rem;
}

.group[data-disabled="true"] .group-data-\[disabled\=true\]\:text-foreground-300 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-foreground-300) / var(--nextui-foreground-300-opacity, var(--tw-text-opacity)));
}

.group[data-filled-within="true"] .group-data-\[filled-within\=true\]\:text-default-600 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-600) / var(--nextui-default-600-opacity, var(--tw-text-opacity)));
}

.group[data-filled-within="true"] .group-data-\[filled-within\=true\]\:text-foreground {
  color: hsl(var(--foreground));
}

.group[data-filled="true"] .group-data-\[filled\=true\]\:text-default-600 {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-600) / var(--nextui-default-600-opacity, var(--tw-text-opacity)));
}

.group[data-filled="true"] .group-data-\[filled\=true\]\:text-foreground {
  color: hsl(var(--foreground));
}

.group[data-has-value="true"] .group-data-\[has-value\=true\]\:text-default-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-text-opacity)));
}

.group[data-has-value="true"] .group-data-\[has-value\=true\]\:text-foreground {
  color: hsl(var(--foreground));
}

.group[data-invalid="true"] .group-data-\[invalid\=true\]\:text-danger {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:text-\[\#24AE7C\] {
  --tw-text-opacity: 1;
  color: rgb(36 174 124 / var(--tw-text-opacity));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:text-danger {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:text-danger-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:text-default-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-default-foreground) / var(--nextui-default-foreground-opacity, var(--tw-text-opacity)));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:text-foreground {
  color: hsl(var(--foreground));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:text-primary {
  color: hsl(var(--primary));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:text-primary-foreground {
  color: hsl(var(--primary-foreground));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:text-secondary {
  color: hsl(var(--secondary));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:text-secondary-foreground {
  color: hsl(var(--secondary-foreground));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:text-success {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-text-opacity)));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:text-success-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:text-warning {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-text-opacity)));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:text-warning-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));
}

.group[data-copied="true"] .group-data-\[copied\=true\]\:opacity-0 {
  opacity: 0;
}

.group[data-copied="true"] .group-data-\[copied\=true\]\:opacity-100 {
  opacity: 1;
}

.group[data-hover="true"] .group-data-\[hover\=true\]\:opacity-100 {
  opacity: 1;
}

.group[data-loaded="true"] .group-data-\[loaded\=true\]\:opacity-100 {
  opacity: 1;
}

.group[data-pressed="true"] .group-data-\[pressed\=true\]\:opacity-70 {
  opacity: 0.7;
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:opacity-0 {
  opacity: 0;
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:opacity-100 {
  opacity: 1;
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:opacity-60 {
  opacity: 0.6;
}

.group[data-focus-visible="true"] .group-data-\[focus-visible\=true\]\:ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.group[data-focus-visible="true"] .group-data-\[focus-visible\=true\]\:ring-focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: hsl(var(--nextui-focus) / var(--nextui-focus-opacity, var(--tw-ring-opacity)));
}

.group[data-focus-visible="true"] .group-data-\[focus-visible\=true\]\:ring-offset-2 {
  --tw-ring-offset-width: 2px;
}

.group[data-focus-visible="true"] .group-data-\[focus-visible\=true\]\:ring-offset-background {
  --tw-ring-offset-color: hsl(var(--background));
}

.group[data-odd="true"] .group-data-\[odd\=true\]\:before\:-z-10::before {
  content: var(--tw-content);
  z-index: -10;
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:before\:w-full::before {
  content: var(--tw-content);
  width: 100%;
}

.group[data-open="true"] .group-data-\[open\=true\]\:before\:translate-y-px::before {
  content: var(--tw-content);
  --tw-translate-y: 1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-open="true"] .group-data-\[open\=true\]\:before\:rotate-45::before {
  content: var(--tw-content);
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-middle="true"] .group-data-\[middle\=true\]\:before\:rounded-none::before {
  content: var(--tw-content);
  border-radius: 0px;
}

.group[data-hover="true"] .group-data-\[hover\=true\]\:before\:bg-default-100::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));
}

.group[data-odd="true"] .group-data-\[odd\=true\]\:before\:bg-default-100::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));
}

.group[data-odd="true"] .group-data-\[odd\=true\]\:before\:opacity-100::before {
  content: var(--tw-content);
  opacity: 1;
}

.group[data-required="true"] .group-data-\[required\=true\]\:after\:ml-0\.5::after {
  content: var(--tw-content);
  margin-left: 0.125rem;
}

.group[data-focus="true"] .group-data-\[focus\=true\]\:after\:w-full::after {
  content: var(--tw-content);
  width: 100%;
}

.group[data-open="true"] .group-data-\[open\=true\]\:after\:translate-y-0::after {
  content: var(--tw-content);
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-open="true"] .group-data-\[open\=true\]\:after\:-rotate-45::after {
  content: var(--tw-content);
  --tw-rotate: -45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:after\:scale-100::after {
  content: var(--tw-content);
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-invalid="true"] .group-data-\[invalid\=true\]\:after\:bg-danger::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));
}

.group[data-required="true"] .group-data-\[required\=true\]\:after\:text-danger::after {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-text-opacity)));
}

.group[data-selected="true"] .group-data-\[selected\=true\]\:after\:opacity-100::after {
  content: var(--tw-content);
  opacity: 1;
}

.group[data-required="true"] .group-data-\[required\=true\]\:after\:content-\[\'\*\'\]::after {
  --tw-content: '*';
  content: var(--tw-content);
}

.group[data-invalid="true"] .group-data-\[invalid\=true\]\:hover\:border-danger:hover {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));
}

.group[data-invalid="true"] .group-data-\[invalid\=true\]\:hover\:bg-danger-100:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger-100) / var(--nextui-danger-100-opacity, var(--tw-bg-opacity)));
}

.group[data-invalid="true"] .group-data-\[invalid\=true\]\:focus-within\:hover\:border-danger:hover:focus-within {
  --tw-border-opacity: 1;
  border-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-border-opacity)));
}

.group[data-invalid="true"] .group-data-\[invalid\=true\]\:focus-within\:hover\:bg-danger-50:hover:focus-within {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity)));
}

.group[aria-selected="false"][data-hover="true"] .group-aria-\[selected\=false\]\:group-data-\[hover\=true\]\:before\:bg-default-100::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));
}

.group[aria-selected="false"][data-hover="true"] .group-aria-\[selected\=false\]\:group-data-\[hover\=true\]\:before\:opacity-70::before {
  content: var(--tw-content);
  opacity: 0.7;
}

.group[data-odd="true"] .group-data-\[odd\=true\]\:data-\[selected\=true\]\:before\:bg-danger\/20[data-selected="true"]::before {
  content: var(--tw-content);
  background-color: hsl(var(--nextui-danger) / 0.2);
}

.group[data-odd="true"] .group-data-\[odd\=true\]\:data-\[selected\=true\]\:before\:bg-default\/60[data-selected="true"]::before {
  content: var(--tw-content);
  background-color: hsl(var(--nextui-default) / 0.6);
}

.group[data-odd="true"] .group-data-\[odd\=true\]\:data-\[selected\=true\]\:before\:bg-primary\/20[data-selected="true"]::before {
  content: var(--tw-content);
  background-color: hsl(var(--primary) / 0.2);
}

.group[data-odd="true"] .group-data-\[odd\=true\]\:data-\[selected\=true\]\:before\:bg-secondary\/20[data-selected="true"]::before {
  content: var(--tw-content);
  background-color: hsl(var(--secondary) / 0.2);
}

.group[data-odd="true"] .group-data-\[odd\=true\]\:data-\[selected\=true\]\:before\:bg-success\/20[data-selected="true"]::before {
  content: var(--tw-content);
  background-color: hsl(var(--nextui-success) / 0.2);
}

.group[data-odd="true"] .group-data-\[odd\=true\]\:data-\[selected\=true\]\:before\:bg-warning\/20[data-selected="true"]::before {
  content: var(--tw-content);
  background-color: hsl(var(--nextui-warning) / 0.2);
}

.peer[data-filled="true"] ~ .peer-data-\[filled\=true\]\:block {
  display: block;
}

.peer[data-filled="true"] ~ .peer-data-\[filled\=true\]\:opacity-70 {
  opacity: 0.7;
}

@media (prefers-reduced-motion: reduce) {

  .motion-reduce\:transition-none {
    transition-property: none;
  }

  .motion-reduce\:after\:transition-none::after {
    content: var(--tw-content);
    transition-property: none;
  }
}

.dark\:border-\[0\.5px\]:is(.dark *) {
  border-width: 0.5px;
}

.dark\:border-t-1:is(.dark *) {
  border-top-width: 1px;
}

.dark\:border-slate-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(30 41 59 / var(--tw-border-opacity));
}

.dark\:bg-background:is(.dark *) {
  background-color: hsl(var(--background));
}

.dark\:bg-background\/20:is(.dark *) {
  background-color: hsl(var(--background) / 0.2);
}

.dark\:bg-black:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}

.dark\:bg-content2:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-content2) / var(--nextui-content2-opacity, var(--tw-bg-opacity)));
}

.dark\:bg-default:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default) / var(--nextui-default-opacity, var(--tw-bg-opacity)));
}

.dark\:bg-neutral-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(38 38 38 / var(--tw-bg-opacity));
}

.dark\:bg-neutral-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(23 23 23 / var(--tw-bg-opacity));
}

.dark\:bg-red-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(127 29 29 / var(--tw-bg-opacity));
}

.dark\:bg-slate-50:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity));
}

.dark\:bg-slate-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
}

.dark\:bg-slate-800\/50:is(.dark *) {
  background-color: rgb(30 41 59 / 0.5);
}

.dark\:bg-slate-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity));
}

.dark\:bg-slate-950:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(2 6 23 / var(--tw-bg-opacity));
}

.dark\:bg-transparent:is(.dark *) {
  background-color: transparent;
}

.dark\:bg-white:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.dark\:to-black:is(.dark *) {
  --tw-gradient-to: #000 var(--tw-gradient-to-position);
}

.dark\:text-danger-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-500) / var(--nextui-danger-500-opacity, var(--tw-text-opacity)));
}

.dark\:text-gray-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.dark\:text-neutral-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(245 245 245 / var(--tw-text-opacity));
}

.dark\:text-neutral-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(229 229 229 / var(--tw-text-opacity));
}

.dark\:text-neutral-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(163 163 163 / var(--tw-text-opacity));
}

.dark\:text-neutral-900:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(23 23 23 / var(--tw-text-opacity));
}

.dark\:text-red-900:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity));
}

.dark\:text-slate-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.dark\:text-slate-50:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(248 250 252 / var(--tw-text-opacity));
}

.dark\:text-slate-900:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark\:text-success:is(.dark *) {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-text-opacity)));
}

.dark\:text-teal-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(94 234 212 / var(--tw-text-opacity));
}

.dark\:text-warning:is(.dark *) {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-text-opacity)));
}

.dark\:text-white:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark\:text-white\/60:is(.dark *) {
  color: rgb(255 255 255 / 0.6);
}

.dark\:shadow-none:is(.dark *) {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.dark\:ring-slate-300:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(203 213 225 / var(--tw-ring-opacity));
}

.dark\:ring-offset-slate-950:is(.dark *) {
  --tw-ring-offset-color: #020617;
}

.dark\:bg-dot-white\/\[0\.2\]:is(.dark *) {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='16' height='16' fill='none'%3e%3ccircle fill='rgb(255 255 255 / 0.2)' id='pattern-circle' cx='10' cy='10' r='1.6257413380501518'%3e%3c/circle%3e%3c/svg%3e");
}

.dark\:placeholder\:text-danger-500:is(.dark *)::-moz-placeholder {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-500) / var(--nextui-danger-500-opacity, var(--tw-text-opacity)));
}

.dark\:placeholder\:text-danger-500:is(.dark *)::placeholder {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-500) / var(--nextui-danger-500-opacity, var(--tw-text-opacity)));
}

.dark\:placeholder\:text-slate-400:is(.dark *)::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.dark\:placeholder\:text-slate-400:is(.dark *)::placeholder {
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.dark\:placeholder\:text-success:is(.dark *)::-moz-placeholder {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-text-opacity)));
}

.dark\:placeholder\:text-success:is(.dark *)::placeholder {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-text-opacity)));
}

.dark\:placeholder\:text-warning:is(.dark *)::-moz-placeholder {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-text-opacity)));
}

.dark\:placeholder\:text-warning:is(.dark *)::placeholder {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-text-opacity)));
}

.dark\:before\:via-default-700\/10:is(.dark *)::before {
  content: var(--tw-content);
  --tw-gradient-to: hsl(var(--nextui-default-700) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--nextui-default-700) / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:after\:bg-content2:is(.dark *)::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-content2) / var(--nextui-content2-opacity, var(--tw-bg-opacity)));
}

.dark\:hover\:bg-accent:hover:is(.dark *) {
  background-color: hsl(var(--accent));
}

.dark\:hover\:bg-red-900\/80:hover:is(.dark *) {
  background-color: rgb(127 29 29 / 0.8);
}

.dark\:hover\:bg-red-900\/90:hover:is(.dark *) {
  background-color: rgb(127 29 29 / 0.9);
}

.dark\:hover\:bg-slate-50\/80:hover:is(.dark *) {
  background-color: rgb(248 250 252 / 0.8);
}

.dark\:hover\:bg-slate-50\/90:hover:is(.dark *) {
  background-color: rgb(248 250 252 / 0.9);
}

.dark\:hover\:bg-slate-800:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-slate-800\/50:hover:is(.dark *) {
  background-color: rgb(30 41 59 / 0.5);
}

.dark\:hover\:bg-slate-800\/80:hover:is(.dark *) {
  background-color: rgb(30 41 59 / 0.8);
}

.dark\:hover\:text-slate-50:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(248 250 252 / var(--tw-text-opacity));
}

.dark\:hover\:text-white\/75:hover:is(.dark *) {
  color: rgb(255 255 255 / 0.75);
}

.dark\:focus\:bg-accent:focus:is(.dark *) {
  background-color: hsl(var(--accent));
}

.dark\:focus\:bg-danger-400\/20:focus:is(.dark *) {
  background-color: hsl(var(--nextui-danger-400) / 0.2);
}

.dark\:focus\:bg-slate-800:focus:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
}

.dark\:focus\:bg-success-400\/20:focus:is(.dark *) {
  background-color: hsl(var(--nextui-success-400) / 0.2);
}

.dark\:focus\:bg-warning-400\/20:focus:is(.dark *) {
  background-color: hsl(var(--nextui-warning-400) / 0.2);
}

.dark\:focus\:text-slate-50:focus:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(248 250 252 / var(--tw-text-opacity));
}

.dark\:focus\:ring-slate-300:focus:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(203 213 225 / var(--tw-ring-opacity));
}

.dark\:focus-visible\:ring-slate-300:focus-visible:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(203 213 225 / var(--tw-ring-opacity));
}

.group.toaster .dark\:group-\[\.toaster\]\:border-slate-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(30 41 59 / var(--tw-border-opacity));
}

.group.toast .dark\:group-\[\.toast\]\:bg-slate-50:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity));
}

.group.toast .dark\:group-\[\.toast\]\:bg-slate-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
}

.group.toaster .dark\:group-\[\.toaster\]\:bg-slate-950:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(2 6 23 / var(--tw-bg-opacity));
}

.group.toast .dark\:group-\[\.toast\]\:text-slate-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.group.toast .dark\:group-\[\.toast\]\:text-slate-900:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.group.toaster .dark\:group-\[\.toaster\]\:text-slate-50:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(248 250 252 / var(--tw-text-opacity));
}

.dark\:aria-selected\:bg-slate-800[aria-selected="true"]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
}

.dark\:aria-selected\:text-slate-50[aria-selected="true"]:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(248 250 252 / var(--tw-text-opacity));
}

.dark\:data-\[active\]\:bg-accent\/50[data-active]:is(.dark *) {
  background-color: hsl(var(--accent) / 0.5);
}

.dark\:data-\[hover\=true\]\:bg-content2[data-hover="true"]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-content2) / var(--nextui-content2-opacity, var(--tw-bg-opacity)));
}

.dark\:data-\[hover\=true\]\:bg-danger-50[data-hover="true"]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger-50) / var(--nextui-danger-50-opacity, var(--tw-bg-opacity)));
}

.dark\:data-\[hover\=true\]\:bg-success-50[data-hover="true"]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success-50) / var(--nextui-success-50-opacity, var(--tw-bg-opacity)));
}

.dark\:data-\[hover\=true\]\:bg-warning-50[data-hover="true"]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning-50) / var(--nextui-warning-50-opacity, var(--tw-bg-opacity)));
}

.dark\:data-\[selected\=true\]\:data-\[hover\=true\]\:bg-danger[data-hover="true"][data-selected="true"]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-danger) / var(--nextui-danger-opacity, var(--tw-bg-opacity)));
}

.dark\:data-\[selected\=true\]\:data-\[hover\=true\]\:bg-success[data-hover="true"][data-selected="true"]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-bg-opacity)));
}

.dark\:data-\[selected\=true\]\:data-\[hover\=true\]\:bg-warning[data-hover="true"][data-selected="true"]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-bg-opacity)));
}

.dark\:data-\[state\=checked\]\:bg-slate-50[data-state="checked"]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity));
}

.dark\:data-\[state\=selected\]\:bg-slate-800[data-state="selected"]:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity));
}

.dark\:data-\[hover\=true\]\:text-danger-500[data-hover="true"]:is(.dark *) {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-500) / var(--nextui-danger-500-opacity, var(--tw-text-opacity)));
}

.dark\:data-\[hover\=true\]\:text-success-500[data-hover="true"]:is(.dark *) {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-500) / var(--nextui-success-500-opacity, var(--tw-text-opacity)));
}

.dark\:data-\[hover\=true\]\:text-warning-500[data-hover="true"]:is(.dark *) {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-500) / var(--nextui-warning-500-opacity, var(--tw-text-opacity)));
}

.dark\:data-\[selected\=true\]\:data-\[hover\=true\]\:text-danger-foreground[data-hover="true"][data-selected="true"]:is(.dark *) {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-foreground) / var(--nextui-danger-foreground-opacity, var(--tw-text-opacity)));
}

.dark\:data-\[selected\=true\]\:data-\[hover\=true\]\:text-success-foreground[data-hover="true"][data-selected="true"]:is(.dark *) {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));
}

.dark\:data-\[selected\=true\]\:data-\[hover\=true\]\:text-warning-foreground[data-hover="true"][data-selected="true"]:is(.dark *) {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning-foreground) / var(--nextui-warning-foreground-opacity, var(--tw-text-opacity)));
}

.dark\:data-\[selected\=true\]\:data-\[range-selection\=true\]\:text-success-500[data-range-selection="true"][data-selected="true"]:is(.dark *) {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-500) / var(--nextui-success-500-opacity, var(--tw-text-opacity)));
}

.dark\:data-\[selected\=true\]\:data-\[selection-end\=true\]\:data-\[range-selection\=true\]\:text-success-foreground[data-range-selection="true"][data-selection-end="true"][data-selected="true"]:is(.dark *) {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));
}

.dark\:data-\[selected\=true\]\:data-\[selection-start\=true\]\:data-\[range-selection\=true\]\:text-success-foreground[data-range-selection="true"][data-selection-start="true"][data-selected="true"]:is(.dark *) {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success-foreground) / var(--nextui-success-foreground-opacity, var(--tw-text-opacity)));
}

.dark\:data-\[selected\=true\]\:text-danger-500[data-selected="true"]:is(.dark *) {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-danger-500) / var(--nextui-danger-500-opacity, var(--tw-text-opacity)));
}

.dark\:data-\[selected\=true\]\:text-success[data-selected="true"]:is(.dark *) {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-success) / var(--nextui-success-opacity, var(--tw-text-opacity)));
}

.dark\:data-\[selected\=true\]\:text-warning[data-selected="true"]:is(.dark *) {
  --tw-text-opacity: 1;
  color: hsl(var(--nextui-warning) / var(--nextui-warning-opacity, var(--tw-text-opacity)));
}

.dark\:data-\[state\=checked\]\:text-slate-900[data-state="checked"]:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.dark\:data-\[selected\=true\]\:data-\[range-selection\=true\]\:before\:bg-success-50[data-range-selection="true"][data-selected="true"]:is(.dark *)::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-success-50) / var(--nextui-success-50-opacity, var(--tw-bg-opacity)));
}

.dark\:data-\[selected\=true\]\:data-\[range-selection\=true\]\:before\:bg-warning-50[data-range-selection="true"][data-selected="true"]:is(.dark *)::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-warning-50) / var(--nextui-warning-50-opacity, var(--tw-bg-opacity)));
}

.dark\:data-\[invalid\=true\]\:focus\:bg-danger-400\/20:focus[data-invalid="true"]:is(.dark *) {
  background-color: hsl(var(--nextui-danger-400) / 0.2);
}

@media (min-width: 640px) {

  .sm\:left-\[50\%\] {
    left: 50%;
  }

  .sm\:top-\[60\%\] {
    top: 60%;
  }

  .sm\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .sm\:mx-6 {
    margin-left: 1.5rem;
    margin-right: 1.5rem;
  }

  .sm\:my-0 {
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .sm\:my-10 {
    margin-top: 2.5rem;
    margin-bottom: 2.5rem;
  }

  .sm\:my-16 {
    margin-top: 4rem;
    margin-bottom: 4rem;
  }

  .sm\:mb-0 {
    margin-bottom: 0px;
  }

  .sm\:mb-12 {
    margin-bottom: 3rem;
  }

  .sm\:mb-4 {
    margin-bottom: 1rem;
  }

  .sm\:mt-0 {
    margin-top: 0px;
  }

  .sm\:mt-11 {
    margin-top: 2.75rem;
  }

  .sm\:mt-16 {
    margin-top: 4rem;
  }

  .sm\:mt-20 {
    margin-top: 5rem;
  }

  .sm\:mt-5 {
    margin-top: 1.25rem;
  }

  .sm\:mt-6 {
    margin-top: 1.5rem;
  }

  .sm\:mt-7 {
    margin-top: 1.75rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:size-6 {
    width: 1.5rem;
    height: 1.5rem;
  }

  .sm\:w-1\/2 {
    width: 50%;
  }

  .sm\:w-\[32px\] {
    width: 32px;
  }

  .sm\:max-w-none {
    max-width: none;
  }

  .sm\:max-w-sm {
    max-width: 24rem;
  }

  .sm\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:items-start {
    align-items: flex-start;
  }

  .sm\:items-end {
    align-items: flex-end;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:gap-1 {
    gap: 0.25rem;
  }

  .sm\:gap-10 {
    gap: 2.5rem;
  }

  .sm\:gap-2 {
    gap: 0.5rem;
  }

  .sm\:gap-2\.5 {
    gap: 0.625rem;
  }

  .sm\:gap-20 {
    gap: 5rem;
  }

  .sm\:gap-4 {
    gap: 1rem;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:overflow-visible {
    overflow: visible;
  }

  .sm\:rounded-lg {
    border-radius: 0.5rem;
  }

  .sm\:p-10 {
    padding: 2.5rem;
  }

  .sm\:p-8 {
    padding: 2rem;
  }

  .sm\:px-1\.5 {
    padding-left: 0.375rem;
    padding-right: 0.375rem;
  }

  .sm\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .sm\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .sm\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .sm\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .sm\:py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .sm\:py-28 {
    padding-top: 7rem;
    padding-bottom: 7rem;
  }

  .sm\:py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .sm\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .sm\:pb-20 {
    padding-bottom: 5rem;
  }

  .sm\:pt-28 {
    padding-top: 7rem;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .sm\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .sm\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .sm\:text-\[20px\] {
    font-size: 20px;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .sm\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .sm\:\[--scale-enter\:100\%\] {
    --scale-enter: 100%;
  }

  .sm\:\[--scale-exit\:103\%\] {
    --scale-exit: 103%;
  }

  .sm\:\[--slide-enter\:0px\] {
    --slide-enter: 0px;
  }

  .sm\:\[--slide-exit\:0px\] {
    --slide-exit: 0px;
  }

  .sm\:data-\[visible\=true\]\:pointer-events-none[data-visible="true"] {
    pointer-events: none;
  }

  .sm\:data-\[visible\=true\]\:opacity-0[data-visible="true"] {
    opacity: 0;
  }

  .group[data-hover="true"] .sm\:group-data-\[hover\=true\]\:data-\[visible\=true\]\:pointer-events-auto[data-visible="true"] {
    pointer-events: auto;
  }

  .group[data-hover="true"] .sm\:group-data-\[hover\=true\]\:data-\[visible\=true\]\:opacity-100[data-visible="true"] {
    opacity: 1;
  }
}

@media (min-width: 768px) {

  .md\:absolute {
    position: absolute;
  }

  .md\:relative {
    position: relative;
  }

  .md\:top-40 {
    top: 10rem;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .md\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .md\:row-span-1 {
    grid-row: span 1 / span 1;
  }

  .md\:row-span-2 {
    grid-row: span 2 / span 2;
  }

  .md\:row-span-4 {
    grid-row: span 4 / span 4;
  }

  .md\:my-20 {
    margin-top: 5rem;
    margin-bottom: 5rem;
  }

  .md\:mb-20 {
    margin-bottom: 5rem;
  }

  .md\:mb-5 {
    margin-bottom: 1.25rem;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-10 {
    margin-top: 2.5rem;
  }

  .md\:mt-16 {
    margin-top: 4rem;
  }

  .md\:mt-24 {
    margin-top: 6rem;
  }

  .md\:mt-5 {
    margin-top: 1.25rem;
  }

  .md\:mt-7 {
    margin-top: 1.75rem;
  }

  .md\:block {
    display: block;
  }

  .md\:inline-block {
    display: inline-block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:aspect-square {
    aspect-ratio: 1 / 1;
  }

  .md\:h-1\/2 {
    height: 50%;
  }

  .md\:h-\[40rem\] {
    height: 40rem;
  }

  .md\:h-auto {
    height: auto;
  }

  .md\:h-full {
    height: 100%;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-60 {
    width: 15rem;
  }

  .md\:w-96 {
    width: 24rem;
  }

  .md\:w-\[360px\] {
    width: 360px;
  }

  .md\:w-\[45px\] {
    width: 45px;
  }

  .md\:w-\[var\(--radix-navigation-menu-viewport-width\)\] {
    width: var(--radix-navigation-menu-viewport-width);
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:max-w-2xl {
    max-width: 42rem;
  }

  .md\:max-w-32 {
    max-width: 8rem;
  }

  .md\:max-w-full {
    max-width: 100%;
  }

  .md\:max-w-md {
    max-width: 28rem;
  }

  .md\:basis-1\/2 {
    flex-basis: 50%;
  }

  .md\:-translate-y-9 {
    --tw-translate-y: -2.25rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:flex-row-reverse {
    flex-direction: row-reverse;
  }

  .md\:items-start {
    align-items: flex-start;
  }

  .md\:justify-start {
    justify-content: flex-start;
  }

  .md\:gap-1 {
    gap: 0.25rem;
  }

  .md\:gap-10 {
    gap: 2.5rem;
  }

  .md\:gap-2 {
    gap: 0.5rem;
  }

  .md\:gap-20 {
    gap: 5rem;
  }

  .md\:gap-4 {
    gap: 1rem;
  }

  .md\:gap-5 {
    gap: 1.25rem;
  }

  .md\:gap-8 {
    gap: 2rem;
  }

  .md\:space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:rounded-3xl {
    border-radius: 1.5rem;
  }

  .md\:border-\[\#1f1f1f\] {
    --tw-border-opacity: 1;
    border-color: rgb(31 31 31 / var(--tw-border-opacity));
  }

  .md\:p-10 {
    padding: 2.5rem;
  }

  .md\:p-14 {
    padding: 3.5rem;
  }

  .md\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .md\:px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:px-7 {
    padding-left: 1.75rem;
    padding-right: 1.75rem;
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .md\:py-14 {
    padding-top: 3.5rem;
    padding-bottom: 3.5rem;
  }

  .md\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .md\:py-28 {
    padding-top: 7rem;
    padding-bottom: 7rem;
  }

  .md\:pl-2 {
    padding-left: 0.5rem;
  }

  .md\:text-left {
    text-align: left;
  }

  .md\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .md\:text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }

  .md\:leading-tight {
    line-height: 1.25;
  }

  .md\:last\:pr-\[33\%\]:last-child {
    padding-right: 33%;
  }
}

@media (min-width: 1024px) {

  .lg\:sticky {
    position: sticky;
  }

  .lg\:bottom-8 {
    bottom: 2rem;
  }

  .lg\:left-\[50\%\] {
    left: 50%;
  }

  .lg\:right-8 {
    right: 2rem;
  }

  .lg\:top-24 {
    top: 6rem;
  }

  .lg\:top-\[50\%\] {
    top: 50%;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\:my-20 {
    margin-top: 5rem;
    margin-bottom: 5rem;
  }

  .lg\:my-24 {
    margin-top: 6rem;
    margin-bottom: 6rem;
  }

  .lg\:mb-28 {
    margin-bottom: 7rem;
  }

  .lg\:mb-5 {
    margin-bottom: 1.25rem;
  }

  .lg\:mt-20 {
    margin-top: 5rem;
  }

  .lg\:mt-28 {
    margin-top: 7rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:grid {
    display: grid;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-fit {
    height: -moz-fit-content;
    height: fit-content;
  }

  .lg\:w-0 {
    width: 0px;
  }

  .lg\:w-1\/4 {
    width: 25%;
  }

  .lg\:w-24 {
    width: 6rem;
  }

  .lg\:w-3\/4 {
    width: 75%;
  }

  .lg\:w-\[84\%\] {
    width: 84%;
  }

  .lg\:w-full {
    width: 100%;
  }

  .lg\:min-w-0 {
    min-width: 0px;
  }

  .lg\:max-w-2xl {
    max-width: 42rem;
  }

  .lg\:max-w-\[100vw\] {
    max-width: 100vw;
  }

  .lg\:max-w-\[45vw\] {
    max-width: 45vw;
  }

  .lg\:max-w-\[80vw\] {
    max-width: 80vw;
  }

  .lg\:max-w-lg {
    max-width: 32rem;
  }

  .lg\:flex-1 {
    flex: 1 1 0%;
  }

  .lg\:basis-1\/3 {
    flex-basis: 33.333333%;
  }

  .lg\:translate-x-\[-50\%\] {
    --tw-translate-x: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:translate-y-\[-50\%\] {
    --tw-translate-y: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:justify-end {
    justify-content: flex-end;
  }

  .lg\:justify-center {
    justify-content: center;
  }

  .lg\:gap-10 {
    gap: 2.5rem;
  }

  .lg\:gap-24 {
    gap: 6rem;
  }

  .lg\:gap-3 {
    gap: 0.75rem;
  }

  .lg\:gap-4 {
    gap: 1rem;
  }

  .lg\:gap-8 {
    gap: 2rem;
  }

  .lg\:gap-x-12 {
    -moz-column-gap: 3rem;
         column-gap: 3rem;
  }

  .lg\:rounded-xl {
    border-radius: 0.75rem;
  }

  .lg\:border {
    border-width: 1px;
  }

  .lg\:p-10 {
    padding: 2.5rem;
  }

  .lg\:px-20 {
    padding-left: 5rem;
    padding-right: 5rem;
  }

  .lg\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .lg\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .lg\:pl-4 {
    padding-left: 1rem;
  }

  .lg\:text-left {
    text-align: left;
  }

  .lg\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .lg\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .lg\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .lg\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .lg\:text-7xl {
    font-size: 4.5rem;
    line-height: 1;
  }

  .lg\:text-\[52px\] {
    font-size: 52px;
  }

  .lg\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .lg\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .lg\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .lg\:transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 250ms;
  }

  .lg\:duration-100 {
    transition-duration: 100ms;
  }

  .lg\:duration-200 {
    transition-duration: 200ms;
  }

  .lg\:ease-in-out {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  .lg\:duration-100 {
    animation-duration: 100ms;
  }

  .lg\:duration-200 {
    animation-duration: 200ms;
  }

  .lg\:ease-in-out {
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  @keyframes hovereffect {

    0%, 100% {
      transform: scale(1);
    }

    50% {
      transform: scale(1.1);
    }
  }

  .lg\:hover\:animate-hovereffect:hover {
    animation: hovereffect 1s ease-in-out infinite;
  }

  .lg\:data-\[state\=open\]\:animate-in[data-state="open"] {
    animation-name: enter;
    animation-duration: 250ms;
    --tw-enter-opacity: initial;
    --tw-enter-scale: initial;
    --tw-enter-rotate: initial;
    --tw-enter-translate-x: initial;
    --tw-enter-translate-y: initial;
  }

  .lg\:data-\[state\=closed\]\:animate-out[data-state="closed"] {
    animation-name: exit;
    animation-duration: 250ms;
    --tw-exit-opacity: initial;
    --tw-exit-scale: initial;
    --tw-exit-rotate: initial;
    --tw-exit-translate-x: initial;
    --tw-exit-translate-y: initial;
  }

  .lg\:data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
    --tw-exit-opacity: 0;
  }

  .lg\:data-\[state\=open\]\:fade-in-0[data-state="open"] {
    --tw-enter-opacity: 0;
  }

  .lg\:data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
    --tw-exit-scale: .95;
  }

  .lg\:data-\[state\=open\]\:zoom-in-95[data-state="open"] {
    --tw-enter-scale: .95;
  }

  .lg\:data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"] {
    --tw-exit-translate-x: -50%;
  }

  .lg\:data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"] {
    --tw-exit-translate-y: -48%;
  }

  .lg\:data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"] {
    --tw-enter-translate-x: -50%;
  }

  .lg\:data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"] {
    --tw-enter-translate-y: -48%;
  }
}

@media (min-width: 1029px) {

  .\32lg\:flex-row {
    flex-direction: row;
  }
}

@media (min-width: 1280px) {

  .xl\:bottom-10 {
    bottom: 2.5rem;
  }

  .xl\:right-8 {
    right: 2rem;
  }

  .xl\:my-16 {
    margin-top: 4rem;
    margin-bottom: 4rem;
  }

  .xl\:mb-0 {
    margin-bottom: 0px;
  }

  .xl\:mt-24 {
    margin-top: 6rem;
  }

  .xl\:mt-3 {
    margin-top: 0.75rem;
  }

  .xl\:block {
    display: block;
  }

  .xl\:hidden {
    display: none;
  }

  .xl\:h-36 {
    height: 9rem;
  }

  .xl\:max-w-4xl {
    max-width: 56rem;
  }

  .xl\:max-w-5xl {
    max-width: 64rem;
  }

  .xl\:max-w-\[45vw\] {
    max-width: 45vw;
  }

  .xl\:max-w-\[60vw\] {
    max-width: 60vw;
  }

  .xl\:-translate-y-4 {
    --tw-translate-y: -1rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .xl\:gap-20 {
    gap: 5rem;
  }

  .xl\:gap-x-20 {
    -moz-column-gap: 5rem;
         column-gap: 5rem;
  }

  .xl\:bg-black\/70 {
    background-color: rgb(0 0 0 / 0.7);
  }

  .xl\:py-14 {
    padding-top: 3.5rem;
    padding-bottom: 3.5rem;
  }

  .xl\:py-28 {
    padding-top: 7rem;
    padding-bottom: 7rem;
  }

  .xl\:pb-6 {
    padding-bottom: 1.5rem;
  }

  .xl\:text-left {
    text-align: left;
  }

  .xl\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .xl\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .xl\:text-\[52px\] {
    font-size: 52px;
  }

  .xl\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1536px) {

  .\32xl\:right-10 {
    right: 2.5rem;
  }

  .\32xl\:my-32 {
    margin-top: 8rem;
    margin-bottom: 8rem;
  }

  .\32xl\:my-\[120px\] {
    margin-top: 120px;
    margin-bottom: 120px;
  }

  .\32xl\:mt-0 {
    margin-top: 0px;
  }

  .\32xl\:size-16 {
    width: 4rem;
    height: 4rem;
  }

  .\32xl\:h-44 {
    height: 11rem;
  }

  .\32xl\:max-w-5xl {
    max-width: 64rem;
  }

  .\32xl\:max-w-6xl {
    max-width: 72rem;
  }

  .\32xl\:max-w-7xl {
    max-width: 80rem;
  }

  .\32xl\:gap-x-40 {
    -moz-column-gap: 10rem;
         column-gap: 10rem;
  }

  .\32xl\:py-36 {
    padding-top: 9rem;
    padding-bottom: 9rem;
  }

  .\32xl\:pb-12 {
    padding-bottom: 3rem;
  }

  .\32xl\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .\32xl\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .\32xl\:text-\[120px\] {
    font-size: 120px;
  }

  .\32xl\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .\32xl\:leading-7 {
    line-height: 1.75rem;
  }
}

.rtl\:left-1:where([dir="rtl"], [dir="rtl"] *) {
  left: 0.25rem;
}

.rtl\:left-1\.5:where([dir="rtl"], [dir="rtl"] *) {
  left: 0.375rem;
}

.rtl\:left-\[unset\]:where([dir="rtl"], [dir="rtl"] *) {
  left: unset;
}

.rtl\:right-0:where([dir="rtl"], [dir="rtl"] *) {
  right: 0px;
}

.rtl\:right-1\.5:where([dir="rtl"], [dir="rtl"] *) {
  right: 0.375rem;
}

.rtl\:right-\[unset\]:where([dir="rtl"], [dir="rtl"] *) {
  right: unset;
}

.rtl\:ml-2:where([dir="rtl"], [dir="rtl"] *) {
  margin-left: 0.5rem;
}

.rtl\:ml-\[unset\]:where([dir="rtl"], [dir="rtl"] *) {
  margin-left: unset;
}

.rtl\:mr-2:where([dir="rtl"], [dir="rtl"] *) {
  margin-right: 0.5rem;
}

.rtl\:mr-\[unset\]:where([dir="rtl"], [dir="rtl"] *) {
  margin-right: unset;
}

.rtl\:origin-top-right:where([dir="rtl"], [dir="rtl"] *) {
  transform-origin: top right;
}

.rtl\:-rotate-180:where([dir="rtl"], [dir="rtl"] *) {
  --tw-rotate: -180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rtl\:flex-row-reverse:where([dir="rtl"], [dir="rtl"] *) {
  flex-direction: row-reverse;
}

.rtl\:space-x-reverse:where([dir="rtl"], [dir="rtl"] *) > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

.rtl\:pl-6:where([dir="rtl"], [dir="rtl"] *) {
  padding-left: 1.5rem;
}

.rtl\:pr-0:where([dir="rtl"], [dir="rtl"] *) {
  padding-right: 0px;
}

.rtl\:text-right:where([dir="rtl"], [dir="rtl"] *) {
  text-align: right;
}

.rtl\:after\:ml-\[unset\]:where([dir="rtl"], [dir="rtl"] *)::after {
  content: var(--tw-content);
  margin-left: unset;
}

.rtl\:after\:mr-0\.5:where([dir="rtl"], [dir="rtl"] *)::after {
  content: var(--tw-content);
  margin-right: 0.125rem;
}

.rtl\:data-\[focus-visible\=true\]\:translate-x-3[data-focus-visible="true"]:where([dir="rtl"], [dir="rtl"] *) {
  --tw-translate-x: 0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rtl\:data-\[hover\=true\]\:translate-x-3[data-hover="true"]:where([dir="rtl"], [dir="rtl"] *) {
  --tw-translate-x: 0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rtl\:data-\[open\=true\]\:-rotate-90[data-open="true"]:where([dir="rtl"], [dir="rtl"] *) {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group[data-selected="true"] .rtl\:group-data-\[selected\=true\]\:ml-0:where([dir="rtl"], [dir="rtl"] *) {
  margin-left: 0px;
}

.group[data-selected="true"] .rtl\:group-data-\[selected\=true\]\:mr-4:where([dir="rtl"], [dir="rtl"] *) {
  margin-right: 1rem;
}

.group[data-selected="true"] .rtl\:group-data-\[selected\=true\]\:mr-5:where([dir="rtl"], [dir="rtl"] *) {
  margin-right: 1.25rem;
}

.group[data-selected="true"] .rtl\:group-data-\[selected\=true\]\:mr-6:where([dir="rtl"], [dir="rtl"] *) {
  margin-right: 1.5rem;
}

.\[\&\+\.border-medium\.border-danger\]\:ms-\[calc\(theme\(borderWidth\.medium\)\*-1\)\]+.border-medium.border-danger {
  margin-inline-start: calc(var(--nextui-border-width-medium) * -1);
}

.\[\&\+\.border-medium\.border-default\]\:ms-\[calc\(theme\(borderWidth\.medium\)\*-1\)\]+.border-medium.border-default {
  margin-inline-start: calc(var(--nextui-border-width-medium) * -1);
}

.\[\&\+\.border-medium\.border-primary\]\:ms-\[calc\(theme\(borderWidth\.medium\)\*-1\)\]+.border-medium.border-primary {
  margin-inline-start: calc(var(--nextui-border-width-medium) * -1);
}

.\[\&\+\.border-medium\.border-secondary\]\:ms-\[calc\(theme\(borderWidth\.medium\)\*-1\)\]+.border-medium.border-secondary {
  margin-inline-start: calc(var(--nextui-border-width-medium) * -1);
}

.\[\&\+\.border-medium\.border-success\]\:ms-\[calc\(theme\(borderWidth\.medium\)\*-1\)\]+.border-medium.border-success {
  margin-inline-start: calc(var(--nextui-border-width-medium) * -1);
}

.\[\&\+\.border-medium\.border-warning\]\:ms-\[calc\(theme\(borderWidth\.medium\)\*-1\)\]+.border-medium.border-warning {
  margin-inline-start: calc(var(--nextui-border-width-medium) * -1);
}

.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role=checkbox]) {
  padding-right: 0px;
}

.\[\&\:not\(\:first-child\)\:not\(\:last-child\)\]\:rounded-none:not(:first-child):not(:last-child) {
  border-radius: 0px;
}

.\[\&\:not\(\:first-child\)\]\:-ml-1:not(:first-child) {
  margin-left: -0.25rem;
}

.\[\&\:not\(\:first-of-type\)\:not\(\:last-of-type\)\]\:rounded-none:not(:first-of-type):not(:last-of-type) {
  border-radius: 0px;
}

.\[\&\:not\(\:first-of-type\)\]\:ms-\[calc\(theme\(borderWidth\.2\)\*-1\)\]:not(:first-of-type) {
  margin-inline-start: calc(2px * -1);
}

.\[\&\>\*\]\:relative>* {
  position: relative;
}

.\[\&\>span\]\:line-clamp-1>span {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.\[\&\>svg\]\:size-3\.5>svg {
  width: 0.875rem;
  height: 0.875rem;
}

.\[\&\>svg\]\:max-w-\[theme\(spacing\.8\)\]>svg {
  max-width: 2rem;
}

.\[\&\>tr\]\:first\:rounded-lg:first-child>tr {
  border-radius: 0.5rem;
}

.\[\&\>tr\]\:first\:shadow-small:first-child>tr {
  --tw-shadow: var(--nextui-box-shadow-small);
  --tw-shadow-colored: var(--nextui-box-shadow-small);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.\[\&\>tr\]\:last\:border-b-0:last-child>tr {
  border-bottom-width: 0px;
}

.\[\&\[data-hover\=true\]\:not\(\[data-active\=true\]\)\]\:bg-default-100[data-hover=true]:not([data-active=true]) {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-100) / var(--nextui-default-100-opacity, var(--tw-bg-opacity)));
}

.\[\&\[data-hover\=true\]\:not\(\[data-active\=true\]\)\]\:bg-default-200[data-hover=true]:not([data-active=true]) {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--nextui-default-200) / var(--nextui-default-200-opacity, var(--tw-bg-opacity)));
}

.\[\&_\.chevron-icon\]\:flex-none .chevron-icon {
  flex: none;
}

.\[\&_\.chevron-icon\]\:rotate-180 .chevron-icon {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&_\.chevron-icon\]\:transition-transform .chevron-icon {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}

.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading] {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading] {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading] {
  font-size: 0.75rem;
  line-height: 1rem;
}

.\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading] {
  font-weight: 500;
}

.\[\&_\[cmdk-group-heading\]\]\:text-slate-500 [cmdk-group-heading] {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}

.dark\:\[\&_\[cmdk-group-heading\]\]\:text-slate-400 [cmdk-group-heading]:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden]) ~[cmdk-group] {
  padding-top: 0px;
}

.\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group] {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.\[\&_\[cmdk-input-wrapper\]_svg\]\:size-5 [cmdk-input-wrapper] svg {
  width: 1.25rem;
  height: 1.25rem;
}

.\[\&_\[cmdk-input\]\]\:h-11 [cmdk-input] {
  height: 2.75rem;
}

.\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item] {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item] {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.\[\&_\[cmdk-item\]_svg\]\:size-5 [cmdk-item] svg {
  width: 1.25rem;
  height: 1.25rem;
}

.\[\&_tr\:last-child\]\:border-0 tr:last-child {
  border-width: 0px;
}

.\[\&_tr\]\:border-b tr {
  border-bottom-width: 1px;
}
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"DM_Sans","arguments":[{"subsets":["latin"],"weight":["400","500","700"]}],"variableName":"dmSans"} ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* latin-ext */
@font-face {
  font-family: '__DM_Sans_c48b40';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/56d4c7a1c09c3371-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__DM_Sans_c48b40';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/7e6a2e30184bb114-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: '__DM_Sans_c48b40';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/56d4c7a1c09c3371-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__DM_Sans_c48b40';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/7e6a2e30184bb114-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: '__DM_Sans_c48b40';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/56d4c7a1c09c3371-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__DM_Sans_c48b40';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/7e6a2e30184bb114-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__DM_Sans_Fallback_c48b40';src: local("Arial");ascent-override: 94.90%;descent-override: 29.66%;line-gap-override: 0.00%;size-adjust: 104.53%
}.__className_c48b40 {font-family: '__DM_Sans_c48b40', '__DM_Sans_Fallback_c48b40';font-style: normal
}

/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"],"display":"swap","variable":"--font-inter"}],"variableName":"inter"} ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: '__Inter_d65c78';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Inter_d65c78';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: '__Inter_d65c78';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: '__Inter_d65c78';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: '__Inter_d65c78';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Inter_d65c78';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/6d93bde91c0c2823-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Inter_d65c78';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/a34f9d1faa5f3315-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Inter_Fallback_d65c78';src: local("Arial");ascent-override: 90.49%;descent-override: 22.56%;line-gap-override: 0.00%;size-adjust: 107.06%
}.__className_d65c78 {font-family: '__Inter_d65c78', '__Inter_Fallback_d65c78';font-style: normal
}.__variable_d65c78 {--font-inter: '__Inter_d65c78', '__Inter_Fallback_d65c78'
}

