"use client";

import { useSignIn } from "@clerk/nextjs";
import { Eye, EyeOff } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

interface ClientLoginFormProps {
  onLoading: (loading: boolean) => void;
}

export const ClientLoginForm = ({ onLoading }: ClientLoginFormProps) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [needsVerification, setNeedsVerification] = useState(false);
  const router = useRouter();
  const { signIn, setActive, isLoaded } = useSignIn();

  if (!isLoaded) return null; // Wait for Clerk to load

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    onLoading(true);
    setError("");

    try {
      // If we're in verification state, attempt to complete the verification
      if (needsVerification && verificationCode) {
        try {
          const completeSignIn = await signIn.attemptFirstFactor({
            strategy: "email_code",
            code: verificationCode,
          });

          if (completeSignIn.status === "complete") {
            // Set the user session as active
            await setActive({ session: completeSignIn.createdSessionId });
            toast.success("Login successful!");
            router.push("/dashboard");
            return;
          } else {
            setError("Verification code is incorrect or expired");
            onLoading(false);
            toast.error("Verification code is incorrect or expired");
            return;
          }
        } catch (verificationErr: any) {
          console.error("Verification error:", verificationErr);
          // Extract specific error message
          const errorMessage = verificationErr.errors?.[0]?.message || "Verification code validation failed";
          setError(errorMessage);
          onLoading(false);
          toast.error(errorMessage);
          return;
        }
      }

      // Regular sign-in flow
      const signInAttempt = await signIn.create({
        identifier: email,
        password,
      });

      // Handle different sign-in statuses
      if (signInAttempt.status === "complete") {
        // Sign-in is complete, set the session
        await setActive({ session: signInAttempt.createdSessionId });
        toast.success("Login successful!");
        router.push("/dashboard");
      } else if (signInAttempt.status === "needs_first_factor") {
        // User needs verification
        if (signInAttempt.firstFactorVerification.status === "transferable") {
          // Send verification code via email
          await signIn.prepareFirstFactor({
            strategy: "email_code",
            emailAddressId: signInAttempt.supportedFirstFactors?.find(
              factor => factor.strategy === "email_code"
            )?.emailAddressId || "",
          });

          setNeedsVerification(true);
          onLoading(false);
          toast.info("Please check your email for a verification code");
        } else {
          setError("Verification method not supported");
          onLoading(false);
          toast.error("Verification method not supported");
        }
      } else {
        setError("Invalid email or password");
        onLoading(false);
        toast.error("Invalid email or password");
      }
    } catch (err: any) {
      console.error("Login error:", err);

      // Check for specific error types and provide clearer messages
      let errorMessage = "An unexpected error occurred";

      if (err.errors && Array.isArray(err.errors)) {
        // Get the specific error message from Clerk
        const clerkError = err.errors[0];
        if (clerkError) {
          errorMessage = clerkError.message;

          // Handle specific error cases
          if (errorMessage.includes("not found")) {
            errorMessage = "No account found with this email";
          } else if (errorMessage.includes("password")) {
            errorMessage = "Incorrect password";
          } else if (errorMessage.includes("identifier")) {
            errorMessage = "Invalid email format";
          } else if (errorMessage.includes("rate limit")) {
            errorMessage = "Too many attempts. Please try again later";
          }
        }
      }

      setError(errorMessage);
      onLoading(false);
      toast.error(errorMessage);
    }
  };

  const handleGoogleLogin = async () => {
    onLoading(true);
    try {
      // Using the correct OAuth redirect pattern for Clerk
      await signIn.authenticateWithRedirect({
        strategy: "oauth_google",
        redirectUrl: "/sso-callback",
        redirectUrlComplete: "/dashboard",
      });
      // The redirect will happen automatically
    } catch (err: any) {
      console.error("Google auth error:", err);

      // Extract specific Google auth errors
      const errorMessage = err.errors?.[0]?.message || "Google sign-in failed";
      setError(errorMessage);
      onLoading(false);
      toast.error(errorMessage);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="mb-2 text-2xl font-bold">Welcome Back</h1>
        <p className="text-gray-400">Sign in to continue to Interview Cracker</p>
      </div>

      {error && (
        <div className="rounded-md border border-red-500/30 bg-red-500/10 px-4 py-3 text-sm text-red-400">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {!needsVerification ? (
          // Regular login form
          <>
            <div>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Email"
                className="w-full rounded-md border border-gray-700 bg-gray-800 p-3 text-white"
                required
                id="email"
                name="email"
                autoComplete="email"
              />
            </div>

            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Password"
                className="w-full rounded-md border border-gray-700 bg-gray-800 p-3 pr-10 text-white"
                required
                id="password"
                name="password"
                autoComplete="current-password"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-400 hover:text-white"
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                {showPassword ? <EyeOff className="size-5" /> : <Eye className="size-5" />}
              </button>
            </div>

            <div className="flex justify-end">
              <a href="/forgot-password" className="text-sm text-blue-400 hover:underline">
                Forgot Password?
              </a>
            </div>

            <button
              type="submit"
              className="w-full rounded-md bg-green-500 p-3 font-medium text-white transition-colors hover:bg-green-600"
            >
              Login
            </button>
          </>
        ) : (
          // Verification code form
          <div className="space-y-4">
            <p className="text-center text-sm">
              A verification code has been sent to your email. Please enter it below.
            </p>
            <input
              type="text"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              placeholder="Verification Code"
              className="w-full rounded-md border border-gray-700 bg-gray-800 p-3 text-white"
              required
            />
            <button
              type="submit"
              className="w-full rounded-md bg-green-500 p-3 font-medium text-white transition-colors hover:bg-green-600"
            >
              Verify
            </button>
            <button
              type="button"
              onClick={() => setNeedsVerification(false)}
              className="w-full rounded-md border border-gray-600 bg-transparent p-3 text-white transition-colors hover:bg-gray-700"
            >
              Back to Login
            </button>
          </div>
        )}
      </form>

      {!needsVerification && (
        <>
          <div className="flex items-center gap-4">
            <hr className="grow border-gray-700" />
            <span className="text-gray-400">OR</span>
            <hr className="grow border-gray-700" />
          </div>

          <button
            onClick={handleGoogleLogin}
            className="flex w-full items-center justify-center gap-2 rounded-md border border-gray-200 bg-white p-3 text-gray-800 hover:bg-gray-100"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 48 48"
              className="size-6"
            >
              <circle cx="24" cy="24" r="20" fill="white" />
              <path
                fill="#FFC107"
                d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"
              />
              <path
                fill="#FF3D00"
                d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"
              />
              <path
                fill="#4CAF50"
                d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"
              />
              <path
                fill="#1976D2"
                d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"
              />
            </svg>
            Sign in with Google
          </button>

          <p className="text-center text-gray-400">
            Don&apos;t have an account?{" "}
            <a href="/register" className="text-blue-400 hover:underline">
              Register
            </a>
          </p>
        </>
      )}
    </div>
  );
};