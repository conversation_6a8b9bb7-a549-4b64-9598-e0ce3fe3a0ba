import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Interview Cracker - #1 Platform for Technical & Coding Interview Cracking',
  description: 'Master technical interviews with AI-powered practice, expert guidance, and real interview simulations. Land your dream tech job with Interview Cracker.',
  keywords: [
    'interview Cracking', 'technical interview', 'coding interview', 'interview cracking',
    'software engineering interview', 'interview practice platform', 'interview questions and answers',
    'AI interview coach', 'coding interview prep', 'tech job interview',
  ],
  alternates: {
    canonical: 'https://www.interviewcracker.in',
  },
  openGraph: {
    title: 'Interview Cracker - Master Technical & Coding Interviews',
    description: 'AI-powered interview Cracking platform to ace technical interviews. Get personalized practice, expert guidance, and real interview simulations.',
    images: [
      {
        url: '/main.png',
        width: 1200,
        height: 627,
        alt: 'Interview Cracker - The Ultimate Technical Interview Cracking Platform',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Interview Cracker - Master Technical & Coding Interviews',
    description: 'AI-powered interview Cracking platform to ace technical interviews. Get personalized practice, expert guidance, and real interview simulations.',
    images: ['/main.png'],
  },
};