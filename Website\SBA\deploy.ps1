# Stop on errors
$ErrorActionPreference = "Stop"

Write-Output "`n Starting full deployment to App Engine..."

# Set variables
$PROJECT_ID = "basic-curve-453309-a9"
$IMAGE_NAME = "sba-app"
$IMAGE_URL = "gcr.io/$PROJECT_ID/$IMAGE_NAME"
$APP_YAML_PATH = "app.yaml"

# 1. Set gcloud project
Write-Output "`n Setting GCP project..."
gcloud config set project $PROJECT_ID

# 2. Build and push Docker image using Cloud Build
Write-Output "`n Building and pushing Docker image via gcloud..."
gcloud builds submit --tag $IMAGE_URL

# 3. Ensure app.yaml exists
if (-Not (Test-Path $APP_YAML_PATH)) {
    Write-Output "`n Creating 'app.yaml' for App Engine..."
    @"
runtime: custom
env: flex
"@ | Out-File -Encoding utf8 $APP_YAML_PATH
}

# 4. Deploy to App Engine
Write-Output "`n Deploying to App Engine using custom image..."
gcloud app deploy --image-url $IMAGE_URL --quiet

Write-Output "`n Deployment completed successfully!"
Pause
