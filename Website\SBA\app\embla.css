.embla {
  max-width: 30rem;
  --slide-spacing: 1rem;
  --slide-size: 70%;
}
@media (min-width: 1028px) {
  .embla {
    margin: auto;
  }
}
@media (max-width: 1028px) {
  .embla {
    --slide-size: 50%;
    max-width: 50rem;
    --slide-spacing: 0.5rem;
  }
}
@media (max-width: 880px) {
  .embla {
    --slide-size: 50%;
    max-width: 50rem;
    --slide-spacing: 1rem;
  }
}

@media (max-width: 525px) {
  .embla {
    --slide-height: 30rem;
    --slide-size: 100%;
    --slide-spacing: 1rem;
    max-width: 100%;
  }
  .embla__progress {
    border-radius: 1.8rem;
    box-shadow: inset 0 0 0 0.2rem rgb(25, 25, 25);
    background-color: rgb(0, 0, 0);
    position: relative;
    height: 0.6rem;
    justify-self: flex-end;
    align-self: center;
    width: 5rem;
    max-width: 50%;
    overflow: hidden;
  }
}

.embla__viewport {
  overflow: hidden;
}
.embla__container {
  backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}
.embla__slide {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
}
.embla__slide__number {
  box-shadow: inset 0 0 0 0.2rem var(--detail-medium-contrast);
  border-radius: 1.8rem;
  font-size: 4rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--slide-height);
}
.embla__controls {
  display: grid;
  grid-template-columns: auto 1fr;
  justify-content: space-between;
  gap: 1.2rem;
  margin-top: 1.8rem;
}
.embla__buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.6rem;
  align-items: center;
}
.embla__button {
  -webkit-tap-highlight-color: rgba(230, 230, 230, 0.5);
  -webkit-appearance: none;
  appearance: none;
  background-color: transparent;
  touch-action: manipulation;
  display: inline-flex;
  text-decoration: none;
  cursor: pointer;
  border: 0;
  padding: 0;
  margin: 0;
  box-shadow: inset 0 0 0 0.2rem rgb(25, 25, 25);
  width: 3.6rem;
  height: 3.6rem;
  z-index: 1;
  border-radius: 50%;
  color: rgb(222, 222, 222);
  display: flex;
  align-items: center;
  justify-content: center;
}
.embla__button:disabled {
  color: rgb(101, 101, 101);
}
.embla__button__svg {
  width: 35%;
  height: 35%;
}
.embla__progress {
  border-radius: 1.8rem;
  box-shadow: inset 0 0 0 0.2rem rgb(25, 25, 25);
  background-color: rgb(0, 0, 0);
  position: relative;
  height: 0.6rem;
  justify-self: flex-end;
  align-self: center;
  width: 10rem;
  max-width: 90%;
  overflow: hidden;
}
.embla__progress__bar {
  background-color: rgb(222, 222, 222);
  position: absolute;
  width: 100%;
  top: 0;
  bottom: 0;
  left: -100%;
}
