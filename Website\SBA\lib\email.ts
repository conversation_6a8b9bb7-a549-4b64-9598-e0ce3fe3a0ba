import SibApiV3Sdk from "sib-api-v3-sdk";

import { transactionalEmailsApi } from "./brevo";

export async function sendResetCode(email: string, resetCode: string) {
  const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();

  // Configure email details
  sendSmtpEmail.to = [{ email }];
  sendSmtpEmail.sender = {
    email: process.env.BREVO_SENDER_EMAIL || "<EMAIL>",
    name: "Interview Cracker Team",
  };
  sendSmtpEmail.subject = "Password Reset Request - Interview Cracker";

  // Enhanced HTML email template with modern design
  sendSmtpEmail.htmlContent = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Password Reset Request</title>
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        :root {
          --primary-color: #4F46E5;
          --primary-dark: #3730A3;
          --accent-color: #8B5CF6;
          --text-main: #1F2937;
          --text-light: #6B7280;
          --border-color: #E5E7EB;
          --background-light: #F9FAFB;
          --background-white: #FFFFFF;
          --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
          --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background-color: var(--background-light);
          color: var(--text-main);
          line-height: 1.6;
          -webkit-font-smoothing: antialiased;
        }
        
        .wrapper {
          background-color: var(--background-light);
          padding: 40px 20px;
        }
        
        .container {
          max-width: 600px;
          margin: 0 auto;
          background-color: var(--background-white);
          border-radius: 16px;
          overflow: hidden;
          box-shadow: var(--shadow-md);
          border: 1px solid var(--border-color);
        }
        
        .header {
          background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
          padding: 30px 20px;
          text-align: center;
        }
        
        .logo {
          margin-bottom: 12px;
        }
        
        .logo img {
          height: 40px;
        }
        
        .title {
          color: white;
          font-size: 24px;
          font-weight: 700;
          margin-bottom: 6px;
        }
        
        .subtitle {
          color: rgba(255, 255, 255, 0.9);
          font-size: 16px;
          font-weight: 400;
        }
        
        .content {
          padding: 40px 32px;
          background-color: var(--background-white);
        }
        
        .greeting {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 16px;
        }
        
        .message {
          color: var(--text-main);
          margin-bottom: 24px;
          font-size: 16px;
        }
        
        .code-container {
          background: linear-gradient(90deg, rgba(79, 70, 229, 0.08) 0%, rgba(139, 92, 246, 0.08) 100%);
          border-radius: 10px;
          padding: 20px;
          margin: 24px 0;
          text-align: center;
          border-left: 4px solid var(--primary-color);
        }
        
        .code {
          font-family: 'Courier New', monospace;
          font-size: 28px;
          font-weight: bold;
          letter-spacing: 3px;
          color: var(--primary-dark);
        }
        
        .code-label {
          font-size: 14px;
          color: var(--text-light);
          margin-top: 8px;
        }
        
        .note {
          background-color: rgba(253, 230, 138, 0.2);
          border-left: 3px solid #F59E0B;
          padding: 12px 16px;
          margin-top: 24px;
          border-radius: 6px;
          font-size: 14px;
        }
        
        .note-title {
          font-weight: 600;
          color: #B45309;
          margin-bottom: 4px;
        }
        
        .note-content {
          color: #92400E;
        }
        
        .divider {
          height: 1px;
          background-color: var(--border-color);
          margin: 32px 0;
        }
        
        .footer {
          padding: 24px 32px;
          background-color: var(--background-light);
          text-align: center;
          color: var(--text-light);
          font-size: 14px;
          border-top: 1px solid var(--border-color);
        }
        
        .footer p {
          margin: 8px 0;
        }
        
        .footer a {
          color: var(--primary-color);
          text-decoration: none;
        }
        
        .social-links {
          margin-top: 16px;
        }
        
        .social-links a {
          display: inline-block;
          margin: 0 8px;
          color: var(--text-light);
        }
        
        .help {
          margin-top: 24px;
          padding-top: 16px;
          border-top: 1px solid var(--border-color);
          font-size: 13px;
        }
        
        .buttons {
          margin-top: 32px;
          text-align: center;
        }
        
        .button {
          display: inline-block;
          padding: 12px 24px;
          background-color: var(--primary-color);
          color: white !important;
          text-decoration: none;
          border-radius: 8px;
          font-weight: 500;
          font-size: 16px;
          transition: background-color 0.2s;
        }
        
        .button:hover {
          background-color: var(--primary-dark);
        }
        
        .signature {
          margin-top: 32px;
          font-style: italic;
          color: var(--text-light);
        }
        
        @media only screen and (max-width: 480px) {
          .content {
            padding: 30px 20px;
          }
          
          .code {
            font-size: 24px;
          }
        }
      </style>
    </head>
    <body>
      <div class="wrapper">
        <div class="container">
          <div class="header">
            <div class="logo">
              <!-- Logo can be added here if needed -->
              <span style="color: white; font-size: 32px; font-weight: 700;">Interview Cracker</span>
            </div>
            <h1 class="title">Password Reset</h1>
            <p class="subtitle">Secure verification code</p>
          </div>
          
          <div class="content">
            <p class="greeting">Hello!</p>
            
            <p class="message">
              We received a request to reset your password for your Interview Cracker account. 
              To proceed with the password reset, please use the verification code below.
            </p>
            
            <div class="code-container">
              <div class="code">${resetCode}</div>
              <div class="code-label">This code will expire in 15 minutes</div>
            </div>
            
            <p class="message">
              If you did not request a password reset, please ignore this email or contact our 
              support team if you have any concerns.
            </p>
            
            <div class="note">
              <div class="note-title">Security Tip</div>
              <div class="note-content">
                For your security, never share this code with anyone, including Interview Cracker staff.
                Our team will never ask for your verification code.
              </div>
            </div>
            
            <div class="signature">
              Best regards,<br>
              The Interview Cracker Team
            </div>
          </div>
          
          <div class="footer">
            <p>© ${new Date().getFullYear()} Interview Cracker. All rights reserved.</p>
            <p>
              Need help? <a href="mailto:<EMAIL>">Contact Support</a>
            </p>
            <div class="help">
              <p>If you're having trouble with the reset code, you can request a new one on the password reset page.</p>
            </div>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;

  // Send the email
  try {
    const data = await transactionalEmailsApi.sendTransacEmail(sendSmtpEmail);
    console.log("Reset code email sent successfully:", data);
  } catch (error) {
    console.error("Error sending reset code email:", error);
    throw error; // Re-throw to be handled by the caller
  }
}

export async function sendVerificationCode(email: string, verificationCode: string) {
  const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();

  sendSmtpEmail.to = [{ email }];
  sendSmtpEmail.sender = {
    email: process.env.BREVO_SENDER_EMAIL || "<EMAIL>",
    name: "Interview Cracker Team",
  };
  sendSmtpEmail.subject = "Verify Your Email - Interview Cracker";

  sendSmtpEmail.htmlContent = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Email Verification</title>
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        :root {
          --primary-color: #4F46E5;
          --primary-dark: #3730A3;
          --accent-color: #8B5CF6;
          --text-main: #1F2937;
          --text-light: #6B7280;
          --border-color: #E5E7EB;
          --background-light: #F9FAFB;
          --background-white: #FFFFFF;
          --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
          --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background-color: var(--background-light);
          color: var(--text-main);
          line-height: 1.6;
          -webkit-font-smoothing: antialiased;
        }
        
        .wrapper {
          background-color: var(--background-light);
          padding: 40px 20px;
        }
        
        .container {
          max-width: 600px;
          margin: 0 auto;
          background-color: var(--background-white);
          border-radius: 16px;
          overflow: hidden;
          box-shadow: var(--shadow-md);
          border: 1px solid var(--border-color);
        }
        
        .header {
          background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
          padding: 30px 20px;
          text-align: center;
        }
        
        .logo {
          margin-bottom: 12px;
        }
        
        .logo img {
          height: 40px;
        }
        
        .title {
          color: white;
          font-size: 24px;
          font-weight: 700;
          margin-bottom: 6px;
        }
        
        .subtitle {
          color: rgba(255, 255, 255, 0.9);
          font-size: 16px;
          font-weight: 400;
        }
        
        .content {
          padding: 40px 32px;
          background-color: var(--background-white);
        }
        
        .greeting {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 16px;
        }
        
        .message {
          color: var(--text-main);
          margin-bottom: 24px;
          font-size: 16px;
        }
        
        .code-container {
          background: linear-gradient(90deg, rgba(79, 70, 229, 0.08) 0%, rgba(139, 92, 246, 0.08) 100%);
          border-radius: 10px;
          padding: 20px;
          margin: 24px 0;
          text-align: center;
          border-left: 4px solid var(--primary-color);
        }
        
        .code {
          font-family: 'Courier New', monospace;
          font-size: 28px;
          font-weight: bold;
          letter-spacing: 3px;
          color: var(--primary-dark);
        }
        
        .code-label {
          font-size: 14px;
          color: var(--text-light);
          margin-top: 8px;
        }
        
        .note {
          background-color: rgba(253, 230, 138, 0.2);
          border-left: 3px solid #F59E0B;
          padding: 12px 16px;
          margin-top: 24px;
          border-radius: 6px;
          font-size: 14px;
        }
        
        .note-title {
          font-weight: 600;
          color: #B45309;
          margin-bottom: 4px;
        }
        
        .note-content {
          color: #92400E;
        }
        
        .divider {
          height: 1px;
          background-color: var(--border-color);
          margin: 32px 0;
        }
        
        .footer {
          padding: 24px 32px;
          background-color: var(--background-light);
          text-align: center;
          color: var(--text-light);
          font-size: 14px;
          border-top: 1px solid var(--border-color);
        }
        
        .footer p {
          margin: 8px 0;
        }
        
        .footer a {
          color: var(--primary-color);
          text-decoration: none;
        }
        
        .social-links {
          margin-top: 16px;
        }
        
        .social-links a {
          display: inline-block;
          margin: 0 8px;
          color: var(--text-light);
        }
        
        .help {
          margin-top: 24px;
          padding-top: 16px;
          border-top: 1px solid var(--border-color);
          font-size: 13px;
        }
        
        .buttons {
          margin-top: 32px;
          text-align: center;
        }
        
        .button {
          display: inline-block;
          padding: 12px 24px;
          background-color: var(--primary-color);
          color: white !important;
          text-decoration: none;
          border-radius: 8px;
          font-weight: 500;
          font-size: 16px;
          transition: background-color 0.2s;
        }
        
        .button:hover {
          background-color: var(--primary-dark);
        }
        
        .signature {
          margin-top: 32px;
          font-style: italic;
          color: var(--text-light);
        }
        
        .welcome-message {
          margin-bottom: 20px;
        }
        
        .steps {
          margin: 24px 0;
          padding-left: 20px;
        }
        
        .steps li {
          margin-bottom: 10px;
        }
        
        .steps-title {
          font-weight: 600;
          margin-bottom: 12px;
          color: var(--primary-color);
        }
        
        @media only screen and (max-width: 480px) {
          .content {
            padding: 30px 20px;
          }
          
          .code {
            font-size: 24px;
          }
        }
      </style>
    </head>
    <body>
      <div class="wrapper">
        <div class="container">
          <div class="header">
            <div class="logo">
              <!-- Logo can be added here if needed -->
              <span style="color: white; font-size: 32px; font-weight: 700;">Interview Cracker</span>
            </div>
            <h1 class="title">Verify Your Email</h1>
            <p class="subtitle">Complete your registration</p>
          </div>
          
          <div class="content">
            <p class="greeting">Welcome to Interview Cracker!</p>
            
            <div class="welcome-message">
              <p class="message">
                Thank you for registering with us. To complete your registration and activate your account,
                please verify your email address using the code below.
              </p>
            </div>
            
            <div class="code-container">
              <div class="code">${verificationCode}</div>
              <div class="code-label">This verification code will expire in 15 minutes</div>
            </div>
            
            <div class="steps-container">
              <p class="steps-title">Next steps:</p>
              <ol class="steps">
                <li>Enter this verification code on the registration page</li>
                <li>Complete your profile setup</li>
                <li>Start using Interview Cracker's powerful tools</li>
              </ol>
            </div>
            
            <div class="note">
              <div class="note-title">Didn't sign up?</div>
              <div class="note-content">
                If you didn't create an account with Interview Cracker, you can safely ignore this email.
                Someone might have entered your email address by mistake.
              </div>
            </div>
            
            <div class="signature">
              Best regards,<br>
              The Interview Cracker Team
            </div>
          </div>
          
          <div class="footer">
            <p>© ${new Date().getFullYear()} Interview Cracker. All rights reserved.</p>
            <p>
              Need help? <a href="mailto:<EMAIL>">Contact Support</a>
            </p>
            <div class="help">
              <p>If you're having trouble with the verification code, you can request a new one on the registration page.</p>
            </div>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;

  try {
    const data = await transactionalEmailsApi.sendTransacEmail(sendSmtpEmail);
    console.log("Verification code email sent successfully:", data);
  } catch (error) {
    console.error("Error sending verification code email:", error);
    throw error;
  }
}

export async function sendSubscriptionConfirmation(
  email: string, 
  name: string, 
  plan: string, 
  amount: number,
  currency: string,
  endDate: Date,
  os?: string // Add OS parameter
) {
  const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();
  
  // OS-specific pricing structure
  const pricing = {
    Windows: {
      USD: { weekly: 10, monthly: 30, yearly: 300 },
      INR: {weekly: 830, monthly: 2490, yearly: 24900 }
    },
    macOS: {
      USD: { weekly: 10, monthly: 30, yearly: 300 },
      INR: { weekly: 830, monthly: 2490, yearly: 24900 }
    }
  };

  // Default to Windows pricing if OS not provided
  const osPricing = os === "macOS" ? pricing.macOS : pricing.Windows;
  
  const formattedAmount = new Intl.NumberFormat(currency === "INR" ? "en-IN" : "en-US", {
    style: "currency",
    currency
  }).format(amount / 100);
  
  const formattedDate = endDate.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric"
  });

  sendSmtpEmail.to = [{ email }];
  sendSmtpEmail.sender = {
    email: process.env.BREVO_SENDER_EMAIL || "<EMAIL>",
    name: "Interview Cracker Team",
  };
  sendSmtpEmail.subject = "Your Premium Subscription is Active - Interview Cracker";

  sendSmtpEmail.htmlContent = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Subscription Confirmation</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          background-color: #f4f4f4;
          margin: 0;
          padding: 0;
          color: #333;
        }
        .container {
          max-width: 600px;
          margin: 20px auto;
          background-color: #ffffff;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .header {
          text-align: center;
          padding-bottom: 20px;
          border-bottom: 1px solid #e0e0e0;
        }
        .header h1 {
          font-size: 24px;
          color: #1a73e8;
          margin: 0;
        }
        .content {
          padding: 20px 0;
          line-height: 1.6;
        }
        .subscription-details {
          background-color: #f1f3f4;
          padding: 15px;
          border-radius: 4px;
          margin: 15px 0;
        }
        .footer {
          text-align: center;
          padding-top: 20px;
          border-top: 1px solid #e0e0e0;
          font-size: 12px;
          color: #777;
        }
        .footer a {
          color: #1a73e8;
          text-decoration: none;
        }
        .button {
          display: inline-block;
          background-color: #1a73e8;
          color: #ffffff !important;
          padding: 10px 20px;
          text-decoration: none;
          border-radius: 4px;
          margin-top: 10px;
        }
        .os-badge {
          display: inline-block;
          background-color: #e8f0fe;
          color: #1a73e8;
          padding: 3px 8px;
          border-radius: 4px;
          font-size: 12px;
          margin-top: 5px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Interview Cracker</h1>
        </div>
        <div class="content">
          <p>Hello ${name},</p>
          <p>Thank you for subscribing to Interview Cracker Premium! Your payment has been processed successfully.</p>
          
          <div class="subscription-details">
            <p><strong>Subscription Plan:</strong> ${plan.charAt(0).toUpperCase() + plan.slice(1)} Premium <span class="os-badge">${os || 'Windows'} pricing</span></p>
            <p><strong>Amount Paid:</strong> ${formattedAmount}</p>
            <p><strong>Active Until:</strong> ${formattedDate}</p>
            <p><strong>Premium Features:</strong></p>
            <ul>
              <li>Advanced Code Generation</li>
              <li>250 prompts per ${plan === "weekly" ? "week" : plan === "monthly" ? "month" : "week (yearly plan)"}</li>
              <li>Priority Support</li>
            </ul>
          </div>
          
          <p>Your premium access is active until ${formattedDate}. To continue using premium features after this date, please subscribe again.</p>
          
          <p style="text-align: center;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL || 'https://interviewcracker.in'}/dashboard" class="button">Go to Dashboard</a>
          </p>
          
          ${
            currency === "USD"
              ? `<p><small>Note: Your ${plan} subscription is at the ${os || 'Windows'} rate of $${osPricing.USD[plan as keyof typeof osPricing.USD]}/${plan}.</small></p>`
              : `<p><small>Note: Your ${plan} subscription is at the ${os || 'Windows'} rate of ₹${osPricing.INR[plan as keyof typeof osPricing.INR]}/${plan}.</small></p>`
          }
        </div>
        <div class="footer">
          <p>Best regards,<br>The Interview Cracker Team</p>
          <p>Need help? <a href="mailto:<EMAIL>">Contact Support</a></p>
          <p>© ${new Date().getFullYear()} Interview Cracker. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  try {
    const data = await transactionalEmailsApi.sendTransacEmail(sendSmtpEmail);
    console.log("Subscription confirmation email sent successfully:", data);
    return data;
  } catch (error) {
    console.error("Error sending subscription confirmation email:", error);
    throw error;
  }
}

export async function sendCancellationConfirmation(
  email: string,
  name: string,
  plan: string,
  endDate: Date
) {
  const sendSmtpEmail = new SibApiV3Sdk.SendSmtpEmail();
  
  // Format today's date for the email
  const today = new Date();
  const formattedToday = today.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  
  sendSmtpEmail.to = [{ email }];
  sendSmtpEmail.sender = {
    email: process.env.BREVO_SENDER_EMAIL || "<EMAIL>",
    name: "Interview Cracker Team",
  };
  sendSmtpEmail.subject = "Premium Access Ended - Interview Cracker";

  sendSmtpEmail.htmlContent = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Subscription Cancellation</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          background-color: #f4f4f4;
          margin: 0;
          padding: 0;
          color: #333;
        }
        .container {
          max-width: 600px;
          margin: 20px auto;
          background-color: #ffffff;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .header {
          text-align: center;
          padding-bottom: 20px;
          border-bottom: 1px solid #e0e0e0;
        }
        .header h1 {
          font-size: 24px;
          color: #1a73e8;
          margin: 0;
        }
        .content {
          padding: 20px 0;
          line-height: 1.6;
        }
        .cancellation-details {
          background-color: #f1f3f4;
          padding: 15px;
          border-radius: 4px;
          margin: 15px 0;
        }
        .footer {
          text-align: center;
          padding-top: 20px;
          border-top: 1px solid #e0e0e0;
          font-size: 12px;
          color: #777;
        }
        .footer a {
          color: #1a73e8;
          text-decoration: none;
        }
        .button {
          display: inline-block;
          background-color: #1a73e8;
          color: #ffffff !important;
          padding: 10px 20px;
          text-decoration: none;
          border-radius: 4px;
          margin-top: 10px;
        }
        .important-notice {
          color: #d93025;
          font-weight: bold;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Interview Cracker</h1>
        </div>
        <div class="content">
          <p>Hello ${name},</p>
          <p>We've processed your request to cancel your Interview Cracker Premium subscription.</p>
          
          <div class="cancellation-details">
            <p><strong>Subscription Plan:</strong> ${plan.charAt(0).toUpperCase() + plan.slice(1)} Plan</p>
            <p><strong>Cancellation Date:</strong> ${formattedToday}</p>
            <p class="important-notice">Your premium features have been deactivated effective immediately.</p>
          </div>
          
          <p>As of today (${formattedToday}), your account has been downgraded to the free plan with the following limitations:</p>
          <ul>
            <li>Basic code generation only</li>
            <li>Limited to 5 prompts total</li>
            <li>Standard community support</li>
            <li>No access to premium features or priority support</li>
          </ul>
          
          <p>We value your time with Interview Cracker and would love to have you back. If you'd like to restore your premium access, you can resubscribe at any time.</p>
          
          <p style="text-align: center;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL || 'https://interviewcracker.in'}/pricing" class="button">Restore Premium Access</a>
          </p>
        </div>
        <div class="footer">
          <p>Best regards,<br>The Interview Cracker Team</p>
          <p>Need help? <a href="mailto:<EMAIL>">Contact Support</a></p>
          <p>© ${new Date().getFullYear()} Interview Cracker. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  try {
    const data = await transactionalEmailsApi.sendTransacEmail(sendSmtpEmail);
    console.log("Cancellation confirmation email sent successfully:", data);
    return data;
  } catch (error) {
    console.error("Error sending cancellation confirmation email:", error);
    throw error;
  }
}