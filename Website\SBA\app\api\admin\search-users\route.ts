import { getAuth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";

import connectDB from "@/lib/mongodb";
import { User } from "@/model/User";

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  try {
    const auth = getAuth(req);
    const { userId } = auth;

    if (!userId) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    await connectDB();

    const adminUser = await User.findOne({ clerkId: userId });
    if (!adminUser || !adminUser.isAdmin) {
      return NextResponse.json({ message: "Unauthorized: Admin access required" }, { status: 403 });
    }

    const searchUrl = new URL(req.url);
    const query = searchUrl.searchParams.get("query");
    const subscriptionType = searchUrl.searchParams.get("subscriptionType");
    const showAll = searchUrl.searchParams.get("showAll") === "true";

    // Build the query filter
    let filter: any = {};

    // Add subscription type filter if provided
    if (subscriptionType && subscriptionType !== "all") {
      if (subscriptionType === "free" || subscriptionType === "premium") {
        filter.subscriptionType = subscriptionType;
      } else if (["weekly", "monthly", "yearly"].includes(subscriptionType)) {
        filter.subscriptionType = "premium";
        filter.subscriptionPlan = subscriptionType;
      }
    }

    // Add search query filter if provided and not showing all
    if (query && !showAll) {
      filter = {
        ...filter,
        $or: [
          { name: { $regex: query, $options: "i" } },
          { email: { $regex: query, $options: "i" } }
        ]
      };
    }

    // If neither query nor showAll is provided, return error
    if (!query && !showAll) {
      return NextResponse.json({ message: "Either query parameter or showAll=true is required" }, { status: 400 });
    }

    const users = await User.find(filter)
      .select("name email subscriptionType subscriptionPlan subscriptionEndDate subscriptionStartDate promptsUsed")
      .limit(50);

    const formattedUsers = users.map(user => ({
      id: user._id,
      name: user.name,
      email: user.email,
      subscriptionType: user.subscriptionType || "free",
      subscriptionPlan: user.subscriptionPlan || "",
      subscriptionStartDate: user.subscriptionStartDate,
      subscriptionEndDate: user.subscriptionEndDate,
      promptsUsed: user.promptsUsed || 0
    }));

    return NextResponse.json({
      users: formattedUsers,
      status: 'success',
      message: 'Users found'
    });
  } catch (error) {
    console.error("Admin search users error:", error);
    return NextResponse.json({
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}