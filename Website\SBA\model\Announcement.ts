import mongoose, { Schema, Document } from 'mongoose';

export interface IAnnouncement extends Document {
  title: string;
  content: string;
  type: 'update' | 'newFeature' | 'bugFix' | 'maintenance' | 'currentBugs' | 'upcomingFeatures' | 'other';
  publishDate: Date;
  expiryDate?: Date;
  isImportant: boolean;
  createdBy: string;
  lastUpdatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Delete the model if it exists to ensure schema changes are picked up
if (mongoose.models.Announcement) {
  delete mongoose.models.Announcement;
}

const AnnouncementSchema: Schema = new Schema({
  title: {
    type: String,
    required: [true, 'Title is required'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  content: {
    type: String,
    required: [true, 'Content is required'],
    trim: true
  },
  type: {
    type: String,
    required: [true, 'Type is required'],
    enum: ['update', 'newFeature', 'bugFix', 'maintenance', 'currentBugs', 'upcomingFeatures', 'other'],
    default: 'update'
  },
  publishDate: {
    type: Date,
    default: Date.now
  },
  expiryDate: {
    type: Date
  },
  isImportant: {
    type: Boolean,
    default: false
  },
  createdBy: {
    type: String,
    required: true
  },
  lastUpdatedBy: {
    type: String
  }
}, {
  timestamps: true
});

// Create and export the model
export default mongoose.model<IAnnouncement>('Announcement', AnnouncementSchema);