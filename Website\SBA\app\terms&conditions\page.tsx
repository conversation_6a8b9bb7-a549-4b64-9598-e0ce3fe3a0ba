import Link from 'next/link'; // Remove if not using Next.js
import React from 'react';

const TermsAndConditionsPage = () => {
  return (
    <div className='mx-auto flex items-center justify-center px-5 py-24 md:py-28'>
      <div className='flex max-w-4xl flex-col items-center justify-center gap-12'>
        <h2 className='text-center text-2xl font-bold md:text-3xl'>
          Terms and Conditions
        </h2>
        <div className='flex flex-col gap-5'>
          <p>
            Welcome to InterviewCracker.in, an Electron-based desktop application and website (collectively, &quot;the Service&quot;). By downloading, installing, or using the Service, you agree to these Terms and Conditions. If you do not agree, please do not use the Service.
          </p>
          <p>
            These terms govern your use of the Service provided by InterviewCracker.in. We reserve the right to modify these Terms and Conditions at any time without prior notice, and your continued use signifies acceptance of any updates.
          </p>
          <div className='flex flex-col gap-3'>
            <p>
              Please review the following key terms:
            </p>
            <ul className='list-disc pl-4'>
              <li>
                <span className='font-semibold'>Use of Service</span>: The Service is for personal use only. You may not modify, distribute, or reverse-engineer it without our permission.
              </li>
              <li>
                <span className='font-semibold'>No Refund Policy</span>: All purchases or subscriptions are final. We do not offer refunds or exchanges under any circumstances, including dissatisfaction or technical issues.
              </li>
              <li>
                <span className='font-semibold'>Intellectual Property</span>: Content within the Service is owned by InterviewCracker.in or licensed from third parties, who retain their rights where applicable.
              </li>
              <li>
                <span className='font-semibold'>Liability</span>: The Service is provided &quot;as is.&quot; We are not liable for damages, data loss, or issues arising from its use.
              </li>
              <li>
                <span className='font-semibold'>Governing Law</span>: These terms are governed by the laws of [Your Jurisdiction/State]. Disputes will be resolved in [Your Jurisdiction/State] courts if necessary.
              </li>
            </ul>
          </div>
          <p>
            We aim to provide a reliable and ethical experience. If you have concerns about the Service or these terms, please reach out to us for assistance.
          </p>
          <p>
            For questions or support, contact us at{' '}
            <Link
              href='mailto:<EMAIL>'
              className='underline underline-offset-4'
            >
              <EMAIL>
            </Link>{' '}
            or{' '}
            <Link
              href='mailto:<EMAIL>'
              className='underline underline-offset-4'
            >
              <EMAIL>
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default TermsAndConditionsPage;