# Currency Detection System

This document explains how the currency detection system works in the application.

## Overview

The application detects the user's location based on their IP address and shows prices in the appropriate currency:
- Users in India see prices in INR (Indian Rupees)
- Users outside India see prices in USD (US Dollars)

## How It Works

1. The system uses IP geolocation to determine the user's country
2. If the user is in India (country code "IN"), prices are shown in INR
3. For all other countries, prices are shown in USD
4. The exchange rate used for INR is fixed at 85.26 INR to 1 USD

## Development vs. Production

### Production Environment

In production:
- The system always uses the real client IP address for geolocation
- No debug information is displayed
- No localStorage overrides are used

### Development Environment

In development:
- The system automatically detects your real external IP address for geolocation
- When running on localhost, if your real IP can't be determined, a fallback is used
- The fallback IP can be controlled via the `USE_INDIAN_IP` environment variable
- No debug information is displayed
- No localStorage overrides are used

## Testing Different Currencies in Development

By default, the system will use your actual IP address for currency detection, even in development. This means you'll see prices in your local currency based on your location.

If you need to test a specific currency:

1. Open the `.env` file
2. Find or add the `USE_INDIAN_IP` setting
3. Set it to one of these values:
   - `USE_INDIAN_IP="true"` - To test INR pricing (Indian users)
   - `USE_INDIAN_IP="false"` - To test USD pricing (International users)
4. Restart the development server

Example:
```
# Set to 'true' to use Indian IP for testing INR pricing, or 'false' for USD pricing
USE_INDIAN_IP="true"
```

Note: The `USE_INDIAN_IP` setting only takes effect when your real IP cannot be determined (e.g., when running on localhost).

## Important Notes

- The system always uses the real client IP address for geolocation when possible
- The `USE_INDIAN_IP` setting is only used as a fallback when the real IP cannot be determined
- The `USE_INDIAN_IP` setting is ignored in production
- Always test both currency scenarios before deploying to production
- The exchange rate is fixed at 85.26 INR to 1 USD
