"use client";
import { <PERSON><PERSON> } from "@nextui-org/button";
import { signIn } from "next-auth/react";
import { FcGoogle } from "react-icons/fc";

export const GoogleButton = () => {
  const handleGoogleLogin = async () => {
    try {
      // Simply redirect to Google OAuth flow without showing toasts
      await signIn("google", {
        redirect: true,
        callbackUrl: "/dashboard"
      });
      // No toast here - the redirect will happen before it can be shown
    } catch (error) {
      console.error("Google sign-in error:", error);
      // No error toast here - we'll handle errors in the redirect callback
    }
  };

  return (
    <Button
      onClick={handleGoogleLogin}
      className="flex w-full gap-2 bg-white text-black hover:bg-gray-100"
    >
      <FcGoogle size={20} />
      Sign in with Google
    </Button>
  );
};