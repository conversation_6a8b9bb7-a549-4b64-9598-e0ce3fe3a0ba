import { withSentryConfig } from "@sentry/nextjs";

const nextConfig = {
  reactStrictMode: false,
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "lh3.googleusercontent.com",
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
      },
      {
        protocol: "https",
        hostname: "assets.aceternity.com",
      },
      {
        protocol: "https",
        hostname: "asset.cloudinary.com",
      },
      {
        protocol: "https",
        hostname: "res.cloudinary.com",
      },
      {
        protocol: "https",
        hostname: "img.clerk.com",
      },
      {
        protocol: "https",
        hostname: "images.clerk.dev",
      },
    ],
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  productionBrowserSourceMaps: false,
  // Add client instrumentation hook configuration
  experimental: {
    instrumentationHook: true,
  },
  // Prioritize App Router API routes over Pages Router
  async rewrites() {
    return {
      beforeFiles: [
        // Redirect API calls from Pages Router to App Router format
        {
          source: '/api/user/:path*',
          destination: '/api/user/:path*',
          has: [
            {
              type: 'header',
              key: 'x-invoke-app-router',
              value: 'true',
            },
          ],
        },
      ],
    };
  },
};

// Sentry configuration options
const sentryConfig = {
  org: "interview-cracker",
  project: "javascript-nextjs",

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers
  tunnelRoute: "/monitoring",

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,

  // Disabled Vercel monitoring as we're not using Vercel for hosting
  automaticVercelMonitors: false,
};

export default withSentryConfig(nextConfig, sentryConfig);