"use client";
import { useUser, useClerk } from "@clerk/nextjs";
import { Button } from "@nextui-org/react";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import React, { useState, useEffect } from "react";

export default function Navigation() {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const { isSignedIn } = useUser();
    // eslint-disable-next-line no-unused-vars
    const { signOut } = useClerk();
    const pathname = usePathname();
    const router = useRouter();
    const isBrowser = typeof window !== "undefined";
    const [isMounted, setIsMounted] = useState(false);
    const [isScrolled, setIsScrolled] = useState(false);

    useEffect(() => {
        setIsMounted(true);
        if (isBrowser) {
            const handleScroll = () => {
                if (window.scrollY > 50) {
                    setIsScrolled(true);
                } else {
                    setIsScrolled(false);
                }
            };

            window.addEventListener("scroll", handleScroll);
            return () => {
                window.removeEventListener("scroll", handleScroll);
            };
        }
    }, [isBrowser]);

    if (!isMounted) return null;

    const closeMenu = () => {
        setIsMenuOpen(false);
    };

    const toggleMenu = () => {
        setIsMenuOpen(!isMenuOpen);
    };

    const handleLogout = async () => {
        try {
            // First, clear localStorage and sessionStorage
            window.localStorage.clear();
            window.sessionStorage.clear();

            // Clear cookies with different paths
            const paths = ['/', '/dashboard', '/api', ''];
            paths.forEach(path => {
                document.cookie.split(";").forEach(c => {
                    const cookie = c.trim();
                    if (cookie) {
                        const cookieName = cookie.split("=")[0];
                        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}`;
                    }
                });
            });

            // Instead of relying on Clerk's automatic redirect, redirect to our logout page
            // which will handle the complete logout process
            router.push('/logout');
        } catch (error) {
            console.error("Logout error:", error);
            // Fallback in case of error
            window.location.href = '/logout';
        }
    };

    return (
        <section>
            <nav
                className={`fixed top-0 z-50 w-full transition-all duration-300 ease-in-out ${isScrolled || isMenuOpen ? "bg-black/70 shadow-xl backdrop-blur-md" : "bg-transparent"
                    }`}
            >
                <div className="mx-auto flex h-16 items-center justify-between px-4 sm:px-6 lg:px-20">
                    <div className="flex items-center lg:w-0 lg:flex-1">
                        <span className="flex">
                            <a href="/">
                                <Image
                                    src="/logo.png"
                                    width={50}
                                    height={50}
                                    alt="logo"
                                    className="h-auto w-[35px] md:w-[45px]"
                                    priority
                                />
                            </a>
                            <p className="my-auto ml-2 hidden text-base font-semibold text-white md:block md:text-2xl">
                                InterviewCracker
                            </p>
                        </span>
                    </div>
                    <div className="-my-2 -mr-2 lg:hidden">
                        <button
                            type="button"
                            className="inline-flex items-center justify-center rounded-md bg-transparent p-2 text-white focus:outline-none"
                            onClick={toggleMenu}
                        >
                            <span className="sr-only">Open menu</span>
                            <svg
                                className="size-6"
                                fill="none"
                                viewBox="0 0 24 24"
                                strokeWidth="1.5"
                                stroke="currentColor"
                                aria-hidden="true"
                            >
                                {isMenuOpen ? (
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        d="M6 18L18 6M6 6l12 12"
                                    />
                                ) : (
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
                                    />
                                )}
                            </svg>
                        </button>
                    </div>
                    <div className="hidden lg:flex lg:min-w-0 lg:flex-1 lg:justify-center lg:gap-x-12">
                        <a
                            href="/"
                            className={`group inline-flex h-10 w-max items-center justify-center rounded-md px-[9px] py-2 text-base font-medium lg:px-3 ${pathname === "/" ? "text-green-500" : "text-white"
                                } cursor-pointer`}
                        >
                            Home
                        </a>
                        <a
                            href="/announcements"
                            className={`group inline-flex h-10 w-max items-center justify-center rounded-md px-[9px] py-2 text-base font-medium lg:px-3 ${pathname === "/announcements" || pathname === "/services" ? "text-green-500" : "text-white"
                                } cursor-pointer`}
                        >
                            Announcements
                        </a>
                        <a
                            href="#"
                            className={`group inline-flex h-10 w-max items-center justify-center rounded-md px-[9px] py-2 text-base font-medium lg:px-3 ${pathname === "/portfolio" ? "text-green-500" : "text-white"
                                } cursor-pointer`}
                        >
                            Portfolio
                        </a>
                        <a
                            href="/contact"
                            className={`group inline-flex h-10 w-max items-center justify-center rounded-md px-[9px] py-2 text-base font-medium lg:px-3 ${pathname === "/contact" ? "text-green-500" : "text-white"
                                } cursor-pointer`}
                        >
                            Contact
                        </a>
                        {isSignedIn && (
                            <a
                                href="/dashboard"
                                className={`text-base font-medium ${pathname === "/dashboard" ? "text-green-500" : "text-white"
                                    } cursor-pointer`}
                            >
                                Dashboard
                            </a>
                        )}
                    </div>
                    <div className="hidden lg:flex lg:min-w-0 lg:flex-1 lg:justify-end">
                        {isSignedIn ? (
                            <Button
                                color="primary"
                                variant="flat"
                                onClick={handleLogout}
                                className="bg-green-500/10 capitalize text-green-500"
                            >
                                Logout
                            </Button>
                        ) : (
                            <Button
                                color="primary"
                                variant="flat"
                                onClick={() => router.push("/login")}
                                className="bg-green-500/10 capitalize text-green-500"
                            >
                                Login
                            </Button>
                        )}
                    </div>
                </div>

                {/* Mobile Menu */}
                <div
                    className={`fixed inset-0 transition-opacity ${isMenuOpen ? "opacity-100" : "pointer-events-none opacity-0"
                        }`}
                >
                    <div
                        className={`absolute inset-y-0 right-0 z-40 w-full max-w-[75%] bg-black shadow-xl transition-transform duration-300 ease-in-out${isMenuOpen ? "translate-x-0" : "translate-x-full"
                            }`}
                    >
                        <div className="flex h-full flex-col overflow-auto p-6 pt-20">
                            <a
                                href="/"
                                className="block py-4 text-lg font-medium text-white hover:text-green-500"
                                onClick={closeMenu}
                            >
                                Home
                            </a>
                            <a
                                href="/announcements"
                                className="block py-4 text-lg font-medium text-white hover:text-green-500"
                                onClick={closeMenu}
                            >
                                Announcements
                            </a>
                            <a
                                href="#"
                                className="block py-4 text-lg font-medium text-white hover:text-green-500"
                                onClick={closeMenu}
                            >
                                Portfolio
                            </a>
                            <a
                                href="/contact"
                                className="block py-4 text-lg font-medium text-white hover:text-green-500"
                                onClick={closeMenu}
                            >
                                Contact
                            </a>
                            {isSignedIn && (
                                <a
                                    href="/dashboard"
                                    className="block py-4 text-lg font-medium text-white hover:text-green-500"
                                    onClick={closeMenu}
                                >
                                    Dashboard
                                </a>
                            )}
                            <div className="mt-8">
                                {isSignedIn ? (
                                    <Button
                                        color="primary"
                                        variant="flat"
                                        onClick={() => {
                                            handleLogout();
                                            closeMenu();
                                        }}
                                        className="w-full bg-green-500/10 capitalize text-green-500"
                                    >
                                        Logout
                                    </Button>
                                ) : (
                                    <Button
                                        color="primary"
                                        variant="flat"
                                        onClick={() => {
                                            closeMenu();
                                            router.push("/login");
                                        }}
                                        className="w-full bg-green-500/10 capitalize text-green-500"
                                    >
                                        Login
                                    </Button>
                                )}
                            </div>
                        </div>
                    </div>
                    {/* Dark overlay behind the menu */}
                    <div
                        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
                        onClick={closeMenu}
                    ></div>
                </div>
            </nav>
        </section>
    );
}