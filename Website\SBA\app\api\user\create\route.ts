import { getAuth, clerkClient } from "@clerk/nextjs/server"; // Correct import for Next.js
import { NextRequest, NextResponse } from "next/server";

import connectDB from "@/lib/mongodb";
import { User } from "@/model/User";

export async function POST(req: NextRequest) {
  try {
    const { userId } = getAuth(req);

    if (!userId) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const { fullName } = await req.json();

    if (!fullName) {
      return NextResponse.json({ message: "Name is required" }, { status: 400 });
    }

    try {
      // Get user info from Clerk
      const clerk = await clerkClient();
      const clerkUser = await clerk.users.getUser(userId); // `users` is still valid in v5.x
      const email = clerkUser.emailAddresses[0]?.emailAddress;

      if (!email) {
        return NextResponse.json({ message: "User email not found" }, { status: 400 });
      }

      await connectDB();

      let dbUser = await User.findOne({ clerkId: userId });

      if (dbUser) {
        dbUser.name = fullName;
        await dbUser.save();

        console.log("User updated successfully:", dbUser);

        return NextResponse.json({
          message: "User updated successfully",
          user: { id: dbUser._id, name: dbUser.name, email: dbUser.email },
          status: "success",
        });
      } else {
        dbUser = await User.create({
          clerkId: userId,
          name: fullName,
          email,
        });

        console.log("User created successfully:", dbUser);

        return NextResponse.json(
          {
            message: "User created successfully",
            user: { id: dbUser._id, name: dbUser.name, email: dbUser.email },
            status: "success",
          },
          { status: 201 }
        );
      }
    } catch (clerkError) {
      console.error("Clerk API error:", clerkError);
      return NextResponse.json(
        { message: "Error fetching user information from Clerk", status: "error" },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error("Error creating/updating user:", error);
    return NextResponse.json(
      { message: error.message || "An error occurred", status: "error" },
      { status: 500 }
    );
  }
}