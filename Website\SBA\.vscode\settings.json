{"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.addMissingImports": "explicit"}, "prettier.tabWidth": 2, "prettier.useTabs": false, "prettier.semi": true, "prettier.singleQuote": true, "prettier.jsxSingleQuote": true, "prettier.trailingComma": "es5", "prettier.arrowParens": "always", "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}}