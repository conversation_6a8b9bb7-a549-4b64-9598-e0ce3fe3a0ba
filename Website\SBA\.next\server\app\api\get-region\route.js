/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/get-region/route";
exports.ids = ["app/api/get-region/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive":
/*!***********************************************************************************!*\
  !*** ./node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync ***!
  \***********************************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/require-in-the-middle sync recursive":
/*!**************************************************!*\
  !*** ./node_modules/require-in-the-middle/ sync ***!
  \**************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/require-in-the-middle sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "diagnostics_channel":
/*!**************************************!*\
  !*** external "diagnostics_channel" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("diagnostics_channel");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("perf_hooks");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "node:child_process":
/*!*************************************!*\
  !*** external "node:child_process" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:child_process");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:inspector":
/*!*********************************!*\
  !*** external "node:inspector" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:inspector");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:os":
/*!**************************!*\
  !*** external "node:os" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:os");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:readline":
/*!********************************!*\
  !*** external "node:readline" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:readline");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./app/api/get-region/route.ts":
/*!*************************************!*\
  !*** ./app/api/get-region/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   HEAD: () => (/* binding */ HEAD),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @sentry/nextjs */ \"(rsc)/./node_modules/@sentry/nextjs/build/cjs/index.server.js\");\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_sentry_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_client_components_request_async_storage_external_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/client/components/request-async-storage.external.js */ \"../../client/components/request-async-storage.external\");\n/* harmony import */ var next_dist_client_components_request_async_storage_external_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_request_async_storage_external_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n\n\n\n// Tell Next.js this route should be dynamically rendered\nconst dynamic = \"force-dynamic\";\nasync function GET$1(request) {\n    try {\n        const isDev = \"development\" === \"development\";\n        console.log(\"Environment:\", isDev ? \"development\" : \"production\");\n        const apiKey = process.env.IPGEOLOCATION_API_KEY;\n        if (!apiKey) {\n            console.error(\"API key not found in environment variables\");\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                country: \"United States\",\n                countryCode: \"US\",\n                currency: \"USD\",\n                symbol: \"$\"\n            }, {\n                status: 200\n            });\n        }\n        // Get client IP with fallback\n        let ip = request.headers.get(\"x-forwarded-for\") || request.headers.get(\"x-real-ip\") || \"\";\n        // Only use fallback IPs if we're on localhost and can't determine the real IP\n        if (!ip || ip === \"::1\" || ip === \"127.0.0.1\") {\n            // Check if we should use an Indian IP for testing (via environment variable)\n            const useIndianIP = process.env.USE_INDIAN_IP === \"true\";\n            if (useIndianIP) {\n                // Use an Indian IP address for testing INR pricing\n                ip = \"**************\"; // Indian IP\n                console.log(\"Using Indian IP for geolocation testing:\", ip);\n            } else {\n                // Use a public IP lookup service to get the real external IP\n                try {\n                    const ipifyResponse = await fetch(\"https://api.ipify.org?format=json\", {\n                        cache: \"no-store\"\n                    });\n                    if (ipifyResponse.ok) {\n                        const ipData = await ipifyResponse.json();\n                        ip = ipData.ip;\n                        console.log(\"Retrieved real external IP:\", ip);\n                    } else {\n                        // Fallback to US IP if we can't determine the real IP\n                        ip = \"*******\";\n                        console.log(\"Could not determine real IP, using fallback:\", ip);\n                    }\n                } catch (ipError) {\n                    // Fallback to US IP if the service fails\n                    ip = \"*******\";\n                    console.log(\"Error getting real IP, using fallback:\", ip);\n                }\n            }\n        } else {\n            console.log(\"Using client IP for geolocation:\", ip);\n        }\n        // If we have multiple IPs (from proxies), use the first one\n        if (ip.includes(\",\")) {\n            ip = ip.split(\",\")[0].trim();\n            console.log(\"Using first IP from proxy chain:\", ip);\n        }\n        try {\n            const response = await fetch(`https://api.ipgeolocation.io/ipgeo?apiKey=${apiKey}&ip=${ip}`, {\n                headers: {\n                    \"Accept\": \"application/json\"\n                },\n                cache: \"no-store\"\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(`IP Geolocation API error (${response.status}):`, errorText);\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                    country: \"United States\",\n                    countryCode: \"US\",\n                    currency: \"USD\",\n                    symbol: \"$\"\n                }, {\n                    status: 200\n                });\n            }\n            const data = await response.json();\n            console.log(\"Geolocation API response structure:\", Object.keys(data).join(\", \"), data.currency ? `Currency fields: ${Object.keys(data.currency).join(\", \")}` : \"No currency data\");\n            const currencyCode = data.currency && data.currency.code ? data.currency.code : \"USD\";\n            const currencySymbol = data.currency && data.currency.symbol ? data.currency.symbol : \"$\";\n            console.log(\"Geolocation data received for country:\", data.country_name, \"with currency:\", currencyCode);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                country: data.country_name || \"United States\",\n                countryCode: data.country_code2 || \"US\",\n                currency: currencyCode,\n                symbol: currencySymbol\n            }, {\n                status: 200\n            });\n        } catch (fetchError) {\n            console.error(\"Fetch error:\", fetchError instanceof Error ? fetchError.message : String(fetchError));\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                country: \"United States\",\n                countryCode: \"US\",\n                currency: \"USD\",\n                symbol: \"$\"\n            }, {\n                status: 200\n            });\n        }\n    } catch (error) {\n        console.error(\"Error in /api/get-region:\", error instanceof Error ? error.message : String(error));\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            country: \"United States\",\n            countryCode: \"US\",\n            currency: \"USD\",\n            symbol: \"$\"\n        }, {\n            status: 200\n        });\n    }\n}\n\nconst asyncStorageModule = { ...next_dist_client_components_request_async_storage_external_js__WEBPACK_IMPORTED_MODULE_0__ } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return _sentry_nextjs__WEBPACK_IMPORTED_MODULE_2__.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/get-region',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(GET$1 , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(undefined , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(undefined , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(undefined , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(undefined , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(undefined , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(undefined , 'OPTIONS');\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/get-region/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fget-region%2Froute&page=%2Fapi%2Fget-region%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fget-region%2Froute.ts&appDir=D%3A%5CWebDevelopment%5Ccoder%5CWebsite%5CSBA%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWebDevelopment%5Ccoder%5CWebsite%5CSBA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fget-region%2Froute&page=%2Fapi%2Fget-region%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fget-region%2Froute.ts&appDir=D%3A%5CWebDevelopment%5Ccoder%5CWebsite%5CSBA%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWebDevelopment%5Ccoder%5CWebsite%5CSBA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_WebDevelopment_coder_Website_SBA_app_api_get_region_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/get-region/route.ts */ \"(rsc)/./app/api/get-region/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/get-region/route\",\n        pathname: \"/api/get-region\",\n        filename: \"route\",\n        bundlePath: \"app/api/get-region/route\"\n    },\n    resolvedPagePath: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\app\\\\api\\\\get-region\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_WebDevelopment_coder_Website_SBA_app_api_get_region_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/get-region/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fget-region%2Froute&page=%2Fapi%2Fget-region%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fget-region%2Froute.ts&appDir=D%3A%5CWebDevelopment%5Ccoder%5CWebsite%5CSBA%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWebDevelopment%5Ccoder%5CWebsite%5CSBA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@sentry","vendor-chunks/semver","vendor-chunks/resolve","vendor-chunks/color-convert","vendor-chunks/debug","vendor-chunks/require-in-the-middle","vendor-chunks/@prisma","vendor-chunks/is-core-module","vendor-chunks/forwarded-parse","vendor-chunks/import-in-the-middle","vendor-chunks/color-name","vendor-chunks/ansi-styles","vendor-chunks/stacktrace-parser","vendor-chunks/supports-color","vendor-chunks/ms","vendor-chunks/shimmer","vendor-chunks/function-bind","vendor-chunks/path-parse","vendor-chunks/module-details-from-path","vendor-chunks/has-flag","vendor-chunks/hasown"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fget-region%2Froute&page=%2Fapi%2Fget-region%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fget-region%2Froute.ts&appDir=D%3A%5CWebDevelopment%5Ccoder%5CWebsite%5CSBA%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWebDevelopment%5Ccoder%5CWebsite%5CSBA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();