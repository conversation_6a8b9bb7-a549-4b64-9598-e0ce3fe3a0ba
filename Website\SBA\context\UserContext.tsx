'use client';
import React, { useState, createContext, useEffect, useCallback } from 'react';
import { useUser, useAuth } from '@clerk/nextjs';

interface UserContextProps {
  isLoggedIn: boolean;
  setIsLoggedIn: React.Dispatch<React.SetStateAction<boolean>>;
}

const UserContext = createContext<UserContextProps | undefined>(undefined);

const UserContextProvider = ({ children }) => {
  const { isLoaded, isSignedIn, user } = useUser();
  const { getToken } = useAuth();
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);

  // Function to sync login state with Clerk authentication
  const syncLoginState = useCallback(() => {
    const isAuthenticated = isLoaded && isSignedIn;
    setIsLoggedIn(isAuthenticated);
  }, [isLoaded, isSignedIn]);

  // Initial sync when Clerk loads authentication state
  useEffect(() => {
    syncLoginState();
  }, [syncLoginState]);

  // Add event listeners for visibility and focus changes
  useEffect(() => {
    // Handler for visibility change - refresh auth state when tab becomes visible
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        syncLoginState();
      }
    };

    // Handler for focus change - refresh auth state when window gains focus
    const handleFocus = () => {
      syncLoginState();
    };

    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    // Clean up event listeners
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [syncLoginState]);

  return (
    <UserContext.Provider value={{ isLoggedIn, setIsLoggedIn }}>
      {children}
    </UserContext.Provider>
  );
};

export { UserContext, UserContextProvider };
