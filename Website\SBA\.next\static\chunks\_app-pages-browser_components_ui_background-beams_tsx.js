"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_components_ui_background-beams_tsx"],{

/***/ "(app-pages-browser)/./components/ui/background-beams.tsx":
/*!********************************************!*\
  !*** ./components/ui/background-beams.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst BackgroundBeams = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(_c = (param)=>{\n    let { className } = param;\n    const paths = [\n        \"M-380 -189C-380 -189 -312 216 152 343C616 470 684 875 684 875\",\n        \"M-373 -197C-373 -197 -305 208 159 335C623 462 691 867 691 867\",\n        \"M-366 -205C-366 -205 -298 200 166 327C630 454 698 859 698 859\",\n        \"M-359 -213C-359 -213 -291 192 173 319C637 446 705 851 705 851\",\n        \"M-352 -221C-352 -221 -284 184 180 311C644 438 712 843 712 843\",\n        \"M-345 -229C-345 -229 -277 176 187 303C651 430 719 835 719 835\",\n        \"M-338 -237C-338 -237 -270 168 194 295C658 422 726 827 726 827\",\n        \"M-331 -245C-331 -245 -263 160 201 287C665 414 733 819 733 819\",\n        \"M-324 -253C-324 -253 -256 152 208 279C672 406 740 811 740 811\",\n        \"M-317 -261C-317 -261 -249 144 215 271C679 398 747 803 747 803\",\n        \"M-310 -269C-310 -269 -242 136 222 263C686 390 754 795 754 795\",\n        \"M-303 -277C-303 -277 -235 128 229 255C693 382 761 787 761 787\",\n        \"M-296 -285C-296 -285 -228 120 236 247C700 374 768 779 768 779\",\n        \"M-289 -293C-289 -293 -221 112 243 239C707 366 775 771 775 771\",\n        \"M-282 -301C-282 -301 -214 104 250 231C714 358 782 763 782 763\",\n        \"M-275 -309C-275 -309 -207 96 257 223C721 350 789 755 789 755\",\n        \"M-268 -317C-268 -317 -200 88 264 215C728 342 796 747 796 747\",\n        \"M-261 -325C-261 -325 -193 80 271 207C735 334 803 739 803 739\",\n        \"M-254 -333C-254 -333 -186 72 278 199C742 326 810 731 810 731\",\n        \"M-247 -341C-247 -341 -179 64 285 191C749 318 817 723 817 723\",\n        \"M-240 -349C-240 -349 -172 56 292 183C756 310 824 715 824 715\",\n        \"M-233 -357C-233 -357 -165 48 299 175C763 302 831 707 831 707\",\n        \"M-226 -365C-226 -365 -158 40 306 167C770 294 838 699 838 699\",\n        \"M-219 -373C-219 -373 -151 32 313 159C777 286 845 691 845 691\",\n        \"M-212 -381C-212 -381 -144 24 320 151C784 278 852 683 852 683\",\n        \"M-205 -389C-205 -389 -137 16 327 143C791 270 859 675 859 675\",\n        \"M-198 -397C-198 -397 -130 8 334 135C798 262 866 667 866 667\",\n        \"M-191 -405C-191 -405 -123 0 341 127C805 254 873 659 873 659\",\n        \"M-184 -413C-184 -413 -116 -8 348 119C812 246 880 651 880 651\",\n        \"M-177 -421C-177 -421 -109 -16 355 111C819 238 887 643 887 643\",\n        \"M-170 -429C-170 -429 -102 -24 362 103C826 230 894 635 894 635\",\n        \"M-163 -437C-163 -437 -95 -32 369 95C833 222 901 627 901 627\",\n        \"M-156 -445C-156 -445 -88 -40 376 87C840 214 908 619 908 619\",\n        \"M-149 -453C-149 -453 -81 -48 383 79C847 206 915 611 915 611\",\n        \"M-142 -461C-142 -461 -74 -56 390 71C854 198 922 603 922 603\",\n        \"M-135 -469C-135 -469 -67 -64 397 63C861 190 929 595 929 595\",\n        \"M-128 -477C-128 -477 -60 -72 404 55C868 182 936 587 936 587\",\n        \"M-121 -485C-121 -485 -53 -80 411 47C875 174 943 579 943 579\",\n        \"M-114 -493C-114 -493 -46 -88 418 39C882 166 950 571 950 571\",\n        \"M-107 -501C-107 -501 -39 -96 425 31C889 158 957 563 957 563\",\n        \"M-100 -509C-100 -509 -32 -104 432 23C896 150 964 555 964 555\",\n        \"M-93 -517C-93 -517 -25 -112 439 15C903 142 971 547 971 547\",\n        \"M-86 -525C-86 -525 -18 -120 446 7C910 134 978 539 978 539\",\n        \"M-79 -533C-79 -533 -11 -128 453 -1C917 126 985 531 985 531\",\n        \"M-72 -541C-72 -541 -4 -136 460 -9C924 118 992 523 992 523\",\n        \"M-65 -549C-65 -549 3 -144 467 -17C931 110 999 515 999 515\",\n        \"M-58 -557C-58 -557 10 -152 474 -25C938 102 1006 507 1006 507\",\n        \"M-51 -565C-51 -565 17 -160 481 -33C945 94 1013 499 1013 499\",\n        \"M-44 -573C-44 -573 24 -168 488 -41C952 86 1020 491 1020 491\",\n        \"M-37 -581C-37 -581 31 -176 495 -49C959 78 1027 483 1027 483\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute  h-full w-full inset-0  [mask-size:40px] [mask-repeat:no-repeat] flex items-center justify-center\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \" pointer-events-none absolute z-0 size-full \",\n            width: \"100%\",\n            height: \"100%\",\n            viewBox: \"0 0 696 316\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M-380 -189C-380 -189 -312 216 152 343C616 470 684 875 684 875M-373 -197C-373 -197 -305 208 159 335C623 462 691 867 691 867M-366 -205C-366 -205 -298 200 166 327C630 454 698 859 698 859M-359 -213C-359 -213 -291 192 173 319C637 446 705 851 705 851M-352 -221C-352 -221 -284 184 180 311C644 438 712 843 712 843M-345 -229C-345 -229 -277 176 187 303C651 430 719 835 719 835M-338 -237C-338 -237 -270 168 194 295C658 422 726 827 726 827M-331 -245C-331 -245 -263 160 201 287C665 414 733 819 733 819M-324 -253C-324 -253 -256 152 208 279C672 406 740 811 740 811M-317 -261C-317 -261 -249 144 215 271C679 398 747 803 747 803M-310 -269C-310 -269 -242 136 222 263C686 390 754 795 754 795M-303 -277C-303 -277 -235 128 229 255C693 382 761 787 761 787M-296 -285C-296 -285 -228 120 236 247C700 374 768 779 768 779M-289 -293C-289 -293 -221 112 243 239C707 366 775 771 775 771M-282 -301C-282 -301 -214 104 250 231C714 358 782 763 782 763M-275 -309C-275 -309 -207 96 257 223C721 350 789 755 789 755M-268 -317C-268 -317 -200 88 264 215C728 342 796 747 796 747M-261 -325C-261 -325 -193 80 271 207C735 334 803 739 803 739M-254 -333C-254 -333 -186 72 278 199C742 326 810 731 810 731M-247 -341C-247 -341 -179 64 285 191C749 318 817 723 817 723M-240 -349C-240 -349 -172 56 292 183C756 310 824 715 824 715M-233 -357C-233 -357 -165 48 299 175C763 302 831 707 831 707M-226 -365C-226 -365 -158 40 306 167C770 294 838 699 838 699M-219 -373C-219 -373 -151 32 313 159C777 286 845 691 845 691M-212 -381C-212 -381 -144 24 320 151C784 278 852 683 852 683M-205 -389C-205 -389 -137 16 327 143C791 270 859 675 859 675M-198 -397C-198 -397 -130 8 334 135C798 262 866 667 866 667M-191 -405C-191 -405 -123 0 341 127C805 254 873 659 873 659M-184 -413C-184 -413 -116 -8 348 119C812 246 880 651 880 651M-177 -421C-177 -421 -109 -16 355 111C819 238 887 643 887 643M-170 -429C-170 -429 -102 -24 362 103C826 230 894 635 894 635M-163 -437C-163 -437 -95 -32 369 95C833 222 901 627 901 627M-156 -445C-156 -445 -88 -40 376 87C840 214 908 619 908 619M-149 -453C-149 -453 -81 -48 383 79C847 206 915 611 915 611M-142 -461C-142 -461 -74 -56 390 71C854 198 922 603 922 603M-135 -469C-135 -469 -67 -64 397 63C861 190 929 595 929 595M-128 -477C-128 -477 -60 -72 404 55C868 182 936 587 936 587M-121 -485C-121 -485 -53 -80 411 47C875 174 943 579 943 579M-114 -493C-114 -493 -46 -88 418 39C882 166 950 571 950 571M-107 -501C-107 -501 -39 -96 425 31C889 158 957 563 957 563M-100 -509C-100 -509 -32 -104 432 23C896 150 964 555 964 555M-93 -517C-93 -517 -25 -112 439 15C903 142 971 547 971 547M-86 -525C-86 -525 -18 -120 446 7C910 134 978 539 978 539M-79 -533C-79 -533 -11 -128 453 -1C917 126 985 531 985 531M-72 -541C-72 -541 -4 -136 460 -9C924 118 992 523 992 523M-65 -549C-65 -549 3 -144 467 -17C931 110 999 515 999 515M-58 -557C-58 -557 10 -152 474 -25C938 102 1006 507 1006 507M-51 -565C-51 -565 17 -160 481 -33C945 94 1013 499 1013 499M-44 -573C-44 -573 24 -168 488 -41C952 86 1020 491 1020 491M-37 -581C-37 -581 31 -176 495 -49C959 78 1027 483 1027 483M-30 -589C-30 -589 38 -184 502 -57C966 70 1034 475 1034 475M-23 -597C-23 -597 45 -192 509 -65C973 62 1041 467 1041 467M-16 -605C-16 -605 52 -200 516 -73C980 54 1048 459 1048 459M-9 -613C-9 -613 59 -208 523 -81C987 46 1055 451 1055 451M-2 -621C-2 -621 66 -216 530 -89C994 38 1062 443 1062 443M5 -629C5 -629 73 -224 537 -97C1001 30 1069 435 1069 435M12 -637C12 -637 80 -232 544 -105C1008 22 1076 427 1076 427M19 -645C19 -645 87 -240 551 -113C1015 14 1083 419 1083 419\",\n                    stroke: \"url(#paint0_radial_242_278)\",\n                    strokeOpacity: \"0.05\",\n                    strokeWidth: \"0.5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\components\\\\ui\\\\background-beams.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, undefined),\n                paths.map((path, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.path, {\n                        d: path,\n                        stroke: \"url(#linearGradient-\".concat(index, \")\"),\n                        strokeOpacity: \"0.4\",\n                        strokeWidth: \"0.5\"\n                    }, \"path-\" + index, false, {\n                        fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\components\\\\ui\\\\background-beams.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, undefined)),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                    children: [\n                        paths.map((path, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.linearGradient, {\n                                id: \"linearGradient-\".concat(index),\n                                initial: {\n                                    x1: \"0%\",\n                                    x2: \"0%\",\n                                    y1: \"0%\",\n                                    y2: \"0%\"\n                                },\n                                animate: {\n                                    x1: [\n                                        \"0%\",\n                                        \"100%\"\n                                    ],\n                                    x2: [\n                                        \"0%\",\n                                        \"95%\"\n                                    ],\n                                    y1: [\n                                        \"0%\",\n                                        \"100%\"\n                                    ],\n                                    y2: [\n                                        \"0%\",\n                                        \"\".concat(93 + Math.random() * 8, \"%\")\n                                    ]\n                                },\n                                transition: {\n                                    duration: Math.random() * 10 + 10,\n                                    ease: \"easeInOut\",\n                                    repeat: Infinity,\n                                    delay: Math.random() * 10\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        stopColor: \"#18CCFC\",\n                                        stopOpacity: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\components\\\\ui\\\\background-beams.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        stopColor: \"#18CCFC\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\components\\\\ui\\\\background-beams.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"32.5%\",\n                                        stopColor: \"#6344F5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\components\\\\ui\\\\background-beams.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"100%\",\n                                        stopColor: \"#AE48FF\",\n                                        stopOpacity: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\components\\\\ui\\\\background-beams.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, \"gradient-\".concat(index), true, {\n                                fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\components\\\\ui\\\\background-beams.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"radialGradient\", {\n                            id: \"paint0_radial_242_278\",\n                            cx: \"0\",\n                            cy: \"0\",\n                            r: \"1\",\n                            gradientUnits: \"userSpaceOnUse\",\n                            gradientTransform: \"translate(352 34) rotate(90) scale(555 1560.62)\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                    offset: \"0.0666667\",\n                                    stopColor: \"var(--neutral-300)\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\components\\\\ui\\\\background-beams.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                    offset: \"0.243243\",\n                                    stopColor: \"var(--neutral-300)\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\components\\\\ui\\\\background-beams.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                    offset: \"0.43594\",\n                                    stopColor: \"white\",\n                                    stopOpacity: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\components\\\\ui\\\\background-beams.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\components\\\\ui\\\\background-beams.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\components\\\\ui\\\\background-beams.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\components\\\\ui\\\\background-beams.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\components\\\\ui\\\\background-beams.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = BackgroundBeams;\nBackgroundBeams.displayName = \"BackgroundBeams\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (BackgroundBeams);\nvar _c, _c1;\n$RefreshReg$(_c, \"BackgroundBeams$React.memo\");\n$RefreshReg$(_c1, \"BackgroundBeams\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/background-beams.tsx\n"));

/***/ })

}]);