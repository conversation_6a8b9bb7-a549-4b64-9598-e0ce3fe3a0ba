// pages/api/auth/resend-verification-code.ts
import crypto from "crypto";

import { NextApiRequest, NextApiResponse } from "next";

import { sendVerificationCode } from "@/lib/email"; // Your email sending utility
import connectDB from "@/lib/mongodb"; // Your MongoDB connection utility
import { PendingUser } from "@/model/PendingUser"; // Your PendingUser model

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Restrict to POST requests
  if (req.method !== "POST") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  // Connect to the database
  await connectDB();

  // Extract email from request body
  const { email } = req.body;
  if (!email) {
    return res.status(400).json({ message: "Email is required" });
  }

  try {
    // Find the pending user by email
    const pendingUser = await PendingUser.findOne({ email });
    if (!pendingUser) {
      return res.status(404).json({ message: "No pending registration found for this email" });
    }

    // Generate a new verification code (e.g., 8-character hexadecimal)
    const verificationCode = crypto.randomBytes(4).toString("hex").toUpperCase();
    // Set expiration time (15 minutes from now)
    const verificationCodeExpires = new Date(Date.now() + 15 * 60 * 1000);

    // Update the pending user record
    pendingUser.verificationCode = verificationCode;
    pendingUser.verificationCodeExpires = verificationCodeExpires;
    await pendingUser.save();

    // Send the new verification email
    await sendVerificationCode(email, verificationCode);

    // Respond with success
    return res.status(200).json({ message: "Verification code resent successfully" });
  } catch (error) {
    console.error("Resend verification code error:", error);
    return res.status(500).json({ message: "Internal server error" });
  }
}