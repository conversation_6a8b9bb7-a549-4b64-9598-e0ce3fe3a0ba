'use client';

import { Button, Input, Textarea } from '@nextui-org/react';
import * as Sentry from '@sentry/nextjs';
import { Bitter } from 'next/font/google';
import { useState } from 'react';
import { FaEnvelope, FaLocationDot } from 'react-icons/fa6';
import { toast } from 'sonner';
import { z } from 'zod';

const bitter = Bitter({ subsets: ['latin'] });

const contactSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  name: z.string().min(1, 'Name is required'),
  subject: z.string().min(1, 'Subject is required'),
  message: z.string().min(10, 'Message should be at least 10 characters long'),
});

export default function Contact() {
  const [formData, setFormData] = useState({
    email: '',
    name: '',
    subject: '',
    message: '',
  });

  const [errors, setErrors] = useState({
    email: '',
    name: '',
    subject: '',
    message: '',
  });

  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    const validation = contactSchema.safeParse(formData);
    if (!validation.success) {
      const formErrors = validation.error.flatten().fieldErrors;
      setErrors({
        email: formErrors.email?.[0] || '',
        name: formErrors.name?.[0] || '',
        subject: formErrors.subject?.[0] || '',
        message: formErrors.message?.[0] || '',
      });
      setIsLoading(false);
      return;
    }

    try {
      const res = await fetch('/api/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (res.ok) {
        toast.success('Email sent successfully! We’ve also sent you a confirmation.');
      } else {
        const text = await res.text();
        let errorData;
        try {
          errorData = JSON.parse(text);
        } catch {
          errorData = { error: text || 'Unknown error' };
        }
        toast.error(`Failed to send email: ${errorData.error}`);
      }
    } catch (error: any) {
      console.error('Error occurred while sending email:', error);
      toast.error('An error occurred. Please try again.');
      Sentry.captureException(error);
    } finally {
      setFormData({ email: '', name: '', subject: '', message: '' });
      setErrors({ email: '', name: '', subject: '', message: '' });
      setIsLoading(false);
    }
  };

  return (
    <div className="mx-auto flex flex-col items-center justify-center overflow-hidden bg-black-100">
      <div className="py-20 sm:py-28 md:px-8">
        <h1 className={`${bitter.className} hidden px-8 text-center sm:block sm:text-4xl lg:text-left`}>
          Get In Touch
          <span className="text-[#EF4444] sm:text-5xl xl:text-[52px]">.</span>
        </h1>
        <h1 className="mt-8 px-8 text-center text-4xl font-medium sm:hidden md:text-left">
          Get In Touch
          <span className="text-[#EF4444] sm:text-5xl lg:text-[52px]">.</span>
        </h1>

        <div className="grid grid-cols-1 gap-10 sm:px-4 md:grid-cols-2 xl:max-w-5xl 2xl:max-w-6xl">
          <div className="px-5 pb-2 sm:px-4 sm:py-2">
            <form className="mt-12" onSubmit={handleSubmit}>
              <div className="relative mt-4">
                <Input
                  size="sm"
                  type="email"
                  isRequired
                  name="email"
                  label="Email"
                  value={formData.email}
                  onChange={handleChange}
                  aria-label="Your email address"
                />
                {errors.email && <div className="text-sm text-red-500">{errors.email}</div>}
              </div>
              <div className="relative mt-8">
                <Input
                  size="sm"
                  type="text"
                  isRequired
                  name="name"
                  label="Your Name"
                  value={formData.name}
                  onChange={handleChange}
                  aria-label="Your full name"
                />
                {errors.name && <div className="text-sm text-red-500">{errors.name}</div>}
              </div>
              <div className="relative mt-8">
                <Input
                  size="sm"
                  type="text"
                  isRequired
                  name="subject"
                  label="Subject"
                  value={formData.subject}
                  onChange={handleChange}
                  aria-label="Subject of your message"
                />
                {errors.subject && <div className="text-sm text-red-500">{errors.subject}</div>}
              </div>
              <div className="relative mt-8">
                <Textarea
                  isRequired
                  name="message"
                  label="Message"
                  value={formData.message}
                  placeholder="Description of the problem or Help regarding any feature"
                  onChange={handleChange}
                  aria-label="Your message"
                  minRows={20}
                />
                {errors.message && <div className="text-sm text-red-500">{errors.message}</div>}
              </div>
              <div className="relative mt-8 md:mt-10">
                <Button
                  color="primary"
                  type="submit"
                  variant="shadow"
                  className="w-full text-sm"
                  isDisabled={isLoading}
                >
                  {isLoading ? 'Sending...' : 'Send Message 🚀'}
                </Button>
              </div>
            </form>
          </div>

          <div className="px-5 py-3 sm:px-4 md:mt-5">
            <div className="mb-7 rounded-xl p-4">
              <h2 className="text-center text-2xl font-medium sm:text-left">Contact Details</h2>
              <div className="mt-8 flex flex-col gap-6 sm:mt-6">
                <div className="inline-flex items-center gap-7 text-slate-300">
                  <FaLocationDot className="size-10 sm:size-6" />
                  <p className="text-pretty tracking-tight">
                    MSR Boys Home 6, 7th Main Rd, M S Ramaiah Nagar, Mathikere, Bengaluru, Karnataka 560054
                  </p>
                </div>
                <a
                  href="mailto:<EMAIL>"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="<EMAIL>"
                  className="inline-flex items-center gap-5 text-slate-300"
                >
                  <FaEnvelope className="size-5" /> <EMAIL>
                </a>
                <a
                  href="mailto:<EMAIL>"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="<EMAIL>"
                  className="inline-flex items-center gap-5 text-slate-300"
                >
                  <FaEnvelope className="size-5" /> <EMAIL>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}