import { getAuth } from '@clerk/nextjs/server';
import paypal from '@paypal/checkout-server-sdk';
import { NextApiRequest, NextApiResponse } from 'next';

import connectDB from '@/lib/mongodb';


// Validate PayPal credentials
const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID;
const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET;

if (!PAYPAL_CLIENT_ID || !PAYPAL_CLIENT_SECRET) {
  console.error('PayPal credentials are missing. Check your environment variables.');
}

// Log the first few characters of credentials for debugging (don't log full credentials)
console.log('PayPal Client ID (first 5 chars):', PAYPAL_CLIENT_ID ? PAYPAL_CLIENT_ID.substring(0, 5) + '...' : 'undefined');
console.log('PayPal Client Secret (first 5 chars):', PAYPAL_CLIENT_SECRET ? PAYPAL_CLIENT_SECRET.substring(0, 5) + '...' : 'undefined');

// Use sandbox environment for development, live for production
const isDev = process.env.NODE_ENV === 'development';
console.log('PayPal Environment:', isDev ? 'Sandbox' : 'Live');

let environment: paypal.core.SandboxEnvironment | paypal.core.LiveEnvironment;
let client: paypal.core.PayPalHttpClient | null = null;

try {
  if (isDev) {
    environment = new paypal.core.SandboxEnvironment(PAYPAL_CLIENT_ID!, PAYPAL_CLIENT_SECRET!);
  } else {
    environment = new paypal.core.LiveEnvironment(PAYPAL_CLIENT_ID!, PAYPAL_CLIENT_SECRET!);
  }
  client = new paypal.core.PayPalHttpClient(environment);
} catch (error) {
  console.error('Error initializing PayPal client:', error);
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  // Check if PayPal client is initialized
  if (!client) {
    console.error('PayPal client is not initialized');
    return res.status(500).json({
      success: false,
      message: 'PayPal integration is not configured properly. Please contact support.'
    });
  }

  // Use getAuth() for Clerk authentication in API routes
  const { userId } = getAuth(req);
  if (!userId) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  try {
    await connectDB();
    const { plan, os } = req.body;
    if (!plan) {
      return res.status(400).json({ success: false, message: 'Plan is required' });
    }

    // Define pricing for plans
    const pricing = {
      weekly: 7,
      monthly: 7,
      yearly: 150
    };

    const amount = pricing[plan as keyof typeof pricing];
    if (!amount) {
      return res.status(400).json({ success: false, message: 'Invalid plan' });
    }

    // Get the base URL from the request or environment variable
    const protocol = req.headers['x-forwarded-proto'] || 'http';
    const host = req.headers.host || 'localhost:3000';
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || `${protocol}://${host}`;

    // Create PayPal order with properly formed URLs
    const request = new paypal.orders.OrdersCreateRequest();
    request.prefer('return=representation');
    request.requestBody({
      intent: 'CAPTURE',
      purchase_units: [
        {
          amount: {
            currency_code: 'USD',
            value: amount.toString()
          },
          description: `${plan.charAt(0).toUpperCase() + plan.slice(1)} Plan Subscription`
        }
      ],
      application_context: {
        return_url: `${baseUrl}/api/paypal/capture-order?os=${encodeURIComponent(os || 'Unknown')}`,
        cancel_url: `${baseUrl}/pricing?payment=cancelled`
      }
    });

    console.log('PayPal URLs:', {
      returnUrl: `${baseUrl}/api/paypal/capture-order?os=${encodeURIComponent(os || 'Unknown')}`,
      cancelUrl: `${baseUrl}/pricing?payment=cancelled`
    });

    const order = await client.execute(request);

    // Return the order ID and approvalLink to the client
    const approvalLink = order.result.links.find((link: { rel: string }) => link.rel === 'approve')?.href;

    return res.status(200).json({
      success: true,
      orderId: order.result.id,
      approvalLink
    });
  } catch (error) {
    console.error('Create PayPal order error:', error);
    // Provide more detailed error message
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return res.status(500).json({
      success: false,
      message: 'Failed to create PayPal order',
      error: errorMessage
    });
  }
}