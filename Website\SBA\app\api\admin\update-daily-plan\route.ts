import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";

import { authOptions } from "@/lib/auth";
import connectDB from "@/lib/mongodb";
import { User } from "@/model/User";

export async function POST(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user || !(session.user as any).isAdmin) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const { newLimit } = await req.json();
        if (typeof newLimit !== 'number' || newLimit < 1) {
            return NextResponse.json({ error: "Invalid limit value" }, { status: 400 });
        }

        await connectDB();
        
        // Update the daily limit for all free users
        const result = await User.updateMany(
            { subscriptionType: "free" },
            { $set: { dailyPromptLimit: newLimit } }
        );

        return NextResponse.json({ 
            success: true, 
            message: "Daily prompt limit updated successfully",
            updatedCount: result.modifiedCount
        });
    } catch (error) {
        console.error("Error updating daily plan:", error);
        return NextResponse.json({ error: "Internal server error" }, { status: 500 });
    }
}