import { getAuth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";

import connectDB from "@/lib/mongodb";
import { User } from "@/model/User";

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

// This route now serves as a fallback and just returns basic user info
export async function GET(req: NextRequest) {
  try {
    const { userId } = getAuth(req);
    
    if (!userId) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    await connectDB();
    
    const user = await User.findOne({ clerkId: userId });
    if (!user) {
      return NextResponse.json({ message: "User not found" }, { status: 404 });
    }
    
    return NextResponse.json({ 
      user: {
        name: user.name,
        email: user.email,
        role: user.role
      }, 
      status: 'success' 
    });
  } catch (error) {
    console.error('Error fetching user data:', error);
    return NextResponse.json({ error, status: 'error' }, { status: 500 });
  }
}
