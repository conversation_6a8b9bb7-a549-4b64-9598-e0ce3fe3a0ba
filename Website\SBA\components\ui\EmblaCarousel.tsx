'use client';
import { CardActionArea } from '@mui/material';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CssBaseline from '@mui/material/CssBaseline';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { EmblaCarouselType, EmblaOptionsType } from 'embla-carousel';
import useEmblaCarousel from 'embla-carousel-react';
import Image from 'next/image';
import React, { useCallback, useEffect, useState } from 'react';

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/Tooltip';
import { ServiceCards } from '@/data';

import {
  NextButton,
  PrevButton,
  usePrevNextButtons,
} from './EmblaCarouselArrowButtons';

type PropType = {
  slides: number[];
  options?: EmblaOptionsType;
};

const darkTheme = createTheme({
  palette: {
    mode: 'dark',
  },
});

const EmblaCarousel: React.FC<PropType> = (props) => {
  const { options } = props;
  const [emblaRef, emblaApi] = useEmblaCarousel(options);
  const [scrollProgress, setScrollProgress] = useState(0);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);

  const onScroll = useCallback((emblaApi: EmblaCarouselType) => {
    const progress = Math.max(0, Math.min(1, emblaApi.scrollProgress()));
    setScrollProgress(progress * 100);
  }, []);

  useEffect(() => {
    if (!emblaApi) return;

    onScroll(emblaApi);
    emblaApi
      .on('reInit', onScroll)
      .on('scroll', onScroll)
      .on('slideFocus', onScroll);
  }, [emblaApi, onScroll]);

  return (
    <div className='embla lg:margin-auto'>
      <div className='embla__viewport' ref={emblaRef}>
        <div className='embla__container'>
          {ServiceCards.map((el) => (
            <div className='embla__slide' key={el.id}>
              <ThemeProvider theme={darkTheme}>
                <CssBaseline />
                <Card
                  sx={{
                    maxWidth: 350,
                    backgroundColor: 'black',
                    color: 'white',
                    border: '1px solid #252525',
                    borderRadius: '6px',
                    paddingBottom: '10px',
                  }}
                >
                  <CardActionArea>
                    <div className='relative h-48 w-full overflow-hidden rounded-t-md'>
                      <Image
                        src={el.imgSrc}
                        alt='services images'
                        className='pointer-events-none absolute size-full select-none rounded-t-md object-cover'
                        width={1000}
                        height={600}
                        loading='lazy'
                      />
                    </div>

                    <CardContent>
                      <Typography gutterBottom variant='h5' component='div'>
                        {el.title}
                      </Typography>
                      <Typography variant='body2' color='text.secondary'>
                        {el.description}
                      </Typography>
                    </CardContent>
                  </CardActionArea>
                </Card>
              </ThemeProvider>
            </div>
          ))}
        </div>
      </div>

      <div className='embla__controls'>
        <div className='embla__buttons'>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div>
                  <PrevButton
                    onClick={onPrevButtonClick}
                    disabled={prevBtnDisabled}
                  />
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Previous</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div>
                  <NextButton
                    onClick={onNextButtonClick}
                    disabled={nextBtnDisabled}
                  />
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Next</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <div className='embla__progress'>
          <div
            className='embla__progress__bar'
            style={{ transform: `translate3d(${scrollProgress}%,0px,0px)` }}
          />
        </div>
      </div>
    </div>
  );
};

export default EmblaCarousel;
