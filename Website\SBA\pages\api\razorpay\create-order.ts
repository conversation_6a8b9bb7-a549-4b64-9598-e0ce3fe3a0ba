import { getAuth } from "@clerk/nextjs/server";
import type { NextApiRequest, NextApiResponse } from "next";
import Razorpay from "razorpay";

import connectDB from "@/lib/mongodb";
import { User } from "@/model/User";

// Validate environment variables
const RAZORPAY_KEY_ID = process.env.RAZORPAY_KEY_ID;
const RAZORPAY_KEY_SECRET = process.env.RAZORPAY_KEY_SECRET;

if (!RAZORPAY_KEY_ID || !RAZORPAY_KEY_SECRET) {
    console.error("Razorpay configuration missing. Check RAZORPAY_KEY_ID and RAZORPAY_KEY_SECRET env variables.");
}

// Create Razorpay instance
const razorpay = RAZORPAY_KEY_ID && RAZORPAY_KEY_SECRET
    ? new Razorpay({
          key_id: RAZORPAY_KEY_ID,
          key_secret: RAZ<PERSON>PAY_KEY_SECRET,
      })
    : null;

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== "POST") {
        return res.status(405).json({ success: false, message: "Method not allowed" });
    }

    if (!razorpay) {
        return res.status(500).json({
            success: false,
            message: "Payment gateway configuration error. Please contact support.",
        });
    }

    // Ensure this runs on the server using the Pages Router compatible approach
    const { userId } = getAuth(req);
    if (!userId) {
        return res.status(401).json({ success: false, message: "Unauthorized" });
    }

    try {
        await connectDB();
        const { plan, os } = req.body;

        if (!plan) {
            return res.status(400).json({ success: false, message: "Plan is required" });
        }

        const usdToInr = 85.26;
        let amountInUsd: number;

        // Pricing logic (simplified since it's the same for all OS in your example)
        switch (plan) {
            case "weekly":
                amountInUsd = 7;
                break;
            case "monthly":
                amountInUsd = 7;
                break;
            case "yearly":
                amountInUsd = 150;
                break;
            default:
                return res.status(400).json({ success: false, message: "Invalid plan" });
        }

        // Get user to check if they're from India
        const user = await User.findOne({ clerkId: userId });
        const isIndianUser = user?.countryCode === 'IN';
        console.log('User country code:', user?.countryCode, 'isIndianUser:', isIndianUser);

        // For Indian users, we'll show prices in INR
        const amountInPaise = Math.round(amountInUsd * usdToInr * 100);

        const order = await razorpay.orders.create({
            amount: amountInPaise,
            currency: "INR",
            receipt: `receipt_${Date.now()}`,
            payment_capture: true,
        });

        return res.status(200).json({
            success: true,
            orderId: order.id,
            amount: isIndianUser ? Math.round(amountInUsd * usdToInr) : amountInUsd,
            currency: isIndianUser ? "INR" : "USD",
            plan,
            os,
        });
    } catch (error: any) {
        console.error("Create order error:", error);
        return res.status(500).json({
            success: false,
            message: "Internal server error",
            error: error.message,
        });
    }
}

// Explicitly mark this file as server-only (optional, for clarity)
export const config = {
    api: {
        // Optional: Add any API-specific configurations here if needed
    },
};