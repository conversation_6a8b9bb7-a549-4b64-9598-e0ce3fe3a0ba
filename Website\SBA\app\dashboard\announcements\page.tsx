"use client";

import { useUser } from "@clerk/nextjs";
import { Button, Input, Card, Table, TableHeader, TableColumn, TableBody, TableRow, TableCell, Pagination, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, useDisclosure, Switch, Select, SelectItem, Textarea, Spinner } from "@nextui-org/react";
import { format } from 'date-fns';
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { FiX, FiEdit, FiTrash2, FiBell, FiPlus, FiCheck, FiCalendar, FiAlertCircle } from "react-icons/fi";
import { toast } from "sonner";

interface Announcement {
    _id: string;
    title: string;
    content: string;
    type: 'update' | 'newFeature' | 'bugFix' | 'maintenance' | 'currentBugs' | 'upcomingFeatures' | 'other';
    publishDate: string;
    expiryDate?: string;
    isImportant: boolean;
    createdBy: string;
    createdAt: string;
    updatedAt: string;
}


// eslint-disable-next-line no-unused-vars
const getTypeLabel = (type: string) => {
    switch (type) {
        case 'newFeature': return 'New Feature';
        case 'bugFix': return 'Bug Fix';
        case 'currentBugs': return 'Current Bugs';
        case 'upcomingFeatures': return 'Upcoming Features';
        default: return type.charAt(0).toUpperCase() + type.slice(1);
    }
};

export default function AnnouncementsAdminPage() {
    const { isLoaded, isSignedIn } = useUser();
    const router = useRouter();
    const [isAdmin, setIsAdmin] = useState(false);
    const [checkingAdmin, setCheckingAdmin] = useState(true);
    const [announcements, setAnnouncements] = useState<Announcement[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [page, setPage] = useState(1);
    const rowsPerPage = 5;
    const [totalPages, setTotalPages] = useState(1);

    // For create/edit modal
    const { isOpen, onOpen, onClose } = useDisclosure();
    const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
    const [currentAnnouncement, setCurrentAnnouncement] = useState<Partial<Announcement> | null>(null);

    // Form fields
    const [title, setTitle] = useState("");
    const [content, setContent] = useState("");
    const [type, setType] = useState<string>("update");
    const [publishDate, setPublishDate] = useState("");
    const [expiryDate, setExpiryDate] = useState("");
    const [isImportant, setIsImportant] = useState(false);

    // Delete modal
    const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();
    const [announcementToDelete, setAnnouncementToDelete] = useState<Announcement | null>(null);

    useEffect(() => {
        // Ensure we only run on the client side
        if (typeof window === 'undefined') return;

        const checkAdminStatus = async () => {
            if (!isLoaded || !isSignedIn) return;

            try {
                // Add cache-busting parameter and specific headers
                const response = await fetch('/api/user/me?' + new URLSearchParams({
                    _ts: Date.now().toString()
                }), {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-invoke-app-router': 'true'
                    },
                    credentials: 'include'
                });

                if (response.ok) {
                    const userData = await response.json();
                    if (userData && userData.isAdmin === true) {
                        setIsAdmin(true);
                        // Fetch announcements once verified as admin
                        fetchAnnouncements();
                    } else {
                        router.push("/dashboard");
                    }
                } else {
                    router.push("/dashboard");
                }
            } catch (error) {
                console.error("Error checking admin status:", error);
                router.push("/dashboard");
            } finally {
                setCheckingAdmin(false);
            }
        };

        if (isLoaded && isSignedIn) {
            checkAdminStatus();
        } else if (isLoaded && !isSignedIn) {
            router.push("/login");
        }
    }, [isLoaded, isSignedIn, router]);

    const fetchAnnouncements = async (pageNum: number = 1) => {
        try {
            setIsLoading(true);
            const response = await fetch(`/api/announcements?page=${pageNum}&limit=${rowsPerPage}`);
            if (!response.ok) throw new Error('Failed to fetch announcements');

            const data = await response.json();
            setAnnouncements(data.announcements);
            setTotalPages(data.pagination.totalPages);
            setPage(pageNum);
        } catch (error) {
            console.error("Error fetching announcements:", error);
            toast.error("Failed to load announcements");
        } finally {
            setIsLoading(false);
        }
    };

    const openCreateModal = () => {
        // Reset form fields
        setTitle("");
        setContent("");
        setType("update");
        setPublishDate(format(new Date(), 'yyyy-MM-dd'));
        setExpiryDate("");
        setIsImportant(false);

        setModalMode('create');
        setCurrentAnnouncement(null);
        onOpen();
    };

    const openEditModal = (announcement: Announcement) => {
        setTitle(announcement.title);
        setContent(announcement.content);
        setType(announcement.type);
        setPublishDate(format(new Date(announcement.publishDate), 'yyyy-MM-dd'));
        setExpiryDate(announcement.expiryDate ? format(new Date(announcement.expiryDate), 'yyyy-MM-dd') : "");
        setIsImportant(announcement.isImportant);

        setModalMode('edit');
        setCurrentAnnouncement(announcement);
        onOpen();
    };

    const handleSaveAnnouncement = async () => {
        if (!title || !content) {
            toast.error("Please fill in all required fields");
            return;
        }

        try {
            setIsLoading(true);

            const announcementData = {
                title,
                content,
                type,
                publishDate: new Date(publishDate).toISOString(),
                expiryDate: expiryDate ? new Date(expiryDate).toISOString() : undefined,
                isImportant
            };

            let response;
            if (modalMode === 'create') {
                // Create new announcement
                response = await fetch('/api/announcements', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(announcementData),
                });
            } else {
                // Update existing announcement
                response = await fetch(`/api/announcements/${currentAnnouncement?._id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(announcementData),
                });
            }

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || `Failed to ${modalMode} announcement`);
            }

            toast.success(`Announcement ${modalMode === 'create' ? 'created' : 'updated'} successfully!`);
            onClose();
            fetchAnnouncements(page); // Refresh the announcements list
        } catch (error: any) {
            console.error(`Error ${modalMode}ing announcement:`, error);
            toast.error(error.message || `Failed to ${modalMode} announcement`);
        } finally {
            setIsLoading(false);
        }
    };

    const handleDeleteConfirm = (announcement: Announcement) => {
        setAnnouncementToDelete(announcement);
        onDeleteOpen();
    };

    const handleDeleteAnnouncement = async () => {
        if (!announcementToDelete) return;

        try {
            setIsLoading(true);
            const response = await fetch(`/api/announcements/${announcementToDelete._id}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw new Error('Failed to delete announcement');
            }

            toast.success("Announcement deleted successfully!");
            onDeleteClose();

            // If we're on the last page and deleted the last item, go to previous page
            const isLastItem = announcements.length === 1;
            const isLastPage = page === totalPages;
            if (isLastItem && isLastPage && page > 1) {
                fetchAnnouncements(page - 1);
            } else {
                fetchAnnouncements(page);
            }
        } catch (error) {
            console.error("Error deleting announcement:", error);
            toast.error("Failed to delete announcement");
        } finally {
            setIsLoading(false);
        }
    };

    if (!isLoaded || checkingAdmin || !isAdmin) {
        return (
            <div className="flex h-full items-center justify-center">
                <div className="text-center">
                    <div className="relative inline-flex items-center justify-center">
                        <div className="size-12 animate-spin rounded-full border-3 border-gray-300 border-t-[#24AE7C]"></div>
                    </div>
                    <div className="mt-4">
                        <p className="font-medium text-gray-100">Verifying admin access...</p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-8">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold text-white">Manage Announcements</h1>
                <Button
                    color="primary"
                    className="bg-[#24AE7C]"
                    startContent={<FiPlus />}
                    onClick={openCreateModal}
                >
                    New Announcement
                </Button>
            </div>

            <Card className="border border-slate-800/60 bg-[#0f172a]/60 p-6 backdrop-blur-sm">
                {isLoading && announcements.length === 0 ? (
                    <div className="flex items-center justify-center py-16">
                        <Spinner color="primary" size="lg" />
                    </div>
                ) : (
                    <Table
                        aria-label="Announcements table"
                        classNames={{
                            base: "max-w-full overflow-auto",
                            table: "min-w-full",
                            thead: "bg-gray-900/50",
                            th: "text-gray-300 font-medium",
                            tr: "border-b border-gray-800/30 hover:bg-gray-800/20",
                            td: "text-gray-300"
                        }}
                        bottomContent={
                            totalPages > 1 ? (
                                <div className="flex justify-center p-2">
                                    <Pagination
                                        total={totalPages}
                                        initialPage={1}
                                        page={page}
                                        onChange={(newPage) => fetchAnnouncements(newPage)}
                                        size="sm"
                                        classNames={{
                                            item: "text-white bg-gray-800/50 border border-gray-700",
                                            cursor: "bg-[#24AE7C] text-white"
                                        }}
                                    />
                                </div>
                            ) : null
                        }
                    >
                        <TableHeader>
                            <TableColumn>TITLE</TableColumn>
                            <TableColumn>TYPE</TableColumn>
                            <TableColumn>DATE</TableColumn>
                            <TableColumn>IMPORTANT</TableColumn>
                            <TableColumn>ACTIONS</TableColumn>
                        </TableHeader>
                        <TableBody emptyContent="No announcements found">
                            {announcements.map((announcement) => (
                                <TableRow key={announcement._id}>
                                    <TableCell>
                                        <p className="font-medium">{announcement.title}</p>
                                        <p className="mt-1 max-w-[280px] truncate text-xs text-gray-400">
                                            {announcement.content.substring(0, 50)}
                                            {announcement.content.length > 50 && '...'}
                                        </p>
                                    </TableCell>
                                    <TableCell>
                                        <span className="capitalize">{announcement.type.replace(/([A-Z])/g, ' $1').trim()}</span>
                                    </TableCell>
                                    <TableCell>
                                        <div>
                                            {format(new Date(announcement.publishDate), 'MMM d, yyyy')}
                                            {announcement.expiryDate && (
                                                <p className="text-xs text-gray-400">
                                                    Expires: {format(new Date(announcement.expiryDate), 'MMM d, yyyy')}
                                                </p>
                                            )}
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        {announcement.isImportant ? (
                                            <FiCheck className="text-green-500" />
                                        ) : (
                                            <FiX className="text-gray-500" />
                                        )}
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex gap-2">
                                            <Button
                                                size="sm"
                                                color="primary"
                                                className="bg-blue-700 text-xs"
                                                onClick={() => openEditModal(announcement)}
                                                startContent={<FiEdit size={14} />}
                                            >
                                                Edit
                                            </Button>
                                            <Button
                                                size="sm"
                                                color="danger"
                                                className="bg-red-800 text-xs"
                                                onClick={() => handleDeleteConfirm(announcement)}
                                                startContent={<FiTrash2 size={14} />}
                                            >
                                                Delete
                                            </Button>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                )}

                {!isLoading && announcements.length === 0 && (
                    <div className="py-12 text-center">
                        <FiBell className="mx-auto mb-4 text-gray-500" size={32} />
                        <p className="text-gray-400">No announcements yet.</p>
                        <Button
                            color="primary"
                            className="mt-4 bg-[#24AE7C]"
                            startContent={<FiPlus />}
                            onClick={openCreateModal}
                        >
                            Create Your First Announcement
                        </Button>
                    </div>
                )}
            </Card>

            {/* Create/Edit Announcement Modal */}
            <Modal
                isOpen={isOpen}
                onClose={onClose}
                backdrop="blur"
                size="3xl"
                scrollBehavior="inside"
                classNames={{
                    base: "bg-[#0f172a] border border-gray-800 shadow-xl",
                    header: "border-b border-gray-800",
                    footer: "border-t border-gray-800"
                }}
            >
                <ModalContent>
                    {(onClose) => (
                        <>
                            <ModalHeader className="text-white">
                                {modalMode === 'create' ? 'Create New Announcement' : 'Edit Announcement'}
                            </ModalHeader>
                            <ModalBody>
                                <div className="space-y-5">
                                    <div>
                                        <p className="mb-1 text-sm text-gray-400">Title*</p>
                                        <Input
                                            value={title}
                                            onChange={(e) => setTitle(e.target.value)}
                                            variant="bordered"
                                            placeholder="Enter announcement title"
                                            classNames={{
                                                input: "text-white",
                                                inputWrapper: "border-gray-600"
                                            }}
                                        />
                                    </div>

                                    <div>
                                        <p className="mb-1 text-sm text-gray-400">Content*</p>
                                        <Textarea
                                            value={content}
                                            onChange={(e) => setContent(e.target.value)}
                                            variant="bordered"
                                            placeholder="Enter announcement content"
                                            minRows={5}
                                            classNames={{
                                                input: "text-white",
                                                inputWrapper: "border-gray-600"
                                            }}
                                        />
                                    </div>

                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <p className="mb-1 text-sm text-gray-400">Type*</p>
                                            <Select
                                                selectedKeys={[type]}
                                                onChange={(e) => setType(e.target.value)}
                                                placeholder="Select announcement type"
                                                variant="bordered"
                                                classNames={{
                                                    trigger: "border-gray-600 text-white",
                                                    value: "text-white"
                                                }}
                                            >
                                                <SelectItem key="update" value="update">Update</SelectItem>
                                                <SelectItem key="newFeature" value="newFeature">New Feature</SelectItem>
                                                <SelectItem key="bugFix" value="bugFix">Bug Fix</SelectItem>
                                                <SelectItem key="currentBugs" value="currentBugs">Current Bugs</SelectItem>
                                                <SelectItem key="upcomingFeatures" value="upcomingFeatures">Upcoming Features</SelectItem>
                                                <SelectItem key="maintenance" value="maintenance">Maintenance</SelectItem>
                                                <SelectItem key="other" value="other">Other</SelectItem>
                                            </Select>
                                        </div>

                                        <div className="flex items-center justify-between py-4">
                                            <p className="text-white">Mark as Important</p>
                                            <Switch
                                                isSelected={isImportant}
                                                onValueChange={setIsImportant}
                                                color="warning"
                                            />
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <p className="mb-1 text-sm text-gray-400">Publish Date*</p>
                                            <Input
                                                type="date"
                                                value={publishDate}
                                                onChange={(e) => setPublishDate(e.target.value)}
                                                variant="bordered"
                                                startContent={<FiCalendar className="text-gray-400" />}
                                                classNames={{
                                                    input: "text-white",
                                                    inputWrapper: "border-gray-600"
                                                }}
                                            />
                                        </div>

                                        <div>
                                            <p className="mb-1 text-sm text-gray-400">Expiry Date (Optional)</p>
                                            <Input
                                                type="date"
                                                value={expiryDate}
                                                onChange={(e) => setExpiryDate(e.target.value)}
                                                variant="bordered"
                                                startContent={<FiCalendar className="text-gray-400" />}
                                                classNames={{
                                                    input: "text-white",
                                                    inputWrapper: "border-gray-600"
                                                }}
                                            />
                                            <p className="mt-1 text-xs text-gray-500">Leave empty if announcement doesn&apos;t expire</p>
                                        </div>
                                    </div>

                                    {isImportant && (
                                        <div className="flex items-start gap-2 rounded-md border border-amber-800/50 bg-amber-900/20 p-3">
                                            <FiAlertCircle className="mt-1 shrink-0 text-amber-500" />
                                            <p className="text-sm text-amber-200">
                                                Important announcements will be highlighted and shown at the top of the list.
                                                Use this for critical updates or important information.
                                            </p>
                                        </div>
                                    )}
                                </div>
                            </ModalBody>
                            <ModalFooter>
                                <Button color="default" variant="light" onPress={onClose}>
                                    Cancel
                                </Button>
                                <Button
                                    color="primary"
                                    className="bg-[#24AE7C]"
                                    onPress={handleSaveAnnouncement}
                                    isLoading={isLoading}
                                >
                                    {modalMode === 'create' ? 'Create Announcement' : 'Save Changes'}
                                </Button>
                            </ModalFooter>
                        </>
                    )}
                </ModalContent>
            </Modal>

            {/* Delete Confirmation Modal */}
            <Modal
                isOpen={isDeleteOpen}
                onClose={onDeleteClose}
                backdrop="blur"
                classNames={{
                    base: "bg-[#0f172a] border border-gray-800 shadow-xl",
                    header: "border-b border-gray-800",
                    footer: "border-t border-gray-800"
                }}
            >
                <ModalContent>
                    {(onClose) => (
                        <>
                            <ModalHeader className="text-white">
                                Confirm Deletion
                            </ModalHeader>
                            <ModalBody>
                                {announcementToDelete && (
                                    <div>
                                        <p className="mb-4 text-white">Are you sure you want to delete this announcement?</p>
                                        <div className="rounded-md bg-gray-800/50 p-3">
                                            <p className="font-medium text-white">{announcementToDelete.title}</p>
                                            <p className="mt-1 text-sm text-gray-300">{announcementToDelete.content.substring(0, 100)}...</p>
                                        </div>
                                        <p className="mt-4 text-sm text-red-500">
                                            This action cannot be undone.
                                        </p>
                                    </div>
                                )}
                            </ModalBody>
                            <ModalFooter>
                                <Button color="default" variant="light" onPress={onDeleteClose}>
                                    Cancel
                                </Button>
                                <Button
                                    color="danger"
                                    className="bg-red-700"
                                    onPress={handleDeleteAnnouncement}
                                    isLoading={isLoading}
                                >
                                    Delete Announcement
                                </Button>
                            </ModalFooter>
                        </>
                    )}
                </ModalContent>
            </Modal>
        </div>
    );
}