"use client";
import { But<PERSON> } from "@nextui-org/react";
import { useRouter } from "next/navigation";
import { signOut } from "next-auth/react";
import { useState } from "react";
import { toast } from "sonner";

interface LogoutButtonProps {
  className?: string;
}

export default function LogoutButton({ className = "" }: LogoutButtonProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleLogout = async () => {
    setIsLoading(true);
    try {
      // First call our custom logout API to ensure cookies are cleared properly
      await fetch("/api/auth/logout", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
      });

      // Use signOut with redirect:false to avoid CSRF confirmation page
      await signOut({ redirect: false });

      // Manually navigate to the login page
      router.push("/login");

      // Optional: show success toast
      toast.success("Logged out successfully");
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("Failed to logout");
      setIsLoading(false);

      // Fallback - force redirect to login page if signOut fails
      router.push("/login");
    }
  };

  return (
    <>
      <Button
        onClick={handleLogout}
        color="danger"
        variant="flat"
        className={`w-full ${className}`}
        disabled={isLoading}
        isLoading={isLoading}
      >
        Logout
      </Button>
    </>
  );
}