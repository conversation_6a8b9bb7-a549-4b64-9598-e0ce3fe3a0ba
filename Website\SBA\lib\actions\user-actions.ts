'use server';

import connectDB from '../mongodb';
import { User } from '../../model/User';

/**
 * Fetches the total count of users from the database
 */
export async function getTotalUserCount(): Promise<number> {
  console.log('Starting to fetch user count from MongoDB...');
  
  try {
    const connection = await connectDB();
    console.log('Connected to MongoDB successfully:', connection ? 'Yes' : 'No');
    
    const count = await User.countDocuments({})+11;
    console.log(`Found ${count} users in the database`);
    
    // Return the count if it's a number, otherwise return a fallback value
    return typeof count === 'number' ? count : 39;
  } catch (error) {
    console.error('Error fetching user count:', error);
    // Return a fallback value of 39 (same as your original placeholder)
    return 39;
  }
}
