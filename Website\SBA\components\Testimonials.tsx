'use client';
import Autoplay from 'embla-carousel-autoplay';
import { Check, Star, StarHalf } from 'lucide-react';

import { Card, CardContent } from '@/components/ui/card';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel';

export default function testimonials() {
  const TestimonialData = [
    {
      id: 1,
      name: '<PERSON>',
      userImg: '/users/userai1-min.jpg',
      rating: 5,
      comment:
        'This app revolutionized my coding interviews! The real-time AI assistance provided optimal solutions instantly. ',
      secondComment: 'Absolutely essential for tech interviews!',
      highlighted: 'real-time AI assistance',
    },
    {
      id: 2,
      name: '<PERSON><PERSON><PERSON>',
      userImg: '/users/testimonials/user2.png',
      rating: 4.5,
      halfrating: 1,
      comment:
        'Saved me during a system design interview! The AI\'s architecture suggestions were spot-on and saved me hours of preparation.',
      secondComment: 'Tech interview game-changer!',
      highlighted: 'spot-on architecture suggestions',
    },
    {
      id: 3,
      name: '<PERSON><PERSON><PERSON><PERSON>',
      userImg: '/users/ai2.jpg',
      rating: 5,
      comment:
        'The code analysis feature is incredible! It identified edge cases I would\'ve missed and provided optimized solutions.',
      secondComment: 'Found my secret weapon!',
      highlighted: 'identified edge cases',
    },
    {
      id: 4,
      name: 'Dev Bhaskar',
      userImg: '/users/testimonials/user4.png',
      rating: 5,
      comment:
        'Perfect for whiteboard interviews! Just snap a photo of the problem and get instant, accurate solutions.',
      secondComment: 'Instant coding superpowers!',
      highlighted: 'instant, accurate solutions',
    },
    {
      id: 5,
      name: 'Antonio',
      userImg: '/users/ai3.jpg',
      rating: 4.5,
      halfrating: 1,
      comment:
        'The ultimate interview companion! The AI explains solutions so clearly, it felt like having a senior dev by my side.',
      secondComment: 'Best interview prep tool!',
      highlighted: 'explains solutions clearly',
    },
  ];
  return (
    <section className=' h-full py-10'>
      <h1 className='bg-gradient-to-br from-slate-300 to-slate-500 bg-clip-text py-4 text-center text-4xl font-medium tracking-tight text-transparent lg:text-5xl'>
        Kind Words from Satisfied Clients
      </h1>
      <Carousel
        opts={{
          loop: true,
        }}
        plugins={[
          Autoplay({
            delay: 7000,
          }),
        ]}
        className='z-30 mt-20 w-full '
      >
        <CarouselContent className='-ml-1 select-none hover:cursor-grab	'>
          {TestimonialData.map((item) => (
            <CarouselItem
              key={item.id}
              className='rounded-lg pl-1 md:basis-1/2 lg:basis-1/3'
            >
              <div className='p-1'>
                <Card>
                  <CardContent className='flex items-center  justify-center rounded-lg bg-black p-6 md:aspect-square'>
                    <div className='flex flex-auto flex-col gap-4 '>
                      <div className='mb-2 flex gap-0.5'>
                        {Array.from({ length: item.rating }).map((_, index) => (
                          <Star
                            className='size-5 fill-green-600 text-green-600'
                            key={index}
                          />
                        ))}
                        {item.halfrating !== undefined &&
                          item.halfrating > 0 &&
                          Array.from({ length: item.halfrating }).map(
                            // eslint-disable-next-line camelcase
                            (_, index_half) => (
                              <StarHalf
                                className='size-5 fill-green-600 text-green-600'
                                // eslint-disable-next-line camelcase
                                key={index_half}
                              />
                            )
                          )}
                      </div>
                      <div className='text-base leading-6 2xl:text-lg 2xl:leading-7'>
                        <p>
                          &quot;
                          {item.comment}
                          <span className='bg-slate-800 p-0.5 text-white'>
                            {item.secondComment}
                          </span>
                          &quot;
                        </p>
                      </div>
                      <div className='mt-2 flex gap-4'>
                        <img
                          className='size-12 rounded-full object-cover'
                          src={`${item.userImg}`}
                          alt='user'
                        />
                        <div className='flex flex-col'>
                          <p className='font-semibold'>{item.name}</p>
                          <div className='flex items-center gap-1.5 text-zinc-600'>
                            <Check className='size-4 stroke-[3px] text-green-600' />
                            <p className='text-sm'>Verified Client</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className='bg-black' />
        <CarouselNext className='bg-black' />
      </Carousel>
    </section>
  );
}
