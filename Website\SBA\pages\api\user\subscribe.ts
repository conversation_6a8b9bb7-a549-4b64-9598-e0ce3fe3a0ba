import { NextApiRequest, NextApiResponse } from "next";
import { getServerSession } from "next-auth/next";

import { authOptions } from "@/lib/auth";
import connectDB from "@/lib/mongodb";
import { User } from "@/model/User";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "POST") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({ message: "Unauthorized" });
  }

  try {
    await connectDB();
    const user = await User.findById(session.user.id);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    const { plan } = req.body;
    const now = new Date();
    let endDate;

    if (plan === "monthly") {
      endDate = new Date(now.setMonth(now.getMonth() + 1));
    } else if (plan === "yearly") {
      endDate = new Date(now.setFullYear(now.getFullYear() + 1));
    } else {
      return res.status(400).json({ message: "Invalid plan" });
    }

    user.subscriptionType = "premium";
    user.subscriptionPlan = plan;
    user.subscriptionStartDate = new Date(); // Store the subscription start date
    user.subscriptionEndDate = endDate;
    user.promptsUsed = 0;
    await user.save();

    const updatedUser = {
      id: user._id.toString(),
      name: user.name,
      email: user.email,
      provider: user.provider,
      subscriptionType: user.subscriptionType,
      subscriptionPlan: user.subscriptionPlan,
      subscriptionEndDate: user.subscriptionEndDate,
      promptsUsed: user.promptsUsed,
      image: user.avatar,
    };

    return res.status(200).json(updatedUser);
  } catch (error) {
    console.error("Subscription error:", error);
    return res.status(500).json({ message: "Internal server error" });
  }
}