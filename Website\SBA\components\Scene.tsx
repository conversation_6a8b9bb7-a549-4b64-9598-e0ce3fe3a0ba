'use client';
import { Environment } from '@react-three/drei';
import { Canvas } from '@react-three/fiber';

import Model from './Model';

export default function Scene() {
  return (
    <Canvas
      className='bg-black'
      orthographic
      camera={{ position: [0, 0, 1], zoom: 800 }}
      onCreated={({ gl }) => {
        // Handle WebGL context loss
        gl.domElement.addEventListener('webglcontextlost', (e) => {
          e.preventDefault();
          console.warn('WebGL context lost. Attempting to restore...');
        });

        gl.domElement.addEventListener('webglcontextrestored', () => {
          console.log('WebGL context restored.');
          gl.setSize(gl.domElement.width, gl.domElement.height);
        });
      }}
    >
      <Model />
      <directionalLight intensity={3} position={[0, 0.1, 1]} />
      <Environment preset='city' />
    </Canvas>
  );
}
