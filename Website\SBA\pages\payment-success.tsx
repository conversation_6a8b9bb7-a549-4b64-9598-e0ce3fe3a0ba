"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useUser, useClerk } from "@clerk/nextjs";
import { apiFetch } from "@/lib/api-utils";

// Payment Success component using Clerk hooks
export default function PaymentSuccess() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const { user, isLoaded, isSignedIn } = useUser();
    const { session } = useClerk();
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [refreshSuccess, setRefreshSuccess] = useState(false);

    useEffect(() => {
        if (!isLoaded || !user || !searchParams) return;

        const method = searchParams.get("method");
        const plan = searchParams.get("plan");
        const amount = searchParams.get("amount");
        const currency = searchParams.get("currency");
        const payment_id = searchParams.get("payment_id");

        // For PayPal, send analytics events if available
        if (typeof window !== "undefined" && window.gtag && amount && currency && plan) {
            window.gtag("event", "purchase", {
                transaction_id: payment_id || `order_${Date.now()}`,
                value: parseFloat(amount),
                currency,
                items: [
                    {
                        item_id: plan,
                        item_name: `${plan.charAt(0).toUpperCase() + plan.slice(1)} Subscription`,
                        price: parseFloat(amount),
                        quantity: 1,
                    },
                ],
                payment_type: method || "unknown",
            });
            console.log("Google Analytics purchase event fired");
        }

        if (typeof window !== "undefined" && window.twq && amount && currency && plan) {
            const eventParams = {
                value: parseFloat(amount),
                currency,
                contents: [{ id: plan, quantity: 1 }],
                email_address: user.emailAddresses[0].emailAddress,
            };
            window.twq("event", "tw-p9l1d-p9l1e", eventParams);
            console.log("Twitter event pixel fired:", eventParams);
        }

        console.log("PaymentSuccess - Method:", method, "Plan:", plan);

        // Instead of logging out, refresh the session to get updated subscription status
        const refreshUserSession = async () => {
            try {
                setIsRefreshing(true);
                console.log("Refreshing user session to update subscription status");

                // Reload the session from Clerk
                if (session) {
                    await session.reload();
                    console.log("Session reloaded successfully");
                }

                // Also fetch the latest user data from our backend
                const response = await apiFetch('/api/user/me');
                if (response.ok) {
                    console.log("User data refreshed successfully");
                    setRefreshSuccess(true);

                    // Redirect to dashboard after successful refresh
                    setTimeout(() => {
                        router.push('/dashboard');
                    }, 2000);
                } else {
                    console.error("Failed to refresh user data");
                    // Fall back to dashboard even if refresh failed
                    setTimeout(() => {
                        router.push('/dashboard');
                    }, 2000);
                }
            } catch (error) {
                console.error("Error refreshing session:", error);
                // Fall back to dashboard even if refresh failed
                setTimeout(() => {
                    router.push('/dashboard');
                }, 2000);
            } finally {
                setIsRefreshing(false);
            }
        };

        // Start refresh process with a small delay to ensure payment processing is complete
        setTimeout(() => {
            refreshUserSession();
        }, 1500);
    }, [searchParams, user, isLoaded, session, router]);

    if (!isLoaded) {
        return <div>Loading...</div>;
    }

    return (
        <div
            style={{
                minHeight: "100vh",
                background: "linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)",
                color: "#ffffff",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                padding: "32px",
                fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
            }}
        >
            <div
                style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    height: "100%",
                    width: "100%",
                }}
            >
                <div
                    style={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(12px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "16px",
                        padding: "48px",
                        maxWidth: "500px",
                        width: "100%",
                        textAlign: "center",
                        boxShadow: "0 8px 32px rgba(0, 0, 0, 0.3)",
                        position: "relative",
                        overflow: "hidden",
                    }}
                >
                    <div
                        style={{
                            position: "absolute",
                            inset: "0",
                            background: "linear-gradient(45deg, rgba(0, 255, 152, 0.1), rgba(0, 122, 255, 0.1))",
                            animation: "pulse 3s infinite",
                            zIndex: 0,
                        }}
                    />
                    <div
                        style={{
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            position: "relative",
                            zIndex: 1,
                        }}
                    >
                        <div style={{ position: "relative", marginBottom: "32px" }}>
                            <div
                                style={{
                                    position: "absolute",
                                    inset: "-10px",
                                    background: "rgba(0, 255, 152, 0.15)",
                                    borderRadius: "50%",
                                    animation: "ping 2s infinite",
                                }}
                            />
                            <svg
                                width="80"
                                height="80"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="#00ff98"
                                strokeWidth="2"
                                style={{ position: "relative", zIndex: 10 }}
                            >
                                <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <img
                            src="/logo.png"
                            alt="Iterview Cracker logo"
                            style={{
                                width: "120px",
                                height: "auto",
                                marginBottom: "32px",
                                filter: "drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2))",
                            }}
                        />
                        <div
                            style={{
                                marginBottom: "32px",
                                fontSize: "18px",
                                lineHeight: "1.5",
                                fontWeight: "400",
                                color: "#e5e7eb",
                            }}
                        >
                            Payment Processing Complete!<br />
                            <span style={{ color: "#d1d5db" }}>
                                {isRefreshing ?
                                    "Updating your subscription status..." :
                                    refreshSuccess ?
                                        "Subscription activated successfully! Redirecting to dashboard..." :
                                        "Please wait while we finalize your subscription."}
                            </span>
                            <br />
                            <span style={{ fontSize: "14px", color: "#ff6b6b" }}>
                                Do not click back or reload this page.
                            </span>
                        </div>
                        <div
                            style={{
                                fontSize: "14px",
                                color: "#9ca3af",
                                marginTop: "24px",
                                paddingTop: "20px",
                                borderTop: "1px solid rgba(255, 255, 255, 0.1)",
                                width: "100%",
                            }}
                        >
                            Not redirected yet?
                            <button
                                onClick={() => router.push('/dashboard')}
                                style={{
                                    marginLeft: "12px",
                                    padding: "8px 20px",
                                    background: "linear-gradient(90deg, #00ff98, #007aff)",
                                    color: "white",
                                    border: "none",
                                    borderRadius: "8px",
                                    cursor: "pointer",
                                    transition: "transform 0.2s, box-shadow 0.2s",
                                    fontWeight: "500",
                                }}
                                onMouseOver={(e) => {
                                    e.currentTarget.style.transform = "translateY(-2px)";
                                    e.currentTarget.style.boxShadow = "0 4px 12px rgba(0, 255, 152, 0.3)";
                                }}
                                onMouseOut={(e) => {
                                    e.currentTarget.style.transform = "translateY(0)";
                                    e.currentTarget.style.boxShadow = "none";
                                }}
                            >
                                Go to Dashboard
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <style jsx global>{`
        @keyframes pulse {
          0% { opacity: 0.5; }
          50% { opacity: 0.2; }
          100% { opacity: 0.5; }
        }
        @keyframes ping {
          0% { transform: scale(1); opacity: 1; }
          80% { transform: scale(1.3); opacity: 0; }
          100% { transform: scale(1.3); opacity: 0; }
        }
      `}</style>
        </div>
    );
}