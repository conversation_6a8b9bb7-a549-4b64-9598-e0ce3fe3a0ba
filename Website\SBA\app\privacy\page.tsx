import Link from 'next/link'; // Remove if not using Next.js
import React from 'react';

const PrivacyPolicyPage = () => {
  return (
    <div className='mx-auto flex items-center justify-center px-5 py-24 md:py-28'>
      <div className='flex max-w-4xl flex-col items-center justify-center gap-12'>
        <h2 className='text-center text-2xl font-bold md:text-3xl'>
          Privacy Policy
        </h2>
        <div className='flex flex-col gap-5'>
          <p>
            Welcome to InterviewCracker.in, an Electron-based desktop application and website (collectively, &quot;the Service&quot;). At InterviewCracker.in, we are committed to protecting your privacy. This Privacy Policy explains how we collect, use, and safeguard your information when you use the Service.
          </p>
          <p>
            By using the Service, you consent to the practices described in this policy. We may update this Privacy Policy from time to time, and changes will be effective upon posting. Please review it periodically.
          </p>
          <div className='flex flex-col gap-3'>
            <p>
              Here’s how we handle your information:
            </p>
            <ul className='list-disc pl-4'>
              <li>
                <span className='font-semibold'>Information We Collect</span>: We may collect personal information such as your name, email address, and usage data (e.g., app interactions, crash reports) when you use the Service or contact us.
              </li>
              <li>
                <span className='font-semibold'>How We Use Your Information</span>: We use your data to provide and improve the Service, respond to inquiries, and ensure functionality. We do not sell or share your personal information with third parties except as required by law.
              </li>
              <li>
                <span className='font-semibold'>Data Storage and Security</span>: Your information is stored securely on our servers or local device storage. We implement reasonable measures to protect it but cannot guarantee absolute security.
              </li>
              <li>
                <span className='font-semibold'>Third-Party Services</span>: The Service may use third-party tools (e.g., analytics or crash reporting). These providers have their own privacy policies, and we encourage you to review them.
              </li>
              <li>
                <span className='font-semibold'>Your Rights</span>: You may request access to or deletion of your personal data by contacting us. We will comply with applicable laws in responding to your requests.
              </li>
            </ul>
          </div>

          <h3 className='text-xl font-semibold'>Permissions Used</h3>
          <ul className='list-disc pl-4'>
            <li>
              <span className='font-semibold'>Screen Capture</span>: Used exclusively for Optical Character Recognition (OCR) to assist users with text extraction from images. No data is stored or shared without user consent.
            </li>
            <li>
              <span className='font-semibold'>Microphone Access</span>: Reserved for future features. The Service does not actively use the microphone at this time. When implemented, it will be secure and used only for intended functionality.
            </li>
          </ul>

          <p>
            We value your trust and strive to handle your information responsibly. If you have questions or concerns about your privacy, please don’t hesitate to reach out.
          </p>
          <p>
            For inquiries or to exercise your rights, contact us at{' '}
            <Link
              href='mailto:<EMAIL>'
              className='underline underline-offset-4'
            >
              <EMAIL>
            </Link>{' '}
            or{' '}
            <Link
              href='mailto:<EMAIL>'
              className='underline underline-offset-4'
            >
              <EMAIL>
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default PrivacyPolicyPage;
