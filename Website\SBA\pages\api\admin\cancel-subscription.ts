// pages/api/user/cancel-subscription.ts
import { NextApiRequest, NextApiResponse } from "next";
import { getServerSession } from "next-auth/next";

import { authOptions } from "@/lib/auth";
import { sendCancellationConfirmation } from "@/lib/email";
import connectDB from "@/lib/mongodb";
import { Payment } from "@/model/Payment";
import { User } from "@/model/User";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "POST") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  const session = await getServerSession(req, res, authOptions);
  if (!session || !session.user.isAdmin) {
    return res.status(401).json({ message: "Unauthorized" });
  }

  try {
    await connectDB();
    const { userId } = req.body;
    
    // First get the user to access their plan before updating
    const user = await User.findById(userId);
    
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    
    const currentPlan = user.subscriptionPlan;
    
    // Update user subscription to free
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { 
        $set: { 
          subscriptionType: "free", 
          subscriptionPlan: null, 
          promptsUsed: 0 // Reset prompts used to 0
        },
        $unset: { 
          subscriptionStartDate: "", 
          subscriptionEndDate: "" 
        } // Remove date fields completely
      },
      { new: true }
    ).select("-password");

    // Create a cancellation record in payment history
    const timestamp = Date.now();
    const cancellationRecord = new Payment({
      user: userId,
      paymentId: `admin-cancel-${timestamp}`,
      orderId: `admin-cancel-${timestamp}`,
      amount: 0, // No charge for cancellation
      currency: "USD", // Default currency
      status: "cancelled", 
      method: "cancellation",
      plan: currentPlan || "subscription",
      createdAt: new Date()
    });
    
    await cancellationRecord.save();

    // Send cancellation email to the user
    try {
      await sendCancellationConfirmation(
        user.email,
        user.name || "Valued Customer", 
        currentPlan || "premium",
        new Date() // Immediate cancellation
      );
    } catch (emailError) {
      console.error("Failed to send cancellation email:", emailError);
      // Don't fail the request if email sending fails
    }

    return res.status(200).json(updatedUser);
  } catch (error) {
    console.error("Cancel subscription error:", error);
    return res.status(500).json({ message: "Internal server error" });
  }
}