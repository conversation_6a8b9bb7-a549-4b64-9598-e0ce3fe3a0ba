import { Button } from '@nextui-org/button';
import { EmblaOptionsType } from 'embla-carousel';
import Link from 'next/link';
import '../app/embla.css';
import EmblaCarousel from './ui/EmblaCarousel';

const OPTIONS: EmblaOptionsType = { dragFree: true };
const SLIDE_COUNT = 5;
const SLIDES = Array.from(Array(SLIDE_COUNT).keys());

const CoreFeatures = () => {
  return (
    <div className='hidden sm:flex xl:hidden py-10 lg:py-20 px-2 sm:px-4 md:p-10 lg:mb-28 flex-col'>
      <h1 className='text-4xl text-center sm:text-4xl lg:text-5xl mb-7 z-50'>
        Our Core Features
      </h1>

      <div className='flex px-4 flex-col 2lg:flex-row mt-10 sm:mt-16 lg:mt-20 justify-center gap-16 xl:gap-20'>
        <EmblaCarousel slides={SLIDES} options={OPTIONS} />
        <div className='flex flex-col gap-9'>
          <div className='text-base text-pretty z-10 pt-10'>
            Our app empowers developers and tech professionals by providing real-time coding assistance during interviews. Key features include:
            <ul className='list-disc pl-5 mt-2'>
              <li>Instant code generation and optimization</li>
              <li>Discreet support for remote interviews</li>
              <li>Real-time problem-solving suggestions</li>
              <li>System design guidance and edge case handling</li>
              <li>Seamless integration with video conferencing tools</li>
            </ul>
            Whether you're a fresher or an experienced techie, our platform ensures you stay confident and focused during technical evaluations.
          </div>
          <div className='flex gap-4'>
            <Link href={'/features'}>
              <Button
                color='primary'
                variant='shadow'
                radius='full'
                className='w-24'
              >
                Features
              </Button>
            </Link>
            <Link href={'/signup'}>
              <Button
                color='primary'
                variant='shadow'
                radius='full'
                className='w-[165px]'
              >
                Start Free Trial
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoreFeatures;