import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    Input,
} from "@nextui-org/react";
import { useState } from "react";

interface CancelSubscriptionModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    isLoading: boolean;
}

export default function CancelSubscriptionModal({
    isOpen,
    onClose,
    onConfirm,
    isLoading,
}: CancelSubscriptionModalProps) {
    const [confirmText, setConfirmText] = useState("");

    const isConfirmDisabled = confirmText !== "CANCEL";

    return (
        <Modal isOpen={isOpen} onClose={onClose}>
            <ModalContent>
                <ModalHeader className="flex flex-col gap-1">Cancel Subscription</ModalHeader>
                <ModalBody>
                    <p className="text-gray-400">
                        Are you sure you want to cancel your premium subscription? You'll lose access to:
                    </p>
                    <ul className="list-inside list-disc space-y-2 text-gray-400">
                        <li>250 prompts per month</li>
                        <li>Priority support</li>
                        <li>Advanced features</li>
                    </ul>
                    <p className="mt-4 text-gray-400">
                        To confirm cancellation, please type "CANCEL" in the field below:
                    </p>
                    <Input
                        placeholder="Type CANCEL to confirm"
                        value={confirmText}
                        onChange={(e) => setConfirmText(e.target.value)}
                    />
                </ModalBody>
                <ModalFooter>
                    <Button variant="bordered" onPress={onClose}>
                        Keep Subscription
                    </Button>
                    <Button
                        color="danger"
                        onPress={onConfirm}
                        isDisabled={isConfirmDisabled}
                        isLoading={isLoading}
                    >
                        Cancel Subscription
                    </Button>
                </ModalFooter>
            </ModalContent>
        </Modal>
    );
}