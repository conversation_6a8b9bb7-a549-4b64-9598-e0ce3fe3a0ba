"use client";

import React, { useEffect, useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Divider } from "@nextui-org/react";
import { format } from 'date-fns';
import {
    FiAlertCircle, FiClock, FiTag, FiRefreshCw, FiPlus,
    FiTerminal, FiTool, FiBell, FiCalendar, FiX, FiCheck
} from 'react-icons/fi';
import { motion } from 'framer-motion';

interface Announcement {
    _id: string;
    title: string;
    content: string;
    type: 'update' | 'newFeature' | 'bugFix' | 'maintenance' | 'currentBugs' | 'upcomingFeatures' | 'other';
    publishDate: string;
    expiryDate?: string;
    isImportant: boolean;
    createdBy: string;
    createdAt: string;
    updatedAt: string;
}

interface PaginationData {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}

const getTypeColor = (type: string) => {
    switch (type) {
        case 'update': return 'primary';
        case 'newFeature': return 'success';
        case 'bugFix': return 'warning';
        case 'currentBugs': return 'danger';
        case 'upcomingFeatures': return 'secondary';
        case 'maintenance': return 'secondary';
        default: return 'default';
    }
};

const getTypeIcon = (type: string) => {
    switch (type) {
        case 'update': return <FiRefreshCw className="text-blue-400" />;
        case 'newFeature': return <FiPlus className="text-green-400" />;
        case 'bugFix': return <FiTool className="text-amber-400" />;
        case 'currentBugs': return <FiX className="text-red-400" />;
        case 'upcomingFeatures': return <FiCalendar className="text-purple-400" />;
        case 'maintenance': return <FiTerminal className="text-gray-400" />;
        default: return <FiBell className="text-gray-400" />;
    }
};

const getTypeLabel = (type: string) => {
    switch (type) {
        case 'newFeature': return 'New Feature';
        case 'bugFix': return 'Bug Fix';
        case 'currentBugs': return 'Current Bug';
        case 'upcomingFeatures': return 'Upcoming Feature';
        default: return type.charAt(0).toUpperCase() + type.slice(1);
    }
};

const Announcements = () => {
    const [announcements, setAnnouncements] = useState<Announcement[]>([]);
    const [filteredAnnouncements, setFilteredAnnouncements] = useState<Announcement[]>([]);
    const [pagination, setPagination] = useState<PaginationData>({
        total: 0,
        page: 1,
        limit: 5,
        totalPages: 0,
    });
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [expandedCard, setExpandedCard] = useState<string | null>(null);
    const [selectedType, setSelectedType] = useState<string>("all");
    const [refreshing, setRefreshing] = useState(false);

    const fetchAnnouncements = async (page: number = 1) => {
        try {
            setLoading(true);
            const res = await fetch(`/api/announcements?page=${page}&limit=${pagination.limit}`);
            if (!res.ok) throw new Error('Failed to fetch announcements');

            const data = await res.json();
            setAnnouncements(data.announcements);
            setFilteredAnnouncements(data.announcements);
            setPagination(data.pagination);
        } catch (err: any) {
            setError(err.message || 'Something went wrong');
            console.error('Error fetching announcements:', err);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchAnnouncements();
    }, []);

    useEffect(() => {
        if (selectedType === "all") {
            setFilteredAnnouncements(announcements);
        } else {
            const filtered = announcements.filter(announcement => announcement.type === selectedType);
            setFilteredAnnouncements(filtered);
        }
    }, [selectedType, announcements]);

    const handlePageChange = (page: number) => {
        fetchAnnouncements(page);
    };

    const handleRefresh = async () => {
        setRefreshing(true);
        await fetchAnnouncements(pagination.page);
        setTimeout(() => setRefreshing(false), 600);
    };

    const toggleCardExpansion = (id: string) => {
        if (expandedCard === id) {
            setExpandedCard(null);
        } else {
            setExpandedCard(id);
        }
    };

    if (error) {
        return (
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="w-full max-w-5xl mx-auto py-8"
            >
                <Card className="bg-red-900/20 border border-red-800/50 overflow-hidden">
                    <CardBody className="text-center py-8">
                        <FiAlertCircle className="mx-auto text-red-400 mb-4" size={40} />
                        <p className="text-red-300 text-lg font-medium mb-2">Error loading announcements</p>
                        <p className="text-red-400 mb-6">{error}</p>
                        <Button
                            color="danger"
                            variant="shadow"
                            className="bg-gradient-to-r from-red-600 to-red-700 text-white font-medium"
                            onClick={() => fetchAnnouncements()}
                            startContent={<FiRefreshCw size={18} />}
                        >
                            Try Again
                        </Button>
                    </CardBody>
                </Card>
            </motion.div>
        );
    }

    const tabStyles = "py-3 px-4 data-[selected=true]:bg-transparent data-[selected=true]:border-b-2 data-[selected=true]:border-[#24AE7C]";

    const renderAnnouncementHeader = (announcement: Announcement) => (
        <div className="flex flex-col sm:flex-row w-full justify-between sm:items-center gap-2">
            <div className="flex-1">
                <div className="flex items-center gap-2">
                    <p className="text-xl font-bold text-white">{announcement.title}</p>
                    {announcement.isImportant && (
                        <Tooltip content="Important announcement" color="warning" placement="right">
                            <span>
                                <FiAlertCircle className="text-amber-400" size={18} />
                            </span>
                        </Tooltip>
                    )}
                </div>
                <p className="text-sm text-gray-400 flex items-center gap-1.5 mt-1">
                    <FiCalendar size={14} />
                    {format(new Date(announcement.publishDate), 'MMMM d, yyyy')}
                </p>
            </div>
            <Chip
                color={getTypeColor(announcement.type) as any}
                variant="flat"
                startContent={getTypeIcon(announcement.type)}
                className="max-w-fit font-medium"
                size="sm"
            >
                {getTypeLabel(announcement.type)}
            </Chip>
        </div>
    );

    const renderEmptyState = () => (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
        >
            <Card className="bg-gradient-to-br from-gray-900/90 to-gray-800/60 border border-gray-800/50 overflow-hidden">
                <CardBody className="text-center py-16 px-6">
                    <FiBell className="mx-auto text-gray-500 mb-4" size={40} />
                    <p className="text-gray-300 text-xl font-medium mb-2">No announcements available</p>
                    <p className="text-gray-400 max-w-md mx-auto">
                        There are currently no announcements in this category. Check back later for updates.
                    </p>
                </CardBody>
            </Card>
        </motion.div>
    );

    const tabFilters = [
        { key: "all", label: "All", icon: <FiBell /> },
        { key: "update", label: "Updates", icon: <FiRefreshCw /> },
        { key: "newFeature", label: "New Features", icon: <FiPlus /> },
        { key: "bugFix", label: "Bug Fixes", icon: <FiTool /> },
        { key: "currentBugs", label: "Current Bugs", icon: <FiX /> },
        { key: "upcomingFeatures", label: "Upcoming Features", icon: <FiCalendar /> },
        { key: "maintenance", label: "Maintenance", icon: <FiTerminal /> },
    ];

    // Format content to display point-wise
    const formatContentPointWise = (content: string) => {
        // Split content by common bullet point patterns or line breaks
        const lines = content.split(/\r?\n|\\n|\s*•\s*|\s*-\s*|\s*\*\s*|\s*\d+\.\s*/g).filter(line => line.trim() !== '');

        if (lines.length <= 1) {
            return <p className="whitespace-pre-line">{content}</p>;
        }

        return (
            <ul className="list-disc pl-5 space-y-2">
                {lines.map((line, i) => (
                    <li key={i} className="text-gray-300">{line.trim()}</li>
                ))}
            </ul>
        );
    };

    return (
        <div className="w-full max-w-5xl mx-auto">
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="mb-6"
            >
                <div className="flex justify-between items-center mb-4">
                    <div className="flex items-center gap-3">
                        <div className="h-8 w-2 bg-gradient-to-b from-[#24AE7C] to-cyan-500 rounded-full" />
                        <h2 className="text-xl font-bold text-white">Latest Updates</h2>
                    </div>
                    <Tooltip content="Refresh announcements">
                        <Button
                            isIconOnly
                            variant="light"
                            onPress={handleRefresh}
                            className="text-gray-400 hover:text-white"
                            isLoading={refreshing}
                        >
                            <FiRefreshCw size={18} />
                        </Button>
                    </Tooltip>
                </div>

                <div className="relative border-b border-gray-800/40 mb-6">
                    <Tabs
                        aria-label="Announcement categories"
                        selectedKey={selectedType}
                        onSelectionChange={(key) => setSelectedType(key as string)}
                        color="primary"
                        variant="light"
                        classNames={{
                            base: "w-full overflow-x-auto",
                            tabList: "px-0 bg-transparent gap-6",
                            tab: "px-0 h-12 data-[selected=true]:text-[#24AE7C] data-[selected=true]:font-medium",
                            tabContent: "text-sm group-data-[selected=true]:text-[#24AE7C]",
                            cursor: "h-[3px] bg-[#24AE7C]",
                            panel: "pt-4"
                        }}
                    >
                        {tabFilters.map((tab) => (
                            <Tab
                                key={tab.key}
                                title={
                                    <motion.div
                                        className="flex items-center gap-2"
                                        whileHover={{ scale: 1.05 }}
                                        transition={{ type: "spring", stiffness: 400, damping: 10 }}
                                    >
                                        {React.cloneElement(tab.icon, { size: 14 })}
                                        <span>{tab.label}</span>
                                    </motion.div>
                                }
                            />
                        ))}
                    </Tabs>
                </div>
            </motion.div>

            {loading && announcements.length === 0 ? (
                <div className="flex justify-center items-center py-20">
                    <div className="text-center">
                        <Spinner color="primary" size="lg" className="mb-4" />
                        <p className="text-gray-400 animate-pulse">Loading announcements...</p>
                    </div>
                </div>
            ) : filteredAnnouncements.length === 0 ? (
                renderEmptyState()
            ) : (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.5 }}
                >
                    <div className="grid gap-6">
                        {filteredAnnouncements.map((announcement, index) => (
                            <motion.div
                                key={announcement._id}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.3, delay: index * 0.1 }}
                                whileHover={{ y: -3 }}
                            >
                                <Card
                                    className={`border cursor-pointer ${announcement.isImportant
                                        ? "bg-gradient-to-br from-amber-900/30 to-black border-amber-800/50 shadow-lg shadow-amber-900/10"
                                        : "bg-gradient-to-br from-gray-900/90 to-black border-gray-800/50"} 
                                        backdrop-blur-sm transition-all`}
                                    isPressable
                                    onClick={() => toggleCardExpansion(announcement._id)}
                                >
                                    <CardHeader className="flex gap-3 px-6 pt-6 pb-4">
                                        {renderAnnouncementHeader(announcement)}
                                    </CardHeader>

                                    {expandedCard === announcement._id ? (
                                        <>
                                            <Divider className="opacity-30" />
                                            <CardBody className="px-6 py-4">
                                                <motion.div
                                                    initial={{ opacity: 0 }}
                                                    animate={{ opacity: 1 }}
                                                    className="text-gray-300 leading-relaxed"
                                                >
                                                    {formatContentPointWise(announcement.content)}
                                                </motion.div>
                                            </CardBody>
                                        </>
                                    ) : (
                                        <CardBody className="px-6 py-3">
                                            <p className="text-gray-400 line-clamp-2 text-sm">
                                                {announcement.content}
                                            </p>
                                            <p className="text-[#24AE7C] text-xs mt-1">Click to read more</p>
                                        </CardBody>
                                    )}

                                    {announcement.expiryDate && (
                                        <CardFooter className="px-6 pt-0 pb-4 flex justify-between">
                                            <div className="flex items-center text-xs text-gray-500">
                                                <FiClock size={12} className="mr-1" />
                                                Valid until {format(new Date(announcement.expiryDate), 'MMMM d, yyyy')}
                                            </div>
                                        </CardFooter>
                                    )}
                                </Card>
                            </motion.div>
                        ))}
                    </div>

                    {pagination.totalPages > 1 && (
                        <div className="flex justify-center mt-8 mb-4">
                            <Pagination
                                total={pagination.totalPages}
                                initialPage={1}
                                page={pagination.page}
                                onChange={handlePageChange}
                                color="primary"
                                size="lg"
                                classNames={{
                                    cursor: "bg-gradient-to-r from-[#24AE7C] to-teal-600",
                                    item: "text-white"
                                }}
                            />
                        </div>
                    )}
                </motion.div>
            )}
        </div>
    );
};

export default Announcements;