'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useUser } from '@clerk/nextjs';

export default function Checkout() {
    const router = useRouter();
    const { user, isLoaded } = useUser();
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        if (!isLoaded || !user) return;

        const initiatePayment = async () => {
            try {
                const response = await fetch('/api/razorpay/create-order', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ plan: 'monthly', os: navigator.platform.includes('Mac') ? 'macOS' : 'Windows' }),
                });

                const orderData = await response.json();
                if (!orderData.success) throw new Error(orderData.message);

                const { orderId, amount, currency, plan } = orderData;

                const script = document.createElement('script');
                script.src = 'https://checkout.razorpay.com/v1/checkout.js';
                script.async = true;
                document.body.appendChild(script);

                script.onload = () => {
                    const options = {
                        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
                        amount: amount * 100,
                        currency,
                        name: 'Iterview Cracker',
                        description: `Subscription: ${plan}`,
                        order_id: orderId,
                        handler: async (response: any) => {
                            try {
                                const verifyResponse = await fetch('/api/razorpay/verify-payment', {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({
                                        razorpay_order_id: response.razorpay_order_id,
                                        razorpay_payment_id: response.razorpay_payment_id,
                                        razorpay_signature: response.razorpay_signature,
                                        plan,
                                    }),
                                });

                                const result = await verifyResponse.json();
                                if (result.success) {
                                    router.push(
                                        `/payment-success?method=razorpay&plan=${result.payment.plan}&amount=${result.payment.amount}&currency=${result.payment.currency}`
                                    );
                                } else {
                                    router.push('/payment-failed');
                                }
                            } catch (error) {
                                console.error('Error verifying payment:', error);
                                router.push('/payment-failed');
                            }
                        },
                        prefill: { email: user.emailAddresses[0].emailAddress },
                        theme: { color: '#00ff98' },
                    };

                    const rzp = new window.Razorpay(options);
                    rzp.open();
                };

                setLoading(false);
            } catch (error) {
                console.error('Payment initiation error:', error);
                setLoading(false);
                router.push('/payment-failed');
            }
        };

        initiatePayment();
    }, [router, user, isLoaded]);

    if (!isLoaded) {
        return <div>Loading...</div>;
    }

    return (
        <div
            style={{
                minHeight: '100vh',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: '#1a1a2e',
                color: '#ffffff',
            }}
        >
            {loading ? <h1>Preparing your payment...</h1> : <h1>Processing payment, please complete in the popup...</h1>}
        </div>
    );
}