# Use official Node.js image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json first (leverages Docker cache)
COPY package.json package-lock.json ./

# Install dependencies (only production dependencies)
RUN npm install --force

# Copy the entire project
COPY . .

# # Change ownership to non-root user (best practice for Cloud Run)
# RUN chown -R node:node /app

# # Switch to a non-root user for better security
# USER node

# Build the Next.js app
RUN npm run build

# Expose the default Next.js port
EXPOSE 3000

# Start the Next.js server
CMD ["npm", "run", "start"]
