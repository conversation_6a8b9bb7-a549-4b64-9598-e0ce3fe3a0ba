import { Button } from '@nextui-org/button';
import { Bitter } from 'next/font/google';
import React from 'react';

import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

import WaterDropGrid from '../ui/WaterDropGrid';

import OurTeam from './OurTeam';

const bitter = Bitter({ subsets: ['latin'] });

const WhoWeAre = () => {
  return (
    <section className='relative mb-8 py-20 sm:py-28 md:px-8 xl:py-28 2xl:py-36'>
      <div className='text-slat-100 flex items-center justify-center overflow-hidden px-8'>
        <WaterDropGrid />

        <div className='relative mx-auto max-w-6xl'>
          <div className='pointer-events-none relative z-10'>
            <div className='opacity-1 transform:none'>
              <h1 className={`${bitter.className} text-4xl lg:text-5xl`}>
                <b>Who We Are</b>
                <span className='select-none text-6xl text-[#EF4444]'>.</span>
              </h1>

              <div
                className='absolute inset-x-0 inset-y-1 z-20 bg-indigo-600'
                style={{ left: '100%' }}
              ></div>
            </div>
          </div>
          <div className='pointer-events-auto relative z-10 mt-6 flex justify-between gap-20 sm:mt-11'>
            <div className='opacity-1 transform:none flex flex-col items-start gap-12'>
              <p className='pointer-events-auto max-w-3xl bg-black/90 text-base font-normal text-neutral-300  sm:text-lg '>
                At <b>Interview Cracker</b>, we are driven by a passion for empowering developers to excel in technical interviews. As Computer Science Engineering students, we understand the challenges developers face during coding interviews. That’s why we’ve created an innovative AI-powered tool designed to help you solve interview questions with confidence and ease.
                <br /><br />
                Our app features a discreet floating window that provides real-time assistance without requiring screenshots or disrupting your workflow. Whether you’re solving algorithmic problems, debugging code, or designing scalable systems, our platform equips you with optimized solutions and step-by-step guidance to ace your interviews.
                <br /><br />
                With a focus on discretion and efficiency, our tool integrates seamlessly into your screen, ensuring no interruptions during video conferencing tools like Google Meet, Zoom, or Microsoft Teams. We believe in helping developers showcase their true potential while staying focused on what matters most—solving problems and impressing interviewers.
              </p>
              <DialogDemo />

              <div
                className='absolute inset-x-0 inset-y-1 z-20 bg-indigo-600'
                style={{ left: '100%' }}
              ></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhoWeAre;

function DialogDemo() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          color='primary'
          variant='shadow'
          radius='full'
          className='z-30 mb-4'
        >
          Meet the Team
        </Button>
      </DialogTrigger>
      <DialogContent className='remove-scrollbar max-h-[80vh] max-w-7xl overflow-y-scroll'>
        <DialogHeader className='mt-5'>
          <DialogTitle className='text-center text-3xl font-bold lg:text-4xl xl:text-5xl'>
            The Minds Behind the Innovation
          </DialogTitle>
          <DialogDescription className='text-center'>
            Yash Budhia and Vaibhav Raina—two passionate developers dedicated to revolutionizing technical interviews.
          </DialogDescription>
        </DialogHeader>
        <OurTeam />
        <DialogFooter>
          <DialogClose asChild>
            <Button radius='lg' size='md' variant='ghost' type='submit'>
              Close
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}