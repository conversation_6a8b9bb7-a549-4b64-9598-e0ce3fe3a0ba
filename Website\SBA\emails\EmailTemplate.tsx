import { Body, Container, Head, Html, Img, Preview, Section, Text, Hr } from '@react-email/components';
import * as React from 'react';

interface EmailProps {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export const ContactEmail = ({ name, email, subject, message }: EmailProps) => (
  <Html>
    <Head />
    <Preview>New contact form submission from {name}</Preview>
    <Body style={main}>
      <Container style={container}>
        <Section style={header}>
          <Img
            src="https://res.cloudinary.com/dtemmbo4i/image/upload/v1741707548/logo_ngsuv8.png"
            width="50"
            height="70"
            alt="Interview Cracker_LOGO"
            style={{ ...logo, width: '50px', height: '70px' }}
          />
        </Section>
        <Text style={title}>
          <strong>@admin</strong>, a new contact form submission has been received.
        </Text>
        <Hr />
        <Section style={detailsSection}>
          <Text style={formDetails}>Form Details</Text>
          <Text style={text}>Username: <strong>{name}</strong></Text>
          <Text style={text}>Email: <strong>{email}</strong></Text>
          <Text style={text}>Subject: <strong>{subject}</strong></Text>
          <Text style={text}>Message: <strong>{message}</strong></Text>
        </Section>
        <Hr />
        <Text style={footer}>Interview Cracker Bangalore</Text>
      </Container>
    </Body>
  </Html>
);

ContactEmail.PreviewProps = {} as EmailProps;

export default ContactEmail;

const main = {
  backgroundColor: '#ffffff',
  color: '#24292e',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',
};

const container = {
  maxWidth: '500px',
  margin: '0 auto',
  padding: '20px 40px',
  backgroundColor: '#ffffff',
  borderRadius: '5px',
  border: 'solid 1px #dedede',
  marginTop: '10px',
};

const detailsSection = {
  textAlign: 'center' as const,
  margin: '40px 0',
};

const header = {
  padding: '20px 0',
  textAlign: 'center' as const,
};

const logo = {
  width: '50',
  height: '70',
};

const title = {
  fontSize: '27px',
  lineHeight: 1.25,
};

const formDetails = {
  fontSize: '26px',
  textAlign: 'center' as const,
  marginBottom: '70px',
};

const text = {
  margin: '0 0 10px 0',
  textAlign: 'left' as const,
  fontSize: '20px',
};

const footer = {
  color: '#6a737d',
  fontSize: '12px',
  textAlign: 'center' as const,
  marginTop: '60px',
};