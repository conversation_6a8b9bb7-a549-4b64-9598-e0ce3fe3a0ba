import Image from 'next/image';
import Link from 'next/link';
import {
  FaXTwitter,
} from 'react-icons/fa6';
import { twMerge } from 'tailwind-merge';

import { linkdata } from '@/data';

const FooterExp = () => {
  const currentYear = new Date().getFullYear();
  const socialLinks = [
    // {
    //   href: 'https://www.facebook.com/sunil.bhor.31/',
    //   icon: FaFacebook,
    //   label: 'Facebook',
    // },
    // {
    //   href: 'https://www.instagram.com/sdbhor/',
    //   icon: FaInstagram,
    //   label: 'Instagram',
    // },
    { href: 'https://x.com/interviewCrac', icon: FaXTwitter, label: 'Twitter' },
    // {
    //   href: 'https://www.linkedin.com/in/sdbhor/',
    //   icon: FaLinkedin,
    //   label: 'LinkedIn',
    // },
  ];

  const iconClass =
    'text-gray-700 text-base hover:text-gray-700/75 dark:text-white dark:hover:text-white/75';

  return (
    <footer className='z-50 w-full border-t-white/10 dark:border-t-1 dark:bg-black'>
      <div className='px-5 pb-7 pt-16 sm:px-6 lg:px-8'>
        {/* Logo Section */}
        <div className='flex justify-center text-teal-600 dark:text-teal-300'>
          <Link href='/'>
            <Image
              src='/logo.png'
              width={48}
              height={72}
              loading='eager'
              className='pointer-events-none h-auto w-12 select-none'
              alt='company logo'
            />
          </Link>
        </div>

        {/* Tagline */}
        <p className='mx-auto mt-6 max-w-md text-center leading-relaxed text-gray-500 dark:text-white/60'>
          Code , Crack , Conquer
        </p>

        {/* Navigation Links */}
        <ul className='mx-auto mt-12 flex max-w-md flex-wrap justify-center gap-6 md:gap-8'>
          {linkdata.map((item) => (
            <li
              key={item.id}
              className={twMerge(iconClass, 'transition-all ease-in-out')}
            >
              {item.next ? (
                <Link className={iconClass} href={item.link}>
                  {item.title}
                </Link>
              ) : (
                <a className={iconClass} href={item.link}>
                  {item.title}
                </a>
              )}
            </li>
          ))}
        </ul>

        {/* Social Icons */}
        <ul className='mt-12 flex items-center justify-center gap-6 md:gap-8'>
          {socialLinks.map(({ href, icon: Icon, label }) => (
            <li key={label}>
              <a
                href={href}
                rel='noreferrer'
                target='_blank'
                className={iconClass}
              >
                <span className='sr-only'>{label}</span>
                <Icon className='pointer-events-none size-5 select-none' />
              </a>
            </li>
          ))}
        </ul>

        {/* Copyright */}
        <p className='mx-auto mt-12 text-center text-sm text-neutral-400'>
          &copy; {currentYear} Interview Cracker.{' '}
          <Link
            className='transition-all hover:text-neutral-100 hover:underline hover:underline-offset-4'
            href='/copyright'
          >
            All rights reserved
          </Link>
          .
        </p>
      </div>
    </footer>
  );
};

export default FooterExp;
