import { NextRequest, NextResponse } from 'next/server';

// Tell Next.js this route should be dynamically rendered
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
    try {
        const isDev = process.env.NODE_ENV === 'development';
        console.log('Environment:', isDev ? 'development' : 'production');

        const apiKey = process.env.IPGEOLOCATION_API_KEY;
        if (!apiKey) {
            console.error('API key not found in environment variables');
            return NextResponse.json({
                country: "United States",
                countryCode: "US",
                currency: "USD",
                symbol: "$"
            }, { status: 200 });
        }

        // Get client IP with fallback
        let ip = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '';

        // Only use fallback IPs if we're on localhost and can't determine the real IP
        if (!ip || ip === '::1' || ip === '127.0.0.1') {
            // Check if we should use an Indian IP for testing (via environment variable)
            const useIndianIP = process.env.USE_INDIAN_IP === 'true';

            if (useIndianIP) {
                // Use an Indian IP address for testing INR pricing
                ip = '**************'; // Indian IP
                console.log('Using Indian IP for geolocation testing:', ip);
            } else {
                // Use a public IP lookup service to get the real external IP
                try {
                    const ipifyResponse = await fetch('https://api.ipify.org?format=json', { cache: 'no-store' });
                    if (ipifyResponse.ok) {
                        const ipData = await ipifyResponse.json();
                        ip = ipData.ip;
                        console.log('Retrieved real external IP:', ip);
                    } else {
                        // Fallback to US IP if we can't determine the real IP
                        ip = '*******';
                        console.log('Could not determine real IP, using fallback:', ip);
                    }
                } catch (ipError) {
                    // Fallback to US IP if the service fails
                    ip = '*******';
                    console.log('Error getting real IP, using fallback:', ip);
                }
            }
        } else {
            console.log('Using client IP for geolocation:', ip);
        }

        // If we have multiple IPs (from proxies), use the first one
        if (ip.includes(',')) {
            ip = ip.split(',')[0].trim();
            console.log('Using first IP from proxy chain:', ip);
        }

        try {
            const response = await fetch(`https://api.ipgeolocation.io/ipgeo?apiKey=${apiKey}&ip=${ip}`, {
                headers: { 'Accept': 'application/json' },
                cache: 'no-store'
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`IP Geolocation API error (${response.status}):`, errorText);
                return NextResponse.json({
                    country: "United States",
                    countryCode: "US",
                    currency: "USD",
                    symbol: "$"
                }, { status: 200 });
            }

            const data = await response.json();

            console.log('Geolocation API response structure:',
                Object.keys(data).join(', '),
                data.currency ? `Currency fields: ${Object.keys(data.currency).join(', ')}` : 'No currency data');

            const currencyCode = data.currency && data.currency.code ? data.currency.code : "USD";
            const currencySymbol = data.currency && data.currency.symbol ? data.currency.symbol : "$";

            console.log('Geolocation data received for country:', data.country_name,
                'with currency:', currencyCode);

            return NextResponse.json({
                country: data.country_name || "United States",
                countryCode: data.country_code2 || "US",
                currency: currencyCode,
                symbol: currencySymbol
            }, { status: 200 });
        } catch (fetchError) {
            console.error('Fetch error:', fetchError instanceof Error ? fetchError.message : String(fetchError));
            return NextResponse.json({
                country: "United States",
                countryCode: "US",
                currency: "USD",
                symbol: "$"
            }, { status: 200 });
        }
    } catch (error) {
        console.error('Error in /api/get-region:', error instanceof Error ? error.message : String(error));
        return NextResponse.json({
            country: "United States",
            countryCode: "US",
            currency: "USD",
            symbol: "$"
        }, { status: 200 });
    }
}