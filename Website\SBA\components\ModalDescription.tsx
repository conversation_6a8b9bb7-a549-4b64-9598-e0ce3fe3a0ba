import { ServiceCardsDescription } from '@/data';

interface ModalContentProps {
  id: number;
  isInvalid?: boolean;
}

const ModalContent = ({ id, isInvalid = false }: ModalContentProps) => {
  const card = ServiceCardsDescription.find((card) => card.id === id);

  if (isInvalid && id === 0 && !card) {
    return (
      <p className='py-10 text-center text-white/70'>
        Request for a service that is currently unavailable. It might not yet be
        listed on our website.
      </p>
    );
  }

  if (!card) {
    return (
      <p className='py-10 text-center text-white/70'>
        No content available for the selected service.
      </p>
    );
  }

  return (
    <div className='flex items-center justify-center gap-20 px-3 py-16 lg:px-5 xl:py-14'>
      <div className='flex max-w-4xl flex-col gap-9'>
        <h2 className='text-xl'>
          Main Services Provided by Interview Cracker for {card.title}
        </h2>
        <div className='flex flex-col gap-2'>
          <ul className='pl-5'>
            {card.list.map((item, index) => (
              <li key={index} className='my-2 list-disc'>
                <b>{item.title}</b>
                <span className='text-white/70'>{item.description}</span>
              </li>
            ))}
          </ul>
          {card.sectors && (
            <div className='flex flex-col'>
              <h2 className='my-5 text-xl'>Sectors we serve</h2>
              <ul className='pl-5'>
                {card.sectors.map((sector, index) => (
                  <li key={index} className='my-2 list-disc'>
                    <b>{sector.title}</b>
                    <span className='text-white/70'>{sector.description}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
          {card.coverage && (
            <div className='flex flex-col'>
              <h2 className='my-5 text-xl'>Coverage</h2>
              <ul className='pl-5'>
                {card.coverage.asset_classes.map((coverage, index) => (
                  <li key={index} className='my-2 list-disc'>
                    {coverage.title}
                    <span className='text-white/70'>
                      {coverage.description}
                    </span>
                  </li>
                ))}
                {card.coverage.valuation_purpose.map((purpose, index) => (
                  <li key={index} className='my-2 list-disc'>
                    {purpose.title}
                    <span className='text-white/60'>{purpose.description}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
          <span className='my-5 text-sm text-gray-500'>
            *Note: The list of services provided may vary depending on the
            specific requirements of the project and the client's needs.
          </span>
        </div>
        <p>{card.description}</p>
      </div>
    </div>
  );
};

export default ModalContent;
