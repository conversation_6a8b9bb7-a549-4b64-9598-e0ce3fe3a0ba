"use client";

import { motion } from "framer-motion";

// Timeline Data
const timelineData = [
    { date: "2025-01-01", users: 0, price: "$0", features: ["Launch Date"] },
    {
        date: "2025-01-20",
        users: 30,
        price: "$7",
        features: ["Remains Undetectable", "Can send screenshots", "Use top models"],
    },
    {
        date: "2025-02-10",
        users: 50,
        price: "$10",
        features: [
            "Remains Undetectable",
            "Can send screenshots",
            "Select LLM model",
            "Get system design chat",
        ],
    },
    {
        date: "2025-04-11",
        users: 100,
        price: "$40",
        features: [
            "Remains Undetectable",
            "Can send screenshots",
            "Select LLM model",
            "Get system design chat",
            "Real-time audio transcription",
        ],
    },
];

// Current user count - hardcoded to 39
const currentUserCount = 39;

const startDate = new Date("2025-01-01");
const endDate = new Date("2025-04-11");
const totalDays = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);

// eslint-disable-next-line no-unused-vars
const calculatePosition = (dateString: string) => {
    const itemDate = new Date(dateString);
    const daysFromStart = (itemDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
    return (daysFromStart / totalDays) * 100;
};

interface PricingTimelineProps {
    className?: string;
}

const PricingTimeline = ({ className }: PricingTimelineProps) => {
    // Get current price tier based on date
    const getCurrentPrice = () => {
        const today = new Date();
        // Reverse loop to get the latest applicable price
        for (let i = timelineData.length - 1; i >= 0; i--) {
            if (today >= new Date(timelineData[i].date)) {
                return timelineData[i].price;
            }
        }
        return timelineData[0].price;
    };

    const currentPrice = getCurrentPrice();

    // Find current tier based on user count
    const getCurrentTier = () => {
        // Since user count is 39, we're between the first and second tier
        if (currentUserCount <= 30) {
            return 0; // First pricing tier (index 1 in timelineData)
        } else if (currentUserCount <= 50) {
            return 1; // Second pricing tier (index 2 in timelineData)
        } else {
            return 2; // Third pricing tier (index 3 in timelineData)
        }
    };

    const currentTierIndex = getCurrentTier();

    return (
        <div className={`flex w-full flex-col items-center py-12 ${className}`}>
            {/* Progress Indicator */}
            <motion.div
                className="relative mx-auto mb-6 w-full max-w-6xl"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1.5 }}
            >                {/* Price Increase Timeline and Current Users removed */}

                {/* Horizontal Timeline Component */}
                <div className="relative mb-8 flex h-20 w-full items-center justify-between px-4">
                    {/* Timeline Line */}
                    <motion.div
                        className="from-purple-600 absolute inset-x-0 h-1 bg-gradient-to-r to-blue-400"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ duration: 2, ease: "easeOut" }}
                    />

                    {/* Timeline Points */}
                    <div className="relative flex w-full justify-between">
                        {timelineData.map((item, index) => (
                            <motion.div
                                key={index}
                                className="relative flex flex-col items-center"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: index * 0.2, duration: 0.6 }}
                            >
                                {/* User Count Marker - Only show if we're at a specific point */}
                                {index > 0 && index - 1 === currentTierIndex && (
                                    <motion.div
                                        className="absolute -top-12 h-16 w-5 bg-gradient-to-b from-yellow-400 to-transparent"
                                        initial={{ height: 0, opacity: 0 }}
                                        animate={{ height: 60, opacity: 0.6 }}
                                        transition={{ duration: 1.5, repeat: Infinity, repeatType: "reverse" }}
                                    />
                                )}

                                {/* Marker */}
                                <motion.div
                                    className={`z-10 size-8 rounded-full border-4 transition-transform duration-300 ${index > 0 && index - 1 === currentTierIndex
                                        ? "border-yellow-600 bg-yellow-400 shadow-lg shadow-yellow-400/50"
                                        : "border-gray-600 bg-gray-400"
                                        }`}
                                    whileHover={{ scale: 1.2, boxShadow: "0 0 15px rgba(250, 204, 21, 0.6)" }}
                                />

                                {/* Date Label */}
                                <p className="mt-3 text-sm font-medium text-white">
                                    {new Date(item.date).toLocaleDateString("en-US", { month: "short", day: "numeric" })}
                                </p>

                                {/* Price */}
                                <p className="bg-gradient-to-r from-yellow-400 to-amber-600 bg-clip-text text-xl font-bold text-transparent">
                                    {item.price}
                                </p>
                            </motion.div>
                        ))}
                    </div>
                </div>
            </motion.div>

            {/* Enhanced Pricing Tiers Section */}
            <motion.div
                className="mx-auto mb-8 flex w-full max-w-6xl justify-center px-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1.2, ease: "easeOut", delay: 0.2 }}
            >
                <div className="w-full rounded-2xl border border-gray-800/50 bg-black p-8 shadow-2xl backdrop-blur-md">
                    <h2 className="mb-8 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-center text-4xl font-extrabold text-transparent">
                        Current Price: <span className="animate-pulse">{currentPrice}</span>
                    </h2>

                    <div className="relative flex flex-col items-stretch justify-between gap-6 md:flex-row">
                        {timelineData.slice(1).map((tier, index) => (
                            <div key={tier.date} className="relative flex flex-1 flex-col">
                                <motion.div
                                    className={`flex-1 bg-gradient-to-b p-6 text-center ${index === currentTierIndex
                                        ? "border-yellow-500/30 from-gray-800/80 to-gray-900/90"
                                        : "border-gray-700/40 from-gray-800/50 to-gray-900/70"
                                        } rounded-xl border shadow-lg backdrop-blur-sm ${index === currentTierIndex ? "shadow-yellow-500/20" : ""
                                        }`}
                                    whileHover={{ scale: 1.02, boxShadow: "0 0 20px rgba(255, 215, 0, 0.2)" }}
                                    whileTap={{ scale: 0.98 }}
                                    transition={{ duration: 0.4, type: "spring" }}
                                >
                                    <p className="text-sm font-medium text-gray-300">First {tier.users} Users</p>
                                    <p className="my-3 text-3xl font-bold text-white">{tier.price}</p>
                                    <p className="mb-4 text-xs text-gray-400">
                                        {new Date(tier.date).toLocaleDateString("en-US", { month: "long", day: "numeric", year: "numeric" })}
                                    </p>
                                    <ul className="space-y-2 text-xs text-gray-400">
                                        {tier.features.map((feature, i) => (
                                            <motion.li
                                                key={i}
                                                className="flex items-center justify-center"
                                                initial={{ opacity: 0, x: -10 }}
                                                animate={{ opacity: 1, x: 0 }}
                                                transition={{ delay: i * 0.1, duration: 0.5 }}
                                            >
                                                <span className="mr-1 text-green-400">✓</span> {feature}
                                            </motion.li>
                                        ))}
                                    </ul>

                                    {/* Highlight Current Tier */}
                                    {index === currentTierIndex && (
                                        <motion.div
                                            className="absolute -top-3 left-1/2 -translate-x-1/2 rounded-full bg-gradient-to-r from-yellow-400 to-amber-500 px-3 py-1 text-xs font-bold text-black"
                                            initial={{ scale: 0.8, opacity: 0 }}
                                            animate={{ scale: 1, opacity: 1 }}
                                            transition={{ delay: 1, duration: 0.5 }}
                                        >
                                            CURRENT
                                        </motion.div>
                                    )}
                                </motion.div>

                                {/* Arrow connecting to next tier */}
                                {index < timelineData.slice(1).length - 1 && (
                                    <div className="absolute -right-3 top-1/2 z-10 hidden -translate-y-1/2 items-center md:flex">
                                        <motion.div
                                            initial={{ width: 0 }}
                                            animate={{ width: "24px" }}
                                            transition={{ delay: 0.8 + index * 0.2, duration: 0.5 }}
                                            className="to-purple-600 h-0.5 bg-gradient-to-r from-blue-400"
                                        />
                                        <motion.div
                                            initial={{ opacity: 0, x: -5 }}
                                            animate={{ opacity: 1, x: 0 }}
                                            transition={{ delay: 1 + index * 0.2, duration: 0.5 }}
                                            className="text-purple-500"
                                        >
                                            ▶
                                        </motion.div>
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>

                    <motion.div
                        className="from-purple-600 mt-8 h-2 w-full rounded-full bg-gradient-to-r via-blue-500 to-cyan-400"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ duration: 2.5, ease: "easeInOut", repeat: Infinity, repeatType: "reverse" }}
                    />                </div>
            </motion.div>
        </div>
    );
};

export default PricingTimeline;
