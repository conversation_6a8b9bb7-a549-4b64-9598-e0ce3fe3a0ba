"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ pageWrapperTemplate),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps)\n/* harmony export */ });\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @sentry/nextjs */ \"@sentry/nextjs\");\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Html, {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Head, {}, void 0, false, {\n                fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"body\", {\n                suppressHydrationWarning: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Main, {}, void 0, false, {\n                        fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.NextScript, {}, void 0, false, {\n                        fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\pages\\\\_document.tsx\",\n                lineNumber: 7,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 9\n    }, this);\n}\n\nvar serverComponentModule = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    default: Document\n});\n\n/*\n * This file is a template for the code which will be substituted when our webpack loader handles non-API files in the\n * `pages/` directory.\n *\n * We use `__SENTRY_WRAPPING_TARGET_FILE__.cjs` as a placeholder for the path to the file being wrapped. Because it's not a real package,\n * this causes both TS and ESLint to complain, hence the pragma comments below.\n */\n\n\nconst userPageModule = serverComponentModule ;\n\nconst pageComponent = userPageModule ? userPageModule.default : undefined;\n\nconst origGetInitialProps = pageComponent ? pageComponent.getInitialProps : undefined;\nconst origGetStaticProps = userPageModule ? userPageModule.getStaticProps : undefined;\nconst origGetServerSideProps = userPageModule ? userPageModule.getServerSideProps : undefined;\n\n// Rollup will aggressively tree-shake what it perceives to be unused properties\n// on objects. Because the key that's used to index into this object (/_document)\n// is replaced during bundling, Rollup can't see that these properties are in fact\n// used. Using `Object.freeze` signals to Rollup that it should not tree-shake\n// this object.\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst getInitialPropsWrappers = Object.freeze({\n  '/_app': _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapAppGetInitialPropsWithSentry,\n  '/_document': _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapDocumentGetInitialPropsWithSentry,\n  '/_error': _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapErrorGetInitialPropsWithSentry,\n});\n\nconst getInitialPropsWrapper = getInitialPropsWrappers['/_document'] || _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapGetInitialPropsWithSentry;\n\nif (pageComponent && typeof origGetInitialProps === 'function') {\n  pageComponent.getInitialProps = getInitialPropsWrapper(origGetInitialProps) ;\n}\n\nconst getStaticProps =\n  typeof origGetStaticProps === 'function'\n    ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapGetStaticPropsWithSentry(origGetStaticProps, '/_document')\n    : undefined;\nconst getServerSideProps =\n  typeof origGetServerSideProps === 'function'\n    ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapGetServerSidePropsWithSentry(origGetServerSideProps, '/_document')\n    : undefined;\n\nconst pageWrapperTemplate = pageComponent ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapPageComponentWithSentry(pageComponent ) : pageComponent;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "@sentry/nextjs":
/*!*********************************!*\
  !*** external "@sentry/nextjs" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@sentry/nextjs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("./pages/_document.tsx")));
module.exports = __webpack_exports__;

})();