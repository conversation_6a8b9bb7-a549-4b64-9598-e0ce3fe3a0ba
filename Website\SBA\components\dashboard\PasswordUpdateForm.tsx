"use client";

import { useUser } from "@clerk/nextjs";
import { <PERSON><PERSON>, Card, Input } from "@nextui-org/react";
import { useState, useEffect } from "react";
import { FcGoogle } from "react-icons/fc";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Off, <PERSON>Lock } from "react-icons/fi";
import { toast } from "sonner";

export default function PasswordUpdateForm() {
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isExternalAuth, setIsExternalAuth] = useState(false);
  const { isLoaded, user } = useUser();

  // Check if user is authenticated with an external provider
  useEffect(() => {
    if (isLoaded && user) {
      // Check if user is authenticated with Google or other OAuth providers
      const externalAuth = user.externalAccounts.some(account =>
        account.provider === "google"
      );
      setIsExternalAuth(externalAuth);
    }
  }, [isLoaded, user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Form validation
    if (newPassword !== confirmPassword) {
      toast.error("New passwords don't match");
      return;
    }

    if (newPassword.length < 8) {
      toast.error("New password must be at least 8 characters");
      return;
    }

    setIsLoading(true);
    try {
      // Use Clerk's password update API
      await user?.updatePassword({
        currentPassword,
        newPassword
      });

      toast.success("Password updated successfully");

      // Clear form
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");

    } catch (error: any) {
      console.error("Password update error:", error);
      toast.error(error.errors?.[0]?.message || "Failed to update password");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="relative overflow-hidden border border-slate-800/60 bg-[#0f172a]/60 p-6 shadow-xl backdrop-blur-sm">
      <div className="absolute right-0 top-0 -z-0 size-40 rounded-full bg-blue-500/5 opacity-70 blur-2xl"></div>

      <div className="relative z-10">
        <h2 className="mb-6 flex items-center gap-2 text-xl font-semibold text-white">
          <FiKey className="text-[#24AE7C]" />
          Change Password
        </h2>

        {isExternalAuth ? (
          <div className="rounded-lg border border-gray-700/50 bg-gray-800/50 p-6">
            <div className="mb-4 flex items-center gap-3">
              <div className="rounded-full bg-white/10 p-2">
                <FcGoogle size={24} />
              </div>
              <h3 className="text-lg font-medium text-white">External Authentication</h3>
            </div>
            <p className="mb-4 text-gray-300">
              You are signed in through Google. Password changes are managed through your Google account.
            </p>
            <a
              href="https://myaccount.google.com/security"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 text-[#24AE7C] hover:underline"
            >
              <FiLock size={16} />
              Manage your Google account security
            </a>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Input
                label="Current Password"
                type={showCurrentPassword ? "text" : "password"}
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                variant="bordered"
                classNames={{
                  label: "text-gray-400",
                  input: "text-white",
                  inputWrapper: "border-gray-600 hover:border-[#24AE7C]"
                }}
                endContent={
                  <button
                    type="button"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    className="text-gray-500 hover:text-gray-300 focus:outline-none"
                  >
                    {showCurrentPassword ? <FiEyeOff size={18} /> : <FiEye size={18} />}
                  </button>
                }
                required
              />
            </div>

            <div>
              <Input
                label="New Password"
                type={showNewPassword ? "text" : "password"}
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                variant="bordered"
                classNames={{
                  label: "text-gray-400",
                  input: "text-white",
                  inputWrapper: "border-gray-600 hover:border-[#24AE7C]"
                }}
                endContent={
                  <button
                    type="button"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    className="text-gray-500 hover:text-gray-300 focus:outline-none"
                  >
                    {showNewPassword ? <FiEyeOff size={18} /> : <FiEye size={18} />}
                  </button>
                }
                required
              />
            </div>

            <div>
              <Input
                label="Confirm New Password"
                type={showConfirmPassword ? "text" : "password"}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                variant="bordered"
                classNames={{
                  label: "text-gray-400",
                  input: "text-white",
                  inputWrapper: "border-gray-600 hover:border-[#24AE7C]"
                }}
                endContent={
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="text-gray-500 hover:text-gray-300 focus:outline-none"
                  >
                    {showConfirmPassword ? <FiEyeOff size={18} /> : <FiEye size={18} />}
                  </button>
                }
                required
              />
            </div>

            <div className="pt-4">
              <Button
                type="submit"
                color="primary"
                className="w-full bg-[#24AE7C] text-white"
                isLoading={isLoading}
              >
                Update Password
              </Button>
            </div>
          </form>
        )}
      </div>
    </Card>
  );
}