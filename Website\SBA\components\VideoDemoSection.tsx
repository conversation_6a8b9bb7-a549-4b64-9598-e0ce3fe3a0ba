'use client';

// components/VideoDemoSection.tsx
import React, { useEffect, useState } from 'react';

// Define the props interface
interface VideoDemoSectionProps {
    videoSource1: string;
    videoSource2: string;
}

const VideoDemoSection: React.FC<VideoDemoSectionProps> = ({
    videoSource1,
    videoSource2,
}) => {
    // Use state to control rendering videos only on client-side
    const [isClient, setIsClient] = useState(false);

    // This effect runs only on the client
    useEffect(() => {
        setIsClient(true);
    }, []);

    return (
        <section className="w-full py-24 relative">
            <div className="max-w-7xl mx-auto w-full px-5 sm:px-10">
                {/* Section heading with more spacing */}
                <div className="text-center mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-red-400 to-purple-600">
                        Experience Our Platform
                    </h2>
                    <p className="text-gray-300 max-w-2xl mx-auto text-lg">
                        See how our solution works from both perspectives - seamlessly integrated for an optimal experience.
                    </p>
                </div>

                {/* Videos container with significantly increased spacing between videos */}
                <div className="flex flex-col sm:flex-row gap-16 md:gap-20 lg:gap-24 justify-center items-center">
                    {/* Video 1 with Label - added more spacing */}
                    <div className="w-full sm:w-1/2 relative group">
                        <div className="absolute inset-0 bg-gradient-to-r from-red-500/20 to-purple-600/20 rounded-xl transform -rotate-1 scale-[1.03] group-hover:scale-[1.05] transition-all duration-300 -z-10"></div>
                        <h3 className="text-red-400 text-xl font-semibold mb-4 flex items-center">
                            <span className="w-3 h-3 bg-red-400 rounded-full mr-3"></span>
                            YOUR VIEW
                        </h3>
                        <div className="w-full aspect-video rounded-xl overflow-hidden shadow-lg border border-gray-700 relative z-30">
                            {isClient ? (
                                <video
                                    className="w-full h-full object-cover"
                                    autoPlay
                                    loop
                                    muted
                                    playsInline
                                >
                                    <source src={videoSource1} type="video/mp4" />
                                    Your browser does not support the video tag.
                                </video>
                            ) : (
                                <div className="w-full h-full bg-gray-900 flex items-center justify-center">
                                    <div className="animate-pulse bg-gray-800 rounded-md h-full w-full"></div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Video 2 with Label - added more spacing */}
                    <div className="w-full sm:w-1/2 relative group">
                        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-red-500/20 rounded-xl transform rotate-1 scale-[1.03] group-hover:scale-[1.05] transition-all duration-300 -z-10"></div>
                        <h3 className="text-purple-400 text-xl font-semibold mb-4 flex items-center">
                            <span className="w-3 h-3 bg-purple-400 rounded-full mr-3"></span>
                            INTERVIEWER'S VIEW
                        </h3>
                        <div className="w-full aspect-video rounded-xl overflow-hidden shadow-lg border border-gray-700 relative z-30">
                            {isClient ? (
                                <video
                                    className="w-full h-full object-cover"
                                    autoPlay
                                    loop
                                    muted
                                    playsInline
                                >
                                    <source src={videoSource2} type="video/mp4" />
                                    Your browser does not support the video tag.
                                </video>
                            ) : (
                                <div className="w-full h-full bg-gray-900 flex items-center justify-center">
                                    <div className="animate-pulse bg-gray-800 rounded-md h-full w-full"></div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default VideoDemoSection;