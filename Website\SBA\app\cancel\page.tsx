import Link from 'next/link'; // Remove if not using Next.js
import React from 'react';

const CancellationPolicyPage = () => {
  return (
    <div className='mx-auto flex items-center justify-center px-5 py-24 md:py-28'>
      <div className='flex max-w-4xl flex-col items-center justify-center gap-12'>
        <h2 className='text-center text-2xl font-bold md:text-3xl'>
          Cancellation Policy
        </h2>
        <div className='flex flex-col gap-5'>
          <p>
            Thank you for choosing InterviewCracker.in, an Electron-based desktop application and website (collectively, &quot;the Service&quot;). This Cancellation Policy outlines the terms regarding cancellations and refunds for purchases or subscriptions made through the Service.
          </p>
          <p>
            We aim to provide clarity and fairness in our policies. Please read this carefully before using the Service, as all transactions are subject to the terms below.
          </p>
          <div className='flex flex-col gap-3'>
            <p>
              Key details of our cancellation policy:
            </p>
            <ul className='list-disc pl-4'>
              <li>
                <span className='font-semibold'>No Cancellations or Refunds</span>: All purchases or subscriptions for InterviewCracker.in are final. We do not offer cancellations, refunds, or exchanges once a transaction is completed, regardless of usage, dissatisfaction, or technical issues.
              </li>
              <li>
                <span className='font-semibold'>Subscription Terms</span>: If you subscribe to a recurring plan, your subscription will remain active until the end of the billing period. You may choose not to renew, but no prorated refunds will be issued for unused time.
              </li>
              <li>
                <span className='font-semibold'>Pre-Purchase Consideration</span>: We encourage you to evaluate the Service thoroughly before purchasing. Trial versions or demos, if available, should be used to ensure the Service meets your needs.
              </li>
              <li>
                <span className='font-semibold'>Exceptions</span>: Refunds or cancellations will not be provided unless required by applicable law. Any such exceptions will be handled on a case-by-case basis at our discretion.
              </li>
              <li>
                <span className='font-semibold'>Contact Us</span>: If you experience issues with the Service, we’re here to assist with troubleshooting or support, though this does not entitle you to a refund.
              </li>
            </ul>
          </div>
          <p>
            We strive to deliver a high-quality experience with InterviewCracker.in. If you have questions or need assistance, please don’t hesitate to reach out.
          </p>
          <p>
            For inquiries or support, contact us at{' '}
            <Link
              href='mailto:<EMAIL>'
              className='underline underline-offset-4'
            >
              <EMAIL>
            </Link>{' '}
            or{' '}
            <Link
              href='mailto:<EMAIL>'
              className='underline underline-offset-4'
            >
              <EMAIL>
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default CancellationPolicyPage;