import { getAuth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";

import connectDB from "@/lib/mongodb";
import Announcement from "@/model/Announcement";

// GET /api/announcements - Get all announcements
export async function GET(req: NextRequest) {
  try {
    await connectDB();
    
    // Parse query parameters
    const url = new URL(req.url);
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const page = parseInt(url.searchParams.get("page") || "1");
    const type = url.searchParams.get("type") || null;
    
    // Build query
    const query: any = {};
    if (type) query.type = type;
    
    // Only show announcements that are not expired or have no expiry date
    query.$or = [
      { expiryDate: { $gt: new Date() } },
      { expiryDate: null }
    ];

    // Get total count for pagination
    const total = await Announcement.countDocuments(query);
    
    // Get announcements
    const announcements = await Announcement.find(query)
      .sort({ isImportant: -1, publishDate: -1 })
      .limit(limit)
      .skip((page - 1) * limit);
    
    return NextResponse.json({
      announcements,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error("Error fetching announcements:", error);
    return NextResponse.json({ error: "Failed to fetch announcements" }, { status: 500 });
  }
}

// POST /api/announcements - Create new announcement (admin only)
export async function POST(req: NextRequest) {
  try {
    const auth = getAuth(req);
    if (!auth?.userId) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }
    
    await connectDB();
    
    // Check if user is admin
    const response = await fetch(`${req.nextUrl.origin}/api/user/me`, {
      headers: {
        cookie: req.headers.get("cookie") || "",
      },
    });
    
    const userData = await response.json();
    if (!userData.isAdmin) {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 });
    }
    
    // Parse request body
    const data = await req.json();
    
    // Add creator information
    data.createdBy = auth.userId;
    
    // Create new announcement
    const announcement = new Announcement(data);
    await announcement.save();
    
    return NextResponse.json(announcement, { status: 201 });
  } catch (error: any) {
    console.error("Error creating announcement:", error);
    return NextResponse.json({ 
      error: "Failed to create announcement", 
      details: error.message 
    }, { status: 500 });
  }
}