/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/resolve";
exports.ids = ["vendor-chunks/resolve"];
exports.modules = {

/***/ "(ssr)/./node_modules/resolve/index.js":
/*!***************************************!*\
  !*** ./node_modules/resolve/index.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var async = __webpack_require__(/*! ./lib/async */ \"(ssr)/./node_modules/resolve/lib/async.js\");\nasync.core = __webpack_require__(/*! ./lib/core */ \"(ssr)/./node_modules/resolve/lib/core.js\");\nasync.isCore = __webpack_require__(/*! ./lib/is-core */ \"(ssr)/./node_modules/resolve/lib/is-core.js\");\nasync.sync = __webpack_require__(/*! ./lib/sync */ \"(ssr)/./node_modules/resolve/lib/sync.js\");\n\nmodule.exports = async;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxZQUFZLG1CQUFPLENBQUMsOERBQWE7QUFDakMsYUFBYSxtQkFBTyxDQUFDLDREQUFZO0FBQ2pDLGVBQWUsbUJBQU8sQ0FBQyxrRUFBZTtBQUN0QyxhQUFhLG1CQUFPLENBQUMsNERBQVk7O0FBRWpDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50ZXJ2aWV3Y3JhY2tlci8uL25vZGVfbW9kdWxlcy9yZXNvbHZlL2luZGV4LmpzPzg3NjYiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGFzeW5jID0gcmVxdWlyZSgnLi9saWIvYXN5bmMnKTtcbmFzeW5jLmNvcmUgPSByZXF1aXJlKCcuL2xpYi9jb3JlJyk7XG5hc3luYy5pc0NvcmUgPSByZXF1aXJlKCcuL2xpYi9pcy1jb3JlJyk7XG5hc3luYy5zeW5jID0gcmVxdWlyZSgnLi9saWIvc3luYycpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IGFzeW5jO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/resolve/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/resolve/lib/async.js":
/*!*******************************************!*\
  !*** ./node_modules/resolve/lib/async.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var fs = __webpack_require__(/*! fs */ \"fs\");\nvar getHomedir = __webpack_require__(/*! ./homedir */ \"(ssr)/./node_modules/resolve/lib/homedir.js\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar caller = __webpack_require__(/*! ./caller */ \"(ssr)/./node_modules/resolve/lib/caller.js\");\nvar nodeModulesPaths = __webpack_require__(/*! ./node-modules-paths */ \"(ssr)/./node_modules/resolve/lib/node-modules-paths.js\");\nvar normalizeOptions = __webpack_require__(/*! ./normalize-options */ \"(ssr)/./node_modules/resolve/lib/normalize-options.js\");\nvar isCore = __webpack_require__(/*! is-core-module */ \"(ssr)/./node_modules/is-core-module/index.js\");\n\nvar realpathFS = process.platform !== 'win32' && fs.realpath && typeof fs.realpath.native === 'function' ? fs.realpath.native : fs.realpath;\n\nvar homedir = getHomedir();\nvar defaultPaths = function () {\n    return [\n        path.join(homedir, '.node_modules'),\n        path.join(homedir, '.node_libraries')\n    ];\n};\n\nvar defaultIsFile = function isFile(file, cb) {\n    fs.stat(file, function (err, stat) {\n        if (!err) {\n            return cb(null, stat.isFile() || stat.isFIFO());\n        }\n        if (err.code === 'ENOENT' || err.code === 'ENOTDIR') return cb(null, false);\n        return cb(err);\n    });\n};\n\nvar defaultIsDir = function isDirectory(dir, cb) {\n    fs.stat(dir, function (err, stat) {\n        if (!err) {\n            return cb(null, stat.isDirectory());\n        }\n        if (err.code === 'ENOENT' || err.code === 'ENOTDIR') return cb(null, false);\n        return cb(err);\n    });\n};\n\nvar defaultRealpath = function realpath(x, cb) {\n    realpathFS(x, function (realpathErr, realPath) {\n        if (realpathErr && realpathErr.code !== 'ENOENT') cb(realpathErr);\n        else cb(null, realpathErr ? x : realPath);\n    });\n};\n\nvar maybeRealpath = function maybeRealpath(realpath, x, opts, cb) {\n    if (opts && opts.preserveSymlinks === false) {\n        realpath(x, cb);\n    } else {\n        cb(null, x);\n    }\n};\n\nvar defaultReadPackage = function defaultReadPackage(readFile, pkgfile, cb) {\n    readFile(pkgfile, function (readFileErr, body) {\n        if (readFileErr) cb(readFileErr);\n        else {\n            try {\n                var pkg = JSON.parse(body);\n                cb(null, pkg);\n            } catch (jsonErr) {\n                cb(null);\n            }\n        }\n    });\n};\n\nvar getPackageCandidates = function getPackageCandidates(x, start, opts) {\n    var dirs = nodeModulesPaths(start, opts, x);\n    for (var i = 0; i < dirs.length; i++) {\n        dirs[i] = path.join(dirs[i], x);\n    }\n    return dirs;\n};\n\nmodule.exports = function resolve(x, options, callback) {\n    var cb = callback;\n    var opts = options;\n    if (typeof options === 'function') {\n        cb = opts;\n        opts = {};\n    }\n    if (typeof x !== 'string') {\n        var err = new TypeError('Path must be a string.');\n        return process.nextTick(function () {\n            cb(err);\n        });\n    }\n\n    opts = normalizeOptions(x, opts);\n\n    var isFile = opts.isFile || defaultIsFile;\n    var isDirectory = opts.isDirectory || defaultIsDir;\n    var readFile = opts.readFile || fs.readFile;\n    var realpath = opts.realpath || defaultRealpath;\n    var readPackage = opts.readPackage || defaultReadPackage;\n    if (opts.readFile && opts.readPackage) {\n        var conflictErr = new TypeError('`readFile` and `readPackage` are mutually exclusive.');\n        return process.nextTick(function () {\n            cb(conflictErr);\n        });\n    }\n    var packageIterator = opts.packageIterator;\n\n    var extensions = opts.extensions || ['.js'];\n    var includeCoreModules = opts.includeCoreModules !== false;\n    var basedir = opts.basedir || path.dirname(caller());\n    var parent = opts.filename || basedir;\n\n    opts.paths = opts.paths || defaultPaths();\n\n    // ensure that `basedir` is an absolute path at this point, resolving against the process' current working directory\n    var absoluteStart = path.resolve(basedir);\n\n    maybeRealpath(\n        realpath,\n        absoluteStart,\n        opts,\n        function (err, realStart) {\n            if (err) cb(err);\n            else init(realStart);\n        }\n    );\n\n    var res;\n    function init(basedir) {\n        if ((/^(?:\\.\\.?(?:\\/|$)|\\/|([A-Za-z]:)?[/\\\\])/).test(x)) {\n            res = path.resolve(basedir, x);\n            if (x === '.' || x === '..' || x.slice(-1) === '/') res += '/';\n            if ((/\\/$/).test(x) && res === basedir) {\n                loadAsDirectory(res, opts.package, onfile);\n            } else loadAsFile(res, opts.package, onfile);\n        } else if (includeCoreModules && isCore(x)) {\n            return cb(null, x);\n        } else loadNodeModules(x, basedir, function (err, n, pkg) {\n            if (err) cb(err);\n            else if (n) {\n                return maybeRealpath(realpath, n, opts, function (err, realN) {\n                    if (err) {\n                        cb(err);\n                    } else {\n                        cb(null, realN, pkg);\n                    }\n                });\n            } else {\n                var moduleError = new Error(\"Cannot find module '\" + x + \"' from '\" + parent + \"'\");\n                moduleError.code = 'MODULE_NOT_FOUND';\n                cb(moduleError);\n            }\n        });\n    }\n\n    function onfile(err, m, pkg) {\n        if (err) cb(err);\n        else if (m) cb(null, m, pkg);\n        else loadAsDirectory(res, function (err, d, pkg) {\n            if (err) cb(err);\n            else if (d) {\n                maybeRealpath(realpath, d, opts, function (err, realD) {\n                    if (err) {\n                        cb(err);\n                    } else {\n                        cb(null, realD, pkg);\n                    }\n                });\n            } else {\n                var moduleError = new Error(\"Cannot find module '\" + x + \"' from '\" + parent + \"'\");\n                moduleError.code = 'MODULE_NOT_FOUND';\n                cb(moduleError);\n            }\n        });\n    }\n\n    function loadAsFile(x, thePackage, callback) {\n        var loadAsFilePackage = thePackage;\n        var cb = callback;\n        if (typeof loadAsFilePackage === 'function') {\n            cb = loadAsFilePackage;\n            loadAsFilePackage = undefined;\n        }\n\n        var exts = [''].concat(extensions);\n        load(exts, x, loadAsFilePackage);\n\n        function load(exts, x, loadPackage) {\n            if (exts.length === 0) return cb(null, undefined, loadPackage);\n            var file = x + exts[0];\n\n            var pkg = loadPackage;\n            if (pkg) onpkg(null, pkg);\n            else loadpkg(path.dirname(file), onpkg);\n\n            function onpkg(err, pkg_, dir) {\n                pkg = pkg_;\n                if (err) return cb(err);\n                if (dir && pkg && opts.pathFilter) {\n                    var rfile = path.relative(dir, file);\n                    var rel = rfile.slice(0, rfile.length - exts[0].length);\n                    var r = opts.pathFilter(pkg, x, rel);\n                    if (r) return load(\n                        [''].concat(extensions.slice()),\n                        path.resolve(dir, r),\n                        pkg\n                    );\n                }\n                isFile(file, onex);\n            }\n            function onex(err, ex) {\n                if (err) return cb(err);\n                if (ex) return cb(null, file, pkg);\n                load(exts.slice(1), x, pkg);\n            }\n        }\n    }\n\n    function loadpkg(dir, cb) {\n        if (dir === '' || dir === '/') return cb(null);\n        if (process.platform === 'win32' && (/^\\w:[/\\\\]*$/).test(dir)) {\n            return cb(null);\n        }\n        if ((/[/\\\\]node_modules[/\\\\]*$/).test(dir)) return cb(null);\n\n        maybeRealpath(realpath, dir, opts, function (unwrapErr, pkgdir) {\n            if (unwrapErr) return loadpkg(path.dirname(dir), cb);\n            var pkgfile = path.join(pkgdir, 'package.json');\n            isFile(pkgfile, function (err, ex) {\n                // on err, ex is false\n                if (!ex) return loadpkg(path.dirname(dir), cb);\n\n                readPackage(readFile, pkgfile, function (err, pkgParam) {\n                    if (err) cb(err);\n\n                    var pkg = pkgParam;\n\n                    if (pkg && opts.packageFilter) {\n                        pkg = opts.packageFilter(pkg, pkgfile);\n                    }\n                    cb(null, pkg, dir);\n                });\n            });\n        });\n    }\n\n    function loadAsDirectory(x, loadAsDirectoryPackage, callback) {\n        var cb = callback;\n        var fpkg = loadAsDirectoryPackage;\n        if (typeof fpkg === 'function') {\n            cb = fpkg;\n            fpkg = opts.package;\n        }\n\n        maybeRealpath(realpath, x, opts, function (unwrapErr, pkgdir) {\n            if (unwrapErr) return cb(unwrapErr);\n            var pkgfile = path.join(pkgdir, 'package.json');\n            isFile(pkgfile, function (err, ex) {\n                if (err) return cb(err);\n                if (!ex) return loadAsFile(path.join(x, 'index'), fpkg, cb);\n\n                readPackage(readFile, pkgfile, function (err, pkgParam) {\n                    if (err) return cb(err);\n\n                    var pkg = pkgParam;\n\n                    if (pkg && opts.packageFilter) {\n                        pkg = opts.packageFilter(pkg, pkgfile);\n                    }\n\n                    if (pkg && pkg.main) {\n                        if (typeof pkg.main !== 'string') {\n                            var mainError = new TypeError('package “' + pkg.name + '” `main` must be a string');\n                            mainError.code = 'INVALID_PACKAGE_MAIN';\n                            return cb(mainError);\n                        }\n                        if (pkg.main === '.' || pkg.main === './') {\n                            pkg.main = 'index';\n                        }\n                        loadAsFile(path.resolve(x, pkg.main), pkg, function (err, m, pkg) {\n                            if (err) return cb(err);\n                            if (m) return cb(null, m, pkg);\n                            if (!pkg) return loadAsFile(path.join(x, 'index'), pkg, cb);\n\n                            var dir = path.resolve(x, pkg.main);\n                            loadAsDirectory(dir, pkg, function (err, n, pkg) {\n                                if (err) return cb(err);\n                                if (n) return cb(null, n, pkg);\n                                loadAsFile(path.join(x, 'index'), pkg, cb);\n                            });\n                        });\n                        return;\n                    }\n\n                    loadAsFile(path.join(x, '/index'), pkg, cb);\n                });\n            });\n        });\n    }\n\n    function processDirs(cb, dirs) {\n        if (dirs.length === 0) return cb(null, undefined);\n        var dir = dirs[0];\n\n        isDirectory(path.dirname(dir), isdir);\n\n        function isdir(err, isdir) {\n            if (err) return cb(err);\n            if (!isdir) return processDirs(cb, dirs.slice(1));\n            loadAsFile(dir, opts.package, onfile);\n        }\n\n        function onfile(err, m, pkg) {\n            if (err) return cb(err);\n            if (m) return cb(null, m, pkg);\n            loadAsDirectory(dir, opts.package, ondir);\n        }\n\n        function ondir(err, n, pkg) {\n            if (err) return cb(err);\n            if (n) return cb(null, n, pkg);\n            processDirs(cb, dirs.slice(1));\n        }\n    }\n    function loadNodeModules(x, start, cb) {\n        var thunk = function () { return getPackageCandidates(x, start, opts); };\n        processDirs(\n            cb,\n            packageIterator ? packageIterator(x, start, thunk, opts) : thunk()\n        );\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/resolve/lib/async.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/resolve/lib/caller.js":
/*!********************************************!*\
  !*** ./node_modules/resolve/lib/caller.js ***!
  \********************************************/
/***/ ((module) => {

eval("module.exports = function () {\n    // see https://code.google.com/p/v8/wiki/JavaScriptStackTraceApi\n    var origPrepareStackTrace = Error.prepareStackTrace;\n    Error.prepareStackTrace = function (_, stack) { return stack; };\n    var stack = (new Error()).stack;\n    Error.prepareStackTrace = origPrepareStackTrace;\n    return stack[2].getFileName();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvY2FsbGVyLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBLG9EQUFvRDtBQUNwRDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGVydmlld2NyYWNrZXIvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvY2FsbGVyLmpzP2RkMTYiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAoKSB7XG4gICAgLy8gc2VlIGh0dHBzOi8vY29kZS5nb29nbGUuY29tL3Avdjgvd2lraS9KYXZhU2NyaXB0U3RhY2tUcmFjZUFwaVxuICAgIHZhciBvcmlnUHJlcGFyZVN0YWNrVHJhY2UgPSBFcnJvci5wcmVwYXJlU3RhY2tUcmFjZTtcbiAgICBFcnJvci5wcmVwYXJlU3RhY2tUcmFjZSA9IGZ1bmN0aW9uIChfLCBzdGFjaykgeyByZXR1cm4gc3RhY2s7IH07XG4gICAgdmFyIHN0YWNrID0gKG5ldyBFcnJvcigpKS5zdGFjaztcbiAgICBFcnJvci5wcmVwYXJlU3RhY2tUcmFjZSA9IG9yaWdQcmVwYXJlU3RhY2tUcmFjZTtcbiAgICByZXR1cm4gc3RhY2tbMl0uZ2V0RmlsZU5hbWUoKTtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/resolve/lib/caller.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/resolve/lib/core.js":
/*!******************************************!*\
  !*** ./node_modules/resolve/lib/core.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nvar isCoreModule = __webpack_require__(/*! is-core-module */ \"(ssr)/./node_modules/is-core-module/index.js\");\nvar data = __webpack_require__(/*! ./core.json */ \"(ssr)/./node_modules/resolve/lib/core.json\");\n\nvar core = {};\nfor (var mod in data) { // eslint-disable-line no-restricted-syntax\n    if (Object.prototype.hasOwnProperty.call(data, mod)) {\n        core[mod] = isCoreModule(mod);\n    }\n}\nmodule.exports = core;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvY29yZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixtQkFBbUIsbUJBQU8sQ0FBQyxvRUFBZ0I7QUFDM0MsV0FBVyxtQkFBTyxDQUFDLCtEQUFhOztBQUVoQztBQUNBLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50ZXJ2aWV3Y3JhY2tlci8uL25vZGVfbW9kdWxlcy9yZXNvbHZlL2xpYi9jb3JlLmpzP2U2MDMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgaXNDb3JlTW9kdWxlID0gcmVxdWlyZSgnaXMtY29yZS1tb2R1bGUnKTtcbnZhciBkYXRhID0gcmVxdWlyZSgnLi9jb3JlLmpzb24nKTtcblxudmFyIGNvcmUgPSB7fTtcbmZvciAodmFyIG1vZCBpbiBkYXRhKSB7IC8vIGVzbGludC1kaXNhYmxlLWxpbmUgbm8tcmVzdHJpY3RlZC1zeW50YXhcbiAgICBpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGRhdGEsIG1vZCkpIHtcbiAgICAgICAgY29yZVttb2RdID0gaXNDb3JlTW9kdWxlKG1vZCk7XG4gICAgfVxufVxubW9kdWxlLmV4cG9ydHMgPSBjb3JlO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/resolve/lib/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/resolve/lib/homedir.js":
/*!*********************************************!*\
  !*** ./node_modules/resolve/lib/homedir.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nvar os = __webpack_require__(/*! os */ \"os\");\n\n// adapted from https://github.com/sindresorhus/os-homedir/blob/11e089f4754db38bb535e5a8416320c4446e8cfd/index.js\n\nmodule.exports = os.homedir || function homedir() {\n    var home = process.env.HOME;\n    var user = process.env.LOGNAME || process.env.USER || process.env.LNAME || process.env.USERNAME;\n\n    if (process.platform === 'win32') {\n        return process.env.USERPROFILE || process.env.HOMEDRIVE + process.env.HOMEPATH || home || null;\n    }\n\n    if (process.platform === 'darwin') {\n        return home || (user ? '/Users/' + user : null);\n    }\n\n    if (process.platform === 'linux') {\n        return home || (process.getuid() === 0 ? '/root' : (user ? '/home/' + user : null)); // eslint-disable-line no-extra-parens\n    }\n\n    return home || null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvaG9tZWRpci5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixTQUFTLG1CQUFPLENBQUMsY0FBSTs7QUFFckI7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSw2RkFBNkY7QUFDN0Y7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGVydmlld2NyYWNrZXIvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvaG9tZWRpci5qcz81NjE3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxudmFyIG9zID0gcmVxdWlyZSgnb3MnKTtcblxuLy8gYWRhcHRlZCBmcm9tIGh0dHBzOi8vZ2l0aHViLmNvbS9zaW5kcmVzb3JodXMvb3MtaG9tZWRpci9ibG9iLzExZTA4OWY0NzU0ZGIzOGJiNTM1ZTVhODQxNjMyMGM0NDQ2ZThjZmQvaW5kZXguanNcblxubW9kdWxlLmV4cG9ydHMgPSBvcy5ob21lZGlyIHx8IGZ1bmN0aW9uIGhvbWVkaXIoKSB7XG4gICAgdmFyIGhvbWUgPSBwcm9jZXNzLmVudi5IT01FO1xuICAgIHZhciB1c2VyID0gcHJvY2Vzcy5lbnYuTE9HTkFNRSB8fCBwcm9jZXNzLmVudi5VU0VSIHx8IHByb2Nlc3MuZW52LkxOQU1FIHx8IHByb2Nlc3MuZW52LlVTRVJOQU1FO1xuXG4gICAgaWYgKHByb2Nlc3MucGxhdGZvcm0gPT09ICd3aW4zMicpIHtcbiAgICAgICAgcmV0dXJuIHByb2Nlc3MuZW52LlVTRVJQUk9GSUxFIHx8IHByb2Nlc3MuZW52LkhPTUVEUklWRSArIHByb2Nlc3MuZW52LkhPTUVQQVRIIHx8IGhvbWUgfHwgbnVsbDtcbiAgICB9XG5cbiAgICBpZiAocHJvY2Vzcy5wbGF0Zm9ybSA9PT0gJ2RhcndpbicpIHtcbiAgICAgICAgcmV0dXJuIGhvbWUgfHwgKHVzZXIgPyAnL1VzZXJzLycgKyB1c2VyIDogbnVsbCk7XG4gICAgfVxuXG4gICAgaWYgKHByb2Nlc3MucGxhdGZvcm0gPT09ICdsaW51eCcpIHtcbiAgICAgICAgcmV0dXJuIGhvbWUgfHwgKHByb2Nlc3MuZ2V0dWlkKCkgPT09IDAgPyAnL3Jvb3QnIDogKHVzZXIgPyAnL2hvbWUvJyArIHVzZXIgOiBudWxsKSk7IC8vIGVzbGludC1kaXNhYmxlLWxpbmUgbm8tZXh0cmEtcGFyZW5zXG4gICAgfVxuXG4gICAgcmV0dXJuIGhvbWUgfHwgbnVsbDtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/resolve/lib/homedir.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/resolve/lib/is-core.js":
/*!*********************************************!*\
  !*** ./node_modules/resolve/lib/is-core.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var isCoreModule = __webpack_require__(/*! is-core-module */ \"(ssr)/./node_modules/is-core-module/index.js\");\n\nmodule.exports = function isCore(x) {\n    return isCoreModule(x);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvaXMtY29yZS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxtQkFBbUIsbUJBQU8sQ0FBQyxvRUFBZ0I7O0FBRTNDO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGVydmlld2NyYWNrZXIvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvaXMtY29yZS5qcz9mN2UwIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBpc0NvcmVNb2R1bGUgPSByZXF1aXJlKCdpcy1jb3JlLW1vZHVsZScpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIGlzQ29yZSh4KSB7XG4gICAgcmV0dXJuIGlzQ29yZU1vZHVsZSh4KTtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/resolve/lib/is-core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/resolve/lib/node-modules-paths.js":
/*!********************************************************!*\
  !*** ./node_modules/resolve/lib/node-modules-paths.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var path = __webpack_require__(/*! path */ \"path\");\nvar parse = path.parse || __webpack_require__(/*! path-parse */ \"(ssr)/./node_modules/path-parse/index.js\"); // eslint-disable-line global-require\n\nvar getNodeModulesDirs = function getNodeModulesDirs(absoluteStart, modules) {\n    var prefix = '/';\n    if ((/^([A-Za-z]:)/).test(absoluteStart)) {\n        prefix = '';\n    } else if ((/^\\\\\\\\/).test(absoluteStart)) {\n        prefix = '\\\\\\\\';\n    }\n\n    var paths = [absoluteStart];\n    var parsed = parse(absoluteStart);\n    while (parsed.dir !== paths[paths.length - 1]) {\n        paths.push(parsed.dir);\n        parsed = parse(parsed.dir);\n    }\n\n    return paths.reduce(function (dirs, aPath) {\n        return dirs.concat(modules.map(function (moduleDir) {\n            return path.resolve(prefix, aPath, moduleDir);\n        }));\n    }, []);\n};\n\nmodule.exports = function nodeModulesPaths(start, opts, request) {\n    var modules = opts && opts.moduleDirectory\n        ? [].concat(opts.moduleDirectory)\n        : ['node_modules'];\n\n    if (opts && typeof opts.paths === 'function') {\n        return opts.paths(\n            request,\n            start,\n            function () { return getNodeModulesDirs(start, modules); },\n            opts\n        );\n    }\n\n    var dirs = getNodeModulesDirs(start, modules);\n    return opts && opts.paths ? dirs.concat(opts.paths) : dirs;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/resolve/lib/node-modules-paths.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/resolve/lib/normalize-options.js":
/*!*******************************************************!*\
  !*** ./node_modules/resolve/lib/normalize-options.js ***!
  \*******************************************************/
/***/ ((module) => {

eval("module.exports = function (x, opts) {\n    /**\n     * This file is purposefully a passthrough. It's expected that third-party\n     * environments will override it at runtime in order to inject special logic\n     * into `resolve` (by manipulating the options). One such example is the PnP\n     * code path in Yarn.\n     */\n\n    return opts || {};\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvbm9ybWFsaXplLW9wdGlvbnMuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGVydmlld2NyYWNrZXIvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvbm9ybWFsaXplLW9wdGlvbnMuanM/NDYzOSJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uICh4LCBvcHRzKSB7XG4gICAgLyoqXG4gICAgICogVGhpcyBmaWxlIGlzIHB1cnBvc2VmdWxseSBhIHBhc3N0aHJvdWdoLiBJdCdzIGV4cGVjdGVkIHRoYXQgdGhpcmQtcGFydHlcbiAgICAgKiBlbnZpcm9ubWVudHMgd2lsbCBvdmVycmlkZSBpdCBhdCBydW50aW1lIGluIG9yZGVyIHRvIGluamVjdCBzcGVjaWFsIGxvZ2ljXG4gICAgICogaW50byBgcmVzb2x2ZWAgKGJ5IG1hbmlwdWxhdGluZyB0aGUgb3B0aW9ucykuIE9uZSBzdWNoIGV4YW1wbGUgaXMgdGhlIFBuUFxuICAgICAqIGNvZGUgcGF0aCBpbiBZYXJuLlxuICAgICAqL1xuXG4gICAgcmV0dXJuIG9wdHMgfHwge307XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/resolve/lib/normalize-options.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/resolve/lib/sync.js":
/*!******************************************!*\
  !*** ./node_modules/resolve/lib/sync.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var isCore = __webpack_require__(/*! is-core-module */ \"(ssr)/./node_modules/is-core-module/index.js\");\nvar fs = __webpack_require__(/*! fs */ \"fs\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar getHomedir = __webpack_require__(/*! ./homedir */ \"(ssr)/./node_modules/resolve/lib/homedir.js\");\nvar caller = __webpack_require__(/*! ./caller */ \"(ssr)/./node_modules/resolve/lib/caller.js\");\nvar nodeModulesPaths = __webpack_require__(/*! ./node-modules-paths */ \"(ssr)/./node_modules/resolve/lib/node-modules-paths.js\");\nvar normalizeOptions = __webpack_require__(/*! ./normalize-options */ \"(ssr)/./node_modules/resolve/lib/normalize-options.js\");\n\nvar realpathFS = process.platform !== 'win32' && fs.realpathSync && typeof fs.realpathSync.native === 'function' ? fs.realpathSync.native : fs.realpathSync;\n\nvar homedir = getHomedir();\nvar defaultPaths = function () {\n    return [\n        path.join(homedir, '.node_modules'),\n        path.join(homedir, '.node_libraries')\n    ];\n};\n\nvar defaultIsFile = function isFile(file) {\n    try {\n        var stat = fs.statSync(file, { throwIfNoEntry: false });\n    } catch (e) {\n        if (e && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) return false;\n        throw e;\n    }\n    return !!stat && (stat.isFile() || stat.isFIFO());\n};\n\nvar defaultIsDir = function isDirectory(dir) {\n    try {\n        var stat = fs.statSync(dir, { throwIfNoEntry: false });\n    } catch (e) {\n        if (e && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) return false;\n        throw e;\n    }\n    return !!stat && stat.isDirectory();\n};\n\nvar defaultRealpathSync = function realpathSync(x) {\n    try {\n        return realpathFS(x);\n    } catch (realpathErr) {\n        if (realpathErr.code !== 'ENOENT') {\n            throw realpathErr;\n        }\n    }\n    return x;\n};\n\nvar maybeRealpathSync = function maybeRealpathSync(realpathSync, x, opts) {\n    if (opts && opts.preserveSymlinks === false) {\n        return realpathSync(x);\n    }\n    return x;\n};\n\nvar defaultReadPackageSync = function defaultReadPackageSync(readFileSync, pkgfile) {\n    var body = readFileSync(pkgfile);\n    try {\n        var pkg = JSON.parse(body);\n        return pkg;\n    } catch (jsonErr) {}\n};\n\nvar getPackageCandidates = function getPackageCandidates(x, start, opts) {\n    var dirs = nodeModulesPaths(start, opts, x);\n    for (var i = 0; i < dirs.length; i++) {\n        dirs[i] = path.join(dirs[i], x);\n    }\n    return dirs;\n};\n\nmodule.exports = function resolveSync(x, options) {\n    if (typeof x !== 'string') {\n        throw new TypeError('Path must be a string.');\n    }\n    var opts = normalizeOptions(x, options);\n\n    var isFile = opts.isFile || defaultIsFile;\n    var readFileSync = opts.readFileSync || fs.readFileSync;\n    var isDirectory = opts.isDirectory || defaultIsDir;\n    var realpathSync = opts.realpathSync || defaultRealpathSync;\n    var readPackageSync = opts.readPackageSync || defaultReadPackageSync;\n    if (opts.readFileSync && opts.readPackageSync) {\n        throw new TypeError('`readFileSync` and `readPackageSync` are mutually exclusive.');\n    }\n    var packageIterator = opts.packageIterator;\n\n    var extensions = opts.extensions || ['.js'];\n    var includeCoreModules = opts.includeCoreModules !== false;\n    var basedir = opts.basedir || path.dirname(caller());\n    var parent = opts.filename || basedir;\n\n    opts.paths = opts.paths || defaultPaths();\n\n    // ensure that `basedir` is an absolute path at this point, resolving against the process' current working directory\n    var absoluteStart = maybeRealpathSync(realpathSync, path.resolve(basedir), opts);\n\n    if ((/^(?:\\.\\.?(?:\\/|$)|\\/|([A-Za-z]:)?[/\\\\])/).test(x)) {\n        var res = path.resolve(absoluteStart, x);\n        if (x === '.' || x === '..' || x.slice(-1) === '/') res += '/';\n        var m = loadAsFileSync(res) || loadAsDirectorySync(res);\n        if (m) return maybeRealpathSync(realpathSync, m, opts);\n    } else if (includeCoreModules && isCore(x)) {\n        return x;\n    } else {\n        var n = loadNodeModulesSync(x, absoluteStart);\n        if (n) return maybeRealpathSync(realpathSync, n, opts);\n    }\n\n    var err = new Error(\"Cannot find module '\" + x + \"' from '\" + parent + \"'\");\n    err.code = 'MODULE_NOT_FOUND';\n    throw err;\n\n    function loadAsFileSync(x) {\n        var pkg = loadpkg(path.dirname(x));\n\n        if (pkg && pkg.dir && pkg.pkg && opts.pathFilter) {\n            var rfile = path.relative(pkg.dir, x);\n            var r = opts.pathFilter(pkg.pkg, x, rfile);\n            if (r) {\n                x = path.resolve(pkg.dir, r); // eslint-disable-line no-param-reassign\n            }\n        }\n\n        if (isFile(x)) {\n            return x;\n        }\n\n        for (var i = 0; i < extensions.length; i++) {\n            var file = x + extensions[i];\n            if (isFile(file)) {\n                return file;\n            }\n        }\n    }\n\n    function loadpkg(dir) {\n        if (dir === '' || dir === '/') return;\n        if (process.platform === 'win32' && (/^\\w:[/\\\\]*$/).test(dir)) {\n            return;\n        }\n        if ((/[/\\\\]node_modules[/\\\\]*$/).test(dir)) return;\n\n        var pkgfile = path.join(maybeRealpathSync(realpathSync, dir, opts), 'package.json');\n\n        if (!isFile(pkgfile)) {\n            return loadpkg(path.dirname(dir));\n        }\n\n        var pkg = readPackageSync(readFileSync, pkgfile);\n\n        if (pkg && opts.packageFilter) {\n            // v2 will pass pkgfile\n            pkg = opts.packageFilter(pkg, /*pkgfile,*/ dir); // eslint-disable-line spaced-comment\n        }\n\n        return { pkg: pkg, dir: dir };\n    }\n\n    function loadAsDirectorySync(x) {\n        var pkgfile = path.join(maybeRealpathSync(realpathSync, x, opts), '/package.json');\n        if (isFile(pkgfile)) {\n            try {\n                var pkg = readPackageSync(readFileSync, pkgfile);\n            } catch (e) {}\n\n            if (pkg && opts.packageFilter) {\n                // v2 will pass pkgfile\n                pkg = opts.packageFilter(pkg, /*pkgfile,*/ x); // eslint-disable-line spaced-comment\n            }\n\n            if (pkg && pkg.main) {\n                if (typeof pkg.main !== 'string') {\n                    var mainError = new TypeError('package “' + pkg.name + '” `main` must be a string');\n                    mainError.code = 'INVALID_PACKAGE_MAIN';\n                    throw mainError;\n                }\n                if (pkg.main === '.' || pkg.main === './') {\n                    pkg.main = 'index';\n                }\n                try {\n                    var m = loadAsFileSync(path.resolve(x, pkg.main));\n                    if (m) return m;\n                    var n = loadAsDirectorySync(path.resolve(x, pkg.main));\n                    if (n) return n;\n                } catch (e) {}\n            }\n        }\n\n        return loadAsFileSync(path.join(x, '/index'));\n    }\n\n    function loadNodeModulesSync(x, start) {\n        var thunk = function () { return getPackageCandidates(x, start, opts); };\n        var dirs = packageIterator ? packageIterator(x, start, thunk, opts) : thunk();\n\n        for (var i = 0; i < dirs.length; i++) {\n            var dir = dirs[i];\n            if (isDirectory(path.dirname(dir))) {\n                var m = loadAsFileSync(dir);\n                if (m) return m;\n                var n = loadAsDirectorySync(dir);\n                if (n) return n;\n            }\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/resolve/lib/sync.js\n");

/***/ }),

/***/ "(instrument)/./node_modules/resolve/index.js":
/*!***************************************!*\
  !*** ./node_modules/resolve/index.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var async = __webpack_require__(/*! ./lib/async */ \"(instrument)/./node_modules/resolve/lib/async.js\");\nasync.core = __webpack_require__(/*! ./lib/core */ \"(instrument)/./node_modules/resolve/lib/core.js\");\nasync.isCore = __webpack_require__(/*! ./lib/is-core */ \"(instrument)/./node_modules/resolve/lib/is-core.js\");\nasync.sync = __webpack_require__(/*! ./lib/sync */ \"(instrument)/./node_modules/resolve/lib/sync.js\");\n\nmodule.exports = async;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4vbm9kZV9tb2R1bGVzL3Jlc29sdmUvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsWUFBWSxtQkFBTyxDQUFDLHFFQUFhO0FBQ2pDLGFBQWEsbUJBQU8sQ0FBQyxtRUFBWTtBQUNqQyxlQUFlLG1CQUFPLENBQUMseUVBQWU7QUFDdEMsYUFBYSxtQkFBTyxDQUFDLG1FQUFZOztBQUVqQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGVydmlld2NyYWNrZXIvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9pbmRleC5qcz9lMmQxIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBhc3luYyA9IHJlcXVpcmUoJy4vbGliL2FzeW5jJyk7XG5hc3luYy5jb3JlID0gcmVxdWlyZSgnLi9saWIvY29yZScpO1xuYXN5bmMuaXNDb3JlID0gcmVxdWlyZSgnLi9saWIvaXMtY29yZScpO1xuYXN5bmMuc3luYyA9IHJlcXVpcmUoJy4vbGliL3N5bmMnKTtcblxubW9kdWxlLmV4cG9ydHMgPSBhc3luYztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/./node_modules/resolve/index.js\n");

/***/ }),

/***/ "(instrument)/./node_modules/resolve/lib/async.js":
/*!*******************************************!*\
  !*** ./node_modules/resolve/lib/async.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var fs = __webpack_require__(/*! fs */ \"fs\");\nvar getHomedir = __webpack_require__(/*! ./homedir */ \"(instrument)/./node_modules/resolve/lib/homedir.js\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar caller = __webpack_require__(/*! ./caller */ \"(instrument)/./node_modules/resolve/lib/caller.js\");\nvar nodeModulesPaths = __webpack_require__(/*! ./node-modules-paths */ \"(instrument)/./node_modules/resolve/lib/node-modules-paths.js\");\nvar normalizeOptions = __webpack_require__(/*! ./normalize-options */ \"(instrument)/./node_modules/resolve/lib/normalize-options.js\");\nvar isCore = __webpack_require__(/*! is-core-module */ \"(instrument)/./node_modules/is-core-module/index.js\");\n\nvar realpathFS = process.platform !== 'win32' && fs.realpath && typeof fs.realpath.native === 'function' ? fs.realpath.native : fs.realpath;\n\nvar homedir = getHomedir();\nvar defaultPaths = function () {\n    return [\n        path.join(homedir, '.node_modules'),\n        path.join(homedir, '.node_libraries')\n    ];\n};\n\nvar defaultIsFile = function isFile(file, cb) {\n    fs.stat(file, function (err, stat) {\n        if (!err) {\n            return cb(null, stat.isFile() || stat.isFIFO());\n        }\n        if (err.code === 'ENOENT' || err.code === 'ENOTDIR') return cb(null, false);\n        return cb(err);\n    });\n};\n\nvar defaultIsDir = function isDirectory(dir, cb) {\n    fs.stat(dir, function (err, stat) {\n        if (!err) {\n            return cb(null, stat.isDirectory());\n        }\n        if (err.code === 'ENOENT' || err.code === 'ENOTDIR') return cb(null, false);\n        return cb(err);\n    });\n};\n\nvar defaultRealpath = function realpath(x, cb) {\n    realpathFS(x, function (realpathErr, realPath) {\n        if (realpathErr && realpathErr.code !== 'ENOENT') cb(realpathErr);\n        else cb(null, realpathErr ? x : realPath);\n    });\n};\n\nvar maybeRealpath = function maybeRealpath(realpath, x, opts, cb) {\n    if (opts && opts.preserveSymlinks === false) {\n        realpath(x, cb);\n    } else {\n        cb(null, x);\n    }\n};\n\nvar defaultReadPackage = function defaultReadPackage(readFile, pkgfile, cb) {\n    readFile(pkgfile, function (readFileErr, body) {\n        if (readFileErr) cb(readFileErr);\n        else {\n            try {\n                var pkg = JSON.parse(body);\n                cb(null, pkg);\n            } catch (jsonErr) {\n                cb(null);\n            }\n        }\n    });\n};\n\nvar getPackageCandidates = function getPackageCandidates(x, start, opts) {\n    var dirs = nodeModulesPaths(start, opts, x);\n    for (var i = 0; i < dirs.length; i++) {\n        dirs[i] = path.join(dirs[i], x);\n    }\n    return dirs;\n};\n\nmodule.exports = function resolve(x, options, callback) {\n    var cb = callback;\n    var opts = options;\n    if (typeof options === 'function') {\n        cb = opts;\n        opts = {};\n    }\n    if (typeof x !== 'string') {\n        var err = new TypeError('Path must be a string.');\n        return process.nextTick(function () {\n            cb(err);\n        });\n    }\n\n    opts = normalizeOptions(x, opts);\n\n    var isFile = opts.isFile || defaultIsFile;\n    var isDirectory = opts.isDirectory || defaultIsDir;\n    var readFile = opts.readFile || fs.readFile;\n    var realpath = opts.realpath || defaultRealpath;\n    var readPackage = opts.readPackage || defaultReadPackage;\n    if (opts.readFile && opts.readPackage) {\n        var conflictErr = new TypeError('`readFile` and `readPackage` are mutually exclusive.');\n        return process.nextTick(function () {\n            cb(conflictErr);\n        });\n    }\n    var packageIterator = opts.packageIterator;\n\n    var extensions = opts.extensions || ['.js'];\n    var includeCoreModules = opts.includeCoreModules !== false;\n    var basedir = opts.basedir || path.dirname(caller());\n    var parent = opts.filename || basedir;\n\n    opts.paths = opts.paths || defaultPaths();\n\n    // ensure that `basedir` is an absolute path at this point, resolving against the process' current working directory\n    var absoluteStart = path.resolve(basedir);\n\n    maybeRealpath(\n        realpath,\n        absoluteStart,\n        opts,\n        function (err, realStart) {\n            if (err) cb(err);\n            else init(realStart);\n        }\n    );\n\n    var res;\n    function init(basedir) {\n        if ((/^(?:\\.\\.?(?:\\/|$)|\\/|([A-Za-z]:)?[/\\\\])/).test(x)) {\n            res = path.resolve(basedir, x);\n            if (x === '.' || x === '..' || x.slice(-1) === '/') res += '/';\n            if ((/\\/$/).test(x) && res === basedir) {\n                loadAsDirectory(res, opts.package, onfile);\n            } else loadAsFile(res, opts.package, onfile);\n        } else if (includeCoreModules && isCore(x)) {\n            return cb(null, x);\n        } else loadNodeModules(x, basedir, function (err, n, pkg) {\n            if (err) cb(err);\n            else if (n) {\n                return maybeRealpath(realpath, n, opts, function (err, realN) {\n                    if (err) {\n                        cb(err);\n                    } else {\n                        cb(null, realN, pkg);\n                    }\n                });\n            } else {\n                var moduleError = new Error(\"Cannot find module '\" + x + \"' from '\" + parent + \"'\");\n                moduleError.code = 'MODULE_NOT_FOUND';\n                cb(moduleError);\n            }\n        });\n    }\n\n    function onfile(err, m, pkg) {\n        if (err) cb(err);\n        else if (m) cb(null, m, pkg);\n        else loadAsDirectory(res, function (err, d, pkg) {\n            if (err) cb(err);\n            else if (d) {\n                maybeRealpath(realpath, d, opts, function (err, realD) {\n                    if (err) {\n                        cb(err);\n                    } else {\n                        cb(null, realD, pkg);\n                    }\n                });\n            } else {\n                var moduleError = new Error(\"Cannot find module '\" + x + \"' from '\" + parent + \"'\");\n                moduleError.code = 'MODULE_NOT_FOUND';\n                cb(moduleError);\n            }\n        });\n    }\n\n    function loadAsFile(x, thePackage, callback) {\n        var loadAsFilePackage = thePackage;\n        var cb = callback;\n        if (typeof loadAsFilePackage === 'function') {\n            cb = loadAsFilePackage;\n            loadAsFilePackage = undefined;\n        }\n\n        var exts = [''].concat(extensions);\n        load(exts, x, loadAsFilePackage);\n\n        function load(exts, x, loadPackage) {\n            if (exts.length === 0) return cb(null, undefined, loadPackage);\n            var file = x + exts[0];\n\n            var pkg = loadPackage;\n            if (pkg) onpkg(null, pkg);\n            else loadpkg(path.dirname(file), onpkg);\n\n            function onpkg(err, pkg_, dir) {\n                pkg = pkg_;\n                if (err) return cb(err);\n                if (dir && pkg && opts.pathFilter) {\n                    var rfile = path.relative(dir, file);\n                    var rel = rfile.slice(0, rfile.length - exts[0].length);\n                    var r = opts.pathFilter(pkg, x, rel);\n                    if (r) return load(\n                        [''].concat(extensions.slice()),\n                        path.resolve(dir, r),\n                        pkg\n                    );\n                }\n                isFile(file, onex);\n            }\n            function onex(err, ex) {\n                if (err) return cb(err);\n                if (ex) return cb(null, file, pkg);\n                load(exts.slice(1), x, pkg);\n            }\n        }\n    }\n\n    function loadpkg(dir, cb) {\n        if (dir === '' || dir === '/') return cb(null);\n        if (process.platform === 'win32' && (/^\\w:[/\\\\]*$/).test(dir)) {\n            return cb(null);\n        }\n        if ((/[/\\\\]node_modules[/\\\\]*$/).test(dir)) return cb(null);\n\n        maybeRealpath(realpath, dir, opts, function (unwrapErr, pkgdir) {\n            if (unwrapErr) return loadpkg(path.dirname(dir), cb);\n            var pkgfile = path.join(pkgdir, 'package.json');\n            isFile(pkgfile, function (err, ex) {\n                // on err, ex is false\n                if (!ex) return loadpkg(path.dirname(dir), cb);\n\n                readPackage(readFile, pkgfile, function (err, pkgParam) {\n                    if (err) cb(err);\n\n                    var pkg = pkgParam;\n\n                    if (pkg && opts.packageFilter) {\n                        pkg = opts.packageFilter(pkg, pkgfile);\n                    }\n                    cb(null, pkg, dir);\n                });\n            });\n        });\n    }\n\n    function loadAsDirectory(x, loadAsDirectoryPackage, callback) {\n        var cb = callback;\n        var fpkg = loadAsDirectoryPackage;\n        if (typeof fpkg === 'function') {\n            cb = fpkg;\n            fpkg = opts.package;\n        }\n\n        maybeRealpath(realpath, x, opts, function (unwrapErr, pkgdir) {\n            if (unwrapErr) return cb(unwrapErr);\n            var pkgfile = path.join(pkgdir, 'package.json');\n            isFile(pkgfile, function (err, ex) {\n                if (err) return cb(err);\n                if (!ex) return loadAsFile(path.join(x, 'index'), fpkg, cb);\n\n                readPackage(readFile, pkgfile, function (err, pkgParam) {\n                    if (err) return cb(err);\n\n                    var pkg = pkgParam;\n\n                    if (pkg && opts.packageFilter) {\n                        pkg = opts.packageFilter(pkg, pkgfile);\n                    }\n\n                    if (pkg && pkg.main) {\n                        if (typeof pkg.main !== 'string') {\n                            var mainError = new TypeError('package “' + pkg.name + '” `main` must be a string');\n                            mainError.code = 'INVALID_PACKAGE_MAIN';\n                            return cb(mainError);\n                        }\n                        if (pkg.main === '.' || pkg.main === './') {\n                            pkg.main = 'index';\n                        }\n                        loadAsFile(path.resolve(x, pkg.main), pkg, function (err, m, pkg) {\n                            if (err) return cb(err);\n                            if (m) return cb(null, m, pkg);\n                            if (!pkg) return loadAsFile(path.join(x, 'index'), pkg, cb);\n\n                            var dir = path.resolve(x, pkg.main);\n                            loadAsDirectory(dir, pkg, function (err, n, pkg) {\n                                if (err) return cb(err);\n                                if (n) return cb(null, n, pkg);\n                                loadAsFile(path.join(x, 'index'), pkg, cb);\n                            });\n                        });\n                        return;\n                    }\n\n                    loadAsFile(path.join(x, '/index'), pkg, cb);\n                });\n            });\n        });\n    }\n\n    function processDirs(cb, dirs) {\n        if (dirs.length === 0) return cb(null, undefined);\n        var dir = dirs[0];\n\n        isDirectory(path.dirname(dir), isdir);\n\n        function isdir(err, isdir) {\n            if (err) return cb(err);\n            if (!isdir) return processDirs(cb, dirs.slice(1));\n            loadAsFile(dir, opts.package, onfile);\n        }\n\n        function onfile(err, m, pkg) {\n            if (err) return cb(err);\n            if (m) return cb(null, m, pkg);\n            loadAsDirectory(dir, opts.package, ondir);\n        }\n\n        function ondir(err, n, pkg) {\n            if (err) return cb(err);\n            if (n) return cb(null, n, pkg);\n            processDirs(cb, dirs.slice(1));\n        }\n    }\n    function loadNodeModules(x, start, cb) {\n        var thunk = function () { return getPackageCandidates(x, start, opts); };\n        processDirs(\n            cb,\n            packageIterator ? packageIterator(x, start, thunk, opts) : thunk()\n        );\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/./node_modules/resolve/lib/async.js\n");

/***/ }),

/***/ "(instrument)/./node_modules/resolve/lib/caller.js":
/*!********************************************!*\
  !*** ./node_modules/resolve/lib/caller.js ***!
  \********************************************/
/***/ ((module) => {

eval("module.exports = function () {\n    // see https://code.google.com/p/v8/wiki/JavaScriptStackTraceApi\n    var origPrepareStackTrace = Error.prepareStackTrace;\n    Error.prepareStackTrace = function (_, stack) { return stack; };\n    var stack = (new Error()).stack;\n    Error.prepareStackTrace = origPrepareStackTrace;\n    return stack[2].getFileName();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4vbm9kZV9tb2R1bGVzL3Jlc29sdmUvbGliL2NhbGxlci5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQSxvREFBb0Q7QUFDcEQ7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRlcnZpZXdjcmFja2VyLy4vbm9kZV9tb2R1bGVzL3Jlc29sdmUvbGliL2NhbGxlci5qcz85NTk0Il0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gKCkge1xuICAgIC8vIHNlZSBodHRwczovL2NvZGUuZ29vZ2xlLmNvbS9wL3Y4L3dpa2kvSmF2YVNjcmlwdFN0YWNrVHJhY2VBcGlcbiAgICB2YXIgb3JpZ1ByZXBhcmVTdGFja1RyYWNlID0gRXJyb3IucHJlcGFyZVN0YWNrVHJhY2U7XG4gICAgRXJyb3IucHJlcGFyZVN0YWNrVHJhY2UgPSBmdW5jdGlvbiAoXywgc3RhY2spIHsgcmV0dXJuIHN0YWNrOyB9O1xuICAgIHZhciBzdGFjayA9IChuZXcgRXJyb3IoKSkuc3RhY2s7XG4gICAgRXJyb3IucHJlcGFyZVN0YWNrVHJhY2UgPSBvcmlnUHJlcGFyZVN0YWNrVHJhY2U7XG4gICAgcmV0dXJuIHN0YWNrWzJdLmdldEZpbGVOYW1lKCk7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/./node_modules/resolve/lib/caller.js\n");

/***/ }),

/***/ "(instrument)/./node_modules/resolve/lib/core.js":
/*!******************************************!*\
  !*** ./node_modules/resolve/lib/core.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nvar isCoreModule = __webpack_require__(/*! is-core-module */ \"(instrument)/./node_modules/is-core-module/index.js\");\nvar data = __webpack_require__(/*! ./core.json */ \"(instrument)/./node_modules/resolve/lib/core.json\");\n\nvar core = {};\nfor (var mod in data) { // eslint-disable-line no-restricted-syntax\n    if (Object.prototype.hasOwnProperty.call(data, mod)) {\n        core[mod] = isCoreModule(mod);\n    }\n}\nmodule.exports = core;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4vbm9kZV9tb2R1bGVzL3Jlc29sdmUvbGliL2NvcmUuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsbUJBQW1CLG1CQUFPLENBQUMsMkVBQWdCO0FBQzNDLFdBQVcsbUJBQU8sQ0FBQyxzRUFBYTs7QUFFaEM7QUFDQSx3QkFBd0I7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGVydmlld2NyYWNrZXIvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvY29yZS5qcz9hYzgwIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxudmFyIGlzQ29yZU1vZHVsZSA9IHJlcXVpcmUoJ2lzLWNvcmUtbW9kdWxlJyk7XG52YXIgZGF0YSA9IHJlcXVpcmUoJy4vY29yZS5qc29uJyk7XG5cbnZhciBjb3JlID0ge307XG5mb3IgKHZhciBtb2QgaW4gZGF0YSkgeyAvLyBlc2xpbnQtZGlzYWJsZS1saW5lIG5vLXJlc3RyaWN0ZWQtc3ludGF4XG4gICAgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChkYXRhLCBtb2QpKSB7XG4gICAgICAgIGNvcmVbbW9kXSA9IGlzQ29yZU1vZHVsZShtb2QpO1xuICAgIH1cbn1cbm1vZHVsZS5leHBvcnRzID0gY29yZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/./node_modules/resolve/lib/core.js\n");

/***/ }),

/***/ "(instrument)/./node_modules/resolve/lib/homedir.js":
/*!*********************************************!*\
  !*** ./node_modules/resolve/lib/homedir.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nvar os = __webpack_require__(/*! os */ \"os\");\n\n// adapted from https://github.com/sindresorhus/os-homedir/blob/11e089f4754db38bb535e5a8416320c4446e8cfd/index.js\n\nmodule.exports = os.homedir || function homedir() {\n    var home = process.env.HOME;\n    var user = process.env.LOGNAME || process.env.USER || process.env.LNAME || process.env.USERNAME;\n\n    if (process.platform === 'win32') {\n        return process.env.USERPROFILE || process.env.HOMEDRIVE + process.env.HOMEPATH || home || null;\n    }\n\n    if (process.platform === 'darwin') {\n        return home || (user ? '/Users/' + user : null);\n    }\n\n    if (process.platform === 'linux') {\n        return home || (process.getuid() === 0 ? '/root' : (user ? '/home/' + user : null)); // eslint-disable-line no-extra-parens\n    }\n\n    return home || null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4vbm9kZV9tb2R1bGVzL3Jlc29sdmUvbGliL2hvbWVkaXIuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsU0FBUyxtQkFBTyxDQUFDLGNBQUk7O0FBRXJCOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsNkZBQTZGO0FBQzdGOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRlcnZpZXdjcmFja2VyLy4vbm9kZV9tb2R1bGVzL3Jlc29sdmUvbGliL2hvbWVkaXIuanM/NTIxNCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciBvcyA9IHJlcXVpcmUoJ29zJyk7XG5cbi8vIGFkYXB0ZWQgZnJvbSBodHRwczovL2dpdGh1Yi5jb20vc2luZHJlc29yaHVzL29zLWhvbWVkaXIvYmxvYi8xMWUwODlmNDc1NGRiMzhiYjUzNWU1YTg0MTYzMjBjNDQ0NmU4Y2ZkL2luZGV4LmpzXG5cbm1vZHVsZS5leHBvcnRzID0gb3MuaG9tZWRpciB8fCBmdW5jdGlvbiBob21lZGlyKCkge1xuICAgIHZhciBob21lID0gcHJvY2Vzcy5lbnYuSE9NRTtcbiAgICB2YXIgdXNlciA9IHByb2Nlc3MuZW52LkxPR05BTUUgfHwgcHJvY2Vzcy5lbnYuVVNFUiB8fCBwcm9jZXNzLmVudi5MTkFNRSB8fCBwcm9jZXNzLmVudi5VU0VSTkFNRTtcblxuICAgIGlmIChwcm9jZXNzLnBsYXRmb3JtID09PSAnd2luMzInKSB7XG4gICAgICAgIHJldHVybiBwcm9jZXNzLmVudi5VU0VSUFJPRklMRSB8fCBwcm9jZXNzLmVudi5IT01FRFJJVkUgKyBwcm9jZXNzLmVudi5IT01FUEFUSCB8fCBob21lIHx8IG51bGw7XG4gICAgfVxuXG4gICAgaWYgKHByb2Nlc3MucGxhdGZvcm0gPT09ICdkYXJ3aW4nKSB7XG4gICAgICAgIHJldHVybiBob21lIHx8ICh1c2VyID8gJy9Vc2Vycy8nICsgdXNlciA6IG51bGwpO1xuICAgIH1cblxuICAgIGlmIChwcm9jZXNzLnBsYXRmb3JtID09PSAnbGludXgnKSB7XG4gICAgICAgIHJldHVybiBob21lIHx8IChwcm9jZXNzLmdldHVpZCgpID09PSAwID8gJy9yb290JyA6ICh1c2VyID8gJy9ob21lLycgKyB1c2VyIDogbnVsbCkpOyAvLyBlc2xpbnQtZGlzYWJsZS1saW5lIG5vLWV4dHJhLXBhcmVuc1xuICAgIH1cblxuICAgIHJldHVybiBob21lIHx8IG51bGw7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/./node_modules/resolve/lib/homedir.js\n");

/***/ }),

/***/ "(instrument)/./node_modules/resolve/lib/is-core.js":
/*!*********************************************!*\
  !*** ./node_modules/resolve/lib/is-core.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var isCoreModule = __webpack_require__(/*! is-core-module */ \"(instrument)/./node_modules/is-core-module/index.js\");\n\nmodule.exports = function isCore(x) {\n    return isCoreModule(x);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4vbm9kZV9tb2R1bGVzL3Jlc29sdmUvbGliL2lzLWNvcmUuanMiLCJtYXBwaW5ncyI6IkFBQUEsbUJBQW1CLG1CQUFPLENBQUMsMkVBQWdCOztBQUUzQztBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRlcnZpZXdjcmFja2VyLy4vbm9kZV9tb2R1bGVzL3Jlc29sdmUvbGliL2lzLWNvcmUuanM/ZGMxYyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgaXNDb3JlTW9kdWxlID0gcmVxdWlyZSgnaXMtY29yZS1tb2R1bGUnKTtcblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBpc0NvcmUoeCkge1xuICAgIHJldHVybiBpc0NvcmVNb2R1bGUoeCk7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/./node_modules/resolve/lib/is-core.js\n");

/***/ }),

/***/ "(instrument)/./node_modules/resolve/lib/node-modules-paths.js":
/*!********************************************************!*\
  !*** ./node_modules/resolve/lib/node-modules-paths.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var path = __webpack_require__(/*! path */ \"path\");\nvar parse = path.parse || __webpack_require__(/*! path-parse */ \"(instrument)/./node_modules/path-parse/index.js\"); // eslint-disable-line global-require\n\nvar getNodeModulesDirs = function getNodeModulesDirs(absoluteStart, modules) {\n    var prefix = '/';\n    if ((/^([A-Za-z]:)/).test(absoluteStart)) {\n        prefix = '';\n    } else if ((/^\\\\\\\\/).test(absoluteStart)) {\n        prefix = '\\\\\\\\';\n    }\n\n    var paths = [absoluteStart];\n    var parsed = parse(absoluteStart);\n    while (parsed.dir !== paths[paths.length - 1]) {\n        paths.push(parsed.dir);\n        parsed = parse(parsed.dir);\n    }\n\n    return paths.reduce(function (dirs, aPath) {\n        return dirs.concat(modules.map(function (moduleDir) {\n            return path.resolve(prefix, aPath, moduleDir);\n        }));\n    }, []);\n};\n\nmodule.exports = function nodeModulesPaths(start, opts, request) {\n    var modules = opts && opts.moduleDirectory\n        ? [].concat(opts.moduleDirectory)\n        : ['node_modules'];\n\n    if (opts && typeof opts.paths === 'function') {\n        return opts.paths(\n            request,\n            start,\n            function () { return getNodeModulesDirs(start, modules); },\n            opts\n        );\n    }\n\n    var dirs = getNodeModulesDirs(start, modules);\n    return opts && opts.paths ? dirs.concat(opts.paths) : dirs;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/./node_modules/resolve/lib/node-modules-paths.js\n");

/***/ }),

/***/ "(instrument)/./node_modules/resolve/lib/normalize-options.js":
/*!*******************************************************!*\
  !*** ./node_modules/resolve/lib/normalize-options.js ***!
  \*******************************************************/
/***/ ((module) => {

eval("module.exports = function (x, opts) {\n    /**\n     * This file is purposefully a passthrough. It's expected that third-party\n     * environments will override it at runtime in order to inject special logic\n     * into `resolve` (by manipulating the options). One such example is the PnP\n     * code path in Yarn.\n     */\n\n    return opts || {};\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4vbm9kZV9tb2R1bGVzL3Jlc29sdmUvbGliL25vcm1hbGl6ZS1vcHRpb25zLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRlcnZpZXdjcmFja2VyLy4vbm9kZV9tb2R1bGVzL3Jlc29sdmUvbGliL25vcm1hbGl6ZS1vcHRpb25zLmpzP2NjYzEiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAoeCwgb3B0cykge1xuICAgIC8qKlxuICAgICAqIFRoaXMgZmlsZSBpcyBwdXJwb3NlZnVsbHkgYSBwYXNzdGhyb3VnaC4gSXQncyBleHBlY3RlZCB0aGF0IHRoaXJkLXBhcnR5XG4gICAgICogZW52aXJvbm1lbnRzIHdpbGwgb3ZlcnJpZGUgaXQgYXQgcnVudGltZSBpbiBvcmRlciB0byBpbmplY3Qgc3BlY2lhbCBsb2dpY1xuICAgICAqIGludG8gYHJlc29sdmVgIChieSBtYW5pcHVsYXRpbmcgdGhlIG9wdGlvbnMpLiBPbmUgc3VjaCBleGFtcGxlIGlzIHRoZSBQblBcbiAgICAgKiBjb2RlIHBhdGggaW4gWWFybi5cbiAgICAgKi9cblxuICAgIHJldHVybiBvcHRzIHx8IHt9O1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/./node_modules/resolve/lib/normalize-options.js\n");

/***/ }),

/***/ "(instrument)/./node_modules/resolve/lib/sync.js":
/*!******************************************!*\
  !*** ./node_modules/resolve/lib/sync.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var isCore = __webpack_require__(/*! is-core-module */ \"(instrument)/./node_modules/is-core-module/index.js\");\nvar fs = __webpack_require__(/*! fs */ \"fs\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar getHomedir = __webpack_require__(/*! ./homedir */ \"(instrument)/./node_modules/resolve/lib/homedir.js\");\nvar caller = __webpack_require__(/*! ./caller */ \"(instrument)/./node_modules/resolve/lib/caller.js\");\nvar nodeModulesPaths = __webpack_require__(/*! ./node-modules-paths */ \"(instrument)/./node_modules/resolve/lib/node-modules-paths.js\");\nvar normalizeOptions = __webpack_require__(/*! ./normalize-options */ \"(instrument)/./node_modules/resolve/lib/normalize-options.js\");\n\nvar realpathFS = process.platform !== 'win32' && fs.realpathSync && typeof fs.realpathSync.native === 'function' ? fs.realpathSync.native : fs.realpathSync;\n\nvar homedir = getHomedir();\nvar defaultPaths = function () {\n    return [\n        path.join(homedir, '.node_modules'),\n        path.join(homedir, '.node_libraries')\n    ];\n};\n\nvar defaultIsFile = function isFile(file) {\n    try {\n        var stat = fs.statSync(file, { throwIfNoEntry: false });\n    } catch (e) {\n        if (e && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) return false;\n        throw e;\n    }\n    return !!stat && (stat.isFile() || stat.isFIFO());\n};\n\nvar defaultIsDir = function isDirectory(dir) {\n    try {\n        var stat = fs.statSync(dir, { throwIfNoEntry: false });\n    } catch (e) {\n        if (e && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) return false;\n        throw e;\n    }\n    return !!stat && stat.isDirectory();\n};\n\nvar defaultRealpathSync = function realpathSync(x) {\n    try {\n        return realpathFS(x);\n    } catch (realpathErr) {\n        if (realpathErr.code !== 'ENOENT') {\n            throw realpathErr;\n        }\n    }\n    return x;\n};\n\nvar maybeRealpathSync = function maybeRealpathSync(realpathSync, x, opts) {\n    if (opts && opts.preserveSymlinks === false) {\n        return realpathSync(x);\n    }\n    return x;\n};\n\nvar defaultReadPackageSync = function defaultReadPackageSync(readFileSync, pkgfile) {\n    var body = readFileSync(pkgfile);\n    try {\n        var pkg = JSON.parse(body);\n        return pkg;\n    } catch (jsonErr) {}\n};\n\nvar getPackageCandidates = function getPackageCandidates(x, start, opts) {\n    var dirs = nodeModulesPaths(start, opts, x);\n    for (var i = 0; i < dirs.length; i++) {\n        dirs[i] = path.join(dirs[i], x);\n    }\n    return dirs;\n};\n\nmodule.exports = function resolveSync(x, options) {\n    if (typeof x !== 'string') {\n        throw new TypeError('Path must be a string.');\n    }\n    var opts = normalizeOptions(x, options);\n\n    var isFile = opts.isFile || defaultIsFile;\n    var readFileSync = opts.readFileSync || fs.readFileSync;\n    var isDirectory = opts.isDirectory || defaultIsDir;\n    var realpathSync = opts.realpathSync || defaultRealpathSync;\n    var readPackageSync = opts.readPackageSync || defaultReadPackageSync;\n    if (opts.readFileSync && opts.readPackageSync) {\n        throw new TypeError('`readFileSync` and `readPackageSync` are mutually exclusive.');\n    }\n    var packageIterator = opts.packageIterator;\n\n    var extensions = opts.extensions || ['.js'];\n    var includeCoreModules = opts.includeCoreModules !== false;\n    var basedir = opts.basedir || path.dirname(caller());\n    var parent = opts.filename || basedir;\n\n    opts.paths = opts.paths || defaultPaths();\n\n    // ensure that `basedir` is an absolute path at this point, resolving against the process' current working directory\n    var absoluteStart = maybeRealpathSync(realpathSync, path.resolve(basedir), opts);\n\n    if ((/^(?:\\.\\.?(?:\\/|$)|\\/|([A-Za-z]:)?[/\\\\])/).test(x)) {\n        var res = path.resolve(absoluteStart, x);\n        if (x === '.' || x === '..' || x.slice(-1) === '/') res += '/';\n        var m = loadAsFileSync(res) || loadAsDirectorySync(res);\n        if (m) return maybeRealpathSync(realpathSync, m, opts);\n    } else if (includeCoreModules && isCore(x)) {\n        return x;\n    } else {\n        var n = loadNodeModulesSync(x, absoluteStart);\n        if (n) return maybeRealpathSync(realpathSync, n, opts);\n    }\n\n    var err = new Error(\"Cannot find module '\" + x + \"' from '\" + parent + \"'\");\n    err.code = 'MODULE_NOT_FOUND';\n    throw err;\n\n    function loadAsFileSync(x) {\n        var pkg = loadpkg(path.dirname(x));\n\n        if (pkg && pkg.dir && pkg.pkg && opts.pathFilter) {\n            var rfile = path.relative(pkg.dir, x);\n            var r = opts.pathFilter(pkg.pkg, x, rfile);\n            if (r) {\n                x = path.resolve(pkg.dir, r); // eslint-disable-line no-param-reassign\n            }\n        }\n\n        if (isFile(x)) {\n            return x;\n        }\n\n        for (var i = 0; i < extensions.length; i++) {\n            var file = x + extensions[i];\n            if (isFile(file)) {\n                return file;\n            }\n        }\n    }\n\n    function loadpkg(dir) {\n        if (dir === '' || dir === '/') return;\n        if (process.platform === 'win32' && (/^\\w:[/\\\\]*$/).test(dir)) {\n            return;\n        }\n        if ((/[/\\\\]node_modules[/\\\\]*$/).test(dir)) return;\n\n        var pkgfile = path.join(maybeRealpathSync(realpathSync, dir, opts), 'package.json');\n\n        if (!isFile(pkgfile)) {\n            return loadpkg(path.dirname(dir));\n        }\n\n        var pkg = readPackageSync(readFileSync, pkgfile);\n\n        if (pkg && opts.packageFilter) {\n            // v2 will pass pkgfile\n            pkg = opts.packageFilter(pkg, /*pkgfile,*/ dir); // eslint-disable-line spaced-comment\n        }\n\n        return { pkg: pkg, dir: dir };\n    }\n\n    function loadAsDirectorySync(x) {\n        var pkgfile = path.join(maybeRealpathSync(realpathSync, x, opts), '/package.json');\n        if (isFile(pkgfile)) {\n            try {\n                var pkg = readPackageSync(readFileSync, pkgfile);\n            } catch (e) {}\n\n            if (pkg && opts.packageFilter) {\n                // v2 will pass pkgfile\n                pkg = opts.packageFilter(pkg, /*pkgfile,*/ x); // eslint-disable-line spaced-comment\n            }\n\n            if (pkg && pkg.main) {\n                if (typeof pkg.main !== 'string') {\n                    var mainError = new TypeError('package “' + pkg.name + '” `main` must be a string');\n                    mainError.code = 'INVALID_PACKAGE_MAIN';\n                    throw mainError;\n                }\n                if (pkg.main === '.' || pkg.main === './') {\n                    pkg.main = 'index';\n                }\n                try {\n                    var m = loadAsFileSync(path.resolve(x, pkg.main));\n                    if (m) return m;\n                    var n = loadAsDirectorySync(path.resolve(x, pkg.main));\n                    if (n) return n;\n                } catch (e) {}\n            }\n        }\n\n        return loadAsFileSync(path.join(x, '/index'));\n    }\n\n    function loadNodeModulesSync(x, start) {\n        var thunk = function () { return getPackageCandidates(x, start, opts); };\n        var dirs = packageIterator ? packageIterator(x, start, thunk, opts) : thunk();\n\n        for (var i = 0; i < dirs.length; i++) {\n            var dir = dirs[i];\n            if (isDirectory(path.dirname(dir))) {\n                var m = loadAsFileSync(dir);\n                if (m) return m;\n                var n = loadAsDirectorySync(dir);\n                if (n) return n;\n            }\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/./node_modules/resolve/lib/sync.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/resolve/index.js":
/*!***************************************!*\
  !*** ./node_modules/resolve/index.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var async = __webpack_require__(/*! ./lib/async */ \"(rsc)/./node_modules/resolve/lib/async.js\");\nasync.core = __webpack_require__(/*! ./lib/core */ \"(rsc)/./node_modules/resolve/lib/core.js\");\nasync.isCore = __webpack_require__(/*! ./lib/is-core */ \"(rsc)/./node_modules/resolve/lib/is-core.js\");\nasync.sync = __webpack_require__(/*! ./lib/sync */ \"(rsc)/./node_modules/resolve/lib/sync.js\");\n\nmodule.exports = async;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxZQUFZLG1CQUFPLENBQUMsOERBQWE7QUFDakMsYUFBYSxtQkFBTyxDQUFDLDREQUFZO0FBQ2pDLGVBQWUsbUJBQU8sQ0FBQyxrRUFBZTtBQUN0QyxhQUFhLG1CQUFPLENBQUMsNERBQVk7O0FBRWpDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50ZXJ2aWV3Y3JhY2tlci8uL25vZGVfbW9kdWxlcy9yZXNvbHZlL2luZGV4LmpzPzIyZjkiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGFzeW5jID0gcmVxdWlyZSgnLi9saWIvYXN5bmMnKTtcbmFzeW5jLmNvcmUgPSByZXF1aXJlKCcuL2xpYi9jb3JlJyk7XG5hc3luYy5pc0NvcmUgPSByZXF1aXJlKCcuL2xpYi9pcy1jb3JlJyk7XG5hc3luYy5zeW5jID0gcmVxdWlyZSgnLi9saWIvc3luYycpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IGFzeW5jO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/resolve/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/resolve/lib/async.js":
/*!*******************************************!*\
  !*** ./node_modules/resolve/lib/async.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var fs = __webpack_require__(/*! fs */ \"fs\");\nvar getHomedir = __webpack_require__(/*! ./homedir */ \"(rsc)/./node_modules/resolve/lib/homedir.js\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar caller = __webpack_require__(/*! ./caller */ \"(rsc)/./node_modules/resolve/lib/caller.js\");\nvar nodeModulesPaths = __webpack_require__(/*! ./node-modules-paths */ \"(rsc)/./node_modules/resolve/lib/node-modules-paths.js\");\nvar normalizeOptions = __webpack_require__(/*! ./normalize-options */ \"(rsc)/./node_modules/resolve/lib/normalize-options.js\");\nvar isCore = __webpack_require__(/*! is-core-module */ \"(rsc)/./node_modules/is-core-module/index.js\");\n\nvar realpathFS = process.platform !== 'win32' && fs.realpath && typeof fs.realpath.native === 'function' ? fs.realpath.native : fs.realpath;\n\nvar homedir = getHomedir();\nvar defaultPaths = function () {\n    return [\n        path.join(homedir, '.node_modules'),\n        path.join(homedir, '.node_libraries')\n    ];\n};\n\nvar defaultIsFile = function isFile(file, cb) {\n    fs.stat(file, function (err, stat) {\n        if (!err) {\n            return cb(null, stat.isFile() || stat.isFIFO());\n        }\n        if (err.code === 'ENOENT' || err.code === 'ENOTDIR') return cb(null, false);\n        return cb(err);\n    });\n};\n\nvar defaultIsDir = function isDirectory(dir, cb) {\n    fs.stat(dir, function (err, stat) {\n        if (!err) {\n            return cb(null, stat.isDirectory());\n        }\n        if (err.code === 'ENOENT' || err.code === 'ENOTDIR') return cb(null, false);\n        return cb(err);\n    });\n};\n\nvar defaultRealpath = function realpath(x, cb) {\n    realpathFS(x, function (realpathErr, realPath) {\n        if (realpathErr && realpathErr.code !== 'ENOENT') cb(realpathErr);\n        else cb(null, realpathErr ? x : realPath);\n    });\n};\n\nvar maybeRealpath = function maybeRealpath(realpath, x, opts, cb) {\n    if (opts && opts.preserveSymlinks === false) {\n        realpath(x, cb);\n    } else {\n        cb(null, x);\n    }\n};\n\nvar defaultReadPackage = function defaultReadPackage(readFile, pkgfile, cb) {\n    readFile(pkgfile, function (readFileErr, body) {\n        if (readFileErr) cb(readFileErr);\n        else {\n            try {\n                var pkg = JSON.parse(body);\n                cb(null, pkg);\n            } catch (jsonErr) {\n                cb(null);\n            }\n        }\n    });\n};\n\nvar getPackageCandidates = function getPackageCandidates(x, start, opts) {\n    var dirs = nodeModulesPaths(start, opts, x);\n    for (var i = 0; i < dirs.length; i++) {\n        dirs[i] = path.join(dirs[i], x);\n    }\n    return dirs;\n};\n\nmodule.exports = function resolve(x, options, callback) {\n    var cb = callback;\n    var opts = options;\n    if (typeof options === 'function') {\n        cb = opts;\n        opts = {};\n    }\n    if (typeof x !== 'string') {\n        var err = new TypeError('Path must be a string.');\n        return process.nextTick(function () {\n            cb(err);\n        });\n    }\n\n    opts = normalizeOptions(x, opts);\n\n    var isFile = opts.isFile || defaultIsFile;\n    var isDirectory = opts.isDirectory || defaultIsDir;\n    var readFile = opts.readFile || fs.readFile;\n    var realpath = opts.realpath || defaultRealpath;\n    var readPackage = opts.readPackage || defaultReadPackage;\n    if (opts.readFile && opts.readPackage) {\n        var conflictErr = new TypeError('`readFile` and `readPackage` are mutually exclusive.');\n        return process.nextTick(function () {\n            cb(conflictErr);\n        });\n    }\n    var packageIterator = opts.packageIterator;\n\n    var extensions = opts.extensions || ['.js'];\n    var includeCoreModules = opts.includeCoreModules !== false;\n    var basedir = opts.basedir || path.dirname(caller());\n    var parent = opts.filename || basedir;\n\n    opts.paths = opts.paths || defaultPaths();\n\n    // ensure that `basedir` is an absolute path at this point, resolving against the process' current working directory\n    var absoluteStart = path.resolve(basedir);\n\n    maybeRealpath(\n        realpath,\n        absoluteStart,\n        opts,\n        function (err, realStart) {\n            if (err) cb(err);\n            else init(realStart);\n        }\n    );\n\n    var res;\n    function init(basedir) {\n        if ((/^(?:\\.\\.?(?:\\/|$)|\\/|([A-Za-z]:)?[/\\\\])/).test(x)) {\n            res = path.resolve(basedir, x);\n            if (x === '.' || x === '..' || x.slice(-1) === '/') res += '/';\n            if ((/\\/$/).test(x) && res === basedir) {\n                loadAsDirectory(res, opts.package, onfile);\n            } else loadAsFile(res, opts.package, onfile);\n        } else if (includeCoreModules && isCore(x)) {\n            return cb(null, x);\n        } else loadNodeModules(x, basedir, function (err, n, pkg) {\n            if (err) cb(err);\n            else if (n) {\n                return maybeRealpath(realpath, n, opts, function (err, realN) {\n                    if (err) {\n                        cb(err);\n                    } else {\n                        cb(null, realN, pkg);\n                    }\n                });\n            } else {\n                var moduleError = new Error(\"Cannot find module '\" + x + \"' from '\" + parent + \"'\");\n                moduleError.code = 'MODULE_NOT_FOUND';\n                cb(moduleError);\n            }\n        });\n    }\n\n    function onfile(err, m, pkg) {\n        if (err) cb(err);\n        else if (m) cb(null, m, pkg);\n        else loadAsDirectory(res, function (err, d, pkg) {\n            if (err) cb(err);\n            else if (d) {\n                maybeRealpath(realpath, d, opts, function (err, realD) {\n                    if (err) {\n                        cb(err);\n                    } else {\n                        cb(null, realD, pkg);\n                    }\n                });\n            } else {\n                var moduleError = new Error(\"Cannot find module '\" + x + \"' from '\" + parent + \"'\");\n                moduleError.code = 'MODULE_NOT_FOUND';\n                cb(moduleError);\n            }\n        });\n    }\n\n    function loadAsFile(x, thePackage, callback) {\n        var loadAsFilePackage = thePackage;\n        var cb = callback;\n        if (typeof loadAsFilePackage === 'function') {\n            cb = loadAsFilePackage;\n            loadAsFilePackage = undefined;\n        }\n\n        var exts = [''].concat(extensions);\n        load(exts, x, loadAsFilePackage);\n\n        function load(exts, x, loadPackage) {\n            if (exts.length === 0) return cb(null, undefined, loadPackage);\n            var file = x + exts[0];\n\n            var pkg = loadPackage;\n            if (pkg) onpkg(null, pkg);\n            else loadpkg(path.dirname(file), onpkg);\n\n            function onpkg(err, pkg_, dir) {\n                pkg = pkg_;\n                if (err) return cb(err);\n                if (dir && pkg && opts.pathFilter) {\n                    var rfile = path.relative(dir, file);\n                    var rel = rfile.slice(0, rfile.length - exts[0].length);\n                    var r = opts.pathFilter(pkg, x, rel);\n                    if (r) return load(\n                        [''].concat(extensions.slice()),\n                        path.resolve(dir, r),\n                        pkg\n                    );\n                }\n                isFile(file, onex);\n            }\n            function onex(err, ex) {\n                if (err) return cb(err);\n                if (ex) return cb(null, file, pkg);\n                load(exts.slice(1), x, pkg);\n            }\n        }\n    }\n\n    function loadpkg(dir, cb) {\n        if (dir === '' || dir === '/') return cb(null);\n        if (process.platform === 'win32' && (/^\\w:[/\\\\]*$/).test(dir)) {\n            return cb(null);\n        }\n        if ((/[/\\\\]node_modules[/\\\\]*$/).test(dir)) return cb(null);\n\n        maybeRealpath(realpath, dir, opts, function (unwrapErr, pkgdir) {\n            if (unwrapErr) return loadpkg(path.dirname(dir), cb);\n            var pkgfile = path.join(pkgdir, 'package.json');\n            isFile(pkgfile, function (err, ex) {\n                // on err, ex is false\n                if (!ex) return loadpkg(path.dirname(dir), cb);\n\n                readPackage(readFile, pkgfile, function (err, pkgParam) {\n                    if (err) cb(err);\n\n                    var pkg = pkgParam;\n\n                    if (pkg && opts.packageFilter) {\n                        pkg = opts.packageFilter(pkg, pkgfile);\n                    }\n                    cb(null, pkg, dir);\n                });\n            });\n        });\n    }\n\n    function loadAsDirectory(x, loadAsDirectoryPackage, callback) {\n        var cb = callback;\n        var fpkg = loadAsDirectoryPackage;\n        if (typeof fpkg === 'function') {\n            cb = fpkg;\n            fpkg = opts.package;\n        }\n\n        maybeRealpath(realpath, x, opts, function (unwrapErr, pkgdir) {\n            if (unwrapErr) return cb(unwrapErr);\n            var pkgfile = path.join(pkgdir, 'package.json');\n            isFile(pkgfile, function (err, ex) {\n                if (err) return cb(err);\n                if (!ex) return loadAsFile(path.join(x, 'index'), fpkg, cb);\n\n                readPackage(readFile, pkgfile, function (err, pkgParam) {\n                    if (err) return cb(err);\n\n                    var pkg = pkgParam;\n\n                    if (pkg && opts.packageFilter) {\n                        pkg = opts.packageFilter(pkg, pkgfile);\n                    }\n\n                    if (pkg && pkg.main) {\n                        if (typeof pkg.main !== 'string') {\n                            var mainError = new TypeError('package “' + pkg.name + '” `main` must be a string');\n                            mainError.code = 'INVALID_PACKAGE_MAIN';\n                            return cb(mainError);\n                        }\n                        if (pkg.main === '.' || pkg.main === './') {\n                            pkg.main = 'index';\n                        }\n                        loadAsFile(path.resolve(x, pkg.main), pkg, function (err, m, pkg) {\n                            if (err) return cb(err);\n                            if (m) return cb(null, m, pkg);\n                            if (!pkg) return loadAsFile(path.join(x, 'index'), pkg, cb);\n\n                            var dir = path.resolve(x, pkg.main);\n                            loadAsDirectory(dir, pkg, function (err, n, pkg) {\n                                if (err) return cb(err);\n                                if (n) return cb(null, n, pkg);\n                                loadAsFile(path.join(x, 'index'), pkg, cb);\n                            });\n                        });\n                        return;\n                    }\n\n                    loadAsFile(path.join(x, '/index'), pkg, cb);\n                });\n            });\n        });\n    }\n\n    function processDirs(cb, dirs) {\n        if (dirs.length === 0) return cb(null, undefined);\n        var dir = dirs[0];\n\n        isDirectory(path.dirname(dir), isdir);\n\n        function isdir(err, isdir) {\n            if (err) return cb(err);\n            if (!isdir) return processDirs(cb, dirs.slice(1));\n            loadAsFile(dir, opts.package, onfile);\n        }\n\n        function onfile(err, m, pkg) {\n            if (err) return cb(err);\n            if (m) return cb(null, m, pkg);\n            loadAsDirectory(dir, opts.package, ondir);\n        }\n\n        function ondir(err, n, pkg) {\n            if (err) return cb(err);\n            if (n) return cb(null, n, pkg);\n            processDirs(cb, dirs.slice(1));\n        }\n    }\n    function loadNodeModules(x, start, cb) {\n        var thunk = function () { return getPackageCandidates(x, start, opts); };\n        processDirs(\n            cb,\n            packageIterator ? packageIterator(x, start, thunk, opts) : thunk()\n        );\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/resolve/lib/async.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/resolve/lib/caller.js":
/*!********************************************!*\
  !*** ./node_modules/resolve/lib/caller.js ***!
  \********************************************/
/***/ ((module) => {

eval("module.exports = function () {\n    // see https://code.google.com/p/v8/wiki/JavaScriptStackTraceApi\n    var origPrepareStackTrace = Error.prepareStackTrace;\n    Error.prepareStackTrace = function (_, stack) { return stack; };\n    var stack = (new Error()).stack;\n    Error.prepareStackTrace = origPrepareStackTrace;\n    return stack[2].getFileName();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvY2FsbGVyLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBLG9EQUFvRDtBQUNwRDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGVydmlld2NyYWNrZXIvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvY2FsbGVyLmpzPzVlYWQiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAoKSB7XG4gICAgLy8gc2VlIGh0dHBzOi8vY29kZS5nb29nbGUuY29tL3Avdjgvd2lraS9KYXZhU2NyaXB0U3RhY2tUcmFjZUFwaVxuICAgIHZhciBvcmlnUHJlcGFyZVN0YWNrVHJhY2UgPSBFcnJvci5wcmVwYXJlU3RhY2tUcmFjZTtcbiAgICBFcnJvci5wcmVwYXJlU3RhY2tUcmFjZSA9IGZ1bmN0aW9uIChfLCBzdGFjaykgeyByZXR1cm4gc3RhY2s7IH07XG4gICAgdmFyIHN0YWNrID0gKG5ldyBFcnJvcigpKS5zdGFjaztcbiAgICBFcnJvci5wcmVwYXJlU3RhY2tUcmFjZSA9IG9yaWdQcmVwYXJlU3RhY2tUcmFjZTtcbiAgICByZXR1cm4gc3RhY2tbMl0uZ2V0RmlsZU5hbWUoKTtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/resolve/lib/caller.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/resolve/lib/core.js":
/*!******************************************!*\
  !*** ./node_modules/resolve/lib/core.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nvar isCoreModule = __webpack_require__(/*! is-core-module */ \"(rsc)/./node_modules/is-core-module/index.js\");\nvar data = __webpack_require__(/*! ./core.json */ \"(rsc)/./node_modules/resolve/lib/core.json\");\n\nvar core = {};\nfor (var mod in data) { // eslint-disable-line no-restricted-syntax\n    if (Object.prototype.hasOwnProperty.call(data, mod)) {\n        core[mod] = isCoreModule(mod);\n    }\n}\nmodule.exports = core;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvY29yZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixtQkFBbUIsbUJBQU8sQ0FBQyxvRUFBZ0I7QUFDM0MsV0FBVyxtQkFBTyxDQUFDLCtEQUFhOztBQUVoQztBQUNBLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50ZXJ2aWV3Y3JhY2tlci8uL25vZGVfbW9kdWxlcy9yZXNvbHZlL2xpYi9jb3JlLmpzP2RiMjMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgaXNDb3JlTW9kdWxlID0gcmVxdWlyZSgnaXMtY29yZS1tb2R1bGUnKTtcbnZhciBkYXRhID0gcmVxdWlyZSgnLi9jb3JlLmpzb24nKTtcblxudmFyIGNvcmUgPSB7fTtcbmZvciAodmFyIG1vZCBpbiBkYXRhKSB7IC8vIGVzbGludC1kaXNhYmxlLWxpbmUgbm8tcmVzdHJpY3RlZC1zeW50YXhcbiAgICBpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGRhdGEsIG1vZCkpIHtcbiAgICAgICAgY29yZVttb2RdID0gaXNDb3JlTW9kdWxlKG1vZCk7XG4gICAgfVxufVxubW9kdWxlLmV4cG9ydHMgPSBjb3JlO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/resolve/lib/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/resolve/lib/homedir.js":
/*!*********************************************!*\
  !*** ./node_modules/resolve/lib/homedir.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nvar os = __webpack_require__(/*! os */ \"os\");\n\n// adapted from https://github.com/sindresorhus/os-homedir/blob/11e089f4754db38bb535e5a8416320c4446e8cfd/index.js\n\nmodule.exports = os.homedir || function homedir() {\n    var home = process.env.HOME;\n    var user = process.env.LOGNAME || process.env.USER || process.env.LNAME || process.env.USERNAME;\n\n    if (process.platform === 'win32') {\n        return process.env.USERPROFILE || process.env.HOMEDRIVE + process.env.HOMEPATH || home || null;\n    }\n\n    if (process.platform === 'darwin') {\n        return home || (user ? '/Users/' + user : null);\n    }\n\n    if (process.platform === 'linux') {\n        return home || (process.getuid() === 0 ? '/root' : (user ? '/home/' + user : null)); // eslint-disable-line no-extra-parens\n    }\n\n    return home || null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvaG9tZWRpci5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixTQUFTLG1CQUFPLENBQUMsY0FBSTs7QUFFckI7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSw2RkFBNkY7QUFDN0Y7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGVydmlld2NyYWNrZXIvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvaG9tZWRpci5qcz80MzNlIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxudmFyIG9zID0gcmVxdWlyZSgnb3MnKTtcblxuLy8gYWRhcHRlZCBmcm9tIGh0dHBzOi8vZ2l0aHViLmNvbS9zaW5kcmVzb3JodXMvb3MtaG9tZWRpci9ibG9iLzExZTA4OWY0NzU0ZGIzOGJiNTM1ZTVhODQxNjMyMGM0NDQ2ZThjZmQvaW5kZXguanNcblxubW9kdWxlLmV4cG9ydHMgPSBvcy5ob21lZGlyIHx8IGZ1bmN0aW9uIGhvbWVkaXIoKSB7XG4gICAgdmFyIGhvbWUgPSBwcm9jZXNzLmVudi5IT01FO1xuICAgIHZhciB1c2VyID0gcHJvY2Vzcy5lbnYuTE9HTkFNRSB8fCBwcm9jZXNzLmVudi5VU0VSIHx8IHByb2Nlc3MuZW52LkxOQU1FIHx8IHByb2Nlc3MuZW52LlVTRVJOQU1FO1xuXG4gICAgaWYgKHByb2Nlc3MucGxhdGZvcm0gPT09ICd3aW4zMicpIHtcbiAgICAgICAgcmV0dXJuIHByb2Nlc3MuZW52LlVTRVJQUk9GSUxFIHx8IHByb2Nlc3MuZW52LkhPTUVEUklWRSArIHByb2Nlc3MuZW52LkhPTUVQQVRIIHx8IGhvbWUgfHwgbnVsbDtcbiAgICB9XG5cbiAgICBpZiAocHJvY2Vzcy5wbGF0Zm9ybSA9PT0gJ2RhcndpbicpIHtcbiAgICAgICAgcmV0dXJuIGhvbWUgfHwgKHVzZXIgPyAnL1VzZXJzLycgKyB1c2VyIDogbnVsbCk7XG4gICAgfVxuXG4gICAgaWYgKHByb2Nlc3MucGxhdGZvcm0gPT09ICdsaW51eCcpIHtcbiAgICAgICAgcmV0dXJuIGhvbWUgfHwgKHByb2Nlc3MuZ2V0dWlkKCkgPT09IDAgPyAnL3Jvb3QnIDogKHVzZXIgPyAnL2hvbWUvJyArIHVzZXIgOiBudWxsKSk7IC8vIGVzbGludC1kaXNhYmxlLWxpbmUgbm8tZXh0cmEtcGFyZW5zXG4gICAgfVxuXG4gICAgcmV0dXJuIGhvbWUgfHwgbnVsbDtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/resolve/lib/homedir.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/resolve/lib/is-core.js":
/*!*********************************************!*\
  !*** ./node_modules/resolve/lib/is-core.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var isCoreModule = __webpack_require__(/*! is-core-module */ \"(rsc)/./node_modules/is-core-module/index.js\");\n\nmodule.exports = function isCore(x) {\n    return isCoreModule(x);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvaXMtY29yZS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxtQkFBbUIsbUJBQU8sQ0FBQyxvRUFBZ0I7O0FBRTNDO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGVydmlld2NyYWNrZXIvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvaXMtY29yZS5qcz84YWNmIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBpc0NvcmVNb2R1bGUgPSByZXF1aXJlKCdpcy1jb3JlLW1vZHVsZScpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIGlzQ29yZSh4KSB7XG4gICAgcmV0dXJuIGlzQ29yZU1vZHVsZSh4KTtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/resolve/lib/is-core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/resolve/lib/node-modules-paths.js":
/*!********************************************************!*\
  !*** ./node_modules/resolve/lib/node-modules-paths.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var path = __webpack_require__(/*! path */ \"path\");\nvar parse = path.parse || __webpack_require__(/*! path-parse */ \"(rsc)/./node_modules/path-parse/index.js\"); // eslint-disable-line global-require\n\nvar getNodeModulesDirs = function getNodeModulesDirs(absoluteStart, modules) {\n    var prefix = '/';\n    if ((/^([A-Za-z]:)/).test(absoluteStart)) {\n        prefix = '';\n    } else if ((/^\\\\\\\\/).test(absoluteStart)) {\n        prefix = '\\\\\\\\';\n    }\n\n    var paths = [absoluteStart];\n    var parsed = parse(absoluteStart);\n    while (parsed.dir !== paths[paths.length - 1]) {\n        paths.push(parsed.dir);\n        parsed = parse(parsed.dir);\n    }\n\n    return paths.reduce(function (dirs, aPath) {\n        return dirs.concat(modules.map(function (moduleDir) {\n            return path.resolve(prefix, aPath, moduleDir);\n        }));\n    }, []);\n};\n\nmodule.exports = function nodeModulesPaths(start, opts, request) {\n    var modules = opts && opts.moduleDirectory\n        ? [].concat(opts.moduleDirectory)\n        : ['node_modules'];\n\n    if (opts && typeof opts.paths === 'function') {\n        return opts.paths(\n            request,\n            start,\n            function () { return getNodeModulesDirs(start, modules); },\n            opts\n        );\n    }\n\n    var dirs = getNodeModulesDirs(start, modules);\n    return opts && opts.paths ? dirs.concat(opts.paths) : dirs;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/resolve/lib/node-modules-paths.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/resolve/lib/normalize-options.js":
/*!*******************************************************!*\
  !*** ./node_modules/resolve/lib/normalize-options.js ***!
  \*******************************************************/
/***/ ((module) => {

eval("module.exports = function (x, opts) {\n    /**\n     * This file is purposefully a passthrough. It's expected that third-party\n     * environments will override it at runtime in order to inject special logic\n     * into `resolve` (by manipulating the options). One such example is the PnP\n     * code path in Yarn.\n     */\n\n    return opts || {};\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvbm9ybWFsaXplLW9wdGlvbnMuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGVydmlld2NyYWNrZXIvLi9ub2RlX21vZHVsZXMvcmVzb2x2ZS9saWIvbm9ybWFsaXplLW9wdGlvbnMuanM/NWQzMyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uICh4LCBvcHRzKSB7XG4gICAgLyoqXG4gICAgICogVGhpcyBmaWxlIGlzIHB1cnBvc2VmdWxseSBhIHBhc3N0aHJvdWdoLiBJdCdzIGV4cGVjdGVkIHRoYXQgdGhpcmQtcGFydHlcbiAgICAgKiBlbnZpcm9ubWVudHMgd2lsbCBvdmVycmlkZSBpdCBhdCBydW50aW1lIGluIG9yZGVyIHRvIGluamVjdCBzcGVjaWFsIGxvZ2ljXG4gICAgICogaW50byBgcmVzb2x2ZWAgKGJ5IG1hbmlwdWxhdGluZyB0aGUgb3B0aW9ucykuIE9uZSBzdWNoIGV4YW1wbGUgaXMgdGhlIFBuUFxuICAgICAqIGNvZGUgcGF0aCBpbiBZYXJuLlxuICAgICAqL1xuXG4gICAgcmV0dXJuIG9wdHMgfHwge307XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/resolve/lib/normalize-options.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/resolve/lib/sync.js":
/*!******************************************!*\
  !*** ./node_modules/resolve/lib/sync.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var isCore = __webpack_require__(/*! is-core-module */ \"(rsc)/./node_modules/is-core-module/index.js\");\nvar fs = __webpack_require__(/*! fs */ \"fs\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar getHomedir = __webpack_require__(/*! ./homedir */ \"(rsc)/./node_modules/resolve/lib/homedir.js\");\nvar caller = __webpack_require__(/*! ./caller */ \"(rsc)/./node_modules/resolve/lib/caller.js\");\nvar nodeModulesPaths = __webpack_require__(/*! ./node-modules-paths */ \"(rsc)/./node_modules/resolve/lib/node-modules-paths.js\");\nvar normalizeOptions = __webpack_require__(/*! ./normalize-options */ \"(rsc)/./node_modules/resolve/lib/normalize-options.js\");\n\nvar realpathFS = process.platform !== 'win32' && fs.realpathSync && typeof fs.realpathSync.native === 'function' ? fs.realpathSync.native : fs.realpathSync;\n\nvar homedir = getHomedir();\nvar defaultPaths = function () {\n    return [\n        path.join(homedir, '.node_modules'),\n        path.join(homedir, '.node_libraries')\n    ];\n};\n\nvar defaultIsFile = function isFile(file) {\n    try {\n        var stat = fs.statSync(file, { throwIfNoEntry: false });\n    } catch (e) {\n        if (e && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) return false;\n        throw e;\n    }\n    return !!stat && (stat.isFile() || stat.isFIFO());\n};\n\nvar defaultIsDir = function isDirectory(dir) {\n    try {\n        var stat = fs.statSync(dir, { throwIfNoEntry: false });\n    } catch (e) {\n        if (e && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) return false;\n        throw e;\n    }\n    return !!stat && stat.isDirectory();\n};\n\nvar defaultRealpathSync = function realpathSync(x) {\n    try {\n        return realpathFS(x);\n    } catch (realpathErr) {\n        if (realpathErr.code !== 'ENOENT') {\n            throw realpathErr;\n        }\n    }\n    return x;\n};\n\nvar maybeRealpathSync = function maybeRealpathSync(realpathSync, x, opts) {\n    if (opts && opts.preserveSymlinks === false) {\n        return realpathSync(x);\n    }\n    return x;\n};\n\nvar defaultReadPackageSync = function defaultReadPackageSync(readFileSync, pkgfile) {\n    var body = readFileSync(pkgfile);\n    try {\n        var pkg = JSON.parse(body);\n        return pkg;\n    } catch (jsonErr) {}\n};\n\nvar getPackageCandidates = function getPackageCandidates(x, start, opts) {\n    var dirs = nodeModulesPaths(start, opts, x);\n    for (var i = 0; i < dirs.length; i++) {\n        dirs[i] = path.join(dirs[i], x);\n    }\n    return dirs;\n};\n\nmodule.exports = function resolveSync(x, options) {\n    if (typeof x !== 'string') {\n        throw new TypeError('Path must be a string.');\n    }\n    var opts = normalizeOptions(x, options);\n\n    var isFile = opts.isFile || defaultIsFile;\n    var readFileSync = opts.readFileSync || fs.readFileSync;\n    var isDirectory = opts.isDirectory || defaultIsDir;\n    var realpathSync = opts.realpathSync || defaultRealpathSync;\n    var readPackageSync = opts.readPackageSync || defaultReadPackageSync;\n    if (opts.readFileSync && opts.readPackageSync) {\n        throw new TypeError('`readFileSync` and `readPackageSync` are mutually exclusive.');\n    }\n    var packageIterator = opts.packageIterator;\n\n    var extensions = opts.extensions || ['.js'];\n    var includeCoreModules = opts.includeCoreModules !== false;\n    var basedir = opts.basedir || path.dirname(caller());\n    var parent = opts.filename || basedir;\n\n    opts.paths = opts.paths || defaultPaths();\n\n    // ensure that `basedir` is an absolute path at this point, resolving against the process' current working directory\n    var absoluteStart = maybeRealpathSync(realpathSync, path.resolve(basedir), opts);\n\n    if ((/^(?:\\.\\.?(?:\\/|$)|\\/|([A-Za-z]:)?[/\\\\])/).test(x)) {\n        var res = path.resolve(absoluteStart, x);\n        if (x === '.' || x === '..' || x.slice(-1) === '/') res += '/';\n        var m = loadAsFileSync(res) || loadAsDirectorySync(res);\n        if (m) return maybeRealpathSync(realpathSync, m, opts);\n    } else if (includeCoreModules && isCore(x)) {\n        return x;\n    } else {\n        var n = loadNodeModulesSync(x, absoluteStart);\n        if (n) return maybeRealpathSync(realpathSync, n, opts);\n    }\n\n    var err = new Error(\"Cannot find module '\" + x + \"' from '\" + parent + \"'\");\n    err.code = 'MODULE_NOT_FOUND';\n    throw err;\n\n    function loadAsFileSync(x) {\n        var pkg = loadpkg(path.dirname(x));\n\n        if (pkg && pkg.dir && pkg.pkg && opts.pathFilter) {\n            var rfile = path.relative(pkg.dir, x);\n            var r = opts.pathFilter(pkg.pkg, x, rfile);\n            if (r) {\n                x = path.resolve(pkg.dir, r); // eslint-disable-line no-param-reassign\n            }\n        }\n\n        if (isFile(x)) {\n            return x;\n        }\n\n        for (var i = 0; i < extensions.length; i++) {\n            var file = x + extensions[i];\n            if (isFile(file)) {\n                return file;\n            }\n        }\n    }\n\n    function loadpkg(dir) {\n        if (dir === '' || dir === '/') return;\n        if (process.platform === 'win32' && (/^\\w:[/\\\\]*$/).test(dir)) {\n            return;\n        }\n        if ((/[/\\\\]node_modules[/\\\\]*$/).test(dir)) return;\n\n        var pkgfile = path.join(maybeRealpathSync(realpathSync, dir, opts), 'package.json');\n\n        if (!isFile(pkgfile)) {\n            return loadpkg(path.dirname(dir));\n        }\n\n        var pkg = readPackageSync(readFileSync, pkgfile);\n\n        if (pkg && opts.packageFilter) {\n            // v2 will pass pkgfile\n            pkg = opts.packageFilter(pkg, /*pkgfile,*/ dir); // eslint-disable-line spaced-comment\n        }\n\n        return { pkg: pkg, dir: dir };\n    }\n\n    function loadAsDirectorySync(x) {\n        var pkgfile = path.join(maybeRealpathSync(realpathSync, x, opts), '/package.json');\n        if (isFile(pkgfile)) {\n            try {\n                var pkg = readPackageSync(readFileSync, pkgfile);\n            } catch (e) {}\n\n            if (pkg && opts.packageFilter) {\n                // v2 will pass pkgfile\n                pkg = opts.packageFilter(pkg, /*pkgfile,*/ x); // eslint-disable-line spaced-comment\n            }\n\n            if (pkg && pkg.main) {\n                if (typeof pkg.main !== 'string') {\n                    var mainError = new TypeError('package “' + pkg.name + '” `main` must be a string');\n                    mainError.code = 'INVALID_PACKAGE_MAIN';\n                    throw mainError;\n                }\n                if (pkg.main === '.' || pkg.main === './') {\n                    pkg.main = 'index';\n                }\n                try {\n                    var m = loadAsFileSync(path.resolve(x, pkg.main));\n                    if (m) return m;\n                    var n = loadAsDirectorySync(path.resolve(x, pkg.main));\n                    if (n) return n;\n                } catch (e) {}\n            }\n        }\n\n        return loadAsFileSync(path.join(x, '/index'));\n    }\n\n    function loadNodeModulesSync(x, start) {\n        var thunk = function () { return getPackageCandidates(x, start, opts); };\n        var dirs = packageIterator ? packageIterator(x, start, thunk, opts) : thunk();\n\n        for (var i = 0; i < dirs.length; i++) {\n            var dir = dirs[i];\n            if (isDirectory(path.dirname(dir))) {\n                var m = loadAsFileSync(dir);\n                if (m) return m;\n                var n = loadAsDirectorySync(dir);\n                if (n) return n;\n            }\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/resolve/lib/sync.js\n");

/***/ }),

/***/ "(instrument)/./node_modules/resolve/lib/core.json":
/*!********************************************!*\
  !*** ./node_modules/resolve/lib/core.json ***!
  \********************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"assert":true,"node:assert":[">= 14.18 && < 15",">= 16"],"assert/strict":">= 15","node:assert/strict":">= 16","async_hooks":">= 8","node:async_hooks":[">= 14.18 && < 15",">= 16"],"buffer_ieee754":">= 0.5 && < 0.9.7","buffer":true,"node:buffer":[">= 14.18 && < 15",">= 16"],"child_process":true,"node:child_process":[">= 14.18 && < 15",">= 16"],"cluster":">= 0.5","node:cluster":[">= 14.18 && < 15",">= 16"],"console":true,"node:console":[">= 14.18 && < 15",">= 16"],"constants":true,"node:constants":[">= 14.18 && < 15",">= 16"],"crypto":true,"node:crypto":[">= 14.18 && < 15",">= 16"],"_debug_agent":">= 1 && < 8","_debugger":"< 8","dgram":true,"node:dgram":[">= 14.18 && < 15",">= 16"],"diagnostics_channel":[">= 14.17 && < 15",">= 15.1"],"node:diagnostics_channel":[">= 14.18 && < 15",">= 16"],"dns":true,"node:dns":[">= 14.18 && < 15",">= 16"],"dns/promises":">= 15","node:dns/promises":">= 16","domain":">= 0.7.12","node:domain":[">= 14.18 && < 15",">= 16"],"events":true,"node:events":[">= 14.18 && < 15",">= 16"],"freelist":"< 6","fs":true,"node:fs":[">= 14.18 && < 15",">= 16"],"fs/promises":[">= 10 && < 10.1",">= 14"],"node:fs/promises":[">= 14.18 && < 15",">= 16"],"_http_agent":">= 0.11.1","node:_http_agent":[">= 14.18 && < 15",">= 16"],"_http_client":">= 0.11.1","node:_http_client":[">= 14.18 && < 15",">= 16"],"_http_common":">= 0.11.1","node:_http_common":[">= 14.18 && < 15",">= 16"],"_http_incoming":">= 0.11.1","node:_http_incoming":[">= 14.18 && < 15",">= 16"],"_http_outgoing":">= 0.11.1","node:_http_outgoing":[">= 14.18 && < 15",">= 16"],"_http_server":">= 0.11.1","node:_http_server":[">= 14.18 && < 15",">= 16"],"http":true,"node:http":[">= 14.18 && < 15",">= 16"],"http2":">= 8.8","node:http2":[">= 14.18 && < 15",">= 16"],"https":true,"node:https":[">= 14.18 && < 15",">= 16"],"inspector":">= 8","node:inspector":[">= 14.18 && < 15",">= 16"],"inspector/promises":[">= 19"],"node:inspector/promises":[">= 19"],"_linklist":"< 8","module":true,"node:module":[">= 14.18 && < 15",">= 16"],"net":true,"node:net":[">= 14.18 && < 15",">= 16"],"node-inspect/lib/_inspect":">= 7.6 && < 12","node-inspect/lib/internal/inspect_client":">= 7.6 && < 12","node-inspect/lib/internal/inspect_repl":">= 7.6 && < 12","os":true,"node:os":[">= 14.18 && < 15",">= 16"],"path":true,"node:path":[">= 14.18 && < 15",">= 16"],"path/posix":">= 15.3","node:path/posix":">= 16","path/win32":">= 15.3","node:path/win32":">= 16","perf_hooks":">= 8.5","node:perf_hooks":[">= 14.18 && < 15",">= 16"],"process":">= 1","node:process":[">= 14.18 && < 15",">= 16"],"punycode":">= 0.5","node:punycode":[">= 14.18 && < 15",">= 16"],"querystring":true,"node:querystring":[">= 14.18 && < 15",">= 16"],"readline":true,"node:readline":[">= 14.18 && < 15",">= 16"],"readline/promises":">= 17","node:readline/promises":">= 17","repl":true,"node:repl":[">= 14.18 && < 15",">= 16"],"smalloc":">= 0.11.5 && < 3","_stream_duplex":">= 0.9.4","node:_stream_duplex":[">= 14.18 && < 15",">= 16"],"_stream_transform":">= 0.9.4","node:_stream_transform":[">= 14.18 && < 15",">= 16"],"_stream_wrap":">= 1.4.1","node:_stream_wrap":[">= 14.18 && < 15",">= 16"],"_stream_passthrough":">= 0.9.4","node:_stream_passthrough":[">= 14.18 && < 15",">= 16"],"_stream_readable":">= 0.9.4","node:_stream_readable":[">= 14.18 && < 15",">= 16"],"_stream_writable":">= 0.9.4","node:_stream_writable":[">= 14.18 && < 15",">= 16"],"stream":true,"node:stream":[">= 14.18 && < 15",">= 16"],"stream/consumers":">= 16.7","node:stream/consumers":">= 16.7","stream/promises":">= 15","node:stream/promises":">= 16","stream/web":">= 16.5","node:stream/web":">= 16.5","string_decoder":true,"node:string_decoder":[">= 14.18 && < 15",">= 16"],"sys":[">= 0.4 && < 0.7",">= 0.8"],"node:sys":[">= 14.18 && < 15",">= 16"],"test/reporters":">= 19.9 && < 20.2","node:test/reporters":[">= 18.17 && < 19",">= 19.9",">= 20"],"node:test":[">= 16.17 && < 17",">= 18"],"timers":true,"node:timers":[">= 14.18 && < 15",">= 16"],"timers/promises":">= 15","node:timers/promises":">= 16","_tls_common":">= 0.11.13","node:_tls_common":[">= 14.18 && < 15",">= 16"],"_tls_legacy":">= 0.11.3 && < 10","_tls_wrap":">= 0.11.3","node:_tls_wrap":[">= 14.18 && < 15",">= 16"],"tls":true,"node:tls":[">= 14.18 && < 15",">= 16"],"trace_events":">= 10","node:trace_events":[">= 14.18 && < 15",">= 16"],"tty":true,"node:tty":[">= 14.18 && < 15",">= 16"],"url":true,"node:url":[">= 14.18 && < 15",">= 16"],"util":true,"node:util":[">= 14.18 && < 15",">= 16"],"util/types":">= 15.3","node:util/types":">= 16","v8/tools/arguments":">= 10 && < 12","v8/tools/codemap":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/consarray":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/csvparser":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/logreader":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/profile_view":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/splaytree":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8":">= 1","node:v8":[">= 14.18 && < 15",">= 16"],"vm":true,"node:vm":[">= 14.18 && < 15",">= 16"],"wasi":[">= 13.4 && < 13.5",">= 18.17 && < 19",">= 20"],"node:wasi":[">= 18.17 && < 19",">= 20"],"worker_threads":">= 11.7","node:worker_threads":[">= 14.18 && < 15",">= 16"],"zlib":">= 0.5","node:zlib":[">= 14.18 && < 15",">= 16"]}');

/***/ }),

/***/ "(rsc)/./node_modules/resolve/lib/core.json":
/*!********************************************!*\
  !*** ./node_modules/resolve/lib/core.json ***!
  \********************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"assert":true,"node:assert":[">= 14.18 && < 15",">= 16"],"assert/strict":">= 15","node:assert/strict":">= 16","async_hooks":">= 8","node:async_hooks":[">= 14.18 && < 15",">= 16"],"buffer_ieee754":">= 0.5 && < 0.9.7","buffer":true,"node:buffer":[">= 14.18 && < 15",">= 16"],"child_process":true,"node:child_process":[">= 14.18 && < 15",">= 16"],"cluster":">= 0.5","node:cluster":[">= 14.18 && < 15",">= 16"],"console":true,"node:console":[">= 14.18 && < 15",">= 16"],"constants":true,"node:constants":[">= 14.18 && < 15",">= 16"],"crypto":true,"node:crypto":[">= 14.18 && < 15",">= 16"],"_debug_agent":">= 1 && < 8","_debugger":"< 8","dgram":true,"node:dgram":[">= 14.18 && < 15",">= 16"],"diagnostics_channel":[">= 14.17 && < 15",">= 15.1"],"node:diagnostics_channel":[">= 14.18 && < 15",">= 16"],"dns":true,"node:dns":[">= 14.18 && < 15",">= 16"],"dns/promises":">= 15","node:dns/promises":">= 16","domain":">= 0.7.12","node:domain":[">= 14.18 && < 15",">= 16"],"events":true,"node:events":[">= 14.18 && < 15",">= 16"],"freelist":"< 6","fs":true,"node:fs":[">= 14.18 && < 15",">= 16"],"fs/promises":[">= 10 && < 10.1",">= 14"],"node:fs/promises":[">= 14.18 && < 15",">= 16"],"_http_agent":">= 0.11.1","node:_http_agent":[">= 14.18 && < 15",">= 16"],"_http_client":">= 0.11.1","node:_http_client":[">= 14.18 && < 15",">= 16"],"_http_common":">= 0.11.1","node:_http_common":[">= 14.18 && < 15",">= 16"],"_http_incoming":">= 0.11.1","node:_http_incoming":[">= 14.18 && < 15",">= 16"],"_http_outgoing":">= 0.11.1","node:_http_outgoing":[">= 14.18 && < 15",">= 16"],"_http_server":">= 0.11.1","node:_http_server":[">= 14.18 && < 15",">= 16"],"http":true,"node:http":[">= 14.18 && < 15",">= 16"],"http2":">= 8.8","node:http2":[">= 14.18 && < 15",">= 16"],"https":true,"node:https":[">= 14.18 && < 15",">= 16"],"inspector":">= 8","node:inspector":[">= 14.18 && < 15",">= 16"],"inspector/promises":[">= 19"],"node:inspector/promises":[">= 19"],"_linklist":"< 8","module":true,"node:module":[">= 14.18 && < 15",">= 16"],"net":true,"node:net":[">= 14.18 && < 15",">= 16"],"node-inspect/lib/_inspect":">= 7.6 && < 12","node-inspect/lib/internal/inspect_client":">= 7.6 && < 12","node-inspect/lib/internal/inspect_repl":">= 7.6 && < 12","os":true,"node:os":[">= 14.18 && < 15",">= 16"],"path":true,"node:path":[">= 14.18 && < 15",">= 16"],"path/posix":">= 15.3","node:path/posix":">= 16","path/win32":">= 15.3","node:path/win32":">= 16","perf_hooks":">= 8.5","node:perf_hooks":[">= 14.18 && < 15",">= 16"],"process":">= 1","node:process":[">= 14.18 && < 15",">= 16"],"punycode":">= 0.5","node:punycode":[">= 14.18 && < 15",">= 16"],"querystring":true,"node:querystring":[">= 14.18 && < 15",">= 16"],"readline":true,"node:readline":[">= 14.18 && < 15",">= 16"],"readline/promises":">= 17","node:readline/promises":">= 17","repl":true,"node:repl":[">= 14.18 && < 15",">= 16"],"smalloc":">= 0.11.5 && < 3","_stream_duplex":">= 0.9.4","node:_stream_duplex":[">= 14.18 && < 15",">= 16"],"_stream_transform":">= 0.9.4","node:_stream_transform":[">= 14.18 && < 15",">= 16"],"_stream_wrap":">= 1.4.1","node:_stream_wrap":[">= 14.18 && < 15",">= 16"],"_stream_passthrough":">= 0.9.4","node:_stream_passthrough":[">= 14.18 && < 15",">= 16"],"_stream_readable":">= 0.9.4","node:_stream_readable":[">= 14.18 && < 15",">= 16"],"_stream_writable":">= 0.9.4","node:_stream_writable":[">= 14.18 && < 15",">= 16"],"stream":true,"node:stream":[">= 14.18 && < 15",">= 16"],"stream/consumers":">= 16.7","node:stream/consumers":">= 16.7","stream/promises":">= 15","node:stream/promises":">= 16","stream/web":">= 16.5","node:stream/web":">= 16.5","string_decoder":true,"node:string_decoder":[">= 14.18 && < 15",">= 16"],"sys":[">= 0.4 && < 0.7",">= 0.8"],"node:sys":[">= 14.18 && < 15",">= 16"],"test/reporters":">= 19.9 && < 20.2","node:test/reporters":[">= 18.17 && < 19",">= 19.9",">= 20"],"node:test":[">= 16.17 && < 17",">= 18"],"timers":true,"node:timers":[">= 14.18 && < 15",">= 16"],"timers/promises":">= 15","node:timers/promises":">= 16","_tls_common":">= 0.11.13","node:_tls_common":[">= 14.18 && < 15",">= 16"],"_tls_legacy":">= 0.11.3 && < 10","_tls_wrap":">= 0.11.3","node:_tls_wrap":[">= 14.18 && < 15",">= 16"],"tls":true,"node:tls":[">= 14.18 && < 15",">= 16"],"trace_events":">= 10","node:trace_events":[">= 14.18 && < 15",">= 16"],"tty":true,"node:tty":[">= 14.18 && < 15",">= 16"],"url":true,"node:url":[">= 14.18 && < 15",">= 16"],"util":true,"node:util":[">= 14.18 && < 15",">= 16"],"util/types":">= 15.3","node:util/types":">= 16","v8/tools/arguments":">= 10 && < 12","v8/tools/codemap":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/consarray":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/csvparser":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/logreader":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/profile_view":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/splaytree":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8":">= 1","node:v8":[">= 14.18 && < 15",">= 16"],"vm":true,"node:vm":[">= 14.18 && < 15",">= 16"],"wasi":[">= 13.4 && < 13.5",">= 18.17 && < 19",">= 20"],"node:wasi":[">= 18.17 && < 19",">= 20"],"worker_threads":">= 11.7","node:worker_threads":[">= 14.18 && < 15",">= 16"],"zlib":">= 0.5","node:zlib":[">= 14.18 && < 15",">= 16"]}');

/***/ }),

/***/ "(ssr)/./node_modules/resolve/lib/core.json":
/*!********************************************!*\
  !*** ./node_modules/resolve/lib/core.json ***!
  \********************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"assert":true,"node:assert":[">= 14.18 && < 15",">= 16"],"assert/strict":">= 15","node:assert/strict":">= 16","async_hooks":">= 8","node:async_hooks":[">= 14.18 && < 15",">= 16"],"buffer_ieee754":">= 0.5 && < 0.9.7","buffer":true,"node:buffer":[">= 14.18 && < 15",">= 16"],"child_process":true,"node:child_process":[">= 14.18 && < 15",">= 16"],"cluster":">= 0.5","node:cluster":[">= 14.18 && < 15",">= 16"],"console":true,"node:console":[">= 14.18 && < 15",">= 16"],"constants":true,"node:constants":[">= 14.18 && < 15",">= 16"],"crypto":true,"node:crypto":[">= 14.18 && < 15",">= 16"],"_debug_agent":">= 1 && < 8","_debugger":"< 8","dgram":true,"node:dgram":[">= 14.18 && < 15",">= 16"],"diagnostics_channel":[">= 14.17 && < 15",">= 15.1"],"node:diagnostics_channel":[">= 14.18 && < 15",">= 16"],"dns":true,"node:dns":[">= 14.18 && < 15",">= 16"],"dns/promises":">= 15","node:dns/promises":">= 16","domain":">= 0.7.12","node:domain":[">= 14.18 && < 15",">= 16"],"events":true,"node:events":[">= 14.18 && < 15",">= 16"],"freelist":"< 6","fs":true,"node:fs":[">= 14.18 && < 15",">= 16"],"fs/promises":[">= 10 && < 10.1",">= 14"],"node:fs/promises":[">= 14.18 && < 15",">= 16"],"_http_agent":">= 0.11.1","node:_http_agent":[">= 14.18 && < 15",">= 16"],"_http_client":">= 0.11.1","node:_http_client":[">= 14.18 && < 15",">= 16"],"_http_common":">= 0.11.1","node:_http_common":[">= 14.18 && < 15",">= 16"],"_http_incoming":">= 0.11.1","node:_http_incoming":[">= 14.18 && < 15",">= 16"],"_http_outgoing":">= 0.11.1","node:_http_outgoing":[">= 14.18 && < 15",">= 16"],"_http_server":">= 0.11.1","node:_http_server":[">= 14.18 && < 15",">= 16"],"http":true,"node:http":[">= 14.18 && < 15",">= 16"],"http2":">= 8.8","node:http2":[">= 14.18 && < 15",">= 16"],"https":true,"node:https":[">= 14.18 && < 15",">= 16"],"inspector":">= 8","node:inspector":[">= 14.18 && < 15",">= 16"],"inspector/promises":[">= 19"],"node:inspector/promises":[">= 19"],"_linklist":"< 8","module":true,"node:module":[">= 14.18 && < 15",">= 16"],"net":true,"node:net":[">= 14.18 && < 15",">= 16"],"node-inspect/lib/_inspect":">= 7.6 && < 12","node-inspect/lib/internal/inspect_client":">= 7.6 && < 12","node-inspect/lib/internal/inspect_repl":">= 7.6 && < 12","os":true,"node:os":[">= 14.18 && < 15",">= 16"],"path":true,"node:path":[">= 14.18 && < 15",">= 16"],"path/posix":">= 15.3","node:path/posix":">= 16","path/win32":">= 15.3","node:path/win32":">= 16","perf_hooks":">= 8.5","node:perf_hooks":[">= 14.18 && < 15",">= 16"],"process":">= 1","node:process":[">= 14.18 && < 15",">= 16"],"punycode":">= 0.5","node:punycode":[">= 14.18 && < 15",">= 16"],"querystring":true,"node:querystring":[">= 14.18 && < 15",">= 16"],"readline":true,"node:readline":[">= 14.18 && < 15",">= 16"],"readline/promises":">= 17","node:readline/promises":">= 17","repl":true,"node:repl":[">= 14.18 && < 15",">= 16"],"smalloc":">= 0.11.5 && < 3","_stream_duplex":">= 0.9.4","node:_stream_duplex":[">= 14.18 && < 15",">= 16"],"_stream_transform":">= 0.9.4","node:_stream_transform":[">= 14.18 && < 15",">= 16"],"_stream_wrap":">= 1.4.1","node:_stream_wrap":[">= 14.18 && < 15",">= 16"],"_stream_passthrough":">= 0.9.4","node:_stream_passthrough":[">= 14.18 && < 15",">= 16"],"_stream_readable":">= 0.9.4","node:_stream_readable":[">= 14.18 && < 15",">= 16"],"_stream_writable":">= 0.9.4","node:_stream_writable":[">= 14.18 && < 15",">= 16"],"stream":true,"node:stream":[">= 14.18 && < 15",">= 16"],"stream/consumers":">= 16.7","node:stream/consumers":">= 16.7","stream/promises":">= 15","node:stream/promises":">= 16","stream/web":">= 16.5","node:stream/web":">= 16.5","string_decoder":true,"node:string_decoder":[">= 14.18 && < 15",">= 16"],"sys":[">= 0.4 && < 0.7",">= 0.8"],"node:sys":[">= 14.18 && < 15",">= 16"],"test/reporters":">= 19.9 && < 20.2","node:test/reporters":[">= 18.17 && < 19",">= 19.9",">= 20"],"node:test":[">= 16.17 && < 17",">= 18"],"timers":true,"node:timers":[">= 14.18 && < 15",">= 16"],"timers/promises":">= 15","node:timers/promises":">= 16","_tls_common":">= 0.11.13","node:_tls_common":[">= 14.18 && < 15",">= 16"],"_tls_legacy":">= 0.11.3 && < 10","_tls_wrap":">= 0.11.3","node:_tls_wrap":[">= 14.18 && < 15",">= 16"],"tls":true,"node:tls":[">= 14.18 && < 15",">= 16"],"trace_events":">= 10","node:trace_events":[">= 14.18 && < 15",">= 16"],"tty":true,"node:tty":[">= 14.18 && < 15",">= 16"],"url":true,"node:url":[">= 14.18 && < 15",">= 16"],"util":true,"node:util":[">= 14.18 && < 15",">= 16"],"util/types":">= 15.3","node:util/types":">= 16","v8/tools/arguments":">= 10 && < 12","v8/tools/codemap":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/consarray":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/csvparser":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/logreader":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/profile_view":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/splaytree":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8":">= 1","node:v8":[">= 14.18 && < 15",">= 16"],"vm":true,"node:vm":[">= 14.18 && < 15",">= 16"],"wasi":[">= 13.4 && < 13.5",">= 18.17 && < 19",">= 20"],"node:wasi":[">= 18.17 && < 19",">= 20"],"worker_threads":">= 11.7","node:worker_threads":[">= 14.18 && < 15",">= 16"],"zlib":">= 0.5","node:zlib":[">= 14.18 && < 15",">= 16"]}');

/***/ })

};
;