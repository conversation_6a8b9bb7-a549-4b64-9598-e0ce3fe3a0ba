import { CardActionArea } from '@mui/material';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardMedia from '@mui/material/CardMedia';
import CssBaseline from '@mui/material/CssBaseline';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import * as React from 'react';

const darkTheme = createTheme({
  palette: {
    mode: 'dark',
  },
});
export default function ActionAreaCard() {
  return (
    <ThemeProvider theme={darkTheme}>
      <CssBaseline />

      <Card sx={{ maxWidth: 350 }}>
        <CardActionArea>
          <CardMedia component="img" width="200" image="/facility.png" alt="" />
          <CardContent>
            <Typography gutterBottom variant="h5" component="div">
              Interview Cracking
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Our firm specializes in cracking Interview in all kinds types of meeting platforms being invisible to the interviewer.
            </Typography>
          </CardContent>
        </CardActionArea>
      </Card>
    </ThemeProvider>
  );
}
