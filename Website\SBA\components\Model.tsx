// @ts-nocheck

import {
  useGLTF,
  Text,
  Float,
  MeshTransmissionMaterial,
} from '@react-three/drei';
import React, { useEffect } from 'react';
import { useThree } from '@react-three/fiber';
import { useControls } from 'leva';

export default function Model() {
  const { viewport } = useThree();
  const { nodes } = useGLTF('/medias/shards.glb');

  const validateAndFixGeometry = (mesh) => {
    if (!mesh.geometry || !mesh.geometry.attributes.position) return null;

    try {
      const positions = mesh.geometry.attributes.position.array;
      let hasInvalidData = false;

      // Check and fix invalid positions
      for (let i = 0; i < positions.length; i++) {
        if (isNaN(positions[i]) || !isFinite(positions[i])) {
          positions[i] = 0;
          hasInvalidData = true;
        }
      }

      if (hasInvalidData) {
        mesh.geometry.attributes.position.needsUpdate = true;

        // Skip computeBoundingSphere if we had invalid data
        // Create a minimal valid bounding sphere instead
        mesh.geometry.boundingSphere = {
          center: { x: 0, y: 0, z: 0 },
          radius: 1
        };
      } else {
        // Only compute if all data is valid
        mesh.geometry.computeBoundingSphere();
      }

      mesh.geometry.computeBoundingBox();
      return mesh;
    } catch (error) {
      console.warn('Error processing mesh geometry:', error);
      return null;
    }
  };

  const cleanupGeometry = (mesh) => {
    if (mesh && mesh.geometry) {
      mesh.geometry.dispose();
    }
  };

  useEffect(() => {
    return () => {
      // Cleanup geometries when component unmounts
      if (nodes && nodes.Scene) {
        nodes.Scene.children.forEach(cleanupGeometry);
      }
    };
  }, [nodes]);

  if (!nodes || !nodes.Scene) {
    console.warn('Model data not loaded properly');
    return null;
  }

  return (
    <group scale={viewport.width / 1.5}>
      {nodes.Scene.children.map((mesh, i) => {
        const validatedMesh = validateAndFixGeometry(mesh);
        return validatedMesh ? <Mesh data={validatedMesh} key={i} /> : null;
      })}
      <Font />
    </group>
  );
}

function Font() {
  const src = '/fonts/PPNeueMontreal-Regular.ttf';
  const textOption = {
    color: 'white',
    anchorX: 'center',
    anchorY: 'middle',
  };
  return (
    <group>
      <Text font={src} position={[0, 0, -0.1]} fontSize={0.4} {...textOption}>
        404
      </Text>
      <Text
        font={src}
        position={[0, -0.15, -0.1]}
        fontSize={0.03}
        {...textOption}
      >
        The link is broken
      </Text>
    </group>
  );
}

function Mesh({ data }) {
  const materialProps = useControls({
    thickness: { value: 0.275, min: 0, max: 1, step: 0.01 },
    ior: { value: 1.8, min: 0, max: 3, step: 0.1 },
    chromaticAberration: { value: 0.75, min: 0, max: 1 },
    resolution: { value: 300 },
  });

  return (
    <Float>
      <mesh {...data}>
        <MeshTransmissionMaterial
          roughness={0}
          transmission={0.99}
          {...materialProps}
        />
      </mesh>
    </Float>
  );
}
