// Client-side action to fetch user count
export async function fetchUserCount(): Promise<number> {
  try {
    // Make a fetch request to an API endpoint that will return the user count
    const response = await fetch('/api/user-count', { cache: 'no-store' });
    
    if (!response.ok) {
      throw new Error('Failed to fetch user count');
    }
    
    const data = await response.json();
    return data.count || 39; // Return the count or fallback to 39
  } catch (error) {
    console.error('Error fetching user count:', error);
    return 39; // Fallback value
  }
}
