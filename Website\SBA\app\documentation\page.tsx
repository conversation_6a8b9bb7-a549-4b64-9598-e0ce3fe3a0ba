"use client";

import { motion } from "framer-motion"; // Optional: for animations
import Image from "next/image";
import React, { useEffect, useRef } from "react";

export default function Documentation() {
    const windowsRef = useRef(null);
    const macosRef = useRef(null);

    // Scroll to section smoothly
    const scrollToSection = (ref) => {
        ref.current.scrollIntoView({ behavior: "smooth" });
    };

    useEffect(() => {
        // Optional: Add smooth scrolling globally
        document.documentElement.style.scrollBehavior = "smooth";
    }, []);

    return (
        <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-gray-900 via-black to-gray-800 pb-20 pt-24 text-white">
            <div className="mx-auto flex max-w-7xl flex-col gap-8 px-6 lg:flex-row lg:px-8">
                {/* Side Navbar */}
                <aside className="h-fit w-full rounded-xl border border-gray-700 bg-gray-900/80 p-6 shadow-lg backdrop-blur-md lg:sticky lg:top-24 lg:w-1/4">
                    <h3 className="mb-6 text-2xl font-bold text-blue-400">Navigation</h3>
                    <ul className="space-y-4">
                        <li>
                            <button
                                onClick={() => scrollToSection(windowsRef)}
                                className="w-full text-left text-lg font-medium text-gray-300 transition-colors duration-300 hover:text-blue-400"
                            >
                                Windows Installation
                            </button>
                        </li>
                        <li>
                            <button
                                onClick={() => scrollToSection(macosRef)}
                                className="w-full text-left text-lg font-medium text-gray-300 transition-colors duration-300 hover:text-blue-400"
                            >
                                macOS Installation
                            </button>
                        </li>
                    </ul>
                </aside>

                {/* Main Content */}
                <main className="w-full lg:w-3/4">
                    {/* Header */}
                    <motion.header
                        initial={{ opacity: 0, y: -50 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        className="mb-16 text-center"
                    >
                        <h1 className="text-5xl font-extrabold tracking-tight md:text-6xl">
                            <span className="bg-gradient-to-r from-blue-400 to-teal-300 bg-clip-text text-transparent">
                                Interview Cracker
                            </span>{" "}
                            Documentation
                        </h1>
                        <p className="mx-auto mt-4 max-w-2xl text-lg font-medium leading-7 text-gray-300">
                            Your ultimate guide to mastering coding interviews with ease and confidence.
                        </p>
                    </motion.header>

                    {/* Introduction Section */}
                    <motion.section
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.8, delay: 0.2 }}
                        className="mb-20 text-center"
                    >
                        <h2 className="mb-6 inline-block border-b-2 border-blue-500 pb-3 text-4xl font-semibold">
                            Introduction
                        </h2>
                        <p className="mx-auto mb-8 max-w-3xl text-lg font-medium leading-7 text-gray-200">
                            Welcome to the official <span className="font-bold">Interview Cracker</span>{" "}
                            documentation. This guide is crafted to help you seamlessly navigate the installation
                            process, unlock advanced features, and maximize your performance in coding interviews and
                            technical assessments—whether you’re a beginner or a seasoned developer.
                        </p>
                        <div className="mb-8 transition-transform duration-300 hover:scale-105">
                            <Image
                                src="/documentation/10.png"
                                alt="Overview of the Interview Cracker application interface"
                                width={600}
                                height={400}
                                className="mx-auto w-full max-w-2xl rounded-xl border border-gray-700 object-contain shadow-2xl"
                            />
                        </div>
                        <p className="mx-auto max-w-3xl text-lg font-medium leading-7 text-gray-200">
                            Interview Cracker equips you with powerful tools like a{" "}
                            <span className="font-bold">live chatbot</span>, a{" "}
                            <span className="font-bold">screenshot-based coding solver</span>, and a discreet{" "}
                            <span className="font-bold">coding test assistant</span>, all designed to operate invisibly
                            and give you a competitive edge.
                        </p>
                    </motion.section>

                    {/* Getting Started for Windows Section */}
                    <motion.section
                        ref={windowsRef}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.8, delay: 0.4 }}
                        className="mb-20"
                    >
                        <h2 className="mb-6 border-b-2 border-blue-500 pb-3 text-center text-4xl font-semibold">
                            Getting Started for Windows
                        </h2>
                        <p className="mx-auto mb-12 max-w-3xl text-center text-lg font-medium leading-7 text-gray-200">
                            Follow this step-by-step guide to install and configure Interview Cracker on Windows. Each
                            step includes clear instructions and visuals for a smooth setup experience.
                        </p>

                        {[
                            {
                                step: "Step 1",
                                text: "Download the Interview Cracker application from the website dashboard.",
                                img: "/documentation/1.png",
                                alt: "Downloading Interview Cracker from the website dashboard",
                            },
                            {
                                step: "NOTE",
                                text: "The software may be flagged as a virus by your antivirus, though this is rare. If it happens, it’s a false positive. Disable your antivirus or add an exception to proceed.",
                            },
                            {
                                step: "Step 2",
                                text: "Locate and double-click <span className='font-mono bg-gray-700 px-1 rounded'>interviewCr-setup-win64.exe</span> (or <span className='font-mono bg-gray-700 px-1 rounded'>interviewCr-setup-win64</span>) to start the installation.",
                                img: "/documentation/2.png",
                                alt: "Locating the Interview Cracker setup file",
                            },
                            {
                                step: "Step 3",
                                text: "If prompted by Windows security, click <span className='font-bold'>More info</span>.",
                                img: "/documentation/3.png",
                                alt: "Windows security prompt - More info",
                            },
                            {
                                step: "Step 4",
                                text: "Select <span className='font-bold'>Run anyway</span> to continue the installation.",
                                img: "/documentation/4.png",
                                alt: "Windows security prompt - Run anyway",
                            },
                            {
                                step: "Step 5",
                                text: "Click <span className='font-bold'>Finish</span> once installation is complete. A desktop shortcut will be created automatically.",
                                img: "/documentation/5a.png",
                                alt: "Completing the installation",
                            },
                            {
                                step: "Step 6",
                                text: "Find the Interview Cracker shortcut on your desktop. To get your login key, visit the website, log in, and go to the <span className='font-bold'>Key</span> section.",
                                img: "/documentation/6.png",
                                alt: "Interview Cracker desktop shortcut",
                            },
                            {
                                step: "Step 7",
                                text: "Copy the provided key or generate a new one as needed.",
                                img: "/documentation/7.png",
                                alt: "Retrieving the login key from the dashboard",
                            },
                            {
                                step: "Step 8",
                                text: "Launch the app, paste the key, enter your login details, and click <span className='font-bold'>Login</span>.",
                                img: "/documentation/8.jpg",
                                alt: "Entering login details in Interview Cracker",
                            },
                            {
                                step: "Step 9",
                                text: "Click <span className='font-bold'>Launch Helper</span> to activate core functionalities.",
                                img: "/documentation/9.png",
                                alt: "Launching the Interview Cracker helper",
                            },
                            {
                                step: "Step 10",
                                text: "Explore features like the live chatbot and coding solver to boost your performance.",
                                img: "/documentation/12.jpg",
                                alt: "Exploring Interview Cracker features",
                            },
                            {
                                step: "Step 11",
                                text: "Use built-in shortcuts to optimize your workflow during interviews.",
                                img: "/documentation/11.png",
                                alt: "Keyboard shortcuts in Interview Cracker",
                            },
                        ].map((item, index) => (
                            <motion.div
                                key={index}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5, delay: index * 0.1 }}
                                className="mb-16"
                            >
                                {item.step === "NOTE" ? (
                                    <div className="rounded-xl border border-red-500 bg-red-600/80 p-6 shadow-lg">
                                        <p className="text-center text-lg font-medium leading-7 text-white">
                                            <span className="font-semibold">{item.step}:</span> {item.text}
                                        </p>
                                    </div>
                                ) : (
                                    <div
                                        className={`flex flex-col items-center gap-8 md:flex-row ${index % 2 === 0 ? "md:flex-row" : "md:flex-row-reverse"
                                            }`}
                                    >
                                        <div className="flex-1 text-center">
                                            <p className="text-lg font-medium leading-7 text-gray-100">
                                                <span className="font-semibold">{item.step}:</span>{" "}
                                                <span dangerouslySetInnerHTML={{ __html: item.text }} />
                                            </p>
                                        </div>
                                        {item.img && (
                                            <div className="flex-1 transition-transform duration-300 hover:scale-105">
                                                <Image
                                                    src={item.img}
                                                    alt={item.alt ?? ""}
                                                    width={600}
                                                    height={400}
                                                    className="w-full rounded-xl border border-gray-700 object-contain shadow-2xl"
                                                />
                                            </div>
                                        )}
                                    </div>
                                )}
                            </motion.div>
                        ))}
                    </motion.section>

                    {/* Getting Started for macOS Section */}
                    <motion.section
                        ref={macosRef}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.8, delay: 0.6 }}
                        className="mb-20"
                    >
                        <h2 className="mb-6 border-b-2 border-blue-500 pb-3 text-center text-4xl font-semibold">
                            Getting Started for macOS
                        </h2>
                        <p className="mx-auto mb-12 max-w-3xl text-center text-lg font-medium leading-7 text-gray-200">
                            Follow this step-by-step guide to install and configure Interview Cracker on macOS. Each
                            step includes detailed instructions to ensure a seamless setup process.
                        </p>

                        {[
                            {
                                step: "Step 1",
                                text: "Download the Interview Cracker application (.dmg file) from the website dashboard.",
                            },
                            {
                                step: "Step 2",
                                text: "Open the downloaded <span className='font-mono bg-gray-700 px-1 rounded'>InterviewCracker.dmg</span> file from your Downloads folder.",
                            },
                            {
                                step: "Step 3",
                                text: "Drag the Interview Cracker icon into the Applications folder as prompted.",
                            },
                            {
                                step: "NOTE",
                                text: "If macOS blocks the app due to an 'unidentified developer,' go to <span className='font-bold'>System Preferences > Security & Privacy</span> and click <span className='font-bold'>Open Anyway</span>.",
                            },
                            {
                                step: "Step 4",
                                text: "Launch Interview Cracker from the Applications folder or Spotlight search.",
                            },
                            {
                                step: "Step 5",
                                text: "To get your login key, visit the website, log in, and navigate to the <span className='font-bold'>Key</span> section in the dashboard.",
                            },
                            {
                                step: "Step 6",
                                text: "Enter the key and your login details in the app, then click <span className='font-bold'>Login</span>.",
                            },
                            {
                                step: "Step 7",
                                text: "Click <span className='font-bold'>Launch Helper</span> to activate the core functionalities.",
                            },
                            {
                                step: "Step 8",
                                text: "Explore key features like the live chatbot and coding solver to enhance your performance.",
                            },
                            {
                                step: "Step 9",
                                text: "Utilize built-in shortcuts to streamline your workflow during interviews.",
                            },
                        ].map((item, index) => (
                            <motion.div
                                key={index}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5, delay: index * 0.1 }}
                                className="mb-16"
                            >
                                {item.step === "NOTE" ? (
                                    <div className="rounded-xl border border-red-500 bg-red-600/80 p-6 shadow-lg">
                                        <p className="text-center text-lg font-medium leading-7 text-white">
                                            <span className="font-semibold">{item.step}:</span>{" "}
                                            <span dangerouslySetInnerHTML={{ __html: item.text }} />
                                        </p>
                                    </div>
                                ) : (
                                    <div className="text-center">
                                        <p className="text-lg font-medium leading-7 text-gray-100">
                                            <span className="font-semibold">{item.step}:</span>{" "}
                                            <span dangerouslySetInnerHTML={{ __html: item.text }} />
                                        </p>
                                    </div>
                                )}
                            </motion.div>
                        ))}
                    </motion.section>

                    {/* Key Features Section */}
                    <motion.section
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.8, delay: 0.8 }}
                        className="mb-20"
                    >
                        <h2 className="mb-8 border-b-2 border-blue-500 pb-3 text-center text-4xl font-semibold">
                            Key Features
                        </h2>
                        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                            {[
                                {
                                    title: "Live Chatbot",
                                    desc: "Receive real-time assistance during interviews with our intelligent chatbot.",
                                },
                                {
                                    title: "Coding Interview Solver",
                                    desc: "Instantly solve coding challenges by uploading problem screenshots.",
                                },
                                {
                                    title: "Invisible Assistance",
                                    desc: "Operate discreetly with tools that remain undetectable to interviewers.",
                                },
                                {
                                    title: "Custom Shortcuts",
                                    desc: "Streamline your workflow with personalized keyboard shortcuts.",
                                },
                            ].map((feature, index) => (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, scale: 0.95 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{ duration: 0.5, delay: index * 0.1 }}
                                    className="rounded-xl border border-gray-700 bg-gray-800 p-6 shadow-lg transition-shadow duration-300 hover:bg-gray-700/80 hover:shadow-xl"
                                >
                                    <h3 className="mb-3 text-xl font-semibold text-blue-400">
                                        {feature.title}
                                    </h3>
                                    <p className="font-medium leading-6 text-gray-300">{feature.desc}</p>
                                </motion.div>
                            ))}
                        </div>
                    </motion.section>
                </main>
            </div>

            {/* Optional: Background Decoration */}
            <div className="absolute inset-0 -z-10 overflow-hidden">
                <div className="absolute left-0 top-0 size-96 rounded-full bg-blue-500/10 blur-3xl"></div>
                <div className="absolute bottom-0 right-0 size-96 rounded-full bg-teal-500/10 blur-3xl"></div>
            </div>
        </div>
    );
}