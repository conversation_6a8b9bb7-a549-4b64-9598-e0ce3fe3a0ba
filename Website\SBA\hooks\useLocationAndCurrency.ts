// app/hooks/useLocationAndCurrency.ts
"use client";

import { useState, useEffect } from "react";

const useLocationAndCurrency = () => {
    const [locationData, setLocationData] = useState({
        currency: "USD",
        symbol: "$",
        exchangeRate: 1,
        isIndia: false,
        dataReady: false
    });

    useEffect(() => {
        const fetchLocationData = async () => {
            try {
                // Use the API for both development and production environments
                const response = await fetch('/api/get-region', {
                    cache: 'no-store'
                });

                if (response.ok) {
                    const data = await response.json();
                    const isIndia = data.countryCode === "IN";

                    setLocationData({
                        currency: isIndia ? "INR" : "USD",
                        symbol: isIndia ? "₹" : "$",
                        exchangeRate: isIndia ? 85.26 : 1, // Using fixed exchange rate of 85.26 INR to 1 USD
                        isIndia: isIndia,
                        dataReady: true
                    });
                } else {
                    // Default to USD if API fails
                    console.error('API call failed');
                    setLocationData({
                        currency: "USD",
                        symbol: "$",
                        exchangeRate: 1,
                        isIndia: false,
                        dataReady: true
                    });
                }
            } catch (error) {
                console.error("Error fetching location data:", error);
                // Default to USD if API fails
                setLocationData({
                    currency: "USD",
                    symbol: "$",
                    exchangeRate: 1,
                    isIndia: false,
                    dataReady: true
                });
            }
        };

        fetchLocationData();
    }, []);

    return locationData;
};

export default useLocationAndCurrency;