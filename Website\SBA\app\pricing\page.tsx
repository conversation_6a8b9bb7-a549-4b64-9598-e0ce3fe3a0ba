"use client";

import { useUser, useClerk } from "@clerk/nextjs";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ead<PERSON>, ModalBody } from "@nextui-org/react";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { toast } from "sonner";

import useLocationAndCurrency from "@/hooks/useLocationAndCurrency";
import { useOSDetection } from "@/hooks/useOSDetection";
import { apiFetch } from "@/lib/api-utils"; // Import our utility

type User = {
    id: string;
    email?: string;
    subscriptionEndDate?: string | Date;
    subscriptionType?: string;
    subscriptionPlan?: string;
    promptsUsed?: number;
    [key: string]: any;
};

export default function PricingPage() {
    const { user, isLoaded } = useUser();
    const { signOut, session } = useClerk();
    const router = useRouter();
    const [isLoading, setIsLoading] = useState(false);
    const os = useOSDetection();
    const { currency, symbol, exchangeRate, isIndia, dataReady } = useLocationAndCurrency();

    const [isPaymentMethodModalOpen, setIsPaymentMethodModalOpen] = useState(false);
    const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
    const [localUser, setLocalUser] = useState<User | null>(null);

    // Different pricing based on OS
    const basePrices = {
        weekly: os === "Windows" ? 7 : 7,
        monthly: os === "Windows" ? 7 : 7,
        yearly: os === "Windows" ? 150 : 150,
    };

    // Calculate prices in local currency
    const displayPrices = {
        weekly: Math.round(basePrices.weekly * exchangeRate),
        monthly: Math.round(basePrices.monthly * exchangeRate),
        yearly: Math.round(basePrices.yearly * exchangeRate),
    };

    // Remove unused loadingRates state

    // Fetch user data from your backend since Clerk doesn't store subscription info
    const fetchUserData = async () => {
        try {
            // Use apiFetch instead of fetch
            const response = await apiFetch("/api/user/me");
            if (response.ok) {
                const userData = await response.json();
                setLocalUser(userData);
            } else {
                toast.error("Failed to fetch user data.");
            }
        } catch (error) {
            console.error("Error fetching user data:", error);
            toast.error("An error occurred while fetching user data.");
        }
    };

    useEffect(() => {
        if (isLoaded && user) {
            fetchUserData();
        }
    }, [isLoaded, user]);

    useEffect(() => {
        const loadRazorpayScript = () => {
            const script = document.createElement("script");
            script.src = "https://checkout.razorpay.com/v1/checkout.js";
            script.async = true;
            document.body.appendChild(script);
        };
        loadRazorpayScript();
    }, []);

    const isSubscribed = localUser?.subscriptionType === "premium";
    const currentPlan = localUser?.subscriptionPlan;
    const isPremiumActive =
        isSubscribed &&
        localUser?.subscriptionEndDate &&
        new Date(localUser.subscriptionEndDate) > new Date();

    const openPaymentMethodModal = (plan: string) => {
        if (!user) {
            router.push("/login");
            return;
        }
        if (isPremiumActive && plan !== "free") {
            toast.error(
                "You have an active premium subscription. Please cancel it first or wait for it to expire."
            );
            return;
        }
        setSelectedPlan(plan);
        setIsPaymentMethodModalOpen(true);
    };

    const handleSubscribe = async (plan: string, paymentMethod?: string) => {
        if (!user) {
            router.push("/login");
            return;
        }

        if (isPremiumActive && plan !== "free") {
            toast.error(
                "You have an active premium subscription. Please cancel it first or wait for it to expire."
            );
            return;
        }

        if (plan === "free") {
            try {
                setIsLoading(true);
                const response = await fetch("/api/user/subscribe", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ plan }),
                });

                if (response.ok) {
                    toast.success("Switched to free plan successfully! Logging out to refresh your session...");
                    setTimeout(() => {
                        signOut({
                            redirectUrl: "/login?message=Please log in again to complete your subscription",
                        });
                    }, 1500);
                } else {
                    const errorData = await response.json();
                    toast.error(`Subscription failed: ${errorData.message}`);
                }
            } catch (error) {
                console.error("Subscription error:", error);
                toast.error("An error occurred during subscription.");
            } finally {
                setIsLoading(false);
            }
            return;
        }

        setIsPaymentMethodModalOpen(false);
        setIsLoading(true);

        if (paymentMethod === "razorpay" || !paymentMethod) {
            try {
                const response = await fetch("/api/razorpay/create-order", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({
                        plan,
                        os,
                    }),
                });

                if (response.ok) {
                    const { orderId } = await response.json();
                    const amountInUsd =
                        plan === "weekly" ? basePrices.weekly : plan === "monthly" ? basePrices.monthly : basePrices.yearly;
                    // Use the display price directly since it's already converted to INR
                    const amountInInr = displayPrices[plan as keyof typeof displayPrices];
                    const options = {
                        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID!,
                        amount: amountInInr * 100,
                        currency: "INR",
                        name: "Interview Cracker",
                        description: `Subscription to ${plan} plan`,
                        order_id: orderId,
                        handler: async function (response: any) {
                            const verifyResponse = await fetch("/api/razorpay/verify-payment", {
                                method: "POST",
                                headers: { "Content-Type": "application/json" },
                                body: JSON.stringify({
                                    razorpay_order_id: response.razorpay_order_id,
                                    razorpay_payment_id: response.razorpay_payment_id,
                                    razorpay_signature: response.razorpay_signature,
                                    plan,
                                    os,
                                }),
                            });

                            if (verifyResponse.ok) {
                                toast.success("Payment successful!");

                                // Refresh user session instead of logging out
                                if (session) {
                                    try {
                                        await session.reload();
                                        console.log("Session reloaded successfully after payment");

                                        // Fetch the latest user data
                                        const userResponse = await apiFetch('/api/user/me');
                                        if (userResponse.ok) {
                                            console.log("User data refreshed successfully");
                                            // Redirect to payment success page with parameters
                                            router.push(`/payment-success?method=razorpay&plan=${plan}&amount=${isIndia ? amountInInr : amountInUsd}&currency=${currency}`);
                                        } else {
                                            console.error("Failed to refresh user data");
                                            router.push(`/payment-success?method=razorpay&plan=${plan}&amount=${isIndia ? amountInInr : amountInUsd}&currency=${currency}`);
                                        }
                                    } catch (error) {
                                        console.error("Error refreshing session:", error);
                                        router.push(`/payment-success?method=razorpay&plan=${plan}&amount=${isIndia ? amountInInr : amountInUsd}&currency=${currency}`);
                                    }
                                } else {
                                    router.push(`/payment-success?method=razorpay&plan=${plan}&amount=${isIndia ? amountInInr : amountInUsd}&currency=${currency}`);
                                }
                            } else {
                                toast.error("Payment verification failed");
                            }
                        },
                        prefill: {
                            name: user.fullName || "",
                            email: user.emailAddresses[0]?.emailAddress || "",
                        },
                        theme: {
                            color: "#F37254",
                        },
                    };

                    const rzp = new (window as any).Razorpay(options);
                    rzp.open();
                } else {
                    const errorData = await response.json();
                    toast.error(`Failed to create order: ${errorData.message}`);
                }
            } catch (error) {
                console.error("Subscription error:", error);
                toast.error("An error occurred during subscription.");
            } finally {
                setIsLoading(false);
            }
        } else if (paymentMethod === "paypal") {
            try {
                toast.info("Connecting to PayPal...");
                const response = await fetch("/api/paypal/create-order", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({
                        plan,
                        os,
                    }),
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.approvalLink) {
                        toast.success("Redirecting to PayPal...");
                        window.location.href = data.approvalLink; // Redirect to PayPal approval
                    } else {
                        toast.error("Failed to get PayPal approval link. Please try again or contact support.");
                    }
                } else {
                    try {
                        const errorData = await response.json();
                        const errorMessage = errorData.error || errorData.message || "Unknown error";
                        toast.error(`Failed to create PayPal order: ${errorMessage}`);
                        console.error("PayPal order creation failed:", errorData);
                    } catch (parseError) {
                        toast.error("Failed to create PayPal order. Please try again later.");
                        console.error("Failed to parse error response:", parseError);
                    }
                }
            } catch (error) {
                console.error("PayPal error:", error);
                toast.error("An error occurred during PayPal subscription. Please try again later.");
            } finally {
                setIsLoading(false);
            }
        }
    };

    if (!isLoaded || !dataReady) {
        return <div>Loading...</div>;
    }

    return (
        <div className="min-h-screen bg-black px-4 pt-24 text-white">
            <div className="mx-auto max-w-7xl">
                <h1 className="mb-4 text-center text-4xl font-bold text-yellow-400">Choose Your Plan</h1>
                <p className="mb-2 text-center text-gray-400">
                    Subscribe to InterviewCracker
                    <span className="ml-1 font-semibold">
                        (Prices shown in {currency} {isIndia ? "for Indian users" : "for international users"})
                    </span>
                </p>

                {/* Show OS-specific pricing message */}
                <p className="mb-4 text-center text-gray-300">
                    {os === "Unknown" ? "Loading pricing..." : `Showing special pricing for ${os} users`}
                </p>



                {isPremiumActive && (
                    <div className="mb-8 text-center">
                        <p className="mb-1 text-green-400">
                            You have an active {currentPlan} subscription until{" "}
                            {localUser?.subscriptionEndDate
                                ? new Date(localUser.subscriptionEndDate).toLocaleDateString()
                                : ""}
                        </p>
                        <p className="text-sm text-gray-300">
                            To cancel your subscription, please go to your Account Settings
                        </p>
                        <p className="text-sm text-gray-300">To Change The Time Period Cancel The Current Plan</p>
                    </div>
                )}

                <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
                    <div className="flex h-full flex-col justify-between rounded-lg border-2 border-blue-500 bg-black p-6 text-center">
                        <div>
                            <h2 className="mb-4 text-2xl font-semibold text-white">Free</h2>
                            <p className="mb-4 text-gray-400">Billed forever</p>
                            <p className="mb-6 text-3xl font-bold text-white">{symbol}0/month</p>
                            <ul className="mb-6 space-y-2 text-gray-300">
                                <li>Basic code generation</li>
                                <li>5 prompts for lifetime</li>
                                <li>Community support</li>
                                <li>Access: Unlimited</li>
                            </ul>
                        </div>
                        <Button
                            color="primary"
                            variant="shadow"
                            radius="full"
                            onClick={() => handleSubscribe("free")}
                            disabled={isPremiumActive || isLoading}
                            isLoading={isLoading}
                            className="mt-auto w-full bg-gray-700 text-white"
                        >
                            {!isSubscribed ? "Current Plan" : "You Are Subscribed"}
                        </Button>
                    </div>
                    {/* Weekly Plan - Commented Out
                    <div className="relative flex h-full flex-col justify-between overflow-hidden rounded-lg border-2 border-blue-500 bg-black p-6 text-center">
                        <div className="absolute -right-10 top-5 z-10 rotate-45 bg-yellow-500 px-12 py-1 text-sm font-bold text-black shadow-lg">
                            NEW
                        </div>
                        <div>
                            <h2 className="mb-4 text-2xl font-semibold text-white">Weekly Premium</h2>
                            <p className="mb-4 text-gray-400">Billed weekly</p>
                            <p className="mb-6 text-3xl font-bold text-white">
                                {symbol}{displayPrices.weekly}/week
                            </p>
                            <ul className="mb-6 space-y-2 text-gray-300">
                                <li>Advance Code Generation</li>
                                <li>250 prompts per week</li>
                                <li>Priority support</li>
                            </ul>
                            <div className="mb-4">
                                <p className="text-sm text-gray-400">
                                    Subscription: {symbol}{displayPrices.weekly}/week
                                </p>
                                <p className="text-sm text-gray-400">Subtotal: {symbol}{displayPrices.weekly}</p>
                                <p className="text-sm text-gray-400">Tax: {symbol}0.00</p>
                                <p className="text-sm font-bold text-white">
                                    Total due today: {symbol}{displayPrices.weekly}
                                </p>
                            </div>
                        </div>
                        <Button
                            color="warning"
                            variant="shadow"
                            radius="full"
                            onClick={() => openPaymentMethodModal("weekly")}
                            disabled={isPremiumActive || isLoading}
                            isLoading={isLoading}
                            className="mt-auto w-full bg-yellow-400 text-black"
                        >
                            {isPremiumActive && currentPlan === "weekly" ? "Current Plan" : "Subscribe"}
                        </Button>
                    </div>
                    */}

                    <div className="flex h-full flex-col justify-between rounded-lg border-2 border-blue-500 bg-black p-6 text-center">
                        <div>
                            <h2 className="mb-4 text-2xl font-semibold text-white">
                                Monthly Premium
                            </h2>
                            <p className="mb-4 text-gray-400">Billed monthly</p>
                            <p className="mb-6 text-3xl font-bold text-white">
                                {symbol}{displayPrices.monthly}/month
                            </p>
                            <ul className="mb-6 space-y-2 text-gray-300">
                                <li>Advance Code Generation</li>
                                <li>250 prompts per week</li>
                                <li>Priority support</li>
                            </ul>
                            <div className="mb-4">
                                <p className="text-sm text-gray-400">
                                    Subscription: {symbol}{displayPrices.monthly}/month
                                </p>
                                <p className="text-sm text-gray-400">Subtotal: {symbol}{displayPrices.monthly}</p>
                                <p className="text-sm text-gray-400">Tax: {symbol}0.00</p>
                                <p className="text-sm font-bold text-white">
                                    Total due today: {symbol}{displayPrices.monthly}
                                </p>
                            </div>
                        </div>
                        <Button
                            color="warning"
                            variant="shadow"
                            radius="full"
                            onClick={() => openPaymentMethodModal("monthly")}
                            disabled={isPremiumActive || isLoading}
                            isLoading={isLoading}
                            className="mt-auto w-full bg-yellow-400 text-black"
                        >
                            {isPremiumActive && currentPlan === "monthly" ? "Current Plan" : "Subscribe"}
                        </Button>
                    </div>

                    <div className="flex h-full flex-col justify-between rounded-lg border-2 border-blue-500 bg-black p-6 text-center">
                        <div>
                            <h2 className="mb-4 text-2xl font-semibold text-white">
                                Yearly Premium{" "}
                                <span className="text-yellow-400">
                                    Save ~{symbol}{Math.round(displayPrices.monthly * 12 - displayPrices.yearly)}
                                </span>
                            </h2>
                            <p className="mb-4 text-gray-400">Billed yearly</p>
                            <p className="mb-6 text-3xl font-bold text-white">
                                {symbol}{displayPrices.yearly}/year
                            </p>
                            <ul className="mb-6 space-y-2 text-gray-300">
                                <li>Advance Code Generation</li>
                                <li>250 prompts per week</li>
                                <li>Priority support</li>
                            </ul>
                            <div className="mb-4">
                                <p className="text-sm text-gray-400">
                                    Subscription: {symbol}{displayPrices.yearly}/year
                                </p>
                                <p className="text-sm text-gray-400">Subtotal: {symbol}{displayPrices.yearly}</p>
                                <p className="text-sm text-gray-400">Tax: {symbol}0.00</p>
                                <p className="text-sm font-bold text-white">
                                    Total due today: {symbol}{displayPrices.yearly}
                                </p>
                            </div>
                        </div>
                        <Button
                            color="warning"
                            variant="shadow"
                            radius="full"
                            onClick={() => openPaymentMethodModal("yearly")}
                            disabled={isPremiumActive || isLoading}
                            isLoading={isLoading}
                            className="mt-auto w-full bg-yellow-400 text-black"
                        >
                            {isPremiumActive && currentPlan === "yearly" ? "Current Plan" : "Subscribe"}
                        </Button>
                    </div>
                </div>

                <Modal
                    isOpen={isPaymentMethodModalOpen}
                    onClose={() => setIsPaymentMethodModalOpen(false)}
                    backdrop="blur"
                >
                    <ModalContent>
                        <ModalHeader>Choose Payment Method</ModalHeader>
                        <ModalBody>
                            <Button onClick={() => handleSubscribe(selectedPlan!, "razorpay")} className="mb-2 w-full">
                                Pay with <b>Razorpay (INR)</b> {isIndia && <span className="ml-1 text-xs">(Recommended for you)</span>}
                            </Button>
                            <Button onClick={() => handleSubscribe(selectedPlan!, "paypal")} className="mb-2 w-full">
                                Pay with <b>PayPal (USD)</b> {!isIndia && <span className="ml-1 text-xs">(Recommended for you)</span>}
                            </Button>
                            <p className="mt-2 text-center text-xs text-gray-500">
                                {isIndia ?
                                    "You're seeing INR pricing as you're located in India." :
                                    "You're seeing USD pricing based on your location."}
                            </p>
                        </ModalBody>
                    </ModalContent>
                </Modal>
            </div>
        </div>
    );
}