import { getAuth } from "@clerk/nextjs/server";
import paypal from "@paypal/checkout-server-sdk";
import { NextApiRequest, NextApiResponse } from "next";

import { sendSubscriptionConfirmation } from "@/lib/email";
import connectDB from "@/lib/mongodb";
import { Payment } from "@/model/Payment";
import { User } from "@/model/User";


// Validate PayPal credentials
const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID;
const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET;

if (!PAYPAL_CLIENT_ID || !PAYPAL_CLIENT_SECRET) {
  console.error('PayPal credentials are missing. Check your environment variables.');
}

// Log the first few characters of credentials for debugging (don't log full credentials)
console.log('PayPal Client ID (first 5 chars):', PAYPAL_CLIENT_ID ? PAYPAL_CLIENT_ID.substring(0, 5) + '...' : 'undefined');
console.log('PayPal Client Secret (first 5 chars):', PAYPAL_CLIENT_SECRET ? PAYPAL_CLIENT_SECRET.substring(0, 5) + '...' : 'undefined');

// Use sandbox environment for development, live for production
const isDev = process.env.NODE_ENV === 'development';
console.log('PayPal Environment:', isDev ? 'Sandbox' : 'Live');

let environment: paypal.core.SandboxEnvironment | paypal.core.LiveEnvironment;
let client: paypal.core.PayPalHttpClient | null = null;

try {
  if (isDev) {
    environment = new paypal.core.SandboxEnvironment(PAYPAL_CLIENT_ID!, PAYPAL_CLIENT_SECRET!);
  } else {
    environment = new paypal.core.LiveEnvironment(PAYPAL_CLIENT_ID!, PAYPAL_CLIENT_SECRET!);
  }
  client = new paypal.core.PayPalHttpClient(environment);
} catch (error) {
  console.error('Error initializing PayPal client:', error);
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== "POST") {
        return res.status(405).json({ message: "Method not allowed" });
    }

    // Check if PayPal client is initialized
    if (!client) {
        console.error('PayPal client is not initialized');
        return res.status(500).json({
            success: false,
            message: 'PayPal integration is not configured properly. Please contact support.'
        });
    }

    // Use getAuth for API routes
    const auth = getAuth(req);
    const userId = auth.userId;

    if (!userId) {
        return res.status(401).json({ message: "Unauthorized" });
    }

    const { orderId, plan } = req.body;
    if (!orderId || !plan || !["monthly", "yearly"].includes(plan)) {
        return res.status(400).json({ message: "Invalid or missing orderId or plan" });
    }

    try {
        await connectDB();

        const request = new paypal.orders.OrdersCaptureRequest(orderId);
        request.requestBody({});
        const response = await client.execute(request);
        const order = response.result;

        if (order.status !== "COMPLETED") {
            console.error("Order not completed:", order);
            return res.status(400).json({ message: "Payment not completed" });
        }

        const capture = order.purchase_units[0].payments.captures[0];
        const value = parseFloat(capture.amount.value);

        // Define pricing for plans - MUST match the pricing in create-order.ts and capture-order.ts
        const pricing = {
            weekly: 7,
            monthly: 7,
            yearly: 150
        };

        const expectedValue = pricing[plan as keyof typeof pricing];
        if (value !== expectedValue) {
            console.error("Amount mismatch:", value, "expected:", expectedValue);
            return res.status(400).json({ message: "Amount mismatch" });
        }

        const newPayment = new Payment({
            user: userId,
            paymentId: capture.id,
            orderId: order.id,
            amount: value * 100,
            currency: capture.amount.currency_code,
            status: order.status,
            method: "paypal",
            plan,
        });
        await newPayment.save();

        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({ message: "User not found" });
        }

        const now = new Date();
        const endDate = plan === "monthly" ? new Date(now.setMonth(now.getMonth() + 1)) :
                        new Date(now.setFullYear(now.getFullYear() + 1));
        user.subscriptionType = "premium";
        user.subscriptionPlan = plan;
        user.subscriptionEndDate = endDate;
        user.promptsUsed = 0;
        await user.save();

        await sendSubscriptionConfirmation(
            user.email,
            user.name || "Valued Customer",
            plan,
            value * 100,
            capture.amount.currency_code,
            endDate
        );

        return res.status(200).json({ success: true });
    } catch (error) {
        console.error("PayPal verify payment error:", error);
        // Provide more detailed error message
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return res.status(500).json({
            success: false,
            message: "Failed to verify payment",
            error: errorMessage
        });
    }
}