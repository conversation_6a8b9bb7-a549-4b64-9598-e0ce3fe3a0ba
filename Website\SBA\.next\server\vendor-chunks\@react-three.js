"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-three";
exports.ids = ["vendor-chunks/@react-three"];
exports.modules = {

/***/ "(ssr)/./node_modules/@react-three/fiber/dist/events-776716bd.esm.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@react-three/fiber/dist/events-776716bd.esm.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (/* binding */ useInstanceHandle),\n/* harmony export */   B: () => (/* binding */ Block),\n/* harmony export */   C: () => (/* binding */ useStore),\n/* harmony export */   D: () => (/* binding */ useThree),\n/* harmony export */   E: () => (/* binding */ ErrorBoundary),\n/* harmony export */   F: () => (/* binding */ useFrame),\n/* harmony export */   G: () => (/* binding */ useGraph),\n/* harmony export */   H: () => (/* binding */ useLoader),\n/* harmony export */   a: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   b: () => (/* binding */ createRoot),\n/* harmony export */   c: () => (/* binding */ createPointerEvents),\n/* harmony export */   d: () => (/* binding */ unmountComponentAtNode),\n/* harmony export */   e: () => (/* binding */ extend),\n/* harmony export */   f: () => (/* binding */ createEvents),\n/* harmony export */   g: () => (/* binding */ context),\n/* harmony export */   h: () => (/* binding */ createPortal),\n/* harmony export */   i: () => (/* binding */ isRef),\n/* harmony export */   j: () => (/* binding */ reconciler),\n/* harmony export */   k: () => (/* binding */ applyProps),\n/* harmony export */   l: () => (/* binding */ dispose),\n/* harmony export */   m: () => (/* binding */ invalidate),\n/* harmony export */   n: () => (/* binding */ advance),\n/* harmony export */   o: () => (/* binding */ addEffect),\n/* harmony export */   p: () => (/* binding */ addAfterEffect),\n/* harmony export */   q: () => (/* binding */ addTail),\n/* harmony export */   r: () => (/* binding */ render),\n/* harmony export */   s: () => (/* binding */ flushGlobalEffects),\n/* harmony export */   t: () => (/* binding */ threeTypes),\n/* harmony export */   u: () => (/* binding */ useMutableCallback),\n/* harmony export */   v: () => (/* binding */ flushSync),\n/* harmony export */   w: () => (/* binding */ getRootState),\n/* harmony export */   x: () => (/* binding */ act),\n/* harmony export */   y: () => (/* binding */ buildGraph),\n/* harmony export */   z: () => (/* binding */ roots)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-reconciler/constants */ \"(ssr)/./node_modules/react-reconciler/constants.js\");\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.js\");\n/* harmony import */ var suspend_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! suspend-react */ \"(ssr)/./node_modules/suspend-react/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-reconciler */ \"(ssr)/./node_modules/react-reconciler/index.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_reconciler__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var scheduler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! scheduler */ \"(ssr)/./node_modules/scheduler/index.js\");\n\n\n\n\n\n\n\n\n\nvar threeTypes = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\nconst catalogue = {};\nconst extend = objects => void Object.assign(catalogue, objects);\nfunction createRenderer(_roots, _getEventPriority) {\n  function createInstance(type, {\n    args = [],\n    attach,\n    ...props\n  }, root) {\n    let name = `${type[0].toUpperCase()}${type.slice(1)}`;\n    let instance;\n    if (type === 'primitive') {\n      if (props.object === undefined) throw new Error(\"R3F: Primitives without 'object' are invalid!\");\n      const object = props.object;\n      instance = prepare(object, {\n        type,\n        root,\n        attach,\n        primitive: true\n      });\n    } else {\n      const target = catalogue[name];\n      if (!target) {\n        throw new Error(`R3F: ${name} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);\n      }\n\n      // Throw if an object or literal was passed for args\n      if (!Array.isArray(args)) throw new Error('R3F: The args prop must be an array!');\n\n      // Instanciate new object, link it to the root\n      // Append memoized props with args so it's not forgotten\n      instance = prepare(new target(...args), {\n        type,\n        root,\n        attach,\n        // Save args in case we need to reconstruct later for HMR\n        memoizedProps: {\n          args\n        }\n      });\n    }\n\n    // Auto-attach geometries and materials\n    if (instance.__r3f.attach === undefined) {\n      if (instance.isBufferGeometry) instance.__r3f.attach = 'geometry';else if (instance.isMaterial) instance.__r3f.attach = 'material';\n    }\n\n    // It should NOT call onUpdate on object instanciation, because it hasn't been added to the\n    // view yet. If the callback relies on references for instance, they won't be ready yet, this is\n    // why it passes \"true\" here\n    // There is no reason to apply props to injects\n    if (name !== 'inject') applyProps$1(instance, props);\n    return instance;\n  }\n  function appendChild(parentInstance, child) {\n    let added = false;\n    if (child) {\n      var _child$__r3f, _parentInstance$__r3f;\n      // The attach attribute implies that the object attaches itself on the parent\n      if ((_child$__r3f = child.__r3f) != null && _child$__r3f.attach) {\n        attach(parentInstance, child, child.__r3f.attach);\n      } else if (child.isObject3D && parentInstance.isObject3D) {\n        // add in the usual parent-child way\n        parentInstance.add(child);\n        added = true;\n      }\n      // This is for anything that used attach, and for non-Object3Ds that don't get attached to props;\n      // that is, anything that's a child in React but not a child in the scenegraph.\n      if (!added) (_parentInstance$__r3f = parentInstance.__r3f) == null ? void 0 : _parentInstance$__r3f.objects.push(child);\n      if (!child.__r3f) prepare(child, {});\n      child.__r3f.parent = parentInstance;\n      updateInstance(child);\n      invalidateInstance(child);\n    }\n  }\n  function insertBefore(parentInstance, child, beforeChild) {\n    let added = false;\n    if (child) {\n      var _child$__r3f2, _parentInstance$__r3f2;\n      if ((_child$__r3f2 = child.__r3f) != null && _child$__r3f2.attach) {\n        attach(parentInstance, child, child.__r3f.attach);\n      } else if (child.isObject3D && parentInstance.isObject3D) {\n        child.parent = parentInstance;\n        child.dispatchEvent({\n          type: 'added'\n        });\n        parentInstance.dispatchEvent({\n          type: 'childadded',\n          child\n        });\n        const restSiblings = parentInstance.children.filter(sibling => sibling !== child);\n        const index = restSiblings.indexOf(beforeChild);\n        parentInstance.children = [...restSiblings.slice(0, index), child, ...restSiblings.slice(index)];\n        added = true;\n      }\n      if (!added) (_parentInstance$__r3f2 = parentInstance.__r3f) == null ? void 0 : _parentInstance$__r3f2.objects.push(child);\n      if (!child.__r3f) prepare(child, {});\n      child.__r3f.parent = parentInstance;\n      updateInstance(child);\n      invalidateInstance(child);\n    }\n  }\n  function removeRecursive(array, parent, dispose = false) {\n    if (array) [...array].forEach(child => removeChild(parent, child, dispose));\n  }\n  function removeChild(parentInstance, child, dispose) {\n    if (child) {\n      var _parentInstance$__r3f3, _child$__r3f3, _child$__r3f5;\n      // Clear the parent reference\n      if (child.__r3f) child.__r3f.parent = null;\n      // Remove child from the parents objects\n      if ((_parentInstance$__r3f3 = parentInstance.__r3f) != null && _parentInstance$__r3f3.objects) parentInstance.__r3f.objects = parentInstance.__r3f.objects.filter(x => x !== child);\n      // Remove attachment\n      if ((_child$__r3f3 = child.__r3f) != null && _child$__r3f3.attach) {\n        detach(parentInstance, child, child.__r3f.attach);\n      } else if (child.isObject3D && parentInstance.isObject3D) {\n        var _child$__r3f4;\n        parentInstance.remove(child);\n        // @ts-expect-error\n        // Remove interactivity on the initial root\n        if ((_child$__r3f4 = child.__r3f) != null && _child$__r3f4.root) {\n          removeInteractivity(findInitialRoot(child), child);\n        }\n      }\n\n      // Allow objects to bail out of recursive dispose altogether by passing dispose={null}\n      // Never dispose of primitives because their state may be kept outside of React!\n      // In order for an object to be able to dispose it has to have\n      //   - a dispose method,\n      //   - it cannot be a <primitive object={...} />\n      //   - it cannot be a THREE.Scene, because three has broken it's own api\n      //\n      // Since disposal is recursive, we can check the optional dispose arg, which will be undefined\n      // when the reconciler calls it, but then carry our own check recursively\n      const isPrimitive = (_child$__r3f5 = child.__r3f) == null ? void 0 : _child$__r3f5.primitive;\n      const shouldDispose = !isPrimitive && (dispose === undefined ? child.dispose !== null : dispose);\n\n      // Remove nested child objects. Primitives should not have objects and children that are\n      // attached to them declaratively ...\n      if (!isPrimitive) {\n        var _child$__r3f6;\n        removeRecursive((_child$__r3f6 = child.__r3f) == null ? void 0 : _child$__r3f6.objects, child, shouldDispose);\n        removeRecursive(child.children, child, shouldDispose);\n      }\n\n      // Remove references\n      delete child.__r3f;\n\n      // Dispose item whenever the reconciler feels like it\n      if (shouldDispose && child.dispose && child.type !== 'Scene') {\n        const callback = () => {\n          try {\n            child.dispose();\n          } catch (e) {\n            /* ... */\n          }\n        };\n\n        // Schedule async at runtime, flush sync in testing\n        if (typeof IS_REACT_ACT_ENVIRONMENT === 'undefined') {\n          (0,scheduler__WEBPACK_IMPORTED_MODULE_4__.unstable_scheduleCallback)(scheduler__WEBPACK_IMPORTED_MODULE_4__.unstable_IdlePriority, callback);\n        } else {\n          callback();\n        }\n      }\n      invalidateInstance(parentInstance);\n    }\n  }\n  function switchInstance(instance, type, newProps, fiber) {\n    var _instance$__r3f;\n    const parent = (_instance$__r3f = instance.__r3f) == null ? void 0 : _instance$__r3f.parent;\n    if (!parent) return;\n    const newInstance = createInstance(type, newProps, instance.__r3f.root);\n\n    // https://github.com/pmndrs/react-three-fiber/issues/1348\n    // When args change the instance has to be re-constructed, which then\n    // forces r3f to re-parent the children and non-scene objects\n    if (instance.children) {\n      for (const child of instance.children) {\n        if (child.__r3f) appendChild(newInstance, child);\n      }\n      instance.children = instance.children.filter(child => !child.__r3f);\n    }\n    instance.__r3f.objects.forEach(child => appendChild(newInstance, child));\n    instance.__r3f.objects = [];\n    if (!instance.__r3f.autoRemovedBeforeAppend) {\n      removeChild(parent, instance);\n    }\n    if (newInstance.parent) {\n      newInstance.__r3f.autoRemovedBeforeAppend = true;\n    }\n    appendChild(parent, newInstance);\n\n    // Re-bind event handlers on the initial root\n    if (newInstance.raycast && newInstance.__r3f.eventCount) {\n      const rootState = findInitialRoot(newInstance).getState();\n      rootState.internal.interaction.push(newInstance);\n    }\n    [fiber, fiber.alternate].forEach(fiber => {\n      if (fiber !== null) {\n        fiber.stateNode = newInstance;\n        if (fiber.ref) {\n          if (typeof fiber.ref === 'function') fiber.ref(newInstance);else fiber.ref.current = newInstance;\n        }\n      }\n    });\n  }\n\n  // Don't handle text instances, make it no-op\n  const handleTextInstance = () => {};\n  const reconciler = react_reconciler__WEBPACK_IMPORTED_MODULE_3___default()({\n    createInstance,\n    removeChild,\n    appendChild,\n    appendInitialChild: appendChild,\n    insertBefore,\n    supportsMutation: true,\n    isPrimaryRenderer: false,\n    supportsPersistence: false,\n    supportsHydration: false,\n    noTimeout: -1,\n    appendChildToContainer: (container, child) => {\n      if (!child) return;\n\n      // Don't append to unmounted container\n      const scene = container.getState().scene;\n      if (!scene.__r3f) return;\n\n      // Link current root to the default scene\n      scene.__r3f.root = container;\n      appendChild(scene, child);\n    },\n    removeChildFromContainer: (container, child) => {\n      if (!child) return;\n      removeChild(container.getState().scene, child);\n    },\n    insertInContainerBefore: (container, child, beforeChild) => {\n      if (!child || !beforeChild) return;\n\n      // Don't append to unmounted container\n      const scene = container.getState().scene;\n      if (!scene.__r3f) return;\n      insertBefore(scene, child, beforeChild);\n    },\n    getRootHostContext: () => null,\n    getChildHostContext: parentHostContext => parentHostContext,\n    finalizeInitialChildren(instance) {\n      var _instance$__r3f2;\n      const localState = (_instance$__r3f2 = instance == null ? void 0 : instance.__r3f) != null ? _instance$__r3f2 : {};\n      // https://github.com/facebook/react/issues/20271\n      // Returning true will trigger commitMount\n      return Boolean(localState.handlers);\n    },\n    prepareUpdate(instance, _type, oldProps, newProps) {\n      var _instance$__r3f3;\n      const localState = (_instance$__r3f3 = instance == null ? void 0 : instance.__r3f) != null ? _instance$__r3f3 : {};\n\n      // Create diff-sets\n      if (localState.primitive && newProps.object && newProps.object !== instance) {\n        return [true];\n      } else {\n        // This is a data object, let's extract critical information about it\n        const {\n          args: argsNew = [],\n          children: cN,\n          ...restNew\n        } = newProps;\n        const {\n          args: argsOld = [],\n          children: cO,\n          ...restOld\n        } = oldProps;\n\n        // Throw if an object or literal was passed for args\n        if (!Array.isArray(argsNew)) throw new Error('R3F: the args prop must be an array!');\n\n        // If it has new props or arguments, then it needs to be re-instantiated\n        if (argsNew.some((value, index) => value !== argsOld[index])) return [true];\n        // Create a diff-set, flag if there are any changes\n        const diff = diffProps(instance, restNew, restOld, true);\n        if (diff.changes.length) return [false, diff];\n\n        // Otherwise do not touch the instance\n        return null;\n      }\n    },\n    commitUpdate(instance, [reconstruct, diff], type, _oldProps, newProps, fiber) {\n      // Reconstruct when args or <primitive object={...} have changes\n      if (reconstruct) switchInstance(instance, type, newProps, fiber);\n      // Otherwise just overwrite props\n      else applyProps$1(instance, diff);\n    },\n    commitMount(instance, _type, _props, _int) {\n      var _instance$__r3f4;\n      // https://github.com/facebook/react/issues/20271\n      // This will make sure events are only added once to the central container on the initial root\n      const localState = (_instance$__r3f4 = instance.__r3f) != null ? _instance$__r3f4 : {};\n      if (instance.raycast && localState.handlers && localState.eventCount) {\n        findInitialRoot(instance).getState().internal.interaction.push(instance);\n      }\n    },\n    getPublicInstance: instance => instance,\n    prepareForCommit: () => null,\n    preparePortalMount: container => prepare(container.getState().scene),\n    resetAfterCommit: () => {},\n    shouldSetTextContent: () => false,\n    clearContainer: () => false,\n    hideInstance(instance) {\n      var _instance$__r3f5;\n      // Detach while the instance is hidden\n      const {\n        attach: type,\n        parent\n      } = (_instance$__r3f5 = instance.__r3f) != null ? _instance$__r3f5 : {};\n      if (type && parent) detach(parent, instance, type);\n      if (instance.isObject3D) instance.visible = false;\n      invalidateInstance(instance);\n    },\n    unhideInstance(instance, props) {\n      var _instance$__r3f6;\n      // Re-attach when the instance is unhidden\n      const {\n        attach: type,\n        parent\n      } = (_instance$__r3f6 = instance.__r3f) != null ? _instance$__r3f6 : {};\n      if (type && parent) attach(parent, instance, type);\n      if (instance.isObject3D && props.visible == null || props.visible) instance.visible = true;\n      invalidateInstance(instance);\n    },\n    createTextInstance: handleTextInstance,\n    hideTextInstance: handleTextInstance,\n    unhideTextInstance: handleTextInstance,\n    // https://github.com/pmndrs/react-three-fiber/pull/2360#discussion_r916356874\n    // @ts-expect-error\n    getCurrentEventPriority: () => _getEventPriority ? _getEventPriority() : react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DefaultEventPriority,\n    beforeActiveInstanceBlur: () => {},\n    afterActiveInstanceBlur: () => {},\n    detachDeletedInstance: () => {},\n    now: typeof performance !== 'undefined' && is.fun(performance.now) ? performance.now : is.fun(Date.now) ? Date.now : () => 0,\n    // https://github.com/pmndrs/react-three-fiber/pull/2360#discussion_r920883503\n    scheduleTimeout: is.fun(setTimeout) ? setTimeout : undefined,\n    cancelTimeout: is.fun(clearTimeout) ? clearTimeout : undefined\n  });\n  return {\n    reconciler,\n    applyProps: applyProps$1\n  };\n}\n\nvar _window$document, _window$navigator;\n/**\r\n * Returns `true` with correct TS type inference if an object has a configurable color space (since r152).\r\n */\nconst hasColorSpace = object => 'colorSpace' in object || 'outputColorSpace' in object;\n/**\r\n * The current THREE.ColorManagement instance, if present.\r\n */\nconst getColorManagement = () => {\n  var _ColorManagement;\n  return (_ColorManagement = catalogue.ColorManagement) != null ? _ColorManagement : null;\n};\nconst isOrthographicCamera = def => def && def.isOrthographicCamera;\nconst isRef = obj => obj && obj.hasOwnProperty('current');\n\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * React currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' && ((_window$document = window.document) != null && _window$document.createElement || ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.product) === 'ReactNative') ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\nfunction useMutableCallback(fn) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fn);\n  useIsomorphicLayoutEffect(() => void (ref.current = fn), [fn]);\n  return ref;\n}\nfunction Block({\n  set\n}) {\n  useIsomorphicLayoutEffect(() => {\n    set(new Promise(() => null));\n    return () => set(false);\n  }, [set]);\n  return null;\n}\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      error: false\n    };\n  }\n  componentDidCatch(err) {\n    this.props.set(err);\n  }\n  render() {\n    return this.state.error ? null : this.props.children;\n  }\n}\nErrorBoundary.getDerivedStateFromError = () => ({\n  error: true\n});\nconst DEFAULT = '__default';\nconst DEFAULTS = new Map();\nconst isDiffSet = def => def && !!def.memoized && !!def.changes;\nfunction calculateDpr(dpr) {\n  var _window$devicePixelRa;\n  // Err on the side of progress by assuming 2x dpr if we can't detect it\n  // This will happen in workers where window is defined but dpr isn't.\n  const target = typeof window !== 'undefined' ? (_window$devicePixelRa = window.devicePixelRatio) != null ? _window$devicePixelRa : 2 : 1;\n  return Array.isArray(dpr) ? Math.min(Math.max(dpr[0], target), dpr[1]) : dpr;\n}\n\n/**\r\n * Returns instance root state\r\n */\nconst getRootState = obj => {\n  var _r3f;\n  return (_r3f = obj.__r3f) == null ? void 0 : _r3f.root.getState();\n};\n\n/**\r\n * Returns the instances initial (outmost) root\r\n */\nfunction findInitialRoot(child) {\n  let root = child.__r3f.root;\n  while (root.getState().previousRoot) root = root.getState().previousRoot;\n  return root;\n}\n// A collection of compare functions\nconst is = {\n  obj: a => a === Object(a) && !is.arr(a) && typeof a !== 'function',\n  fun: a => typeof a === 'function',\n  str: a => typeof a === 'string',\n  num: a => typeof a === 'number',\n  boo: a => typeof a === 'boolean',\n  und: a => a === void 0,\n  arr: a => Array.isArray(a),\n  equ(a, b, {\n    arrays = 'shallow',\n    objects = 'reference',\n    strict = true\n  } = {}) {\n    // Wrong type or one of the two undefined, doesn't match\n    if (typeof a !== typeof b || !!a !== !!b) return false;\n    // Atomic, just compare a against b\n    if (is.str(a) || is.num(a) || is.boo(a)) return a === b;\n    const isObj = is.obj(a);\n    if (isObj && objects === 'reference') return a === b;\n    const isArr = is.arr(a);\n    if (isArr && arrays === 'reference') return a === b;\n    // Array or Object, shallow compare first to see if it's a match\n    if ((isArr || isObj) && a === b) return true;\n    // Last resort, go through keys\n    let i;\n    // Check if a has all the keys of b\n    for (i in a) if (!(i in b)) return false;\n    // Check if values between keys match\n    if (isObj && arrays === 'shallow' && objects === 'shallow') {\n      for (i in strict ? b : a) if (!is.equ(a[i], b[i], {\n        strict,\n        objects: 'reference'\n      })) return false;\n    } else {\n      for (i in strict ? b : a) if (a[i] !== b[i]) return false;\n    }\n    // If i is undefined\n    if (is.und(i)) {\n      // If both arrays are empty we consider them equal\n      if (isArr && a.length === 0 && b.length === 0) return true;\n      // If both objects are empty we consider them equal\n      if (isObj && Object.keys(a).length === 0 && Object.keys(b).length === 0) return true;\n      // Otherwise match them by value\n      if (a !== b) return false;\n    }\n    return true;\n  }\n};\n\n/**\r\n * Collects nodes and materials from a THREE.Object3D.\r\n */\nfunction buildGraph(object) {\n  const data = {\n    nodes: {},\n    materials: {}\n  };\n  if (object) {\n    object.traverse(obj => {\n      if (obj.name) data.nodes[obj.name] = obj;\n      if (obj.material && !data.materials[obj.material.name]) data.materials[obj.material.name] = obj.material;\n    });\n  }\n  return data;\n}\n\n// Disposes an object and all its properties\nfunction dispose(obj) {\n  if (obj.dispose && obj.type !== 'Scene') obj.dispose();\n  for (const p in obj) {\n    p.dispose == null ? void 0 : p.dispose();\n    delete obj[p];\n  }\n}\n\n// Each object in the scene carries a small LocalState descriptor\nfunction prepare(object, state) {\n  const instance = object;\n  instance.__r3f = {\n    type: '',\n    root: null,\n    previousAttach: null,\n    memoizedProps: {},\n    eventCount: 0,\n    handlers: {},\n    objects: [],\n    parent: null,\n    ...state\n  };\n  return object;\n}\nfunction resolve(instance, key) {\n  let target = instance;\n  if (key.includes('-')) {\n    const entries = key.split('-');\n    const last = entries.pop();\n    target = entries.reduce((acc, key) => acc[key], instance);\n    return {\n      target,\n      key: last\n    };\n  } else return {\n    target,\n    key\n  };\n}\n\n// Checks if a dash-cased string ends with an integer\nconst INDEX_REGEX = /-\\d+$/;\nfunction attach(parent, child, type) {\n  if (is.str(type)) {\n    // If attaching into an array (foo-0), create one\n    if (INDEX_REGEX.test(type)) {\n      const root = type.replace(INDEX_REGEX, '');\n      const {\n        target,\n        key\n      } = resolve(parent, root);\n      if (!Array.isArray(target[key])) target[key] = [];\n    }\n    const {\n      target,\n      key\n    } = resolve(parent, type);\n    child.__r3f.previousAttach = target[key];\n    target[key] = child;\n  } else child.__r3f.previousAttach = type(parent, child);\n}\nfunction detach(parent, child, type) {\n  var _child$__r3f, _child$__r3f2;\n  if (is.str(type)) {\n    const {\n      target,\n      key\n    } = resolve(parent, type);\n    const previous = child.__r3f.previousAttach;\n    // When the previous value was undefined, it means the value was never set to begin with\n    if (previous === undefined) delete target[key];\n    // Otherwise set the previous value\n    else target[key] = previous;\n  } else (_child$__r3f = child.__r3f) == null ? void 0 : _child$__r3f.previousAttach == null ? void 0 : _child$__r3f.previousAttach(parent, child);\n  (_child$__r3f2 = child.__r3f) == null ? true : delete _child$__r3f2.previousAttach;\n}\n// This function prepares a set of changes to be applied to the instance\nfunction diffProps(instance, {\n  children: cN,\n  key: kN,\n  ref: rN,\n  ...props\n}, {\n  children: cP,\n  key: kP,\n  ref: rP,\n  ...previous\n} = {}, remove = false) {\n  const localState = instance.__r3f;\n  const entries = Object.entries(props);\n  const changes = [];\n\n  // Catch removed props, prepend them so they can be reset or removed\n  if (remove) {\n    const previousKeys = Object.keys(previous);\n    for (let i = 0; i < previousKeys.length; i++) {\n      if (!props.hasOwnProperty(previousKeys[i])) entries.unshift([previousKeys[i], DEFAULT + 'remove']);\n    }\n  }\n  entries.forEach(([key, value]) => {\n    var _instance$__r3f;\n    // Bail out on primitive object\n    if ((_instance$__r3f = instance.__r3f) != null && _instance$__r3f.primitive && key === 'object') return;\n    // When props match bail out\n    if (is.equ(value, previous[key])) return;\n    // Collect handlers and bail out\n    if (/^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/.test(key)) return changes.push([key, value, true, []]);\n    // Split dashed props\n    let entries = [];\n    if (key.includes('-')) entries = key.split('-');\n    changes.push([key, value, false, entries]);\n\n    // Reset pierced props\n    for (const prop in props) {\n      const value = props[prop];\n      if (prop.startsWith(`${key}-`)) changes.push([prop, value, false, prop.split('-')]);\n    }\n  });\n  const memoized = {\n    ...props\n  };\n  if (localState != null && localState.memoizedProps && localState != null && localState.memoizedProps.args) memoized.args = localState.memoizedProps.args;\n  if (localState != null && localState.memoizedProps && localState != null && localState.memoizedProps.attach) memoized.attach = localState.memoizedProps.attach;\n  return {\n    memoized,\n    changes\n  };\n}\nconst __DEV__ = typeof process !== 'undefined' && \"development\" !== 'production';\n\n// This function applies a set of changes to the instance\nfunction applyProps$1(instance, data) {\n  var _instance$__r3f2;\n  // Filter equals, events and reserved props\n  const localState = instance.__r3f;\n  const root = localState == null ? void 0 : localState.root;\n  const rootState = root == null ? void 0 : root.getState == null ? void 0 : root.getState();\n  const {\n    memoized,\n    changes\n  } = isDiffSet(data) ? data : diffProps(instance, data);\n  const prevHandlers = localState == null ? void 0 : localState.eventCount;\n\n  // Prepare memoized props\n  if (instance.__r3f) instance.__r3f.memoizedProps = memoized;\n  for (let i = 0; i < changes.length; i++) {\n    let [key, value, isEvent, keys] = changes[i];\n\n    // Alias (output)encoding => (output)colorSpace (since r152)\n    // https://github.com/pmndrs/react-three-fiber/pull/2829\n    if (hasColorSpace(instance)) {\n      const sRGBEncoding = 3001;\n      const SRGBColorSpace = 'srgb';\n      const LinearSRGBColorSpace = 'srgb-linear';\n      if (key === 'encoding') {\n        key = 'colorSpace';\n        value = value === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n      } else if (key === 'outputEncoding') {\n        key = 'outputColorSpace';\n        value = value === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n      }\n    }\n    let currentInstance = instance;\n    let targetProp = currentInstance[key];\n\n    // Revolve dashed props\n    if (keys.length) {\n      targetProp = keys.reduce((acc, key) => acc[key], instance);\n      // If the target is atomic, it forces us to switch the root\n      if (!(targetProp && targetProp.set)) {\n        const [name, ...reverseEntries] = keys.reverse();\n        currentInstance = reverseEntries.reverse().reduce((acc, key) => acc[key], instance);\n        key = name;\n      }\n    }\n\n    // https://github.com/mrdoob/three.js/issues/21209\n    // HMR/fast-refresh relies on the ability to cancel out props, but threejs\n    // has no means to do this. Hence we curate a small collection of value-classes\n    // with their respective constructor/set arguments\n    // For removed props, try to set default values, if possible\n    if (value === DEFAULT + 'remove') {\n      if (currentInstance.constructor) {\n        // create a blank slate of the instance and copy the particular parameter.\n        let ctor = DEFAULTS.get(currentInstance.constructor);\n        if (!ctor) {\n          // @ts-expect-error\n          ctor = new currentInstance.constructor();\n          DEFAULTS.set(currentInstance.constructor, ctor);\n        }\n        value = ctor[key];\n      } else {\n        // instance does not have constructor, just set it to 0\n        value = 0;\n      }\n    }\n\n    // Deal with pointer events ...\n    if (isEvent && localState) {\n      if (value) localState.handlers[key] = value;else delete localState.handlers[key];\n      localState.eventCount = Object.keys(localState.handlers).length;\n    }\n    // Special treatment for objects with support for set/copy, and layers\n    else if (targetProp && targetProp.set && (targetProp.copy || targetProp instanceof three__WEBPACK_IMPORTED_MODULE_5__.Layers)) {\n      // If value is an array\n      if (Array.isArray(value)) {\n        if (targetProp.fromArray) targetProp.fromArray(value);else targetProp.set(...value);\n      }\n      // Test again target.copy(class) next ...\n      else if (targetProp.copy && value && value.constructor && (\n      // Some environments may break strict identity checks by duplicating versions of three.js.\n      // Loosen to unminified names, ignoring descendents.\n      // https://github.com/pmndrs/react-three-fiber/issues/2856\n      // TODO: fix upstream and remove in v9\n      __DEV__ ? targetProp.constructor.name === value.constructor.name : targetProp.constructor === value.constructor)) {\n        targetProp.copy(value);\n      }\n      // If nothing else fits, just set the single value, ignore undefined\n      // https://github.com/pmndrs/react-three-fiber/issues/274\n      else if (value !== undefined) {\n        var _targetProp;\n        const isColor = (_targetProp = targetProp) == null ? void 0 : _targetProp.isColor;\n        // Allow setting array scalars\n        if (!isColor && targetProp.setScalar) targetProp.setScalar(value);\n        // Layers have no copy function, we must therefore copy the mask property\n        else if (targetProp instanceof three__WEBPACK_IMPORTED_MODULE_5__.Layers && value instanceof three__WEBPACK_IMPORTED_MODULE_5__.Layers) targetProp.mask = value.mask;\n        // Otherwise just set ...\n        else targetProp.set(value);\n        // For versions of three which don't support THREE.ColorManagement,\n        // Auto-convert sRGB colors\n        // https://github.com/pmndrs/react-three-fiber/issues/344\n        if (!getColorManagement() && rootState && !rootState.linear && isColor) targetProp.convertSRGBToLinear();\n      }\n      // Else, just overwrite the value\n    } else {\n      var _currentInstance$key;\n      currentInstance[key] = value;\n\n      // Auto-convert sRGB textures, for now ...\n      // https://github.com/pmndrs/react-three-fiber/issues/344\n      if ((_currentInstance$key = currentInstance[key]) != null && _currentInstance$key.isTexture &&\n      // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n      currentInstance[key].format === three__WEBPACK_IMPORTED_MODULE_5__.RGBAFormat && currentInstance[key].type === three__WEBPACK_IMPORTED_MODULE_5__.UnsignedByteType && rootState) {\n        const texture = currentInstance[key];\n        if (hasColorSpace(texture) && hasColorSpace(rootState.gl)) texture.colorSpace = rootState.gl.outputColorSpace;else texture.encoding = rootState.gl.outputEncoding;\n      }\n    }\n    invalidateInstance(instance);\n  }\n  if (localState && localState.parent && instance.raycast && prevHandlers !== localState.eventCount) {\n    // Get the initial root state's internals\n    const internal = findInitialRoot(instance).getState().internal;\n    // Pre-emptively remove the instance from the interaction manager\n    const index = internal.interaction.indexOf(instance);\n    if (index > -1) internal.interaction.splice(index, 1);\n    // Add the instance to the interaction manager only when it has handlers\n    if (localState.eventCount) internal.interaction.push(instance);\n  }\n\n  // Call the update lifecycle when it is being updated, but only when it is part of the scene.\n  // Skip updates to the `onUpdate` prop itself\n  const isCircular = changes.length === 1 && changes[0][0] === 'onUpdate';\n  if (!isCircular && changes.length && (_instance$__r3f2 = instance.__r3f) != null && _instance$__r3f2.parent) updateInstance(instance);\n  return instance;\n}\nfunction invalidateInstance(instance) {\n  var _instance$__r3f3, _instance$__r3f3$root;\n  const state = (_instance$__r3f3 = instance.__r3f) == null ? void 0 : (_instance$__r3f3$root = _instance$__r3f3.root) == null ? void 0 : _instance$__r3f3$root.getState == null ? void 0 : _instance$__r3f3$root.getState();\n  if (state && state.internal.frames === 0) state.invalidate();\n}\nfunction updateInstance(instance) {\n  instance.onUpdate == null ? void 0 : instance.onUpdate(instance);\n}\nfunction updateCamera(camera, size) {\n  // https://github.com/pmndrs/react-three-fiber/issues/92\n  // Do not mess with the camera if it belongs to the user\n  if (!camera.manual) {\n    if (isOrthographicCamera(camera)) {\n      camera.left = size.width / -2;\n      camera.right = size.width / 2;\n      camera.top = size.height / 2;\n      camera.bottom = size.height / -2;\n    } else {\n      camera.aspect = size.width / size.height;\n    }\n    camera.updateProjectionMatrix();\n    // https://github.com/pmndrs/react-three-fiber/issues/178\n    // Update matrix world since the renderer is a frame late\n    camera.updateMatrixWorld();\n  }\n}\n\nfunction makeId(event) {\n  return (event.eventObject || event.object).uuid + '/' + event.index + event.instanceId;\n}\n\n// https://github.com/facebook/react/tree/main/packages/react-reconciler#getcurrenteventpriority\n// Gives React a clue as to how import the current interaction is\nfunction getEventPriority() {\n  var _globalScope$event;\n  // Get a handle to the current global scope in window and worker contexts if able\n  // https://github.com/pmndrs/react-three-fiber/pull/2493\n  const globalScope = typeof self !== 'undefined' && self || typeof window !== 'undefined' && window;\n  if (!globalScope) return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DefaultEventPriority;\n  const name = (_globalScope$event = globalScope.event) == null ? void 0 : _globalScope$event.type;\n  switch (name) {\n    case 'click':\n    case 'contextmenu':\n    case 'dblclick':\n    case 'pointercancel':\n    case 'pointerdown':\n    case 'pointerup':\n      return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DiscreteEventPriority;\n    case 'pointermove':\n    case 'pointerout':\n    case 'pointerover':\n    case 'pointerenter':\n    case 'pointerleave':\n    case 'wheel':\n      return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.ContinuousEventPriority;\n    default:\n      return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DefaultEventPriority;\n  }\n}\n\n/**\r\n * Release pointer captures.\r\n * This is called by releasePointerCapture in the API, and when an object is removed.\r\n */\nfunction releaseInternalPointerCapture(capturedMap, obj, captures, pointerId) {\n  const captureData = captures.get(obj);\n  if (captureData) {\n    captures.delete(obj);\n    // If this was the last capturing object for this pointer\n    if (captures.size === 0) {\n      capturedMap.delete(pointerId);\n      captureData.target.releasePointerCapture(pointerId);\n    }\n  }\n}\nfunction removeInteractivity(store, object) {\n  const {\n    internal\n  } = store.getState();\n  // Removes every trace of an object from the data store\n  internal.interaction = internal.interaction.filter(o => o !== object);\n  internal.initialHits = internal.initialHits.filter(o => o !== object);\n  internal.hovered.forEach((value, key) => {\n    if (value.eventObject === object || value.object === object) {\n      // Clear out intersects, they are outdated by now\n      internal.hovered.delete(key);\n    }\n  });\n  internal.capturedMap.forEach((captures, pointerId) => {\n    releaseInternalPointerCapture(internal.capturedMap, object, captures, pointerId);\n  });\n}\nfunction createEvents(store) {\n  /** Calculates delta */\n  function calculateDistance(event) {\n    const {\n      internal\n    } = store.getState();\n    const dx = event.offsetX - internal.initialClick[0];\n    const dy = event.offsetY - internal.initialClick[1];\n    return Math.round(Math.sqrt(dx * dx + dy * dy));\n  }\n\n  /** Returns true if an instance has a valid pointer-event registered, this excludes scroll, clicks etc */\n  function filterPointerEvents(objects) {\n    return objects.filter(obj => ['Move', 'Over', 'Enter', 'Out', 'Leave'].some(name => {\n      var _r3f;\n      return (_r3f = obj.__r3f) == null ? void 0 : _r3f.handlers['onPointer' + name];\n    }));\n  }\n  function intersect(event, filter) {\n    const state = store.getState();\n    const duplicates = new Set();\n    const intersections = [];\n    // Allow callers to eliminate event objects\n    const eventsObjects = filter ? filter(state.internal.interaction) : state.internal.interaction;\n    // Reset all raycaster cameras to undefined\n    for (let i = 0; i < eventsObjects.length; i++) {\n      const state = getRootState(eventsObjects[i]);\n      if (state) {\n        state.raycaster.camera = undefined;\n      }\n    }\n    if (!state.previousRoot) {\n      // Make sure root-level pointer and ray are set up\n      state.events.compute == null ? void 0 : state.events.compute(event, state);\n    }\n    function handleRaycast(obj) {\n      const state = getRootState(obj);\n      // Skip event handling when noEvents is set, or when the raycasters camera is null\n      if (!state || !state.events.enabled || state.raycaster.camera === null) return [];\n\n      // When the camera is undefined we have to call the event layers update function\n      if (state.raycaster.camera === undefined) {\n        var _state$previousRoot;\n        state.events.compute == null ? void 0 : state.events.compute(event, state, (_state$previousRoot = state.previousRoot) == null ? void 0 : _state$previousRoot.getState());\n        // If the camera is still undefined we have to skip this layer entirely\n        if (state.raycaster.camera === undefined) state.raycaster.camera = null;\n      }\n\n      // Intersect object by object\n      return state.raycaster.camera ? state.raycaster.intersectObject(obj, true) : [];\n    }\n\n    // Collect events\n    let hits = eventsObjects\n    // Intersect objects\n    .flatMap(handleRaycast)\n    // Sort by event priority and distance\n    .sort((a, b) => {\n      const aState = getRootState(a.object);\n      const bState = getRootState(b.object);\n      if (!aState || !bState) return a.distance - b.distance;\n      return bState.events.priority - aState.events.priority || a.distance - b.distance;\n    })\n    // Filter out duplicates\n    .filter(item => {\n      const id = makeId(item);\n      if (duplicates.has(id)) return false;\n      duplicates.add(id);\n      return true;\n    });\n\n    // https://github.com/mrdoob/three.js/issues/16031\n    // Allow custom userland intersect sort order, this likely only makes sense on the root filter\n    if (state.events.filter) hits = state.events.filter(hits, state);\n\n    // Bubble up the events, find the event source (eventObject)\n    for (const hit of hits) {\n      let eventObject = hit.object;\n      // Bubble event up\n      while (eventObject) {\n        var _r3f2;\n        if ((_r3f2 = eventObject.__r3f) != null && _r3f2.eventCount) intersections.push({\n          ...hit,\n          eventObject\n        });\n        eventObject = eventObject.parent;\n      }\n    }\n\n    // If the interaction is captured, make all capturing targets part of the intersect.\n    if ('pointerId' in event && state.internal.capturedMap.has(event.pointerId)) {\n      for (let captureData of state.internal.capturedMap.get(event.pointerId).values()) {\n        if (!duplicates.has(makeId(captureData.intersection))) intersections.push(captureData.intersection);\n      }\n    }\n    return intersections;\n  }\n\n  /**  Handles intersections by forwarding them to handlers */\n  function handleIntersects(intersections, event, delta, callback) {\n    const rootState = store.getState();\n\n    // If anything has been found, forward it to the event listeners\n    if (intersections.length) {\n      const localState = {\n        stopped: false\n      };\n      for (const hit of intersections) {\n        const state = getRootState(hit.object) || rootState;\n        const {\n          raycaster,\n          pointer,\n          camera,\n          internal\n        } = state;\n        const unprojectedPoint = new three__WEBPACK_IMPORTED_MODULE_5__.Vector3(pointer.x, pointer.y, 0).unproject(camera);\n        const hasPointerCapture = id => {\n          var _internal$capturedMap, _internal$capturedMap2;\n          return (_internal$capturedMap = (_internal$capturedMap2 = internal.capturedMap.get(id)) == null ? void 0 : _internal$capturedMap2.has(hit.eventObject)) != null ? _internal$capturedMap : false;\n        };\n        const setPointerCapture = id => {\n          const captureData = {\n            intersection: hit,\n            target: event.target\n          };\n          if (internal.capturedMap.has(id)) {\n            // if the pointerId was previously captured, we add the hit to the\n            // event capturedMap.\n            internal.capturedMap.get(id).set(hit.eventObject, captureData);\n          } else {\n            // if the pointerId was not previously captured, we create a map\n            // containing the hitObject, and the hit. hitObject is used for\n            // faster access.\n            internal.capturedMap.set(id, new Map([[hit.eventObject, captureData]]));\n          }\n          event.target.setPointerCapture(id);\n        };\n        const releasePointerCapture = id => {\n          const captures = internal.capturedMap.get(id);\n          if (captures) {\n            releaseInternalPointerCapture(internal.capturedMap, hit.eventObject, captures, id);\n          }\n        };\n\n        // Add native event props\n        let extractEventProps = {};\n        // This iterates over the event's properties including the inherited ones. Native PointerEvents have most of their props as getters which are inherited, but polyfilled PointerEvents have them all as their own properties (i.e. not inherited). We can't use Object.keys() or Object.entries() as they only return \"own\" properties; nor Object.getPrototypeOf(event) as that *doesn't* return \"own\" properties, only inherited ones.\n        for (let prop in event) {\n          let property = event[prop];\n          // Only copy over atomics, leave functions alone as these should be\n          // called as event.nativeEvent.fn()\n          if (typeof property !== 'function') extractEventProps[prop] = property;\n        }\n        let raycastEvent = {\n          ...hit,\n          ...extractEventProps,\n          pointer,\n          intersections,\n          stopped: localState.stopped,\n          delta,\n          unprojectedPoint,\n          ray: raycaster.ray,\n          camera: camera,\n          // Hijack stopPropagation, which just sets a flag\n          stopPropagation() {\n            // https://github.com/pmndrs/react-three-fiber/issues/596\n            // Events are not allowed to stop propagation if the pointer has been captured\n            const capturesForPointer = 'pointerId' in event && internal.capturedMap.get(event.pointerId);\n\n            // We only authorize stopPropagation...\n            if (\n            // ...if this pointer hasn't been captured\n            !capturesForPointer ||\n            // ... or if the hit object is capturing the pointer\n            capturesForPointer.has(hit.eventObject)) {\n              raycastEvent.stopped = localState.stopped = true;\n              // Propagation is stopped, remove all other hover records\n              // An event handler is only allowed to flush other handlers if it is hovered itself\n              if (internal.hovered.size && Array.from(internal.hovered.values()).find(i => i.eventObject === hit.eventObject)) {\n                // Objects cannot flush out higher up objects that have already caught the event\n                const higher = intersections.slice(0, intersections.indexOf(hit));\n                cancelPointer([...higher, hit]);\n              }\n            }\n          },\n          // there should be a distinction between target and currentTarget\n          target: {\n            hasPointerCapture,\n            setPointerCapture,\n            releasePointerCapture\n          },\n          currentTarget: {\n            hasPointerCapture,\n            setPointerCapture,\n            releasePointerCapture\n          },\n          nativeEvent: event\n        };\n\n        // Call subscribers\n        callback(raycastEvent);\n        // Event bubbling may be interrupted by stopPropagation\n        if (localState.stopped === true) break;\n      }\n    }\n    return intersections;\n  }\n  function cancelPointer(intersections) {\n    const {\n      internal\n    } = store.getState();\n    for (const hoveredObj of internal.hovered.values()) {\n      // When no objects were hit or the the hovered object wasn't found underneath the cursor\n      // we call onPointerOut and delete the object from the hovered-elements map\n      if (!intersections.length || !intersections.find(hit => hit.object === hoveredObj.object && hit.index === hoveredObj.index && hit.instanceId === hoveredObj.instanceId)) {\n        const eventObject = hoveredObj.eventObject;\n        const instance = eventObject.__r3f;\n        const handlers = instance == null ? void 0 : instance.handlers;\n        internal.hovered.delete(makeId(hoveredObj));\n        if (instance != null && instance.eventCount) {\n          // Clear out intersects, they are outdated by now\n          const data = {\n            ...hoveredObj,\n            intersections\n          };\n          handlers.onPointerOut == null ? void 0 : handlers.onPointerOut(data);\n          handlers.onPointerLeave == null ? void 0 : handlers.onPointerLeave(data);\n        }\n      }\n    }\n  }\n  function pointerMissed(event, objects) {\n    for (let i = 0; i < objects.length; i++) {\n      const instance = objects[i].__r3f;\n      instance == null ? void 0 : instance.handlers.onPointerMissed == null ? void 0 : instance.handlers.onPointerMissed(event);\n    }\n  }\n  function handlePointer(name) {\n    // Deal with cancelation\n    switch (name) {\n      case 'onPointerLeave':\n      case 'onPointerCancel':\n        return () => cancelPointer([]);\n      case 'onLostPointerCapture':\n        return event => {\n          const {\n            internal\n          } = store.getState();\n          if ('pointerId' in event && internal.capturedMap.has(event.pointerId)) {\n            // If the object event interface had onLostPointerCapture, we'd call it here on every\n            // object that's getting removed. We call it on the next frame because onLostPointerCapture\n            // fires before onPointerUp. Otherwise pointerUp would never be called if the event didn't\n            // happen in the object it originated from, leaving components in a in-between state.\n            requestAnimationFrame(() => {\n              // Only release if pointer-up didn't do it already\n              if (internal.capturedMap.has(event.pointerId)) {\n                internal.capturedMap.delete(event.pointerId);\n                cancelPointer([]);\n              }\n            });\n          }\n        };\n    }\n\n    // Any other pointer goes here ...\n    return function handleEvent(event) {\n      const {\n        onPointerMissed,\n        internal\n      } = store.getState();\n\n      // prepareRay(event)\n      internal.lastEvent.current = event;\n\n      // Get fresh intersects\n      const isPointerMove = name === 'onPointerMove';\n      const isClickEvent = name === 'onClick' || name === 'onContextMenu' || name === 'onDoubleClick';\n      const filter = isPointerMove ? filterPointerEvents : undefined;\n      const hits = intersect(event, filter);\n      const delta = isClickEvent ? calculateDistance(event) : 0;\n\n      // Save initial coordinates on pointer-down\n      if (name === 'onPointerDown') {\n        internal.initialClick = [event.offsetX, event.offsetY];\n        internal.initialHits = hits.map(hit => hit.eventObject);\n      }\n\n      // If a click yields no results, pass it back to the user as a miss\n      // Missed events have to come first in order to establish user-land side-effect clean up\n      if (isClickEvent && !hits.length) {\n        if (delta <= 2) {\n          pointerMissed(event, internal.interaction);\n          if (onPointerMissed) onPointerMissed(event);\n        }\n      }\n      // Take care of unhover\n      if (isPointerMove) cancelPointer(hits);\n      function onIntersect(data) {\n        const eventObject = data.eventObject;\n        const instance = eventObject.__r3f;\n        const handlers = instance == null ? void 0 : instance.handlers;\n\n        // Check presence of handlers\n        if (!(instance != null && instance.eventCount)) return;\n\n        /*\r\n        MAYBE TODO, DELETE IF NOT: \r\n          Check if the object is captured, captured events should not have intersects running in parallel\r\n          But wouldn't it be better to just replace capturedMap with a single entry?\r\n          Also, are we OK with straight up making picking up multiple objects impossible?\r\n          \r\n        const pointerId = (data as ThreeEvent<PointerEvent>).pointerId        \r\n        if (pointerId !== undefined) {\r\n          const capturedMeshSet = internal.capturedMap.get(pointerId)\r\n          if (capturedMeshSet) {\r\n            const captured = capturedMeshSet.get(eventObject)\r\n            if (captured && captured.localState.stopped) return\r\n          }\r\n        }*/\n\n        if (isPointerMove) {\n          // Move event ...\n          if (handlers.onPointerOver || handlers.onPointerEnter || handlers.onPointerOut || handlers.onPointerLeave) {\n            // When enter or out is present take care of hover-state\n            const id = makeId(data);\n            const hoveredItem = internal.hovered.get(id);\n            if (!hoveredItem) {\n              // If the object wasn't previously hovered, book it and call its handler\n              internal.hovered.set(id, data);\n              handlers.onPointerOver == null ? void 0 : handlers.onPointerOver(data);\n              handlers.onPointerEnter == null ? void 0 : handlers.onPointerEnter(data);\n            } else if (hoveredItem.stopped) {\n              // If the object was previously hovered and stopped, we shouldn't allow other items to proceed\n              data.stopPropagation();\n            }\n          }\n          // Call mouse move\n          handlers.onPointerMove == null ? void 0 : handlers.onPointerMove(data);\n        } else {\n          // All other events ...\n          const handler = handlers[name];\n          if (handler) {\n            // Forward all events back to their respective handlers with the exception of click events,\n            // which must use the initial target\n            if (!isClickEvent || internal.initialHits.includes(eventObject)) {\n              // Missed events have to come first\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n              // Now call the handler\n              handler(data);\n            }\n          } else {\n            // Trigger onPointerMissed on all elements that have pointer over/out handlers, but not click and weren't hit\n            if (isClickEvent && internal.initialHits.includes(eventObject)) {\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n            }\n          }\n        }\n      }\n      handleIntersects(hits, event, delta, onIntersect);\n    };\n  }\n  return {\n    handlePointer\n  };\n}\n\n// Keys that shouldn't be copied between R3F stores\nconst privateKeys = ['set', 'get', 'setSize', 'setFrameloop', 'setDpr', 'events', 'invalidate', 'advance', 'size', 'viewport'];\nconst isRenderer = def => !!(def != null && def.render);\nconst context = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst createStore = (invalidate, advance) => {\n  const rootState = (0,zustand__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((set, get) => {\n    const position = new three__WEBPACK_IMPORTED_MODULE_5__.Vector3();\n    const defaultTarget = new three__WEBPACK_IMPORTED_MODULE_5__.Vector3();\n    const tempTarget = new three__WEBPACK_IMPORTED_MODULE_5__.Vector3();\n    function getCurrentViewport(camera = get().camera, target = defaultTarget, size = get().size) {\n      const {\n        width,\n        height,\n        top,\n        left\n      } = size;\n      const aspect = width / height;\n      if (target.isVector3) tempTarget.copy(target);else tempTarget.set(...target);\n      const distance = camera.getWorldPosition(position).distanceTo(tempTarget);\n      if (isOrthographicCamera(camera)) {\n        return {\n          width: width / camera.zoom,\n          height: height / camera.zoom,\n          top,\n          left,\n          factor: 1,\n          distance,\n          aspect\n        };\n      } else {\n        const fov = camera.fov * Math.PI / 180; // convert vertical fov to radians\n        const h = 2 * Math.tan(fov / 2) * distance; // visible height\n        const w = h * (width / height);\n        return {\n          width: w,\n          height: h,\n          top,\n          left,\n          factor: width / w,\n          distance,\n          aspect\n        };\n      }\n    }\n    let performanceTimeout = undefined;\n    const setPerformanceCurrent = current => set(state => ({\n      performance: {\n        ...state.performance,\n        current\n      }\n    }));\n    const pointer = new three__WEBPACK_IMPORTED_MODULE_5__.Vector2();\n    const rootState = {\n      set,\n      get,\n      // Mock objects that have to be configured\n      gl: null,\n      camera: null,\n      raycaster: null,\n      events: {\n        priority: 1,\n        enabled: true,\n        connected: false\n      },\n      xr: null,\n      scene: null,\n      invalidate: (frames = 1) => invalidate(get(), frames),\n      advance: (timestamp, runGlobalEffects) => advance(timestamp, runGlobalEffects, get()),\n      legacy: false,\n      linear: false,\n      flat: false,\n      controls: null,\n      clock: new three__WEBPACK_IMPORTED_MODULE_5__.Clock(),\n      pointer,\n      mouse: pointer,\n      frameloop: 'always',\n      onPointerMissed: undefined,\n      performance: {\n        current: 1,\n        min: 0.5,\n        max: 1,\n        debounce: 200,\n        regress: () => {\n          const state = get();\n          // Clear timeout\n          if (performanceTimeout) clearTimeout(performanceTimeout);\n          // Set lower bound performance\n          if (state.performance.current !== state.performance.min) setPerformanceCurrent(state.performance.min);\n          // Go back to upper bound performance after a while unless something regresses meanwhile\n          performanceTimeout = setTimeout(() => setPerformanceCurrent(get().performance.max), state.performance.debounce);\n        }\n      },\n      size: {\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        updateStyle: false\n      },\n      viewport: {\n        initialDpr: 0,\n        dpr: 0,\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        aspect: 0,\n        distance: 0,\n        factor: 0,\n        getCurrentViewport\n      },\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      })),\n      setSize: (width, height, updateStyle, top, left) => {\n        const camera = get().camera;\n        const size = {\n          width,\n          height,\n          top: top || 0,\n          left: left || 0,\n          updateStyle\n        };\n        set(state => ({\n          size,\n          viewport: {\n            ...state.viewport,\n            ...getCurrentViewport(camera, defaultTarget, size)\n          }\n        }));\n      },\n      setDpr: dpr => set(state => {\n        const resolved = calculateDpr(dpr);\n        return {\n          viewport: {\n            ...state.viewport,\n            dpr: resolved,\n            initialDpr: state.viewport.initialDpr || resolved\n          }\n        };\n      }),\n      setFrameloop: (frameloop = 'always') => {\n        const clock = get().clock;\n\n        // if frameloop === \"never\" clock.elapsedTime is updated using advance(timestamp)\n        clock.stop();\n        clock.elapsedTime = 0;\n        if (frameloop !== 'never') {\n          clock.start();\n          clock.elapsedTime = 0;\n        }\n        set(() => ({\n          frameloop\n        }));\n      },\n      previousRoot: undefined,\n      internal: {\n        active: false,\n        priority: 0,\n        frames: 0,\n        lastEvent: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createRef(),\n        interaction: [],\n        hovered: new Map(),\n        subscribers: [],\n        initialClick: [0, 0],\n        initialHits: [],\n        capturedMap: new Map(),\n        subscribe: (ref, priority, store) => {\n          const internal = get().internal;\n          // If this subscription was given a priority, it takes rendering into its own hands\n          // For that reason we switch off automatic rendering and increase the manual flag\n          // As long as this flag is positive there can be no internal rendering at all\n          // because there could be multiple render subscriptions\n          internal.priority = internal.priority + (priority > 0 ? 1 : 0);\n          internal.subscribers.push({\n            ref,\n            priority,\n            store\n          });\n          // Register subscriber and sort layers from lowest to highest, meaning,\n          // highest priority renders last (on top of the other frames)\n          internal.subscribers = internal.subscribers.sort((a, b) => a.priority - b.priority);\n          return () => {\n            const internal = get().internal;\n            if (internal != null && internal.subscribers) {\n              // Decrease manual flag if this subscription had a priority\n              internal.priority = internal.priority - (priority > 0 ? 1 : 0);\n              // Remove subscriber from list\n              internal.subscribers = internal.subscribers.filter(s => s.ref !== ref);\n            }\n          };\n        }\n      }\n    };\n    return rootState;\n  });\n  const state = rootState.getState();\n  let oldSize = state.size;\n  let oldDpr = state.viewport.dpr;\n  let oldCamera = state.camera;\n  rootState.subscribe(() => {\n    const {\n      camera,\n      size,\n      viewport,\n      gl,\n      set\n    } = rootState.getState();\n\n    // Resize camera and renderer on changes to size and pixelratio\n    if (size.width !== oldSize.width || size.height !== oldSize.height || viewport.dpr !== oldDpr) {\n      var _size$updateStyle;\n      oldSize = size;\n      oldDpr = viewport.dpr;\n      // Update camera & renderer\n      updateCamera(camera, size);\n      gl.setPixelRatio(viewport.dpr);\n      const updateStyle = (_size$updateStyle = size.updateStyle) != null ? _size$updateStyle : typeof HTMLCanvasElement !== 'undefined' && gl.domElement instanceof HTMLCanvasElement;\n      gl.setSize(size.width, size.height, updateStyle);\n    }\n\n    // Update viewport once the camera changes\n    if (camera !== oldCamera) {\n      oldCamera = camera;\n      // Update viewport\n      set(state => ({\n        viewport: {\n          ...state.viewport,\n          ...state.viewport.getCurrentViewport(camera)\n        }\n      }));\n    }\n  });\n\n  // Invalidate on any change\n  rootState.subscribe(state => invalidate(state));\n\n  // Return root state\n  return rootState;\n};\n\nfunction createSubs(callback, subs) {\n  const sub = {\n    callback\n  };\n  subs.add(sub);\n  return () => void subs.delete(sub);\n}\nlet i;\nlet globalEffects = new Set();\nlet globalAfterEffects = new Set();\nlet globalTailEffects = new Set();\n\n/**\r\n * Adds a global render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addEffect\r\n */\nconst addEffect = callback => createSubs(callback, globalEffects);\n\n/**\r\n * Adds a global after-render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addAfterEffect\r\n */\nconst addAfterEffect = callback => createSubs(callback, globalAfterEffects);\n\n/**\r\n * Adds a global callback which is called when rendering stops.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addTail\r\n */\nconst addTail = callback => createSubs(callback, globalTailEffects);\nfunction run(effects, timestamp) {\n  if (!effects.size) return;\n  for (const {\n    callback\n  } of effects.values()) {\n    callback(timestamp);\n  }\n}\nfunction flushGlobalEffects(type, timestamp) {\n  switch (type) {\n    case 'before':\n      return run(globalEffects, timestamp);\n    case 'after':\n      return run(globalAfterEffects, timestamp);\n    case 'tail':\n      return run(globalTailEffects, timestamp);\n  }\n}\nlet subscribers;\nlet subscription;\nfunction render$1(timestamp, state, frame) {\n  // Run local effects\n  let delta = state.clock.getDelta();\n  // In frameloop='never' mode, clock times are updated using the provided timestamp\n  if (state.frameloop === 'never' && typeof timestamp === 'number') {\n    delta = timestamp - state.clock.elapsedTime;\n    state.clock.oldTime = state.clock.elapsedTime;\n    state.clock.elapsedTime = timestamp;\n  }\n  // Call subscribers (useFrame)\n  subscribers = state.internal.subscribers;\n  for (i = 0; i < subscribers.length; i++) {\n    subscription = subscribers[i];\n    subscription.ref.current(subscription.store.getState(), delta, frame);\n  }\n  // Render content\n  if (!state.internal.priority && state.gl.render) state.gl.render(state.scene, state.camera);\n  // Decrease frame count\n  state.internal.frames = Math.max(0, state.internal.frames - 1);\n  return state.frameloop === 'always' ? 1 : state.internal.frames;\n}\nfunction createLoop(roots) {\n  let running = false;\n  let useFrameInProgress = false;\n  let repeat;\n  let frame;\n  let state;\n  function loop(timestamp) {\n    frame = requestAnimationFrame(loop);\n    running = true;\n    repeat = 0;\n\n    // Run effects\n    flushGlobalEffects('before', timestamp);\n\n    // Render all roots\n    useFrameInProgress = true;\n    for (const root of roots.values()) {\n      var _state$gl$xr;\n      state = root.store.getState();\n      // If the frameloop is invalidated, do not run another frame\n      if (state.internal.active && (state.frameloop === 'always' || state.internal.frames > 0) && !((_state$gl$xr = state.gl.xr) != null && _state$gl$xr.isPresenting)) {\n        repeat += render$1(timestamp, state);\n      }\n    }\n    useFrameInProgress = false;\n\n    // Run after-effects\n    flushGlobalEffects('after', timestamp);\n\n    // Stop the loop if nothing invalidates it\n    if (repeat === 0) {\n      // Tail call effects, they are called when rendering stops\n      flushGlobalEffects('tail', timestamp);\n\n      // Flag end of operation\n      running = false;\n      return cancelAnimationFrame(frame);\n    }\n  }\n  function invalidate(state, frames = 1) {\n    var _state$gl$xr2;\n    if (!state) return roots.forEach(root => invalidate(root.store.getState(), frames));\n    if ((_state$gl$xr2 = state.gl.xr) != null && _state$gl$xr2.isPresenting || !state.internal.active || state.frameloop === 'never') return;\n    if (frames > 1) {\n      // legacy support for people using frames parameters\n      // Increase frames, do not go higher than 60\n      state.internal.frames = Math.min(60, state.internal.frames + frames);\n    } else {\n      if (useFrameInProgress) {\n        //called from within a useFrame, it means the user wants an additional frame\n        state.internal.frames = 2;\n      } else {\n        //the user need a new frame, no need to increment further than 1\n        state.internal.frames = 1;\n      }\n    }\n\n    // If the render-loop isn't active, start it\n    if (!running) {\n      running = true;\n      requestAnimationFrame(loop);\n    }\n  }\n  function advance(timestamp, runGlobalEffects = true, state, frame) {\n    if (runGlobalEffects) flushGlobalEffects('before', timestamp);\n    if (!state) for (const root of roots.values()) render$1(timestamp, root.store.getState());else render$1(timestamp, state, frame);\n    if (runGlobalEffects) flushGlobalEffects('after', timestamp);\n  }\n  return {\n    loop,\n    invalidate,\n    advance\n  };\n}\n\n/**\r\n * Exposes an object's {@link LocalState}.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#useInstanceHandle\r\n *\r\n * **Note**: this is an escape hatch to react-internal fields. Expect this to change significantly between versions.\r\n */\nfunction useInstanceHandle(ref) {\n  const instance = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  useIsomorphicLayoutEffect(() => void (instance.current = ref.current.__r3f), [ref]);\n  return instance;\n}\nfunction useStore() {\n  const store = react__WEBPACK_IMPORTED_MODULE_0__.useContext(context);\n  if (!store) throw new Error('R3F: Hooks can only be used within the Canvas component!');\n  return store;\n}\n\n/**\r\n * Accesses R3F's internal state, containing renderer, canvas, scene, etc.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usethree\r\n */\nfunction useThree(selector = state => state, equalityFn) {\n  return useStore()(selector, equalityFn);\n}\n\n/**\r\n * Executes a callback before render in a shared frame loop.\r\n * Can order effects with render priority or manually render with a positive priority.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useframe\r\n */\nfunction useFrame(callback, renderPriority = 0) {\n  const store = useStore();\n  const subscribe = store.getState().internal.subscribe;\n  // Memoize ref\n  const ref = useMutableCallback(callback);\n  // Subscribe on mount, unsubscribe on unmount\n  useIsomorphicLayoutEffect(() => subscribe(ref, renderPriority, store), [renderPriority, subscribe, store]);\n  return null;\n}\n\n/**\r\n * Returns a node graph of an object with named nodes & materials.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usegraph\r\n */\nfunction useGraph(object) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => buildGraph(object), [object]);\n}\nconst memoizedLoaders = new WeakMap();\nfunction loadingFn(extensions, onProgress) {\n  return function (Proto, ...input) {\n    // Construct new loader and run extensions\n    let loader = memoizedLoaders.get(Proto);\n    if (!loader) {\n      loader = new Proto();\n      memoizedLoaders.set(Proto, loader);\n    }\n    if (extensions) extensions(loader);\n    // Go through the urls and load them\n    return Promise.all(input.map(input => new Promise((res, reject) => loader.load(input, data => {\n      if (data.scene) Object.assign(data, buildGraph(data.scene));\n      res(data);\n    }, onProgress, error => reject(new Error(`Could not load ${input}: ${error == null ? void 0 : error.message}`))))));\n  };\n}\n/**\r\n * Synchronously loads and caches assets with a three loader.\r\n *\r\n * Note: this hook's caller must be wrapped with `React.Suspense`\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useloader\r\n */\nfunction useLoader(Proto, input, extensions, onProgress) {\n  // Use suspense to load async assets\n  const keys = Array.isArray(input) ? input : [input];\n  const results = (0,suspend_react__WEBPACK_IMPORTED_MODULE_7__.suspend)(loadingFn(extensions, onProgress), [Proto, ...keys], {\n    equal: is.equ\n  });\n  // Return the object/s\n  return Array.isArray(input) ? results : results[0];\n}\n\n/**\r\n * Preloads an asset into cache as a side-effect.\r\n */\nuseLoader.preload = function (Proto, input, extensions) {\n  const keys = Array.isArray(input) ? input : [input];\n  return (0,suspend_react__WEBPACK_IMPORTED_MODULE_7__.preload)(loadingFn(extensions), [Proto, ...keys]);\n};\n\n/**\r\n * Removes a loaded asset from cache.\r\n */\nuseLoader.clear = function (Proto, input) {\n  const keys = Array.isArray(input) ? input : [input];\n  return (0,suspend_react__WEBPACK_IMPORTED_MODULE_7__.clear)([Proto, ...keys]);\n};\n\nconst roots = new Map();\nconst {\n  invalidate,\n  advance\n} = createLoop(roots);\nconst {\n  reconciler,\n  applyProps\n} = createRenderer(roots, getEventPriority);\nconst shallowLoose = {\n  objects: 'shallow',\n  strict: false\n};\nconst createRendererInstance = (gl, canvas) => {\n  const customRenderer = typeof gl === 'function' ? gl(canvas) : gl;\n  if (isRenderer(customRenderer)) return customRenderer;else return new three__WEBPACK_IMPORTED_MODULE_5__.WebGLRenderer({\n    powerPreference: 'high-performance',\n    canvas: canvas,\n    antialias: true,\n    alpha: true,\n    ...gl\n  });\n};\nfunction computeInitialSize(canvas, defaultSize) {\n  const defaultStyle = typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement;\n  if (defaultSize) {\n    const {\n      width,\n      height,\n      top,\n      left,\n      updateStyle = defaultStyle\n    } = defaultSize;\n    return {\n      width,\n      height,\n      top,\n      left,\n      updateStyle\n    };\n  } else if (typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement && canvas.parentElement) {\n    const {\n      width,\n      height,\n      top,\n      left\n    } = canvas.parentElement.getBoundingClientRect();\n    return {\n      width,\n      height,\n      top,\n      left,\n      updateStyle: defaultStyle\n    };\n  } else if (typeof OffscreenCanvas !== 'undefined' && canvas instanceof OffscreenCanvas) {\n    return {\n      width: canvas.width,\n      height: canvas.height,\n      top: 0,\n      left: 0,\n      updateStyle: defaultStyle\n    };\n  }\n  return {\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0\n  };\n}\nfunction createRoot(canvas) {\n  // Check against mistaken use of createRoot\n  const prevRoot = roots.get(canvas);\n  const prevFiber = prevRoot == null ? void 0 : prevRoot.fiber;\n  const prevStore = prevRoot == null ? void 0 : prevRoot.store;\n  if (prevRoot) console.warn('R3F.createRoot should only be called once!');\n\n  // Report when an error was detected in a previous render\n  // https://github.com/pmndrs/react-three-fiber/pull/2261\n  const logRecoverableError = typeof reportError === 'function' ?\n  // In modern browsers, reportError will dispatch an error event,\n  // emulating an uncaught JavaScript error.\n  reportError :\n  // In older browsers and test environments, fallback to console.error.\n  console.error;\n\n  // Create store\n  const store = prevStore || createStore(invalidate, advance);\n  // Create renderer\n  const fiber = prevFiber || reconciler.createContainer(store, react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.ConcurrentRoot, null, false, null, '', logRecoverableError, null);\n  // Map it\n  if (!prevRoot) roots.set(canvas, {\n    fiber,\n    store\n  });\n\n  // Locals\n  let onCreated;\n  let configured = false;\n  let lastCamera;\n  return {\n    configure(props = {}) {\n      let {\n        gl: glConfig,\n        size: propsSize,\n        scene: sceneOptions,\n        events,\n        onCreated: onCreatedCallback,\n        shadows = false,\n        linear = false,\n        flat = false,\n        legacy = false,\n        orthographic = false,\n        frameloop = 'always',\n        dpr = [1, 2],\n        performance,\n        raycaster: raycastOptions,\n        camera: cameraOptions,\n        onPointerMissed\n      } = props;\n      let state = store.getState();\n\n      // Set up renderer (one time only!)\n      let gl = state.gl;\n      if (!state.gl) state.set({\n        gl: gl = createRendererInstance(glConfig, canvas)\n      });\n\n      // Set up raycaster (one time only!)\n      let raycaster = state.raycaster;\n      if (!raycaster) state.set({\n        raycaster: raycaster = new three__WEBPACK_IMPORTED_MODULE_5__.Raycaster()\n      });\n\n      // Set raycaster options\n      const {\n        params,\n        ...options\n      } = raycastOptions || {};\n      if (!is.equ(options, raycaster, shallowLoose)) applyProps(raycaster, {\n        ...options\n      });\n      if (!is.equ(params, raycaster.params, shallowLoose)) applyProps(raycaster, {\n        params: {\n          ...raycaster.params,\n          ...params\n        }\n      });\n\n      // Create default camera, don't overwrite any user-set state\n      if (!state.camera || state.camera === lastCamera && !is.equ(lastCamera, cameraOptions, shallowLoose)) {\n        lastCamera = cameraOptions;\n        const isCamera = cameraOptions instanceof three__WEBPACK_IMPORTED_MODULE_5__.Camera;\n        const camera = isCamera ? cameraOptions : orthographic ? new three__WEBPACK_IMPORTED_MODULE_5__.OrthographicCamera(0, 0, 0, 0, 0.1, 1000) : new three__WEBPACK_IMPORTED_MODULE_5__.PerspectiveCamera(75, 0, 0.1, 1000);\n        if (!isCamera) {\n          camera.position.z = 5;\n          if (cameraOptions) {\n            applyProps(camera, cameraOptions);\n            // Preserve user-defined frustum if possible\n            // https://github.com/pmndrs/react-three-fiber/issues/3160\n            if ('aspect' in cameraOptions || 'left' in cameraOptions || 'right' in cameraOptions || 'bottom' in cameraOptions || 'top' in cameraOptions) {\n              camera.manual = true;\n              camera.updateProjectionMatrix();\n            }\n          }\n          // Always look at center by default\n          if (!state.camera && !(cameraOptions != null && cameraOptions.rotation)) camera.lookAt(0, 0, 0);\n        }\n        state.set({\n          camera\n        });\n\n        // Configure raycaster\n        // https://github.com/pmndrs/react-xr/issues/300\n        raycaster.camera = camera;\n      }\n\n      // Set up scene (one time only!)\n      if (!state.scene) {\n        let scene;\n        if (sceneOptions != null && sceneOptions.isScene) {\n          scene = sceneOptions;\n        } else {\n          scene = new three__WEBPACK_IMPORTED_MODULE_5__.Scene();\n          if (sceneOptions) applyProps(scene, sceneOptions);\n        }\n        state.set({\n          scene: prepare(scene)\n        });\n      }\n\n      // Set up XR (one time only!)\n      if (!state.xr) {\n        var _gl$xr;\n        // Handle frame behavior in WebXR\n        const handleXRFrame = (timestamp, frame) => {\n          const state = store.getState();\n          if (state.frameloop === 'never') return;\n          advance(timestamp, true, state, frame);\n        };\n\n        // Toggle render switching on session\n        const handleSessionChange = () => {\n          const state = store.getState();\n          state.gl.xr.enabled = state.gl.xr.isPresenting;\n          state.gl.xr.setAnimationLoop(state.gl.xr.isPresenting ? handleXRFrame : null);\n          if (!state.gl.xr.isPresenting) invalidate(state);\n        };\n\n        // WebXR session manager\n        const xr = {\n          connect() {\n            const gl = store.getState().gl;\n            gl.xr.addEventListener('sessionstart', handleSessionChange);\n            gl.xr.addEventListener('sessionend', handleSessionChange);\n          },\n          disconnect() {\n            const gl = store.getState().gl;\n            gl.xr.removeEventListener('sessionstart', handleSessionChange);\n            gl.xr.removeEventListener('sessionend', handleSessionChange);\n          }\n        };\n\n        // Subscribe to WebXR session events\n        if (typeof ((_gl$xr = gl.xr) == null ? void 0 : _gl$xr.addEventListener) === 'function') xr.connect();\n        state.set({\n          xr\n        });\n      }\n\n      // Set shadowmap\n      if (gl.shadowMap) {\n        const oldEnabled = gl.shadowMap.enabled;\n        const oldType = gl.shadowMap.type;\n        gl.shadowMap.enabled = !!shadows;\n        if (is.boo(shadows)) {\n          gl.shadowMap.type = three__WEBPACK_IMPORTED_MODULE_5__.PCFSoftShadowMap;\n        } else if (is.str(shadows)) {\n          var _types$shadows;\n          const types = {\n            basic: three__WEBPACK_IMPORTED_MODULE_5__.BasicShadowMap,\n            percentage: three__WEBPACK_IMPORTED_MODULE_5__.PCFShadowMap,\n            soft: three__WEBPACK_IMPORTED_MODULE_5__.PCFSoftShadowMap,\n            variance: three__WEBPACK_IMPORTED_MODULE_5__.VSMShadowMap\n          };\n          gl.shadowMap.type = (_types$shadows = types[shadows]) != null ? _types$shadows : three__WEBPACK_IMPORTED_MODULE_5__.PCFSoftShadowMap;\n        } else if (is.obj(shadows)) {\n          Object.assign(gl.shadowMap, shadows);\n        }\n        if (oldEnabled !== gl.shadowMap.enabled || oldType !== gl.shadowMap.type) gl.shadowMap.needsUpdate = true;\n      }\n\n      // Safely set color management if available.\n      // Avoid accessing THREE.ColorManagement to play nice with older versions\n      const ColorManagement = getColorManagement();\n      if (ColorManagement) {\n        if ('enabled' in ColorManagement) ColorManagement.enabled = !legacy;else if ('legacyMode' in ColorManagement) ColorManagement.legacyMode = legacy;\n      }\n      if (!configured) {\n        // Set color space and tonemapping preferences, once\n        const LinearEncoding = 3000;\n        const sRGBEncoding = 3001;\n        applyProps(gl, {\n          outputEncoding: linear ? LinearEncoding : sRGBEncoding,\n          toneMapping: flat ? three__WEBPACK_IMPORTED_MODULE_5__.NoToneMapping : three__WEBPACK_IMPORTED_MODULE_5__.ACESFilmicToneMapping\n        });\n      }\n\n      // Update color management state\n      if (state.legacy !== legacy) state.set(() => ({\n        legacy\n      }));\n      if (state.linear !== linear) state.set(() => ({\n        linear\n      }));\n      if (state.flat !== flat) state.set(() => ({\n        flat\n      }));\n\n      // Set gl props\n      if (glConfig && !is.fun(glConfig) && !isRenderer(glConfig) && !is.equ(glConfig, gl, shallowLoose)) applyProps(gl, glConfig);\n      // Store events internally\n      if (events && !state.events.handlers) state.set({\n        events: events(store)\n      });\n      // Check size, allow it to take on container bounds initially\n      const size = computeInitialSize(canvas, propsSize);\n      if (!is.equ(size, state.size, shallowLoose)) {\n        state.setSize(size.width, size.height, size.updateStyle, size.top, size.left);\n      }\n      // Check pixelratio\n      if (dpr && state.viewport.dpr !== calculateDpr(dpr)) state.setDpr(dpr);\n      // Check frameloop\n      if (state.frameloop !== frameloop) state.setFrameloop(frameloop);\n      // Check pointer missed\n      if (!state.onPointerMissed) state.set({\n        onPointerMissed\n      });\n      // Check performance\n      if (performance && !is.equ(performance, state.performance, shallowLoose)) state.set(state => ({\n        performance: {\n          ...state.performance,\n          ...performance\n        }\n      }));\n\n      // Set locals\n      onCreated = onCreatedCallback;\n      configured = true;\n      return this;\n    },\n    render(children) {\n      // The root has to be configured before it can be rendered\n      if (!configured) this.configure();\n      reconciler.updateContainer( /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Provider, {\n        store: store,\n        children: children,\n        onCreated: onCreated,\n        rootElement: canvas\n      }), fiber, null, () => undefined);\n      return store;\n    },\n    unmount() {\n      unmountComponentAtNode(canvas);\n    }\n  };\n}\nfunction render(children, canvas, config) {\n  console.warn('R3F.render is no longer supported in React 18. Use createRoot instead!');\n  const root = createRoot(canvas);\n  root.configure(config);\n  return root.render(children);\n}\nfunction Provider({\n  store,\n  children,\n  onCreated,\n  rootElement\n}) {\n  useIsomorphicLayoutEffect(() => {\n    const state = store.getState();\n    // Flag the canvas active, rendering will now begin\n    state.set(state => ({\n      internal: {\n        ...state.internal,\n        active: true\n      }\n    }));\n    // Notifiy that init is completed, the scene graph exists, but nothing has yet rendered\n    if (onCreated) onCreated(state);\n    // Connect events to the targets parent, this is done to ensure events are registered on\n    // a shared target, and not on the canvas itself\n    if (!store.getState().events.connected) state.events.connect == null ? void 0 : state.events.connect(rootElement);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(context.Provider, {\n    value: store,\n    children: children\n  });\n}\nfunction unmountComponentAtNode(canvas, callback) {\n  const root = roots.get(canvas);\n  const fiber = root == null ? void 0 : root.fiber;\n  if (fiber) {\n    const state = root == null ? void 0 : root.store.getState();\n    if (state) state.internal.active = false;\n    reconciler.updateContainer(null, fiber, null, () => {\n      if (state) {\n        setTimeout(() => {\n          try {\n            var _state$gl, _state$gl$renderLists, _state$gl2, _state$gl3;\n            state.events.disconnect == null ? void 0 : state.events.disconnect();\n            (_state$gl = state.gl) == null ? void 0 : (_state$gl$renderLists = _state$gl.renderLists) == null ? void 0 : _state$gl$renderLists.dispose == null ? void 0 : _state$gl$renderLists.dispose();\n            (_state$gl2 = state.gl) == null ? void 0 : _state$gl2.forceContextLoss == null ? void 0 : _state$gl2.forceContextLoss();\n            if ((_state$gl3 = state.gl) != null && _state$gl3.xr) state.xr.disconnect();\n            dispose(state);\n            roots.delete(canvas);\n            if (callback) callback(canvas);\n          } catch (e) {\n            /* ... */\n          }\n        }, 500);\n      }\n    });\n  }\n}\nfunction createPortal(children, container, state) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Portal, {\n    children: children,\n    container: container,\n    state: state\n  }, container.uuid);\n}\nfunction Portal({\n  state = {},\n  children,\n  container\n}) {\n  /** This has to be a component because it would not be able to call useThree/useStore otherwise since\r\n   *  if this is our environment, then we are not in r3f's renderer but in react-dom, it would trigger\r\n   *  the \"R3F hooks can only be used within the Canvas component!\" warning:\r\n   *  <Canvas>\r\n   *    {createPortal(...)} */\n  const {\n    events,\n    size,\n    ...rest\n  } = state;\n  const previousRoot = useStore();\n  const [raycaster] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => new three__WEBPACK_IMPORTED_MODULE_5__.Raycaster());\n  const [pointer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => new three__WEBPACK_IMPORTED_MODULE_5__.Vector2());\n  const inject = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((rootState, injectState) => {\n    const intersect = {\n      ...rootState\n    }; // all prev state props\n\n    // Only the fields of \"rootState\" that do not differ from injectState\n    // Some props should be off-limits\n    // Otherwise filter out the props that are different and let the inject layer take precedence\n    Object.keys(rootState).forEach(key => {\n      if (\n      // Some props should be off-limits\n      privateKeys.includes(key) ||\n      // Otherwise filter out the props that are different and let the inject layer take precedence\n      // Unless the inject layer props is undefined, then we keep the root layer\n      rootState[key] !== injectState[key] && injectState[key]) {\n        delete intersect[key];\n      }\n    });\n    let viewport = undefined;\n    if (injectState && size) {\n      const camera = injectState.camera;\n      // Calculate the override viewport, if present\n      viewport = rootState.viewport.getCurrentViewport(camera, new three__WEBPACK_IMPORTED_MODULE_5__.Vector3(), size);\n      // Update the portal camera, if it differs from the previous layer\n      if (camera !== rootState.camera) updateCamera(camera, size);\n    }\n    return {\n      // The intersect consists of the previous root state\n      ...intersect,\n      // Portals have their own scene, which forms the root, a raycaster and a pointer\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      // Their previous root is the layer before it\n      previousRoot,\n      // Events, size and viewport can be overridden by the inject layer\n      events: {\n        ...rootState.events,\n        ...(injectState == null ? void 0 : injectState.events),\n        ...events\n      },\n      size: {\n        ...rootState.size,\n        ...size\n      },\n      viewport: {\n        ...rootState.viewport,\n        ...viewport\n      },\n      ...rest\n    };\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [state]);\n  const [usePortalStore] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => {\n    // Create a mirrored store, based on the previous root with a few overrides ...\n    const previousState = previousRoot.getState();\n    const store = (0,zustand__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((set, get) => ({\n      ...previousState,\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      previousRoot,\n      events: {\n        ...previousState.events,\n        ...events\n      },\n      size: {\n        ...previousState.size,\n        ...size\n      },\n      ...rest,\n      // Set and get refer to this root-state\n      set,\n      get,\n      // Layers are allowed to override events\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      }))\n    }));\n    return store;\n  });\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    // Subscribe to previous root-state and copy changes over to the mirrored portal-state\n    const unsub = previousRoot.subscribe(prev => usePortalStore.setState(state => inject(prev, state)));\n    return () => {\n      unsub();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [inject]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    usePortalStore.setState(injectState => inject(previousRoot.getState(), injectState));\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [inject]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    return () => {\n      usePortalStore.destroy();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n    children: reconciler.createPortal( /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(context.Provider, {\n      value: usePortalStore,\n      children: children\n    }), usePortalStore, null)\n  });\n}\n\n/**\r\n * Force React to flush any updates inside the provided callback synchronously and immediately.\r\n * All the same caveats documented for react-dom's `flushSync` apply here (see https://react.dev/reference/react-dom/flushSync).\r\n * Nevertheless, sometimes one needs to render synchronously, for example to keep DOM and 3D changes in lock-step without\r\n * having to revert to a non-React solution.\r\n */\nfunction flushSync(fn) {\n  // `flushSync` implementation only takes one argument. I don't know what's up with the type declaration for it.\n  return reconciler.flushSync(fn, undefined);\n}\nreconciler.injectIntoDevTools({\n  bundleType:  false ? 0 : 1,\n  rendererPackageName: '@react-three/fiber',\n  version: react__WEBPACK_IMPORTED_MODULE_0__.version\n});\nconst act = react__WEBPACK_IMPORTED_MODULE_0__.unstable_act;\n\nconst DOM_EVENTS = {\n  onClick: ['click', false],\n  onContextMenu: ['contextmenu', false],\n  onDoubleClick: ['dblclick', false],\n  onWheel: ['wheel', true],\n  onPointerDown: ['pointerdown', true],\n  onPointerUp: ['pointerup', true],\n  onPointerLeave: ['pointerleave', true],\n  onPointerMove: ['pointermove', true],\n  onPointerCancel: ['pointercancel', true],\n  onLostPointerCapture: ['lostpointercapture', true]\n};\n\n/** Default R3F event manager for web */\nfunction createPointerEvents(store) {\n  const {\n    handlePointer\n  } = createEvents(store);\n  return {\n    priority: 1,\n    enabled: true,\n    compute(event, state, previous) {\n      // https://github.com/pmndrs/react-three-fiber/pull/782\n      // Events trigger outside of canvas when moved, use offsetX/Y by default and allow overrides\n      state.pointer.set(event.offsetX / state.size.width * 2 - 1, -(event.offsetY / state.size.height) * 2 + 1);\n      state.raycaster.setFromCamera(state.pointer, state.camera);\n    },\n    connected: undefined,\n    handlers: Object.keys(DOM_EVENTS).reduce((acc, key) => ({\n      ...acc,\n      [key]: handlePointer(key)\n    }), {}),\n    update: () => {\n      var _internal$lastEvent;\n      const {\n        events,\n        internal\n      } = store.getState();\n      if ((_internal$lastEvent = internal.lastEvent) != null && _internal$lastEvent.current && events.handlers) events.handlers.onPointerMove(internal.lastEvent.current);\n    },\n    connect: target => {\n      var _events$handlers;\n      const {\n        set,\n        events\n      } = store.getState();\n      events.disconnect == null ? void 0 : events.disconnect();\n      set(state => ({\n        events: {\n          ...state.events,\n          connected: target\n        }\n      }));\n      Object.entries((_events$handlers = events.handlers) != null ? _events$handlers : []).forEach(([name, event]) => {\n        const [eventName, passive] = DOM_EVENTS[name];\n        target.addEventListener(eventName, event, {\n          passive\n        });\n      });\n    },\n    disconnect: () => {\n      const {\n        set,\n        events\n      } = store.getState();\n      if (events.connected) {\n        var _events$handlers2;\n        Object.entries((_events$handlers2 = events.handlers) != null ? _events$handlers2 : []).forEach(([name, event]) => {\n          if (events && events.connected instanceof HTMLElement) {\n            const [eventName] = DOM_EVENTS[name];\n            events.connected.removeEventListener(eventName, event);\n          }\n        });\n        set(state => ({\n          events: {\n            ...state.events,\n            connected: undefined\n          }\n        }));\n      }\n    }\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/dist/events-776716bd.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Canvas: () => (/* binding */ Canvas),\n/* harmony export */   ReactThreeFiber: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.t),\n/* harmony export */   _roots: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.z),\n/* harmony export */   act: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.x),\n/* harmony export */   addAfterEffect: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.p),\n/* harmony export */   addEffect: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.o),\n/* harmony export */   addTail: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.q),\n/* harmony export */   advance: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.n),\n/* harmony export */   applyProps: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.k),\n/* harmony export */   buildGraph: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.y),\n/* harmony export */   context: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.g),\n/* harmony export */   createEvents: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.f),\n/* harmony export */   createPointerEvents: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   createPortal: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.h),\n/* harmony export */   createRoot: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.b),\n/* harmony export */   dispose: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.l),\n/* harmony export */   events: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   extend: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.e),\n/* harmony export */   flushGlobalEffects: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.s),\n/* harmony export */   flushSync: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.v),\n/* harmony export */   getRootState: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.w),\n/* harmony export */   invalidate: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.m),\n/* harmony export */   reconciler: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.j),\n/* harmony export */   render: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.r),\n/* harmony export */   unmountComponentAtNode: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   useFrame: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.F),\n/* harmony export */   useGraph: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.G),\n/* harmony export */   useInstanceHandle: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.A),\n/* harmony export */   useLoader: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.H),\n/* harmony export */   useStore: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.C),\n/* harmony export */   useThree: () => (/* reexport safe */ _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.D)\n/* harmony export */ });\n/* harmony import */ var _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./events-776716bd.esm.js */ \"(ssr)/./node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var react_use_measure__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-use-measure */ \"(ssr)/./node_modules/react-use-measure/dist/index.js\");\n/* harmony import */ var its_fine__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! its-fine */ \"(ssr)/./node_modules/its-fine/dist/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_reconciler_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-reconciler/constants */ \"(ssr)/./node_modules/react-reconciler/constants.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-reconciler */ \"(ssr)/./node_modules/react-reconciler/index.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_reconciler__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var scheduler__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! scheduler */ \"(ssr)/./node_modules/scheduler/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CanvasImpl = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(function Canvas({\n  children,\n  fallback,\n  resize,\n  style,\n  gl,\n  events = _events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.c,\n  eventSource,\n  eventPrefix,\n  shadows,\n  linear,\n  flat,\n  legacy,\n  orthographic,\n  frameloop,\n  dpr,\n  performance,\n  raycaster,\n  camera,\n  scene,\n  onPointerMissed,\n  onCreated,\n  ...props\n}, forwardedRef) {\n  // Create a known catalogue of Threejs-native elements\n  // This will include the entire THREE namespace by default, users can extend\n  // their own elements by using the createRoot API instead\n  react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => (0,_events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.e)(three__WEBPACK_IMPORTED_MODULE_6__), []);\n  const Bridge = (0,its_fine__WEBPACK_IMPORTED_MODULE_7__.useContextBridge)();\n  const [containerRef, containerRect] = (0,react_use_measure__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({\n    scroll: true,\n    debounce: {\n      scroll: 50,\n      resize: 0\n    },\n    ...resize\n  });\n  const canvasRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const divRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(forwardedRef, () => canvasRef.current);\n  const handlePointerMissed = (0,_events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.u)(onPointerMissed);\n  const [block, setBlock] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n  const [error, setError] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n\n  // Suspend this component if block is a promise (2nd run)\n  if (block) throw block;\n  // Throw exception outwards if anything within canvas throws\n  if (error) throw error;\n  const root = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  (0,_events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.a)(() => {\n    const canvas = canvasRef.current;\n    if (containerRect.width > 0 && containerRect.height > 0 && canvas) {\n      if (!root.current) root.current = (0,_events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.b)(canvas);\n      root.current.configure({\n        gl,\n        events,\n        shadows,\n        linear,\n        flat,\n        legacy,\n        orthographic,\n        frameloop,\n        dpr,\n        performance,\n        raycaster,\n        camera,\n        scene,\n        size: containerRect,\n        // Pass mutable reference to onPointerMissed so it's free to update\n        onPointerMissed: (...args) => handlePointerMissed.current == null ? void 0 : handlePointerMissed.current(...args),\n        onCreated: state => {\n          // Connect to event source\n          state.events.connect == null ? void 0 : state.events.connect(eventSource ? (0,_events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.i)(eventSource) ? eventSource.current : eventSource : divRef.current);\n          // Set up compute function\n          if (eventPrefix) {\n            state.setEvents({\n              compute: (event, state) => {\n                const x = event[eventPrefix + 'X'];\n                const y = event[eventPrefix + 'Y'];\n                state.pointer.set(x / state.size.width * 2 - 1, -(y / state.size.height) * 2 + 1);\n                state.raycaster.setFromCamera(state.pointer, state.camera);\n              }\n            });\n          }\n          // Call onCreated callback\n          onCreated == null ? void 0 : onCreated(state);\n        }\n      });\n      root.current.render( /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Bridge, {\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.E, {\n          set: setError,\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n            fallback: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.B, {\n              set: setBlock\n            }),\n            children: children != null ? children : null\n          })\n        })\n      }));\n    }\n  });\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    const canvas = canvasRef.current;\n    if (canvas) return () => (0,_events_776716bd_esm_js__WEBPACK_IMPORTED_MODULE_0__.d)(canvas);\n  }, []);\n\n  // When the event source is not this div, we need to set pointer-events to none\n  // Or else the canvas will block events from reaching the event source\n  const pointerEvents = eventSource ? 'none' : 'auto';\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n    ref: divRef,\n    style: {\n      position: 'relative',\n      width: '100%',\n      height: '100%',\n      overflow: 'hidden',\n      pointerEvents,\n      ...style\n    },\n    ...props,\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      },\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          display: 'block'\n        },\n        children: fallback\n      })\n    })\n  });\n});\n\n/**\r\n * A DOM canvas which accepts threejs elements as children.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/canvas\r\n */\nconst Canvas = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(function CanvasWrapper(props, ref) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(its_fine__WEBPACK_IMPORTED_MODULE_7__.FiberProvider, {\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(CanvasImpl, {\n      ...props,\n      ref: ref\n    })\n  });\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\n");

/***/ })

};
;