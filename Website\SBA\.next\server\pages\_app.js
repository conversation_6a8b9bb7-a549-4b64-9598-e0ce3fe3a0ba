"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ pageWrapperTemplate),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps)\n/* harmony export */ });\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @sentry/nextjs */ \"@sentry/nextjs\");\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @clerk/nextjs */ \"@clerk/nextjs\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nfunction MyApp({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__.ClerkProvider, {\n        ...pageProps,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\pages\\\\_app.tsx\",\n            lineNumber: 8,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\pages\\\\_app.tsx\",\n        lineNumber: 7,\n        columnNumber: 9\n    }, this);\n}\n\nvar serverComponentModule = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    default: MyApp\n});\n\n/*\n * This file is a template for the code which will be substituted when our webpack loader handles non-API files in the\n * `pages/` directory.\n *\n * We use `__SENTRY_WRAPPING_TARGET_FILE__.cjs` as a placeholder for the path to the file being wrapped. Because it's not a real package,\n * this causes both TS and ESLint to complain, hence the pragma comments below.\n */\n\n\nconst userPageModule = serverComponentModule ;\n\nconst pageComponent = userPageModule ? userPageModule.default : undefined;\n\nconst origGetInitialProps = pageComponent ? pageComponent.getInitialProps : undefined;\nconst origGetStaticProps = userPageModule ? userPageModule.getStaticProps : undefined;\nconst origGetServerSideProps = userPageModule ? userPageModule.getServerSideProps : undefined;\n\n// Rollup will aggressively tree-shake what it perceives to be unused properties\n// on objects. Because the key that's used to index into this object (/_app)\n// is replaced during bundling, Rollup can't see that these properties are in fact\n// used. Using `Object.freeze` signals to Rollup that it should not tree-shake\n// this object.\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst getInitialPropsWrappers = Object.freeze({\n  '/_app': _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapAppGetInitialPropsWithSentry,\n  '/_document': _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapDocumentGetInitialPropsWithSentry,\n  '/_error': _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapErrorGetInitialPropsWithSentry,\n});\n\nconst getInitialPropsWrapper = getInitialPropsWrappers['/_app'] || _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapGetInitialPropsWithSentry;\n\nif (pageComponent && typeof origGetInitialProps === 'function') {\n  pageComponent.getInitialProps = getInitialPropsWrapper(origGetInitialProps) ;\n}\n\nconst getStaticProps =\n  typeof origGetStaticProps === 'function'\n    ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapGetStaticPropsWithSentry(origGetStaticProps, '/_app')\n    : undefined;\nconst getServerSideProps =\n  typeof origGetServerSideProps === 'function'\n    ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapGetServerSidePropsWithSentry(origGetServerSideProps, '/_app')\n    : undefined;\n\nconst pageWrapperTemplate = pageComponent ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapPageComponentWithSentry(pageComponent ) : pageComponent;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "@clerk/nextjs":
/*!********************************!*\
  !*** external "@clerk/nextjs" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("@clerk/nextjs");

/***/ }),

/***/ "@sentry/nextjs":
/*!*********************************!*\
  !*** external "@sentry/nextjs" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@sentry/nextjs");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_app.tsx"));
module.exports = __webpack_exports__;

})();