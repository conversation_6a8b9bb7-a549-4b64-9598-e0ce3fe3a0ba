"use client";

import { useAuth, useSignIn } from '@clerk/nextjs';
import { motion } from 'framer-motion';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { FiMail, <PERSON>Lock, <PERSON>Eye, FiEyeOff, FiArrowRight, FiAlertCircle, FiRefreshCw } from 'react-icons/fi';

export default function ForgotPasswordPage() {
    const searchParams = useSearchParams();
    const urlCode = searchParams?.get('code');
    const urlEmail = searchParams?.get('email');

    const [email, setEmail] = useState(urlEmail || '');
    const [password, setPassword] = useState('');
    const [code, setCode] = useState(urlCode || '');
    const [successfulCreation, setSuccessfulCreation] = useState(!!urlCode && !!urlEmail);
    const [, setSecondFactor] = useState(false);
    const [error, setError] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [confirmPassword, setConfirmPassword] = useState('');
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [passwordMatch, setPasswordMatch] = useState(true);
    // New state variables for resend functionality
    const [timeLeft, setTimeLeft] = useState(60);
    const [canResend, setCanResend] = useState(false);
    const [isResending, setIsResending] = useState(false);

    const router = useRouter();
    const { isSignedIn } = useAuth();
    const { isLoaded, signIn, setActive } = useSignIn();

    // Check password match
    useEffect(() => {
        if (confirmPassword && password) {
            setPasswordMatch(password === confirmPassword);
        }
    }, [password, confirmPassword]);

    // Countdown timer for resend functionality
    useEffect(() => {
        if (successfulCreation && !canResend && timeLeft > 0) {
            const timer = setTimeout(() => {
                setTimeLeft(timeLeft - 1);
            }, 1000);

            return () => clearTimeout(timer);
        } else if (timeLeft === 0) {
            setCanResend(true);
        }
    }, [timeLeft, canResend, successfulCreation]);

    // Redirect if user is already signed in
    useEffect(() => {
        if (isSignedIn) {
            router.push('/dashboard');
        }
    }, [isSignedIn, router]);

    async function create(e: React.FormEvent) {
        e.preventDefault();
        setIsLoading(true);

        try {
            await signIn?.create({
                strategy: 'reset_password_email_code',
                identifier: email,
            });

            setSuccessfulCreation(true);
            setError('');
            // Initialize timer when code is sent
            setTimeLeft(60);
            setCanResend(false);
        } catch (err: any) {
            console.error('error', err.errors?.[0]?.longMessage || err.message);
            setError(err.errors?.[0]?.longMessage || "An error occurred");
        } finally {
            setIsLoading(false);
        }
    }

    // New function to handle resending the verification code
    async function handleResendCode() {
        if (!canResend || !email) return;

        setIsResending(true);

        try {
            await signIn?.create({
                strategy: 'reset_password_email_code',
                identifier: email,
            });

            // Reset timer after successful resend
            setTimeLeft(60);
            setCanResend(false);
            setError('');
        } catch (err: any) {
            console.error('Resend error:', err.errors?.[0]?.longMessage || err.message);
            setError(err.errors?.[0]?.longMessage || "Failed to resend verification code");
        } finally {
            setIsResending(false);
        }
    }

    async function reset(e: React.FormEvent) {
        e.preventDefault();

        if (password !== confirmPassword) {
            setError("Passwords do not match");
            return;
        }

        setIsLoading(true);

        try {
            const result = await signIn?.attemptFirstFactor({
                strategy: 'reset_password_email_code',
                code,
                password,
            });

            if (result?.status === 'needs_second_factor') {
                setSecondFactor(true);
                setError('');
            } else if (result?.status === 'complete') {
                setActive?.({ session: result.createdSessionId });
                router.push('/dashboard');
            } else {
                console.log(result);
            }
        } catch (err: any) {
            console.error('error', err.errors?.[0]?.longMessage || err.message);
            setError(err.errors?.[0]?.longMessage || "An error occurred");
        } finally {
            setIsLoading(false);
        }
    }

    if (!isLoaded) {
        return (
            <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-900">
                <div className="size-16 animate-spin rounded-full border-4 border-blue-600 border-t-transparent"></div>
            </div>
        );
    }

    return (
        <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-900 px-4 text-white">
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="relative w-full max-w-md overflow-hidden"
            >
                {/* Abstract design elements */}
                <div className="absolute right-0 top-0 -z-10 size-64 -translate-y-1/2 translate-x-1/2 rounded-full bg-blue-500/10 blur-3xl"></div>
                <div className="bg-purple-500/10 absolute bottom-0 left-0 -z-10 size-64 -translate-x-1/2 translate-y-1/2 rounded-full blur-3xl"></div>

                <div className="overflow-hidden rounded-2xl border border-gray-800 bg-black/60 shadow-2xl backdrop-blur-sm">
                    <div className="p-8">
                        <div className="mb-8 text-center">
                            <h2 className="mb-2 text-2xl font-bold text-white">
                                {!successfulCreation ? 'Forgot Password' : 'Reset Password'}
                            </h2>
                            <p className="text-sm text-gray-400">
                                {!successfulCreation
                                    ? "Enter your email address to receive a reset code"
                                    : "Enter your new password and the code we sent to your email"}
                            </p>
                        </div>

                        <form onSubmit={!successfulCreation ? create : reset} className="space-y-5">
                            {!successfulCreation ? (
                                <div className="space-y-5">
                                    <div className="relative">
                                        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">
                                            <FiMail />
                                        </div>
                                        <input
                                            id="email"
                                            type="email"
                                            placeholder="Email address"
                                            value={email}
                                            onChange={(e) => setEmail(e.target.value)}
                                            className="w-full rounded-lg border border-gray-700 bg-gray-800/80 py-3 pl-10 pr-4 text-white transition duration-200 placeholder:text-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                                            required
                                        />
                                    </div>

                                    <button
                                        type="submit"
                                        disabled={isLoading}
                                        className="flex w-full items-center justify-center gap-2 rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 px-4 py-3 font-medium text-white transition duration-200 hover:-translate-y-px hover:from-blue-700 hover:to-indigo-700 disabled:transform-none disabled:cursor-not-allowed disabled:opacity-60"
                                    >
                                        {isLoading ? (
                                            <>
                                                <svg className="-ml-1 mr-2 size-4 animate-spin text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                                Sending code...
                                            </>
                                        ) : (
                                            <>
                                                Send reset code <FiArrowRight className="ml-1" />
                                            </>
                                        )}
                                    </button>
                                </div>
                            ) : (
                                <div className="space-y-5">
                                    <div className="relative">
                                        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">
                                            <FiLock />
                                        </div>
                                        <input
                                            id="password"
                                            type={showPassword ? "text" : "password"}
                                            placeholder="New password"
                                            value={password}
                                            onChange={(e) => setPassword(e.target.value)}
                                            className="w-full rounded-lg border border-gray-700 bg-gray-800/80 py-3 pl-10 pr-12 text-white transition duration-200 placeholder:text-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                                            required
                                        />
                                        <button
                                            type="button"
                                            onClick={() => setShowPassword(!showPassword)}
                                            className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 transition duration-200 hover:text-gray-200"
                                        >
                                            {showPassword ? <FiEyeOff size={18} /> : <FiEye size={18} />}
                                        </button>
                                    </div>

                                    <div className="relative">
                                        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">
                                            <FiLock />
                                        </div>
                                        <input
                                            id="confirmPassword"
                                            type={showConfirmPassword ? "text" : "password"}
                                            placeholder="Confirm new password"
                                            value={confirmPassword}
                                            onChange={(e) => setConfirmPassword(e.target.value)}
                                            className={`w-full border bg-gray-800/80 py-3 pl-10 pr-12 ${confirmPassword && !passwordMatch ? 'border-red-500' : 'border-gray-700'} rounded-lg text-white transition duration-200 placeholder:text-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500`}
                                            required
                                        />
                                        <button
                                            type="button"
                                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                            className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 transition duration-200 hover:text-gray-200"
                                        >
                                            {showConfirmPassword ? <FiEyeOff size={18} /> : <FiEye size={18} />}
                                        </button>
                                    </div>

                                    {confirmPassword && !passwordMatch && (
                                        <p className="flex items-center text-sm text-red-500">
                                            <FiAlertCircle className="mr-1" /> Passwords do not match
                                        </p>
                                    )}

                                    <div className="relative">
                                        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="size-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M10 2a5 5 0 00-5 5v2a2 2 0 00-2 2v5a2 2 0 002 2h10a2 2 0 002-2v-5a2 2 0 00-2-2H7V7a3 3 0 016 0v2h2V7a5 5 0 00-5-5z" />
                                            </svg>
                                        </div>
                                        <input
                                            id="code"
                                            type="text"
                                            placeholder="Reset code"
                                            value={code}
                                            onChange={(e) => setCode(e.target.value)}
                                            className="w-full rounded-lg border border-gray-700 bg-gray-800/80 py-3 pl-10 pr-4 text-white transition duration-200 placeholder:text-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                                            required
                                        />
                                    </div>

                                    {/* Resend code button with timer */}
                                    <div className="mt-2 flex justify-center">
                                        <button
                                            type="button"
                                            onClick={handleResendCode}
                                            disabled={isResending || !canResend}
                                            className={`flex items-center justify-center gap-2 rounded-full px-4 py-2 ${canResend
                                                ? "text-blue-400 hover:bg-blue-500/10 hover:text-blue-300"
                                                : "cursor-not-allowed text-gray-500"
                                                } border transition-all duration-200 ${canResend ? "border-blue-500/30" : "border-gray-700"}`}
                                        >
                                            {isResending ? (
                                                <>
                                                    <svg className="size-4 animate-spin text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                    </svg>
                                                    Sending...
                                                </>
                                            ) : canResend ? (
                                                <>
                                                    <FiRefreshCw className="size-4" />
                                                    Resend Code
                                                </>
                                            ) : (
                                                <>
                                                    <FiRefreshCw className="size-4" />
                                                    Resend in {timeLeft}s
                                                </>
                                            )}
                                        </button>
                                    </div>

                                    <button
                                        type="submit"
                                        disabled={isLoading || !passwordMatch}
                                        className="flex w-full items-center justify-center gap-2 rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 px-4 py-3 font-medium text-white transition duration-200 hover:-translate-y-px hover:from-blue-700 hover:to-indigo-700 disabled:transform-none disabled:cursor-not-allowed disabled:opacity-60"
                                    >
                                        {isLoading ? (
                                            <>
                                                <svg className="-ml-1 mr-2 size-4 animate-spin text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                </svg>
                                                Resetting password...
                                            </>
                                        ) : (
                                            <>
                                                Reset Password <FiArrowRight className="ml-1" />
                                            </>
                                        )}
                                    </button>
                                </div>
                            )}

                            {error && (
                                <motion.div
                                    initial={{ opacity: 0, y: -10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className="mt-4 flex items-start gap-2 rounded-lg border border-red-500/50 bg-red-500/20 p-3 text-sm text-red-200"
                                >
                                    <FiAlertCircle className="mt-0.5 shrink-0" />
                                    <span>{error}</span>
                                </motion.div>
                            )}
                        </form>

                        <div className="mt-6 border-t border-gray-800 pt-4 text-center">
                            <p className="text-sm text-gray-400">
                                Remember your password?{" "}
                                <a href="/login" className="text-blue-400 transition duration-200 hover:text-blue-300 hover:underline">
                                    Sign in
                                </a>
                            </p>
                        </div>
                    </div>
                </div>

                <p className="mt-6 text-center text-xs text-gray-500">
                    © {new Date().getFullYear()} Interview Cracker. All rights reserved.
                </p>
            </motion.div>
        </div>
    );
}