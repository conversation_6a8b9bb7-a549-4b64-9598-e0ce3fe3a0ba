// API utilities for consistent fetch behavior across the application

/**
 * Wrapper for fetch that ensures API calls are properly routed to the App Router implementation
 * This helps avoid 404 errors when mixing Pages Router and App Router
 */
export async function apiFetch(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  // Add custom header to ensure the App Router handles this request
  const headers = {
    ...options.headers,
    'x-invoke-app-router': 'true',
    'Content-Type': 'application/json',
  };

  // Add random query parameter to bypass cache and ensure fresh requests
  const urlWithNoCacheParam = `${url}${url.includes('?') ? '&' : '?'}_nc=${Date.now()}`;
  
  // Add credentials to include cookies (important for Clerk sessions)
  return fetch(urlWithNoCacheParam, {
    ...options,
    headers,
    credentials: 'include' // This ensures cookies are sent with the request
  });
}