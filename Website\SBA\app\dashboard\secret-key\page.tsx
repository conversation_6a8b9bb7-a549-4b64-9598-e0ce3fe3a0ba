"use client"

import { useUser } from "@clerk/nextjs";
import { But<PERSON> } from "@nextui-org/react";
import { Key, Copy, RefreshCcw, Shield } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { toast } from "sonner";

import { apiFetch } from "@/lib/api-utils"; // Import our utility

export default function SecretKeyPage() {
    const { user, isLoaded } = useUser();
    const router = useRouter();
    const [isLoading, setIsLoading] = useState(true);
    const [isGenerating, setIsGenerating] = useState(false);
    const [secretKey, setSecretKey] = useState<string>("");
    const [showKey, setShowKey] = useState(false);
    const [showInstructions, setShowInstructions] = useState(false);

    useEffect(() => {
        if (isLoaded) {
            if (!user) {
                router.push("/login");
            } else {
                fetchSecretKey();
            }
        }
    }, [isLoaded, user, router]);

    const fetchSecretKey = async () => {
        try {
            // Use apiFetch instead of fetch
            const response = await apiFetch("/api/user/api-key");
            if (response.ok) {
                const data = await response.json();
                setSecretKey(data.apiKey || "");
            } else {
                toast.error("Failed to fetch secret key");
            }
        } catch (error) {
            console.error("Error fetching secret key:", error);
            toast.error("An error occurred while fetching secret key");
        } finally {
            setIsLoading(false);
        }
    };

    const generateNewKey = async () => {
        try {
            setIsGenerating(true);
            // Use apiFetch instead of fetch
            const response = await apiFetch("/api/user/generate-api-key", {
                method: "POST",
            });
            if (response.ok) {
                const data = await response.json();
                setSecretKey(data.secretKey || data.apiKey);
                toast.success("New secret key generated successfully");
            } else {
                toast.error("Failed to generate new secret key");
            }
        } catch (error) {
            console.error("Error generating secret key:", error);
            toast.error("An error occurred while generating new secret key");
        } finally {
            setIsGenerating(false);
        }
    };

    const copyToClipboard = async () => {
        if (!secretKey) {
            toast.error("No secret key available to copy");
            return;
        }

        try {
            await navigator.clipboard.writeText(secretKey);
            toast.success("Secret key copied to clipboard");
        } catch (error) {
            console.error("Error copying to clipboard:", error);
            // Fallback method for browsers that don't support clipboard API
            const textArea = document.createElement("textarea");
            textArea.value = secretKey;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand("copy");
                toast.success("Secret key copied to clipboard");
            } catch (fallbackError) {
                toast.error("Failed to copy secret key");
            }
            document.body.removeChild(textArea);
        }
    };

    if (!isLoaded || isLoading) {
        return (
            <div className="relative flex min-h-screen w-full items-center justify-center bg-white bg-dot-black/[0.05] dark:bg-black dark:bg-dot-white/[0.2]">
                <div className="pointer-events-none absolute inset-0 flex items-center justify-center bg-white [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)] dark:bg-black"></div>
                <div className="relative z-10 text-center">
                    <div className="relative inline-flex items-center justify-center">
                        <div className="size-16 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                        <div className="absolute flex space-x-2">
                            <div className="size-2 animate-[pulse_1.5s_ease-in-out_infinite_0.2s] rounded-full bg-primary"></div>
                            <div className="size-2 animate-[pulse_1.5s_ease-in-out_infinite_0.4s] rounded-full bg-primary"></div>
                            <div className="size-2 animate-[pulse_1.5s_ease-in-out_infinite_0.6s] rounded-full bg-primary"></div>
                        </div>
                    </div>
                    <div className="mt-6">
                        <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                            Loading Secret Key
                        </h3>
                        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                            Please wait while we fetch your credentials
                        </p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="relative min-h-screen w-full bg-white bg-dot-black/[0.05] dark:bg-black dark:bg-dot-white/[0.2]">
            <div className="pointer-events-none absolute inset-0 flex items-center justify-center bg-white [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)] dark:bg-black"></div>

            <div className="container relative z-10 mx-auto px-4 py-16">
                <div className="mx-auto max-w-4xl">
                    <div className="mb-12 text-center">
                        <h1 className="bg-gradient-to-r from-primary via-red-500 to-secondary bg-clip-text text-4xl font-bold text-transparent md:text-5xl">
                            Secret Key Management
                        </h1>
                        <p className="mt-4 text-gray-400">
                            Manage your secret key for accessing our desktop application
                        </p>
                    </div>

                    <div className="mb-12 grid gap-8 md:grid-cols-2">
                        {/* Security Info Card */}
                        <div className="group relative">
                            <div className="absolute -inset-0.5 rounded-2xl bg-gradient-to-r from-primary to-secondary opacity-30 blur transition duration-300 group-hover:opacity-50"></div>
                            <div className="relative h-full rounded-2xl border border-gray-800/50 bg-black/40 p-8 backdrop-blur-xl">
                                <div className="mb-6 flex items-center gap-4">
                                    <div className="rounded-xl bg-primary/10 p-3">
                                        <Shield className="size-6 text-primary" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-white">Security Notice</h3>
                                </div>
                                <ul className="space-y-4 text-gray-400">
                                    <li className="flex items-start gap-2">
                                        <span className="mt-1 block size-1.5 rounded-full bg-primary"></span>
                                        <span>Keep your secret key secure and never share it with anyone</span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span className="mt-1 block size-1.5 rounded-full bg-primary"></span>
                                        <span>Regenerate your key immediately if you suspect it&apos;s been compromised</span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span className="mt-1 block size-1.5 rounded-full bg-primary"></span>
                                        <span>If you&apos;re logged out from the desktop app, generate a new key and use it to log in</span>
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <span className="mt-1 block size-1.5 rounded-full bg-primary"></span>
                                        <span>Always use secure methods to store and transmit your secret key</span>
                                    </li>
                                </ul>

                                <div className="mt-6">
                                    <Button
                                        className="w-full bg-gray-800/50 text-white transition-colors hover:bg-gray-700/70"
                                        onClick={() => setShowInstructions(!showInstructions)}
                                    >
                                        {showInstructions ? "Hide Desktop App Instructions" : "Show Desktop App Instructions"}
                                    </Button>

                                    {showInstructions && (
                                        <div className="mt-4 rounded-xl border border-gray-800/50 bg-black/30 p-4">
                                            <h4 className="mb-2 font-medium text-white">Desktop Application Login</h4>
                                            <ol className="list-decimal space-y-2 pl-5 text-gray-400">
                                                <li>Generate a new secret key using the button on the right</li>
                                                <li>Copy the key using the copy button</li>
                                                <li>Open the desktop application</li>
                                                <li>Enter your email address and paste the secret key</li>
                                                <li>Click &quot;Sign In&quot; to authenticate</li>
                                            </ol>
                                            <p className="mt-3 text-sm text-amber-400">Note: If you&apos;re experiencing login issues with the desktop app, try generating a new key and logging in again.</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Secret Key Card */}
                        <div className="group relative">
                            <div className="absolute -inset-0.5 rounded-2xl bg-gradient-to-r from-primary to-secondary opacity-30 blur transition duration-300 group-hover:opacity-50"></div>
                            <div className="relative h-full rounded-2xl border border-gray-800/50 bg-black/40 p-8 backdrop-blur-xl">
                                <div className="mb-6 flex items-center gap-4">
                                    <div className="rounded-xl bg-primary/10 p-3">
                                        <Key className="size-6 text-primary" />
                                    </div>
                                    <h3 className="text-xl font-semibold text-white">Your Secret Key</h3>
                                </div>

                                <div className="space-y-6">
                                    <div className="relative">
                                        <div className="flex items-center gap-3 rounded-xl border border-gray-800/50 bg-black/40 p-4">
                                            <code className="flex-1 overflow-x-auto font-mono text-gray-300">
                                                {showKey ? secretKey : '••••••••••••••••••••••'}
                                            </code>
                                            <button
                                                onClick={() => setShowKey(!showKey)}
                                                className="text-gray-400 transition-colors hover:text-white"
                                                title={showKey ? "Hide key" : "Show key"}
                                            >
                                                <Key className="size-4" />
                                            </button>
                                            <button
                                                onClick={copyToClipboard}
                                                className="text-gray-400 transition-colors hover:text-white"
                                                title="Copy to clipboard"
                                            >
                                                <Copy className="size-4" />
                                            </button>
                                        </div>
                                    </div>

                                    <Button
                                        className="w-full rounded-xl bg-gradient-to-r from-primary to-secondary py-6 font-medium text-white shadow-lg transition-all duration-200 hover:opacity-90"
                                        onClick={generateNewKey}
                                        isLoading={isGenerating}
                                        startContent={!isGenerating && <RefreshCcw className="size-4" />}
                                    >
                                        {isGenerating ? "Generating..." : "Generate New Key for Desktop App"}
                                    </Button>

                                    <p className="text-center text-sm text-gray-400">
                                        Use this key to log in to the desktop application.
                                        <br />
                                        Generating a new key will invalidate your old key.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}