import { gridItems } from '@/data';

import { BentoGrid, BentoGridItem } from './ui/BentoGrid';

const Grid = () => {
  return (
    <section id='about'>
      <BentoGrid className='mb-0 mt-20 w-full py-20'>
        {gridItems.map((item, i) => (
          <BentoGridItem
            id={item.id}
            key={i}
            title={item.title}
            description={item.description}
            className={item.className}
            img={item.img}
            imgClassName={item.imgClassName}
            titleClassName={item.titleClassName}
            spareImg={item.spareImg}
            type={item.type}
            videoOptions={item.videoOptions}
          />
        ))}
      </BentoGrid>
    </section>
  );
};

export default Grid;
