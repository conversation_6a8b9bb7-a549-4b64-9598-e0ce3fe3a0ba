import crypto from "crypto";

import bcrypt from "bcryptjs";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

import { sendVerificationCode } from "@/lib/email";
import connectDB from "@/lib/mongodb";
import { RegisterFormValidation } from "@/lib/validation"; // Adjust the import path
import { PendingUser } from "@/model/PendingUser";
import { User } from "@/model/User";



export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "POST") return res.status(405).json({ message: "Method not allowed" });

  await connectDB();
  const { name, email, password } = req.body;

  try {
    RegisterFormValidation.parse({ name, email, password });
  } catch (error) {
    if (error instanceof z.ZodError) return res.status(400).json({ message: error.errors[0].message });
    return res.status(500).json({ message: "Internal server error" });
  }

  const existingUser = await User.findOne({ email });
  if (existingUser) return res.status(400).json({ message: "User already exists" });

  const existingPendingUser = await PendingUser.findOne({ email });
  if (existingPendingUser) await PendingUser.deleteOne({ email });

  const verificationCode = crypto.randomBytes(4).toString("hex").toUpperCase();
  const verificationCodeExpires = new Date(Date.now() + 15 * 60 * 1000);
  const hashedPassword = await bcrypt.hash(password, 10);

  const newPendingUser = new PendingUser({
    name,
    email,
    password: hashedPassword,
    verificationCode,
    verificationCodeExpires,
  });

  await newPendingUser.save();
  await sendVerificationCode(email, verificationCode);

  return res.status(200).json({ message: "Verification code sent. Please check your email." });
}