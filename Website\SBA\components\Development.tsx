/* eslint-disable camelcase */
import { Playfair_Display, Bitter } from 'next/font/google';
import React from 'react';


// eslint-disable-next-line no-unused-vars
const playfair = Playfair_Display({ subsets: ['latin'] });
const bitter = Bitter({ subsets: ['latin'] });

const Development = () => {
  const DevelopmentData = [
    {
      id: 1,
      title: 'Real-Time Code Assistance',
      imgSrc: '/d1.svg', // Placeholder for image
    },
    {
      id: 2,
      title: 'Completely Safe to use',
      imgSrc: '/d2.svg', // Placeholder for image
    },
    {
      id: 3,
      title: 'Discreet Interview Support',
      imgSrc: '/d3.svg', // Placeholder for image
    },
    {
      id: 4,
      title: 'Ace Your Interviews',
      imgSrc: '/d4.svg', // Placeholder for image
    },
  ];
  return (
    <section className='relative my-16 flex	w-full  items-center justify-center rounded-xl border-[0.5px] border-[#353535] bg-black  px-4 py-7 md:border-[#1f1f1f] md:px-7 md:py-14 lg:my-20 2xl:my-32'>
      <div className='w-full max-w-7xl'>
        <div className='flex flex-col items-center justify-center'>
          <h2
            className={` ${bitter.className} mt-4 text-center text-3xl font-normal sm:mt-0 md:text-left md:text-4xl xl:text-5xl`}
          >
            Why Choose <span className='font-medium text-red-400'>Us</span>?
          </h2>
          <div className='mb-5 mt-12 flex select-none flex-col justify-around gap-7 sm:mt-16 md:flex-row md:gap-5 lg:gap-10 lg:px-3 '>
            {DevelopmentData.map((item) => (
              <div
                key={item.id}
                className='flex flex-col items-center justify-center gap-5 rounded-xl border-[0.5px] border-[#353535] bg-zinc-950 p-7 text-left	transition-all hover:border-collapse hover:cursor-pointer hover:bg-[#1F1F1F] active:border-collapse active:bg-[#1F1F1F] md:gap-10 md:border-[#1f1f1f]'
              >
                <img
                  src={`${item.imgSrc}`}
                  alt=''
                  className='pointer-events-none w-16 select-none lg:w-24'
                />
                <h3 className='text-center text-lg capitalize lg:text-xl'>
                  {item.title}
                </h3>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Development;
