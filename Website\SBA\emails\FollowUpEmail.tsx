import {
  Body,
  Container,
  Head,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
  Hr,
} from '@react-email/components';
import * as React from 'react';

interface FollowUpEmailProps {
  name: string;
  time: string;
  message: string;
  type: string;
}

export const FollowUpEmail = ({
  name,
  time,
  message,
  type,
}: FollowUpEmailProps) => (
  <Html>
    <Head />
    <Preview>{`Confirmation: Your Submission Has Been Received`}</Preview>
    <Body style={main}>
      <Container style={container}>
        <Section style={header}>
          <Link href='https://interviewcracker.in/'>
            <Img
              src='https://res.cloudinary.com/dtemmbo4i/image/upload/v1741707548/logo_ngsuv8.png'
              width='50'
              height='70'
              alt='Interview Cracker Logo'
              style={logo}
            />
          </Link>
        </Section>
        <Text style={title}>
          Dear {name},
        </Text>
        <Section style={detailsSection}>
          <Text style={text}>
            Thank you for reaching out to Interview Cracker. We are pleased to confirm that we have successfully received your submission on {time}.
          </Text>
          <Text style={text}>
            Our team is currently reviewing your request, and we will respond to you as soon as possible. Should we require any additional information, we will contact you promptly.
          </Text>
          {type !== 'success' && (
            <Text style={text}>
              Cancellation Reason: <strong>{message}</strong>
            </Text>
          )}
        </Section>
        <Hr style={hr} />
        <Text style={links}>
          For more information, please visit our{' '}
          <Link href='https://interviewcracker.in/' style={link}>
            website
          </Link>.
        </Text>
        <Text style={footer}>
          Best regards,<br />
          The Interview Cracker Team<br />
          Bangalore, India
        </Text>
      </Container>
    </Body>
  </Html>
);

FollowUpEmail.PreviewProps = {} as FollowUpEmailProps;

export default FollowUpEmail;

// Styles
const main = {
  backgroundColor: '#f5f7fa',
  color: '#1a2b49',
  fontFamily: '"Helvetica Neue", Arial, sans-serif',
  padding: '40px 0',
};

const container = {
  maxWidth: '600px',
  margin: '0 auto',
  padding: '30px',
  backgroundColor: '#ffffff',
  borderRadius: '8px',
  border: '1px solid #e2e8f0',
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
};

const detailsSection = {
  textAlign: 'left' as const,
  margin: '25px 0',
};

const header = {
  padding: '20px 0',
  textAlign: 'center' as const,
};

const logo = {
  width: '50px',
  height: '70px',
};

const title = {
  fontSize: '26px',
  fontWeight: '600' as const,
  lineHeight: '1.3',
  color: '#2d3748',
  marginBottom: '20px',
};

const text = {
  margin: '0 0 20px 0',
  textAlign: 'left' as const,
  fontSize: '16px',
  lineHeight: '1.6',
  color: '#4a5568',
};

const hr = {
  border: 'none',
  borderTop: '1px solid #e2e8f0',
  margin: '30px 0',
};

const links = {
  textAlign: 'center' as const,
  margin: '20px 0',
  fontSize: '14px',
  color: '#718096',
};

const link = {
  color: '#3182ce',
  fontSize: '14px',
  textDecoration: 'underline',
  fontWeight: '500' as const,
};

const footer = {
  color: '#718096',
  fontSize: '13px',
  textAlign: 'left' as const,
  marginTop: '30px',
  lineHeight: '1.6',
};