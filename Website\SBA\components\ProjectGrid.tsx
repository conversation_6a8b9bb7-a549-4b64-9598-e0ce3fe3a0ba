'use client';
import Image from 'next/image';
import React, { useState } from 'react';

import { ProjectData } from '@/data/index';

import { BreadcrumbWithCustomSeparator } from './OurServices';

const ProjectGrid = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentImage, setCurrentImage] = useState('');

  const openModal = (imgSrc) => {
    setCurrentImage(imgSrc);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setCurrentImage('');
  };

  return (
    <div className='mb-10 px-8 xl:max-w-5xl 2xl:max-w-6xl'>
      <BreadcrumbWithCustomSeparator currentPage='Recent Projects' />
      <div className='mt-10 grid grid-cols-1 gap-7 sm:grid-cols-2 md:grid-cols-3'>
        {ProjectData.map((project) => (
          <div
            key={project.id}
            className='cursor-pointer snap-y rounded-lg border-[0.5px] border-[#353535] bg-black pb-3 transition-all hover:bg-[#141414] active:bg-[#1F1F1F] md:border-[#1f1f1f]'
          >
            <div className='flex size-full flex-col justify-between gap-8'>
              <div>
                <div
                  className='relative h-48 w-full overflow-hidden rounded-t-lg xl:h-36 2xl:h-44'
                  onClick={() => openModal(project.imgSrc)} // Open modal on click
                >
                  <Image
                    src={project.imgSrc}
                    alt={project.title}
                    className='pointer-events-none absolute size-full select-none rounded-t-lg object-cover'
                    width={1000}
                    height={600}
                    loading='eager'
                    sizes='(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
                  />
                </div>
                <h1 className='mt-3 px-3 text-lg lg:text-xl'>
                  {project.title}
                </h1>
                <div className='mb-5 mt-2 flex flex-wrap gap-2 px-3 text-sm font-semibold'>
                  <div className='rounded-md bg-blue-900/50 px-2 py-1 text-blue-400'>
                    {project.loc}
                  </div>
                  <div className='rounded-md bg-green-900/60 px-2 py-1 text-green-500'>
                    {project.type}
                  </div>
                </div>
                <p className='my-3 px-3 text-sm text-neutral-300'>
                  {project.description}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Modal for Fullscreen Image */}
      {isModalOpen && (
        <div
          className='fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80'
          onClick={closeModal} // Close modal on backdrop click
        >
          <div className='relative'>
            <Image
              src={currentImage}
              alt='Full Screen Image'
              className='pointer-events-none select-none rounded-lg'
              width={1000}
              height={600}
              quality={100}
            />
            <button
              className='absolute right-3 top-3 rounded-full bg-white px-3 py-2 font-black text-black'
              onClick={closeModal} // Close modal on button click
            >
              ✕
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProjectGrid;
