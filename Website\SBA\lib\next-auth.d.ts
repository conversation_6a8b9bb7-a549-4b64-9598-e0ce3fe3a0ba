/* eslint-disable no-unused-vars */
// lib/types/next-auth.d.ts
import { DefaultSession, DefaultUser } from "next-auth";

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      provider?: string;
      email?: string;
      subscriptionEndDate?: string | Date;
      subscriptionType?: string;
      subscriptionPlan?: string;
      promptsUsed?: number;
      isAdmin?: boolean; // Add isAdmin
    } & DefaultSession["user"];
  }

  interface User extends DefaultUser {
    subscriptionEndDate?: string | Date;
    subscriptionType?: string;
    subscriptionPlan?: string;
    promptsUsed?: number;
    isAdmin?: boolean; // Add isAdmin
  }
}