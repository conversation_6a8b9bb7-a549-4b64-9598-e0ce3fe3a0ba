'use client';
import <PERSON><PERSON> from 'lottie-react';
import { Check } from 'lucide-react';
import dynamic from 'next/dynamic';
// eslint-disable-next-line camelcase
import { Playfair_Display } from 'next/font/google';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { FaLocationArrow } from 'react-icons/fa6';
import { IoCopyOutline } from 'react-icons/io5';

import animationData from '@/data/confetti.json';
import { cn } from '@/lib/utils';

import MagicButton from '../MagicButton';
import NumberTicker from '../magicui/number-ticker';


import { CardSpotlight } from './card-spotlight';
import { BackgroundGradientAnimation } from './GradientBg';

const GridGlobe = dynamic(() => import('./GridGlobe'), { ssr: false });

const playfair = Playfair_Display({ subsets: ['latin'] });

export const BentoGrid = ({
  className,
  children,
}: {
  className?: string;
  children?: React.ReactNode;
}) => {
  return (
    <div
      className={cn(
        'grid grid-cols-1 md:grid-cols-6 lg:grid-cols-5 md:grid-row-7 gap-4 lg:gap-8 mx-auto',
        className
      )}
    >
      {children}
    </div>
  );
};

export const BentoGridItem = ({
  className,
  id,
  title,
  description,
  img,
  imgClassName,
  titleClassName,
  spareImg,
  type,
  videoOptions,
}: {
  className?: string;
  id?: number;
  title?: string | React.ReactNode;
  description?: string | React.ReactNode;
  img?: string;
  imgClassName?: string;
  titleClassName?: string;
  spareImg?: string;
  type?: string;
  videoOptions?: {
    autoPlay?: boolean;
    loop?: boolean;
    muted?: boolean;
    playsInline?: boolean;
  };
}) => {
  const intern = [
    'Short time for code generation',
    'Solving real-time coding challenges',
    'Crack Interview using AI-powered tools',
    'Work on diverse interview scenarios',
    'Crack technical interview strategies',
  ];

  const [copied, setCopied] = useState(false);
  const router = useRouter();

  const handlerAbout = () => router.push('/about');

  const handleCopy = () => {
    try {
      navigator.clipboard.writeText('<EMAIL>');
      setCopied(true);
    } catch (error) {
      console.error('Failed to copy email:', error);
    }
  };

  useEffect(() => {
    if (copied) {
      const timeout = setTimeout(() => setCopied(false), 3000);
      return () => clearTimeout(timeout);
    }
  }, [copied]);

  return (
    <div
      className={cn(
        'relative overflow-hidden bg-black group-hover/bento:bg-white transition-all rounded-xl md:rounded-3xl border-[0.9px] duration-300 border-[#353535] md:border-[#1f1f1f] hover:border-collapse hover:bg-[#131313] hover:cursor-pointer group/bento hover:shadow-xl shadow-input dark:shadow-none justify-between flex flex-col space-y-4',
        className
      )}
    >
      <div className={`${id === 6 ? 'flex justify-center' : ''} h-full`}>
        {img && type === 'video' ? (
          <div className='absolute size-full'>
            <video
              src={img}
              className={cn("w-full h-full object-cover", imgClassName)}
              autoPlay={videoOptions?.autoPlay}
              loop={videoOptions?.loop}
              muted={videoOptions?.muted}
              playsInline={videoOptions?.playsInline}
            />
          </div>
        ) : img ? (
          <div className='absolute size-full'>
            <Image
              src={img}
              alt={typeof title === 'string' ? title : 'Feature image'}
              fill
              className={cn('object-cover opacity-60 hover:opacity-40', imgClassName)}
            />
          </div>
        ) : null}

        {id === 6 && (
          <BackgroundGradientAnimation>
            <div className='pointer-events-none absolute inset-0 z-50 flex items-center justify-center px-4 text-center text-3xl font-bold text-white md:text-4xl lg:text-7xl' />
          </BackgroundGradientAnimation>
        )}

        <div
          className={cn(
            titleClassName,
            `${id === 4 ? '' : id === 5 ? '' : 'group-hover/bento:translate-x-1'} transition duration-200 relative md:h-full min-h-40 flex flex-col`,
            id === 3 ? 'p-3' : id === 4 ? 'p-0' : 'px-5 p-5 lg:p-10'
          )}
        >
          <div className={`absolute -bottom-5 right-0 ${id === 5 ? 'w-full opacity-80' : ''} h-[200px]`}>
            {spareImg && (
              <Image
                src={spareImg}
                alt="Additional feature image"
                fill
                className='size-full object-cover object-center'
                sizes="(max-width: 768px) 50vw, 33vw"
              />
            )}
          </div>

          <div className='z-10 font-sans text-sm font-extralight text-[#C1C2D3] md:max-w-32 md:text-xs lg:text-base'>
            <span>{description}</span>
          </div>

          <div className={`z-10 max-w-96 font-sans text-lg font-bold lg:text-2xl`}>
            {title}
          </div>

          {id === 2 && (
            <div className='translate-y-5 sm:translate-y-0 md:-translate-y-9 xl:-translate-y-4'>
              <GridGlobe />
            </div>
          )}

          {id === 5 && (
            <>
              <div className='my-2.5 flex flex-col gap-1 space-y-2 py-2 sm:py-3'>
                <ul className='list-none space-y-3'>
                  {intern.map((item, i) => (
                    <li
                      className='flex max-w-fit items-center gap-1.5 rounded-lg px-2 text-left text-sm sm:text-base xl:bg-black/70'
                      key={i}
                    >
                      <Check className='size-5 shrink-0' />
                      <span className='text-neutral-300'>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <MagicButton
                title='Start Free Trial'
                icon={<FaLocationArrow />}
                position='right'
                width={true}
                className='md:mt-5'
                handleClick={() => router.push('/dashboard')}
                otherClasses='bg-black'
              />
            </>
          )}

          {id === 3 && (
            <div className='flex flex-col gap-10 py-3'>
              <h2 className={`px-4 text-lg font-bold lg:text-2xl ${playfair.className}`}>
                We Know What We&apos;re Doing
              </h2>
              <div className='flex justify-center gap-5 sm:px-5'>
                <div className='flex flex-col gap-5'>
                  <h2 className='text-3xl font-bold text-green-400 xl:text-4xl'>
                    <NumberTicker value={450} />+
                  </h2>
                  <span className='text-neutral-300'>Lines Code Generated</span>
                </div>
                <div className='flex flex-col gap-5'>
                  <h2 className='text-3xl font-bold text-green-400 xl:text-4xl'>
                    <NumberTicker value={30} />+
                  </h2>
                  <span className='text-neutral-300'>Happy Clients</span>
                </div>
              </div>
            </div>
          )}

          {id === 4 && (
            <CardSpotlight className='rounded-xl md:rounded-3xl'>
              <div className='relative'>
                <div className={`z-10 font-sans text-lg font-bold lg:text-2xl`}>
                  <span className='text-red-400'>Uses OpenAI Latest</span> AI Models For
                  <br />
                  Providing Exceptional Code Generation Within{' '}
                  <span className='text-red-400'>Minutes</span>
                </div>
                <MagicButton
                  handleClick={handlerAbout}
                  title='About Us'
                  icon={<FaLocationArrow />}
                  position='right'
                  width={true}
                  className='mt-7'
                  otherClasses='bg-black'
                />
              </div>
            </CardSpotlight>
          )}

          {id === 6 && (
            <div className='relative mt-5'>
              <div className={`absolute -bottom-5 right-0`}>
                <Lottie
                  animationData={animationData}
                  loop={copied}
                  autoplay={copied}
                  style={{ height: 200, width: 400 }}
                />
              </div>
              <MagicButton
                title={copied ? 'Email Copied!' : 'Contact us'}
                icon={<IoCopyOutline />}
                position='left'
                handleClick={handleCopy}
                width={true}
                otherClasses='!bg-[#161A31]'
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};