/* eslint-disable no-unused-vars */
import crypto from "crypto";

import bcrypt from "bcryptjs";
// eslint-disable-next-line no-unused-vars
import NextAuth, { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";

import connectDB from "@/lib/mongodb";
import { User } from "@/model/User";


// Define custom user types to extend NextAuth types
declare module "next-auth" {
  // eslint-disable-next-line no-unused-vars
  interface User {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    provider?: string;
    subscriptionType?: string;
    subscriptionPlan?: string;
    subscriptionStartDate?: Date;
    subscriptionEndDate?: Date;
    promptsUsed?: number;
    isAdmin?: boolean;
    secretKey?: string;
  }
  
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      provider?: string;
      image?: string | null;
      subscriptionType?: string;
      subscriptionPlan?: string;
      subscriptionStartDate?: Date;
      subscriptionEndDate?: Date;
      promptsUsed?: number;
      isAdmin?: boolean;
      secretKey?: string;
    };
  }
}

// Extend JWT token type
declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    name?: string | null;
    email?: string | null;
    provider?: string;
    image?: string | null;
    subscriptionType?: string;
    subscriptionPlan?: string;
    subscriptionStartDate?: Date;
    subscriptionEndDate?: Date;
    promptsUsed?: number;
    isAdmin?: boolean;
    secretKey?: string;
  }
}

// Helper function to generate consistent secret keys
const generateSecretKey = () => `sk_${crypto.randomBytes(16).toString("hex")}`;

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          access_type: "offline",
          response_type: "code",
        },
      },
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "text" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        await connectDB();
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Please provide both email and password");
        }
        const user = await User.findOne({ email: credentials.email });
        if (!user || user.provider !== "email") {
          throw new Error("Invalid email or password");
        }
        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password || ""
        );
        if (!isPasswordValid) {
          throw new Error("Invalid email or password");
        }
        return {
          id: user._id.toString(),
          name: user.name,
          email: user.email,
          provider: user.provider,
          subscriptionType: user.subscriptionType,
          subscriptionPlan: user.subscriptionPlan,
          subscriptionEndDate: user.subscriptionEndDate,
          promptsUsed: user.promptsUsed,
          image: user.avatar,
          isAdmin: user.isAdmin,
        };
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account }) {
      try {
        if (account?.provider === "google") {
          console.log("Google sign-in attempt for:", user.email);
          
          try {
            await connectDB();
            console.log("MongoDB connected successfully");
          } catch (dbErr) {
            console.error("MongoDB connection failed:", dbErr);
            throw new Error("Database connection error. Please try again later.");
          }
          
          let existingUser;
          try {
            existingUser = await User.findOne({ email: user.email });
            console.log("User lookup result:", existingUser ? "User found" : "User not found");
          } catch (findErr) {
            console.error("Error finding user:", findErr);
            throw new Error("Error verifying account. Please try again.");
          }
          
          if (!existingUser) {
            console.log("Creating new user for:", user.email);
            try {
              const newUser = new User({
                name: user.name!,
                email: user.email!,
                avatar: user.image || "/default-avatar.png",
                provider: "google",
                subscriptionType: "free",
                promptsUsed: 0,
                isAdmin: false,
                secretKey: generateSecretKey(),
              });
              
              await newUser.save();
              console.log("New user created successfully:", newUser._id.toString());
              
              user.id = newUser._id.toString();
              user.subscriptionType = "free";
              user.subscriptionPlan = undefined;
              user.subscriptionEndDate = undefined;
              user.promptsUsed = 0;
              user.image = newUser.avatar;
              user.isAdmin = false;
              user.secretKey = newUser.secretKey;
              user.provider = "google";
            } catch (saveErr) {
              console.error("Error saving new user:", saveErr);
              throw new Error("Could not create account. Please try again.");
            }
          } else {
            // Check if user exists but with a different provider
            if (existingUser.provider && existingUser.provider !== "google") {
              console.log(`User exists with different provider: ${existingUser.provider}`);
              
              // Update the user to also use Google provider
              try {
                existingUser.provider = "google";
                await existingUser.save();
                console.log("Updated user provider to include Google");
              } catch (updateErr) {
                console.error("Error updating user provider:", updateErr);
                // Continue anyway as this is not critical
              }
            }
            
            console.log("Using existing user:", existingUser._id.toString());
            user.id = existingUser._id.toString();
            user.subscriptionType = existingUser.subscriptionType || "free";
            user.subscriptionPlan = existingUser.subscriptionPlan;
            user.subscriptionStartDate = existingUser.subscriptionStartDate;
            user.subscriptionEndDate = existingUser.subscriptionEndDate;
            user.promptsUsed = existingUser.promptsUsed || 0;
            user.image = existingUser.avatar || user.image;
            user.isAdmin = existingUser.isAdmin || false;
            user.secretKey = existingUser.secretKey || generateSecretKey();
            user.provider = "google";
          }
          
          return true;
        }
        
        return true;
      } catch (error: any) {
        console.error("SignIn callback error:", error);
        throw new Error(error.message || "Authentication error. Please try again.");
      }
    },
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.name = user.name;
        token.email = user.email;
        token.provider = user.provider;
        token.image = user.image;
        token.subscriptionType = user.subscriptionType;
        token.subscriptionPlan = user.subscriptionPlan;
        token.subscriptionStartDate = user.subscriptionStartDate;
        token.subscriptionEndDate = user.subscriptionEndDate;
        token.promptsUsed = user.promptsUsed;
        token.isAdmin = user.isAdmin;
        token.secretKey = user.secretKey;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user = {
          id: token.id,
          name: token.name,
          email: token.email,
          provider: token.provider,
          image: token.image,
          subscriptionType: token.subscriptionType,
          subscriptionPlan: token.subscriptionPlan,
          subscriptionStartDate: token.subscriptionStartDate,
          subscriptionEndDate: token.subscriptionEndDate,
          promptsUsed: token.promptsUsed,
          isAdmin: token.isAdmin,
          secretKey: token.secretKey,
        };
      }
      return session;
    },
    // Force redirect to dashboard after successful sign-in
    async redirect({ url, baseUrl }) {
      // If the URL starts with the base URL, it's safe to redirect
      if (url.startsWith(baseUrl)) {
        // Special case: if coming from Google auth, always redirect to dashboard
        if (url.includes('auth/callback/google')) {
          return `${baseUrl}/dashboard`;
        }
        return url;
      }
      
      // Handle callback URLs that include the domain
      if (url.includes('/dashboard')) {
        return `${baseUrl}/dashboard`;
      }
      
      // Default to dashboard instead of home page
      return `${baseUrl}/dashboard`;
    }
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: true,
        domain: process.env.NEXTAUTH_URL?.includes("interviewcracker.in") ? "interviewcracker.in" : undefined,
      },
    },
  },
  pages: { 
    signIn: "/login",
    signOut: "/logout",
    error: "/login" // Redirect to login page when error
  },
  debug: true, // Enable debugging to help troubleshoot
  secret: process.env.NEXTAUTH_SECRET,
};