"use client";
import { useUser } from "@clerk/nextjs";
import { Table, TableHeader, TableColumn, TableBody, TableRow, TableCell, Pagination, Button, Badge } from "@nextui-org/react";
import { format } from "date-fns";
import { useState, useEffect } from "react";
import { FiRefreshCw, FiInfo, FiCreditCard, FiClock, FiFileText } from "react-icons/fi";

import { apiFetch } from "@/lib/api-utils";

// Define a TypeScript interface for a payment record
interface Payment {
    id: string;
    amount: number;
    currency: string;
    status: 'completed' | 'failed' | 'pending' | 'captured' | 'COMPLETED';
    date: string;
    method: string;
    description: string;
    invoice_url?: string;
    plan?: string;
    paymentId?: string;
}

export default function PaymentHistoryPage() {
    const { isLoaded, isSignedIn } = useUser();
    const [payments, setPayments] = useState<Payment[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [page, setPage] = useState(1);
    const rowsPerPage = 5;

    // Fetch payment history when component mounts
    useEffect(() => {
        const fetchPayments = async () => {
            if (!isSignedIn) return;

            setIsLoading(true);
            try {
                const response = await apiFetch('/api/user/payment-history');

                if (response.ok) {
                    const data = await response.json();
                    setPayments(data.payments || []);
                } else {
                    console.error("Failed to fetch payment history");
                }
            } catch (error) {
                console.error("Error fetching payment history:", error);
            } finally {
                setIsLoading(false);
            }
        };

        if (isLoaded && isSignedIn) {
            fetchPayments();
        }
    }, [isLoaded, isSignedIn]);

    // Handle page changes
    const handlePageChange = (newPage: number) => {
        setPage(newPage);
    };

    // Download invoice function

    // Format currency
    const formatCurrency = (amount: number, currency: string) => {
        const formatter = new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency,
        });
        return formatter.format(amount);
    };

    // Format date
    const formatDate = (dateString: string) => {
        try {
            return format(new Date(dateString), 'MMM dd, yyyy');
        } catch (error) {
            return dateString;
        }
    };

    // Get status display with badge
    const getStatusDisplay = (status: string) => {
        if (status === 'completed' || status === 'captured' || status === 'COMPLETED') {
            return (
                <Badge color="success" variant="flat" className="capitalize">
                    Successful
                </Badge>
            );
        } else if (status === 'pending') {
            return (
                <Badge color="warning" variant="flat" className="capitalize">
                    Pending
                </Badge>
            );
        } else {
            return (
                <Badge color="danger" variant="flat" className="capitalize">
                    {status}
                </Badge>
            );
        }
    };

    // Get transaction type
    const getTransactionType = (payment: Payment) => {
        if (payment.method === "cancellation") {
            return "Subscription Cancellation";
        }
        return payment.method.charAt(0).toUpperCase() + payment.method.slice(1);
    };

    // Calculate pagination
    const pages = Math.ceil(payments.length / rowsPerPage);
    const items = payments.slice((page - 1) * rowsPerPage, page * rowsPerPage);

    // Show loading state
    if (!isLoaded || !isSignedIn) {
        return (
            <div className="relative flex min-h-screen w-full items-center justify-center">
                <div className="text-center">
                    <div className="relative inline-flex items-center justify-center">
                        <div className="size-16 animate-spin rounded-full border-4 border-gray-300 border-t-[#24AE7C]"></div>
                        <div className="absolute flex space-x-2">
                            <div className="size-2 animate-[pulse_1.5s_ease-in-out_infinite_0.2s] rounded-full bg-[#24AE7C]"></div>
                            <div className="size-2 animate-[pulse_1.5s_ease-in-out_infinite_0.4s] rounded-full bg-[#24AE7C]"></div>
                            <div className="size-2 animate-[pulse_1.5s_ease-in-out_infinite_0.6s] rounded-full bg-[#24AE7C]"></div>
                        </div>
                    </div>
                    <div className="mt-6">
                        <h3 className="text-xl font-semibold text-white">
                            Loading Payment History
                        </h3>
                        <p className="mt-2 text-sm text-gray-400">
                            Please wait while we fetch your transactions
                        </p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="relative w-full">
            <div className="container mx-auto px-4 py-8">
                <div className="mx-auto max-w-6xl">
                    <div className="mb-12 text-center">
                        <h1 className="to-purple-500 bg-gradient-to-r from-[#24AE7C] via-blue-500 bg-clip-text text-4xl font-bold text-transparent md:text-5xl">
                            Payment History
                        </h1>
                        <p className="mt-4 text-gray-400">
                            View and track all your transactions
                        </p>
                    </div>

                    <div className="mb-12 grid gap-6 md:grid-cols-3">
                        {/* Transaction Summary Cards */}
                        <div className="group relative">
                            <div className="absolute -inset-0.5 rounded-2xl bg-gradient-to-r from-[#24AE7C] to-blue-500 opacity-30 blur transition duration-300 group-hover:opacity-50"></div>
                            <div className="relative rounded-2xl border border-slate-800/50 bg-[#0f172a]/60 p-6 backdrop-blur-xl">
                                <div className="flex items-center gap-4">
                                    <div className="rounded-xl bg-[#24AE7C]/10 p-3">
                                        <FiFileText className="size-6 text-[#24AE7C]" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-400">Total Transactions</p>
                                        <p className="text-2xl font-bold text-white">{payments.length}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="group relative">
                            <div className="absolute -inset-0.5 rounded-2xl bg-gradient-to-r from-[#24AE7C] to-blue-500 opacity-30 blur transition duration-300 group-hover:opacity-50"></div>
                            <div className="relative rounded-2xl border border-slate-800/50 bg-[#0f172a]/60 p-6 backdrop-blur-xl">
                                <div className="flex items-center gap-4">
                                    <div className="rounded-xl bg-[#24AE7C]/10 p-3">
                                        <FiCreditCard className="size-6 text-[#24AE7C]" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-400">Last Payment</p>
                                        <p className="text-2xl font-bold text-white">
                                            {payments.length > 0
                                                ? formatCurrency(payments[0].amount, payments[0].currency)
                                                : "—"}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="group relative">
                            <div className="absolute -inset-0.5 rounded-2xl bg-gradient-to-r from-[#24AE7C] to-blue-500 opacity-30 blur transition duration-300 group-hover:opacity-50"></div>
                            <div className="relative rounded-2xl border border-slate-800/50 bg-[#0f172a]/60 p-6 backdrop-blur-xl">
                                <div className="flex items-center gap-4">
                                    <div className="rounded-xl bg-[#24AE7C]/10 p-3">
                                        <FiClock className="size-6 text-[#24AE7C]" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-400">Last Transaction</p>
                                        <p className="text-2xl font-bold text-white">
                                            {payments.length > 0
                                                ? formatDate(payments[0].date)
                                                : "—"}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Transaction Table */}
                    <div className="group relative">
                        <div className="absolute -inset-0.5 rounded-2xl bg-gradient-to-r from-[#24AE7C] to-blue-500 opacity-30 blur"></div>
                        <div className="relative rounded-2xl border border-slate-800/50 bg-[#0f172a]/60 p-6 backdrop-blur-xl">
                            <div className="mb-6 flex items-center justify-between">
                                <h2 className="text-xl font-semibold text-white">Your Transactions</h2>

                                {!isLoading && (
                                    <Button
                                        size="sm"
                                        variant="flat"
                                        color="default"
                                        startContent={<FiRefreshCw />}
                                        className="bg-gray-800/50 text-gray-300"
                                        onClick={() => {
                                            setIsLoading(true);
                                            apiFetch('/api/user/payment-history')
                                                .then(res => res.json())
                                                .then(data => {
                                                    setPayments(data.payments || []);
                                                    setIsLoading(false);
                                                })
                                                .catch(() => {
                                                    setIsLoading(false);
                                                });
                                        }}
                                    >
                                        Refresh
                                    </Button>
                                )}
                            </div>

                            {isLoading ? (
                                <div className="flex items-center justify-center py-12">
                                    <div className="relative inline-flex items-center justify-center">
                                        <div className="size-8 animate-spin rounded-full border-2 border-gray-300 border-t-[#24AE7C]"></div>
                                    </div>
                                    <span className="ml-3 text-gray-400">Loading your payment history...</span>
                                </div>
                            ) : payments.length > 0 ? (
                                <div>
                                    <Table
                                        aria-label="Payment history"
                                        classNames={{
                                            base: "max-w-full overflow-auto",
                                            table: "min-w-full",
                                            thead: "bg-gray-900/50",
                                            th: "text-gray-300 font-medium",
                                            tr: "border-b border-gray-800/30 hover:bg-gray-800/20",
                                            td: "text-gray-300"
                                        }}
                                    >
                                        <TableHeader>
                                            <TableColumn>DATE</TableColumn>
                                            <TableColumn>PLAN</TableColumn>
                                            <TableColumn>AMOUNT</TableColumn>
                                            <TableColumn>STATUS</TableColumn>
                                            <TableColumn>TYPE</TableColumn>
                                            <TableColumn>REFERENCE ID</TableColumn>
                                        </TableHeader>
                                        <TableBody>
                                            {items.map((payment) => (
                                                <TableRow key={payment.id}>
                                                    <TableCell>{formatDate(payment.date)}</TableCell>
                                                    <TableCell>{payment.plan || payment.description}</TableCell>
                                                    <TableCell>{formatCurrency(payment.amount, payment.currency)}</TableCell>
                                                    <TableCell>{getStatusDisplay(payment.status)}</TableCell>
                                                    <TableCell>{getTransactionType(payment)}</TableCell>
                                                    <TableCell className="font-mono text-xs">
                                                        {payment.method === "cancellation" ? "—" : (payment.paymentId || payment.id)}
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>

                                    {/* Pagination */}
                                    {pages > 1 && (
                                        <div className="mt-4 flex justify-center">
                                            <Pagination
                                                total={pages}
                                                initialPage={1}
                                                page={page}
                                                onChange={handlePageChange}
                                                size="sm"
                                                classNames={{
                                                    item: "text-white bg-gray-800/50 border border-gray-700",
                                                    cursor: "bg-[#24AE7C] text-white"
                                                }}
                                            />
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div className="py-12 text-center">
                                    <div className="mb-4 inline-flex items-center justify-center rounded-full bg-gray-800/30 p-4">
                                        <FiInfo className="text-2xl text-gray-400" />
                                    </div>
                                    <p className="mb-2 text-gray-400">No payment history found</p>
                                    <p className="text-sm text-gray-500">Transactions will appear here after you make a purchase</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}