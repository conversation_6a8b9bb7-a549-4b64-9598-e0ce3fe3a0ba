// model/Appointment.ts
import mongoose, { Schema } from "mongoose";

const appointmentSchema = new Schema({
  client: { type: Schema.Types.ObjectId, ref: "User", required: true },
  schedule: { type: Date, required: true },
  status: { type: String, enum: ["pending", "scheduled", "cancelled"], required: true },
  reason: { type: String, required: true },
  note: { type: String },
  cancellationReason: { type: String },
  userId: { type: String, required: true },
});

export const Appointment = mongoose.models.Appointment || mongoose.model("Appointment", appointmentSchema);