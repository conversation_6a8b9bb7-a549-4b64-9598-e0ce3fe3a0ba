// model/PendingUser.ts
import { Schema, model, models } from "mongoose";

const PendingUserSchema = new Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  verificationCode: { type: String, required: true },
  verificationCodeExpires: { type: Date, required: true },
});

export const PendingUser = models.PendingUser || model("PendingUser", PendingUserSchema);