"use client";

import { useUser, useClerk } from "@clerk/nextjs";
import { <PERSON><PERSON> } from "@nextui-org/react";
import { Crown, Zap, Monitor } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { toast } from "sonner";

import CancelSubscriptionModal from "@/components/CancelSubscriptionModal";
import { useOSDetection } from "@/hooks/useOSDetection";
import { apiFetch } from "@/lib/api-utils"; // Import our utility

export default function AccountType() {
    const { user, isLoaded } = useUser();
    const clerk = useClerk();
    const router = useRouter();
    const [localUser, setLocalUser] = useState<any>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
    const os = useOSDetection();

    const pricing = {
        windows: { weekly: 10, monthly: 30, yearly: 300 },
        macos: { weekly: 10, monthly: 30, yearly: 300 },
    };
    const currentPricing = os === "Windows" ? pricing.windows : pricing.macos;

    const fetchUserData = async () => {
        try {
            // Use apiFetch instead of fetch
            const response = await apiFetch("/api/user/me");
            if (response.ok) {
                const userData = await response.json();
                setLocalUser(userData);
            } else {
                toast.error("Failed to fetch user data.");
            }
        } catch (error) {
            console.error("Error fetching user data:", error);
            toast.error("An error occurred while fetching user data.");
        }
    };

    const handleCancelSubscription = async () => {
        try {
            setIsLoading(true);
            // Use apiFetch instead of fetch
            const response = await apiFetch("/api/user/cancel-subscription", { method: "POST" });
            if (response.ok) {
                toast.success("Subscription cancelled successfully");
                setIsCancelModalOpen(false);

                // Refresh the session instead of signing out
                try {
                    if (clerk.session) {
                        await clerk.session.reload();
                        console.log("Session reloaded successfully after cancellation");
                    }

                    // Fetch the latest user data

                    // Explicit toast notification about the status change
                    toast.success("Your subscription has been updated to free plan", {
                        duration: 5000
                    });
                } catch (sessionError) {
                    console.error("Error refreshing session:", sessionError);
                }
            } else {
                const error = await response.json();
                toast.error(error.message || "Failed to cancel subscription");
            }
        } catch (error) {
            console.error("Error cancelling subscription:", error);
            toast.error("An error occurred while cancelling subscription");
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        if (isLoaded && user) {
            fetchUserData();
            const interval = setInterval(fetchUserData, 30000);
            return () => clearInterval(interval);
        }
    }, [isLoaded, user]);

    if (!isLoaded) {
        return (
            <div className="flex min-h-[50vh] items-center justify-center">
                <div className="size-10 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            </div>
        );
    }

    const effectiveUser = localUser || { subscriptionType: "free", promptsUsed: 0, subscriptionPlan: "" };
    const promptsLimit =
        effectiveUser.subscriptionType === "free"
            ? 5
            : effectiveUser.subscriptionPlan === "weekly"
                ? 250
                : effectiveUser.subscriptionPlan === "monthly"
                    ? 250
                    : effectiveUser.subscriptionPlan === "yearly"
                        ? 250
                        : 5;
    const promptsLeft = promptsLimit - (effectiveUser.promptsUsed || 0);
    const promptsPercentage = Math.round((promptsLeft / promptsLimit) * 100);

    const getCurrentPlanPrice = () =>
        effectiveUser.subscriptionType === "premium" && effectiveUser.subscriptionPlan
            ? currentPricing[effectiveUser.subscriptionPlan as keyof typeof currentPricing]
            : 0;

    return (
        <div className="relative min-h-screen w-full bg-white bg-dot-black/[0.05] dark:bg-black dark:bg-dot-white/[0.2]">
            <div className="pointer-events-none absolute inset-0 flex items-center justify-center bg-white [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)] dark:bg-black"></div>
            <div className="container relative z-10 mx-auto px-4 py-16">
                <div className="mx-auto max-w-4xl">
                    <div className="mb-12 text-center">
                        <h1 className="bg-gradient-to-r from-primary via-red-500 to-secondary bg-clip-text text-4xl font-bold text-transparent md:text-5xl">
                            Your Account Dashboard
                        </h1>
                        <p className="mt-4 text-gray-400">Monitor your usage and upgrade your plan for enhanced features</p>
                        <p className="mt-2 text-sm text-gray-500">
                            <Monitor className="mr-1 inline-block size-4" />
                            Detected operating system: {os}
                        </p>
                    </div>
                    <div className="grid gap-8 md:grid-cols-2">
                        <div className="group relative">
                            <div className="absolute -inset-0.5 rounded-2xl bg-gradient-to-r from-primary to-secondary opacity-30 blur transition duration-300 group-hover:opacity-50"></div>
                            <div className="relative rounded-2xl border border-gray-800/50 bg-black/40 p-8 backdrop-blur-xl">
                                <div className="mb-6 flex items-center justify-between">
                                    <h3 className="text-xl font-semibold text-white">Current Plan</h3>
                                    <div className="flex items-center gap-2 rounded-full bg-primary/10 px-4 py-1.5">
                                        {effectiveUser.subscriptionType === "premium" ? (
                                            <Crown className="size-4 text-primary" />
                                        ) : (
                                            <Zap className="size-4 text-primary" />
                                        )}
                                        <span className="font-medium text-primary">
                                            {effectiveUser.subscriptionType === "premium"
                                                ? `${(effectiveUser.subscriptionPlan || "").charAt(0).toUpperCase() + (effectiveUser.subscriptionPlan || "").slice(1)} Premium`
                                                : "Free"}
                                        </span>
                                    </div>
                                </div>
                                <div className="space-y-6">
                                    {effectiveUser.subscriptionType === "premium" && (
                                        <div className="mb-4 text-white">
                                            <p className="mb-1 text-gray-400">Subscription Cost:</p>
                                            <p className="text-lg">${getCurrentPlanPrice()}/{effectiveUser.subscriptionPlan}</p>
                                            <p className="mt-1 text-xs text-gray-500">
                                                {os === "Windows" ? "Standard Windows pricing" : "Special macOS pricing"}
                                            </p>
                                        </div>
                                    )}
                                    <div>
                                        <div className="mb-2 flex justify-between text-sm">
                                            <span className="text-gray-400">Prompts Remaining</span>
                                            <span className="font-medium text-white">
                                                {promptsLeft}/{promptsLimit}
                                            </span>
                                        </div>
                                        <div className="h-2 overflow-hidden rounded-full bg-gray-700/50">
                                            <div
                                                className="h-full rounded-full bg-gradient-to-r from-primary to-secondary transition-all duration-500"
                                                style={{ width: `${promptsPercentage}%` }}
                                            ></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="group relative">
                            <div className="absolute -inset-0.5 rounded-2xl bg-gradient-to-r from-primary to-secondary opacity-30 blur transition duration-300 group-hover:opacity-50"></div>
                            <div className="relative flex flex-col justify-between rounded-2xl border border-gray-800/50 bg-black/40 p-8 backdrop-blur-xl">
                                <div>
                                    <h3 className="mb-4 text-xl font-semibold text-white">
                                        {effectiveUser.subscriptionType === "premium" ? "Free Plan Features" : "Premium Features"}
                                    </h3>
                                    <ul className="mb-6 space-y-3">
                                        {effectiveUser.subscriptionType === "premium" ? (
                                            <>
                                                <li className="flex items-center text-gray-400">
                                                    <Zap className="mr-2 size-4 text-primary" />5 prompts lifetime
                                                </li>
                                                <li className="flex items-center text-gray-400">
                                                    <Zap className="mr-2 size-4 text-primary" />
                                                    Basic support
                                                </li>
                                                <li className="flex items-center text-gray-400">
                                                    <Zap className="mr-2 size-4 text-primary" />
                                                    Standard features
                                                </li>
                                            </>
                                        ) : (
                                            <>
                                                <li className="flex items-center text-gray-400">
                                                    <Zap className="mr-2 size-4 text-primary" />
                                                    Up to 250 prompts per week
                                                </li>
                                                <li className="flex items-center text-gray-400">
                                                    <Zap className="mr-2 size-4 text-primary" />
                                                    Priority support
                                                </li>
                                                <li className="flex items-center text-gray-400">
                                                    <Zap className="mr-2 size-4 text-primary" />
                                                    Advanced features
                                                </li>
                                                <li className="mt-4 flex items-center text-gray-400">
                                                    <Monitor className="mr-2 size-4 text-green-400" />
                                                    {os === "macOS" ? "Special pricing for macOS users" : "Standard Windows pricing"}
                                                </li>
                                            </>
                                        )}
                                    </ul>
                                </div>
                                {effectiveUser.subscriptionType === "premium" ? (
                                    <Button
                                        className="w-full rounded-xl bg-red-500 py-6 font-medium text-white shadow-lg transition-all duration-200 hover:opacity-90"
                                        onClick={() => setIsCancelModalOpen(true)}
                                    >
                                        Cancel Subscription
                                    </Button>
                                ) : (
                                    <Button
                                        className="w-full rounded-xl bg-gradient-to-r from-primary to-secondary py-6 font-medium text-white shadow-lg transition-all duration-200 hover:opacity-90"
                                        onClick={() => router.push("/pricing")}
                                    >
                                        Upgrade Now
                                    </Button>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <CancelSubscriptionModal
                isOpen={isCancelModalOpen}
                onClose={() => setIsCancelModalOpen(false)}
                onConfirm={handleCancelSubscription}
                isLoading={isLoading}
            />
        </div>
    );
}