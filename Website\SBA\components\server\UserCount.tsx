import { getTotalUserCount } from '@/lib/actions/user-actions';

export async function UserCount() {
    try {
        // Fetch the user count from MongoDB
        const count = await getTotalUserCount();
        // Return a value that can be safely serialized and passed to client components
        return { count: count > 0 ? count : 39 }; // Fall back to 39 if count is 0 or error
    } catch (error) {
        console.error('Error fetching user count:', error);
        return { count: 39 }; // Fallback value
    }
}
