/* eslint-disable no-unused-vars */

// Remove `export {};` and declare the types normally

export type SearchParamProps = {
  params: { [key: string]: string };
  searchParams: { [key: string]: string | string[] | undefined };
};

export type Status = 'pending' | 'scheduled' | 'cancelled';

export interface CreateUserParams {
  name: string;
  email: string;
  phone: string;
  password: string;
}

export interface ContactFormParams {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export interface LoginUserParams {
  email: string;
  password: string;
}

export interface User extends CreateUserParams {
  $id: string;
}

export type CreateAppointmentParams = {
  client: string;
  userId: string;
  reason: string;
  schedule: Date;
  status: Status;
  note: string | undefined;
};

export interface UpdateAppointmentParams {
  appointmentId: string;
  userId: string;
  type: string;
};
