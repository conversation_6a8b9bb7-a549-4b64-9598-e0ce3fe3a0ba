export const navItems = [
  { path: '/announcements', display: 'Announcements', id: 1 },
  { path: '/about', display: 'About', id: 2 },
  { path: '/contact', display: 'Contact', id: 3 },
];
// TODO: Create ProjectData file or update import path
// export { ProjectData } from './ProjectData';

export const linkdata = [
  {
    id: 1,
    link: '/privacy',
    title: 'Privacy Policy',
    next: true,
  },
  {
    id: 2,
    link: '/terms&conditions',
    title: 'Terms of service',
    next: false,
  },
  {
    id: 3,
    link: '/cancel',
    title: 'Cancellation Policy',
    next: true,
  },
  {
    id: 4,
    link: '/contact',
    title: 'Contact us',
    next: true,
  },
];

export const ServiceCards = [
  {
    id: 1,
    imgSrc: '/gif-2.gif', // Placeholder for image
    title: 'Invisible Assistance',
    description:
      'Receive real-time coding assistance during interviews. Our AI delivers optimized code snippets and problem-solving insights discreetly, undetectable by screen-sharing tools.',
    imgAlt: 'Real-Time Coding',
  },
  {
    id: 2,
    imgSrc: '/embla/sys4.png', // Placeholder for image
    title: 'Live Chatbot',
    description:
      'Enhance remote interview performance with discreet support. Our system provides seamless guidance for complex coding tasks, ensuring confidence and focus throughout.',
    imgAlt: 'Discreet Interview Support',
  },
  {
    id: 3,
    imgSrc: '/embla/sys3.png', // Placeholder for image
    title: 'Coding Interview Solver',
    description:
      'Excel in system design interviews with expert advice. Our platform offers scalable architecture solutions and effective edge-case strategies tailored for success.',
    imgAlt: 'System Design Guidance',
  },
];
export const ServicePageCards = [
  {
    id: 1,
    imgSrc: '/embla/sys1.png', // Placeholder for image
    title: 'Real-Time Coding Assistance',
    description:
      'Get instant help with coding challenges during interviews. Our AI-powered tool generates optimized code snippets and provides problem-solving suggestions in real-time.',
    imgAlt: 'Real-Time Coding Assistance',
  },
  {
    id: 2,
    imgSrc: '/embla/sys2.png', // Placeholder for image
    title: 'Discreet Interview Support',
    description:
      'Stay confident during remote interviews with our seamless support system. We ensure you stay focused while handling complex coding tasks without being noticed.',
    imgAlt: 'Discreet Interview Support',
  },
  {
    id: 3,
    imgSrc: '/embla/sys3.png', // Placeholder for image
    title: 'System Design Guidance',
    description:
      'Ace system design rounds with expert recommendations. Our platform helps you create scalable architectures and handle edge cases effectively during interviews.',
    imgAlt: 'System Design Guidance',
  }
];

export const ServiceCardsDescription = [
  {
    id: 1,
    title: 'Real-Time Coding Assistance',
    imgSrc: '/embla/1.png', // Placeholder for image
    imgAlt: 'Real-Time Coding Assistance',
  
    description:
      'Our platform provides real-time coding assistance to help you ace technical interviews. With AI-powered tools, we generate optimized code snippets and problem-solving strategies on the fly. Whether you’re solving algorithms, debugging code, or designing systems, our platform ensures you stay confident and focused throughout the interview process.',
    list: [
      {
        title: 'Instant Code Generation: ',
        description:
          'Generate clean, optimized code snippets in seconds, ensuring you never get stuck during a coding challenge.',
      },
      {
        title: 'Problem-Solving Suggestions: ',
        description:
          'Receive step-by-step guidance for solving complex coding problems, helping you break down challenges into manageable steps.',
      },
      {
        title: 'Edge Case Handling: ',
        description:
          'Identify and handle edge cases effectively, ensuring your solutions are robust and error-free.',
      },
      {
        title: 'System Design Recommendations: ',
        description:
          'Get expert advice on scalable system architectures, database designs, and API integrations for system design rounds.',
      },
      {
        title: 'Algorithm Optimization: ',
        description:
          'Optimize your algorithms for time and space complexity, ensuring your solutions meet interview expectations.',
      },
      {
        title: 'Debugging Assistance: ',
        description:
          'Quickly identify and fix bugs in your code, saving valuable time during live interviews.',
      },
      {
        title: 'Code Visualization: ',
        description:
          'Visualize data structures, algorithms, and workflows in real-time to better understand and explain your solutions.',
      },
      {
        title: 'Discreet Support Integration: ',
        description:
          'Seamlessly integrate with video conferencing tools to provide discreet assistance without detection.',
      },
      {
        title: 'Practice Mode: ',
        description:
          'Access a practice mode to simulate real interview scenarios and refine your skills before the actual interview.',
      },
      {
        title: 'Interview Feedback: ',
        description:
          'Receive actionable feedback after mock interviews to improve your performance over time.',
      },
    ],
    sectors: [
      {
        title: 'Entry-Level Developers: ',
        description:
          'Helping freshers build confidence and tackle coding challenges during their first technical interviews.',
      },
      {
        title: 'Experienced Professionals: ',
        description:
          'Assisting senior developers in preparing for advanced coding rounds and system design interviews.',
      },
      {
        title: 'Remote Interview Candidates: ',
        description:
          'Providing discreet support for candidates attending remote interviews via platforms like Google Meet or Zoom.',
      },
      {
        title: 'Bootcamp Graduates: ',
        description:
          'Empowering coding bootcamp alumni to transition into tech roles by mastering technical interview skills.',
      },
      {
        title: 'Freelancers and Consultants: ',
        description:
          'Supporting freelancers and consultants in showcasing their technical expertise during client interviews.',
      },
      {
        title: 'Tech Companies: ',
        description:
          'Helping hiring teams streamline candidate assessments with consistent and reliable coding evaluations.',
      },
      {
        title: 'Educational Institutions: ',
        description:
          'Partnering with universities and coding schools to prepare students for technical interviews and placements.',
      },
      {
        title: 'Recruitment Agencies: ',
        description:
          'Assisting recruitment agencies in evaluating candidates’ technical skills efficiently and accurately.',
      },
    ],
  },
  {
    id: 2,
    title: 'Discreet Interview Support',
    imgSrc: '/embla/2.png', // Placeholder for image
    imgAlt: 'Discreet Interview Support',
  
    description:
      'Our platform provides real-time coding assistance to help you ace technical interviews. With AI-powered tools, we generate optimized code snippets and problem-solving strategies on the fly. Whether you’re solving algorithms, debugging code, or designing systems, our platform ensures you stay confident and focused throughout the interview process.',
    list: [
      {
        title: 'Instant Code Generation: ',
        description:
          'Generate clean, optimized code snippets in seconds, ensuring you never get stuck during a coding challenge.',
      },
      {
        title: 'Problem-Solving Suggestions: ',
        description:
          'Receive step-by-step guidance for solving complex coding problems, helping you break down challenges into manageable steps.',
      },
      {
        title: 'Edge Case Handling: ',
        description:
          'Identify and handle edge cases effectively, ensuring your solutions are robust and error-free.',
      },
      {
        title: 'System Design Recommendations: ',
        description:
          'Get expert advice on scalable system architectures, database designs, and API integrations for system design rounds.',
      },
      {
        title: 'Algorithm Optimization: ',
        description:
          'Optimize your algorithms for time and space complexity, ensuring your solutions meet interview expectations.',
      },
      {
        title: 'Debugging Assistance: ',
        description:
          'Quickly identify and fix bugs in your code, saving valuable time during live interviews.',
      },
      {
        title: 'Code Visualization: ',
        description:
          'Visualize data structures, algorithms, and workflows in real-time to better understand and explain your solutions.',
      },
      {
        title: 'Discreet Support Integration: ',
        description:
          'Seamlessly integrate with video conferencing tools to provide discreet assistance without detection.',
      },
      {
        title: 'Practice Mode: ',
        description:
          'Access a practice mode to simulate real interview scenarios and refine your skills before the actual interview.',
      },
      {
        title: 'Interview Feedback: ',
        description:
          'Receive actionable feedback after mock interviews to improve your performance over time.',
      },
    ],
    sectors: [
      {
        title: 'Entry-Level Developers: ',
        description:
          'Helping freshers build confidence and tackle coding challenges during their first technical interviews.',
      },
      {
        title: 'Experienced Professionals: ',
        description:
          'Assisting senior developers in preparing for advanced coding rounds and system design interviews.',
      },
      {
        title: 'Remote Interview Candidates: ',
        description:
          'Providing discreet support for candidates attending remote interviews via platforms like Google Meet or Zoom.',
      },
      {
        title: 'Bootcamp Graduates: ',
        description:
          'Empowering coding bootcamp alumni to transition into tech roles by mastering technical interview skills.',
      },
      {
        title: 'Freelancers and Consultants: ',
        description:
          'Supporting freelancers and consultants in showcasing their technical expertise during client interviews.',
      },
      {
        title: 'Tech Companies: ',
        description:
          'Helping hiring teams streamline candidate assessments with consistent and reliable coding evaluations.',
      },
      {
        title: 'Educational Institutions: ',
        description:
          'Partnering with universities and coding schools to prepare students for technical interviews and placements.',
      },
      {
        title: 'Recruitment Agencies: ',
        description:
          'Assisting recruitment agencies in evaluating candidates’ technical skills efficiently and accurately.',
      },
    ],
  },
  {
    id: 3,
    title: 'System Design Guidance',
    imgSrc: '/embla/3.png', // Placeholder for image
    imgAlt: 'System Design Guidance',
  
    description:
      'Our platform provides real-time coding assistance to help you ace technical interviews. With AI-powered tools, we generate optimized code snippets and problem-solving strategies on the fly. Whether you’re solving algorithms, debugging code, or designing systems, our platform ensures you stay confident and focused throughout the interview process.',
    list: [
      {
        title: 'Instant Code Generation: ',
        description:
          'Generate clean, optimized code snippets in seconds, ensuring you never get stuck during a coding challenge.',
      },
      {
        title: 'Problem-Solving Suggestions: ',
        description:
          'Receive step-by-step guidance for solving complex coding problems, helping you break down challenges into manageable steps.',
      },
      {
        title: 'Edge Case Handling: ',
        description:
          'Identify and handle edge cases effectively, ensuring your solutions are robust and error-free.',
      },
      {
        title: 'System Design Recommendations: ',
        description:
          'Get expert advice on scalable system architectures, database designs, and API integrations for system design rounds.',
      },
      {
        title: 'Algorithm Optimization: ',
        description:
          'Optimize your algorithms for time and space complexity, ensuring your solutions meet interview expectations.',
      },
      {
        title: 'Debugging Assistance: ',
        description:
          'Quickly identify and fix bugs in your code, saving valuable time during live interviews.',
      },
      {
        title: 'Code Visualization: ',
        description:
          'Visualize data structures, algorithms, and workflows in real-time to better understand and explain your solutions.',
      },
      {
        title: 'Discreet Support Integration: ',
        description:
          'Seamlessly integrate with video conferencing tools to provide discreet assistance without detection.',
      },
      {
        title: 'Practice Mode: ',
        description:
          'Access a practice mode to simulate real interview scenarios and refine your skills before the actual interview.',
      },
      {
        title: 'Interview Feedback: ',
        description:
          'Receive actionable feedback after mock interviews to improve your performance over time.',
      },
    ],
    sectors: [
      {
        title: 'Entry-Level Developers: ',
        description:
          'Helping freshers build confidence and tackle coding challenges during their first technical interviews.',
      },
      {
        title: 'Experienced Professionals: ',
        description:
          'Assisting senior developers in preparing for advanced coding rounds and system design interviews.',
      },
      {
        title: 'Remote Interview Candidates: ',
        description:
          'Providing discreet support for candidates attending remote interviews via platforms like Google Meet or Zoom.',
      },
      {
        title: 'Bootcamp Graduates: ',
        description:
          'Empowering coding bootcamp alumni to transition into tech roles by mastering technical interview skills.',
      },
      {
        title: 'Freelancers and Consultants: ',
        description:
          'Supporting freelancers and consultants in showcasing their technical expertise during client interviews.',
      },
      {
        title: 'Tech Companies: ',
        description:
          'Helping hiring teams streamline candidate assessments with consistent and reliable coding evaluations.',
      },
      {
        title: 'Educational Institutions: ',
        description:
          'Partnering with universities and coding schools to prepare students for technical interviews and placements.',
      },
      {
        title: 'Recruitment Agencies: ',
        description:
          'Assisting recruitment agencies in evaluating candidates’ technical skills efficiently and accurately.',
      },
    ],
  },
];

export const gridItems = [
  {
    id: 1,
    title: 'See How We Solved the Toughest LeetCode Bi-Weekly Contest Question Using Our Tool!',
    description: '',
    className: 'lg:col-span-3 md:col-span-6 md:row-span-4',
    imgClassName: 'w-full h-full opacity-60 hover:blur-3xl',
    titleClassName: 'justify-end ',
    img: '/video1.mp4',
    type: 'video',
    videoOptions: {
      autoPlay: true,
      loop: true,
      muted: true,
      playsInline: true
    },
    spareImg: '',
  },
  {
    id: 2,
    title: 'We provide Service all over the Globe.',
    description: '',
    className: 'lg:col-span-2 md:col-span-3 md:row-span-2',
    imgClassName: '',
    titleClassName: 'justify-start text-lg',
    img: '',
    spareImg: '',
  },
  {
    id: 3,
    title: '',
    description: '',
    className: 'lg:col-span-2 md:col-span-3 md:row-span-2',
    imgClassName: '',
    titleClassName: 'justify-center',
    img: '',
    spareImg: '/grid.svg',
  },
  {
    id: 4,
    title: '',
    description: '',
    className: 'lg:col-span-2 md:col-span-3 md:row-span-1',
  },

  {
    id: 5,
    title: 'Join to crack interview without a sweat',
    description: '',
    className: 'md:col-span-3 md:row-span-2',
    imgClassName:
      'absolute -right-1/4 inset-y-9 rounded-lg xl:block md:w-[360px] hidden',
    titleClassName: 'justify-center md:justify-start lg:justify-center',
    img: '/id6.jpeg',
    spareImg: '/grid.svg',
  },
  {
    id: 6,
    title: 'Do you want to ace in your interview?',
    description: '',
    className: 'lg:col-span-2 md:col-span-3 md:row-span-1',
    imgClassName: '',
    titleClassName: 'justify-center md:max-w-full max-w-60 text-center',
    img: '',
    spareImg: '',
  },
];

export const companieslogo = [
  {
    imgSrc: '/logos/gmeet4.png',
    imgClassname: 'w-auto h-12',
    alt: 'Google Meet',
  },
  {
    imgSrc: '/logos/team.png',
    imgClassname: 'w-auto h-12',
    alt: 'Microsoft Teams',
  },
  {
    imgSrc: '/logos/zoombw.png',
    imgClassname: 'w-auto h-12', // Changed from w-32 h-auto to match others
    alt: 'Everest',
  },
  {
    imgSrc: '/logos/webex.png',
    imgClassname: 'w-auto h-12',
    alt: 'WebEX',
  },
  {
    imgSrc: '/logos/hackerrank.png',
    imgClassname: 'w-auto h-12',
    alt: 'hackerrank',
  },
  {
    imgSrc: '/logos/chime.png',
    imgClassname: 'w-auto h-12',
    alt: 'chime',
  },
  {
    imgSrc: '/logos/coderpad.png',
    imgClassname: 'w-auto h-12',
    alt: 'coderpad',
  }
];

export const organization = [
  {
    id: 1,
    name: 'Vaibhav',
    position: 'Full Stack Developer',
    experience: '2+ years',
    keyperson: true,
  },
  {
    id: 2,
    name: 'Yash Budhia',
    position: 'Software Engineer',
    experience: '2+ years',
    keyperson: true,
  }
];
