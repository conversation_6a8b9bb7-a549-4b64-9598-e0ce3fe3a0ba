import { getAuth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";

import connectDB from "@/lib/mongodb";
import { User } from "@/model/User";

export async function DELETE(req: NextRequest) {
  try {
    // Authenticate request with Clerk
    const auth = getAuth(req);
    const { userId } = auth;
    
    if (!userId) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    await connectDB();
    
    // Verify admin status
    const adminUser = await User.findOne({ clerkId: userId });
    if (!adminUser || !adminUser.isAdmin) {
      return NextResponse.json({ message: "Unauthorized: Admin access required" }, { status: 403 });
    }
    
    // Get the target user ID from the URL params
    const url = new URL(req.url);
    const targetUserId = url.searchParams.get("userId");
    
    if (!targetUserId) {
      return NextResponse.json({ message: "User ID is required" }, { status: 400 });
    }

    // Delete the user
    const deletedUser = await User.findByIdAndDelete(targetUserId);
    
    if (!deletedUser) {
      return NextResponse.json({ message: "User not found" }, { status: 404 });
    }

    return NextResponse.json({ 
      success: true,
      message: "User deleted successfully"
    });
  } catch (error) {
    console.error("Admin delete user error:", error);
    return NextResponse.json({ 
      message: "Internal server error", 
      error: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 });
  }
}