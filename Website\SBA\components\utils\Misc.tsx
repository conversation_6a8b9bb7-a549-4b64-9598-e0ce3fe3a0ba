"use client";
import { But<PERSON> } from "@nextui-org/button";
import { useRouter } from "next/navigation";
import React from "react";
import { FaLocationArrow, FaWindows, FaApple } from "react-icons/fa6";

import MagicButton from "../MagicButton";

const MagicButtonCTA = () => {
  const router = useRouter();

  return (
    <MagicButton
      className="z-50 mt-2 sm:mt-5 md:hidden"
      title="Let's talk"
      icon={<FaLocationArrow />}
      position="right"
      otherClasses="bg-black"
      handleClick={() => {
        router.push("/contact");
      }}
    />
  );
};

export default MagicButtonCTA;

export const HeroButtonGetStarted = () => {
  // eslint-disable-next-line no-unused-vars
  const router = useRouter();

  const handleWindowsDownload = () => {
    window.location.href =
      "https://github.com/VaibhavRaina/Pytorch/releases/download/v1.0.0/interviewCr-setup-win64.exe";
  };

  const handleMacDownload = () => {
    window.location.href =
      "https://github.com/VaibhavRaina/Pytorch/releases/download/v2.5.0/interview-cracker-setup-macOS.zip";
  };

  return (
    <div className="flex flex-col items-center gap-4 sm:flex-row">
      <Button
        color="primary"
        variant="shadow"
        radius="full"
        className="relative w-56 overflow-visible px-6 py-3 text-sm font-medium"
        aria-description="Download for Windows Button"
        onClick={handleWindowsDownload}
      >
        <span className="absolute right-0 top-0">
          <span className="relative flex size-3">
            <span className="absolute inline-flex size-full animate-ping rounded-full bg-teal-600 opacity-75"></span>
            <span className="relative inline-flex size-3 rounded-full bg-[#0B879C]/90"></span>
          </span>
        </span>
        <FaWindows className="mr-2" /> Download for Windows
      </Button>

      <Button
        color="primary"
        variant="shadow"
        radius="full"
        className="relative w-56 overflow-visible px-6 py-3 text-sm font-medium"
        aria-description="Download for Mac Button"
        onClick={handleMacDownload}
      >
        <span className="absolute right-0 top-0">
          <span className="relative flex size-3">
            <span className="absolute inline-flex size-full animate-ping rounded-full bg-teal-600 opacity-75"></span>
            <span className="relative inline-flex size-3 rounded-full bg-[#0B879C]/90"></span>
          </span>
        </span>
        <FaApple className="mr-2" /> Download for Mac
      </Button>
    </div>
  );
};