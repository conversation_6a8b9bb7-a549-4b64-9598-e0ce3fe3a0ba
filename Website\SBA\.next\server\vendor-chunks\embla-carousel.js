"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/embla-carousel";
exports.ids = ["vendor-chunks/embla-carousel"];
exports.modules = {

/***/ "(ssr)/./node_modules/embla-carousel/esm/embla-carousel.esm.js":
/*!***************************************************************!*\
  !*** ./node_modules/embla-carousel/esm/embla-carousel.esm.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EmblaCarousel)\n/* harmony export */ });\nfunction isNumber(subject) {\n  return typeof subject === 'number';\n}\nfunction isString(subject) {\n  return typeof subject === 'string';\n}\nfunction isBoolean(subject) {\n  return typeof subject === 'boolean';\n}\nfunction isObject(subject) {\n  return Object.prototype.toString.call(subject) === '[object Object]';\n}\nfunction mathAbs(n) {\n  return Math.abs(n);\n}\nfunction mathSign(n) {\n  return Math.sign(n);\n}\nfunction deltaAbs(valueB, valueA) {\n  return mathAbs(valueB - valueA);\n}\nfunction factorAbs(valueB, valueA) {\n  if (valueB === 0 || valueA === 0) return 0;\n  if (mathAbs(valueB) <= mathAbs(valueA)) return 0;\n  const diff = deltaAbs(mathAbs(valueB), mathAbs(valueA));\n  return mathAbs(diff / valueB);\n}\nfunction arrayKeys(array) {\n  return objectKeys(array).map(Number);\n}\nfunction arrayLast(array) {\n  return array[arrayLastIndex(array)];\n}\nfunction arrayLastIndex(array) {\n  return Math.max(0, array.length - 1);\n}\nfunction arrayIsLastIndex(array, index) {\n  return index === arrayLastIndex(array);\n}\nfunction arrayFromNumber(n, startAt = 0) {\n  return Array.from(Array(n), (_, i) => startAt + i);\n}\nfunction objectKeys(object) {\n  return Object.keys(object);\n}\nfunction objectsMergeDeep(objectA, objectB) {\n  return [objectA, objectB].reduce((mergedObjects, currentObject) => {\n    objectKeys(currentObject).forEach(key => {\n      const valueA = mergedObjects[key];\n      const valueB = currentObject[key];\n      const areObjects = isObject(valueA) && isObject(valueB);\n      mergedObjects[key] = areObjects ? objectsMergeDeep(valueA, valueB) : valueB;\n    });\n    return mergedObjects;\n  }, {});\n}\nfunction isMouseEvent(evt, ownerWindow) {\n  return typeof ownerWindow.MouseEvent !== 'undefined' && evt instanceof ownerWindow.MouseEvent;\n}\n\nfunction Alignment(align, viewSize) {\n  const predefined = {\n    start,\n    center,\n    end\n  };\n  function start() {\n    return 0;\n  }\n  function center(n) {\n    return end(n) / 2;\n  }\n  function end(n) {\n    return viewSize - n;\n  }\n  function measure(n, index) {\n    if (isString(align)) return predefined[align](n);\n    return align(viewSize, n, index);\n  }\n  const self = {\n    measure\n  };\n  return self;\n}\n\nfunction EventStore() {\n  let listeners = [];\n  function add(node, type, handler, options = {\n    passive: true\n  }) {\n    let removeListener;\n    if ('addEventListener' in node) {\n      node.addEventListener(type, handler, options);\n      removeListener = () => node.removeEventListener(type, handler, options);\n    } else {\n      const legacyMediaQueryList = node;\n      legacyMediaQueryList.addListener(handler);\n      removeListener = () => legacyMediaQueryList.removeListener(handler);\n    }\n    listeners.push(removeListener);\n    return self;\n  }\n  function clear() {\n    listeners = listeners.filter(remove => remove());\n  }\n  const self = {\n    add,\n    clear\n  };\n  return self;\n}\n\nfunction Animations(ownerDocument, ownerWindow, update, render) {\n  const documentVisibleHandler = EventStore();\n  const timeStep = 1000 / 60;\n  let lastTimeStamp = null;\n  let lag = 0;\n  let animationFrame = 0;\n  function init() {\n    documentVisibleHandler.add(ownerDocument, 'visibilitychange', () => {\n      if (ownerDocument.hidden) reset();\n    });\n  }\n  function destroy() {\n    stop();\n    documentVisibleHandler.clear();\n  }\n  function animate(timeStamp) {\n    if (!animationFrame) return;\n    if (!lastTimeStamp) lastTimeStamp = timeStamp;\n    const elapsed = timeStamp - lastTimeStamp;\n    lastTimeStamp = timeStamp;\n    lag += elapsed;\n    while (lag >= timeStep) {\n      update(timeStep);\n      lag -= timeStep;\n    }\n    const lagOffset = lag / timeStep;\n    render(lagOffset);\n    if (animationFrame) ownerWindow.requestAnimationFrame(animate);\n  }\n  function start() {\n    if (animationFrame) return;\n    animationFrame = ownerWindow.requestAnimationFrame(animate);\n  }\n  function stop() {\n    ownerWindow.cancelAnimationFrame(animationFrame);\n    lastTimeStamp = null;\n    lag = 0;\n    animationFrame = 0;\n  }\n  function reset() {\n    lastTimeStamp = null;\n    lag = 0;\n  }\n  const self = {\n    init,\n    destroy,\n    start,\n    stop,\n    update: () => update(timeStep),\n    render\n  };\n  return self;\n}\n\nfunction Axis(axis, contentDirection) {\n  const isRightToLeft = contentDirection === 'rtl';\n  const isVertical = axis === 'y';\n  const scroll = isVertical ? 'y' : 'x';\n  const cross = isVertical ? 'x' : 'y';\n  const sign = !isVertical && isRightToLeft ? -1 : 1;\n  const startEdge = getStartEdge();\n  const endEdge = getEndEdge();\n  function measureSize(nodeRect) {\n    const {\n      height,\n      width\n    } = nodeRect;\n    return isVertical ? height : width;\n  }\n  function getStartEdge() {\n    if (isVertical) return 'top';\n    return isRightToLeft ? 'right' : 'left';\n  }\n  function getEndEdge() {\n    if (isVertical) return 'bottom';\n    return isRightToLeft ? 'left' : 'right';\n  }\n  function direction(n) {\n    return n * sign;\n  }\n  const self = {\n    scroll,\n    cross,\n    startEdge,\n    endEdge,\n    measureSize,\n    direction\n  };\n  return self;\n}\n\nfunction Limit(min = 0, max = 0) {\n  const length = mathAbs(min - max);\n  function reachedMin(n) {\n    return n < min;\n  }\n  function reachedMax(n) {\n    return n > max;\n  }\n  function reachedAny(n) {\n    return reachedMin(n) || reachedMax(n);\n  }\n  function constrain(n) {\n    if (!reachedAny(n)) return n;\n    return reachedMin(n) ? min : max;\n  }\n  function removeOffset(n) {\n    if (!length) return n;\n    return n - length * Math.ceil((n - max) / length);\n  }\n  const self = {\n    length,\n    max,\n    min,\n    constrain,\n    reachedAny,\n    reachedMax,\n    reachedMin,\n    removeOffset\n  };\n  return self;\n}\n\nfunction Counter(max, start, loop) {\n  const {\n    constrain\n  } = Limit(0, max);\n  const loopEnd = max + 1;\n  let counter = withinLimit(start);\n  function withinLimit(n) {\n    return !loop ? constrain(n) : mathAbs((loopEnd + n) % loopEnd);\n  }\n  function get() {\n    return counter;\n  }\n  function set(n) {\n    counter = withinLimit(n);\n    return self;\n  }\n  function add(n) {\n    return clone().set(get() + n);\n  }\n  function clone() {\n    return Counter(max, get(), loop);\n  }\n  const self = {\n    get,\n    set,\n    add,\n    clone\n  };\n  return self;\n}\n\nfunction DragHandler(axis, rootNode, ownerDocument, ownerWindow, target, dragTracker, location, animation, scrollTo, scrollBody, scrollTarget, index, eventHandler, percentOfView, dragFree, dragThreshold, skipSnaps, baseFriction, watchDrag) {\n  const {\n    cross: crossAxis,\n    direction\n  } = axis;\n  const focusNodes = ['INPUT', 'SELECT', 'TEXTAREA'];\n  const nonPassiveEvent = {\n    passive: false\n  };\n  const initEvents = EventStore();\n  const dragEvents = EventStore();\n  const goToNextThreshold = Limit(50, 225).constrain(percentOfView.measure(20));\n  const snapForceBoost = {\n    mouse: 300,\n    touch: 400\n  };\n  const freeForceBoost = {\n    mouse: 500,\n    touch: 600\n  };\n  const baseSpeed = dragFree ? 43 : 25;\n  let isMoving = false;\n  let startScroll = 0;\n  let startCross = 0;\n  let pointerIsDown = false;\n  let preventScroll = false;\n  let preventClick = false;\n  let isMouse = false;\n  function init(emblaApi) {\n    if (!watchDrag) return;\n    function downIfAllowed(evt) {\n      if (isBoolean(watchDrag) || watchDrag(emblaApi, evt)) down(evt);\n    }\n    const node = rootNode;\n    initEvents.add(node, 'dragstart', evt => evt.preventDefault(), nonPassiveEvent).add(node, 'touchmove', () => undefined, nonPassiveEvent).add(node, 'touchend', () => undefined).add(node, 'touchstart', downIfAllowed).add(node, 'mousedown', downIfAllowed).add(node, 'touchcancel', up).add(node, 'contextmenu', up).add(node, 'click', click, true);\n  }\n  function destroy() {\n    initEvents.clear();\n    dragEvents.clear();\n  }\n  function addDragEvents() {\n    const node = isMouse ? ownerDocument : rootNode;\n    dragEvents.add(node, 'touchmove', move, nonPassiveEvent).add(node, 'touchend', up).add(node, 'mousemove', move, nonPassiveEvent).add(node, 'mouseup', up);\n  }\n  function isFocusNode(node) {\n    const nodeName = node.nodeName || '';\n    return focusNodes.includes(nodeName);\n  }\n  function forceBoost() {\n    const boost = dragFree ? freeForceBoost : snapForceBoost;\n    const type = isMouse ? 'mouse' : 'touch';\n    return boost[type];\n  }\n  function allowedForce(force, targetChanged) {\n    const next = index.add(mathSign(force) * -1);\n    const baseForce = scrollTarget.byDistance(force, !dragFree).distance;\n    if (dragFree || mathAbs(force) < goToNextThreshold) return baseForce;\n    if (skipSnaps && targetChanged) return baseForce * 0.5;\n    return scrollTarget.byIndex(next.get(), 0).distance;\n  }\n  function down(evt) {\n    const isMouseEvt = isMouseEvent(evt, ownerWindow);\n    isMouse = isMouseEvt;\n    preventClick = dragFree && isMouseEvt && !evt.buttons && isMoving;\n    isMoving = deltaAbs(target.get(), location.get()) >= 2;\n    if (isMouseEvt && evt.button !== 0) return;\n    if (isFocusNode(evt.target)) return;\n    pointerIsDown = true;\n    dragTracker.pointerDown(evt);\n    scrollBody.useFriction(0).useDuration(0);\n    target.set(location);\n    addDragEvents();\n    startScroll = dragTracker.readPoint(evt);\n    startCross = dragTracker.readPoint(evt, crossAxis);\n    eventHandler.emit('pointerDown');\n  }\n  function move(evt) {\n    const isTouchEvt = !isMouseEvent(evt, ownerWindow);\n    if (isTouchEvt && evt.touches.length >= 2) return up(evt);\n    const lastScroll = dragTracker.readPoint(evt);\n    const lastCross = dragTracker.readPoint(evt, crossAxis);\n    const diffScroll = deltaAbs(lastScroll, startScroll);\n    const diffCross = deltaAbs(lastCross, startCross);\n    if (!preventScroll && !isMouse) {\n      if (!evt.cancelable) return up(evt);\n      preventScroll = diffScroll > diffCross;\n      if (!preventScroll) return up(evt);\n    }\n    const diff = dragTracker.pointerMove(evt);\n    if (diffScroll > dragThreshold) preventClick = true;\n    scrollBody.useFriction(0.3).useDuration(0.75);\n    animation.start();\n    target.add(direction(diff));\n    evt.preventDefault();\n  }\n  function up(evt) {\n    const currentLocation = scrollTarget.byDistance(0, false);\n    const targetChanged = currentLocation.index !== index.get();\n    const rawForce = dragTracker.pointerUp(evt) * forceBoost();\n    const force = allowedForce(direction(rawForce), targetChanged);\n    const forceFactor = factorAbs(rawForce, force);\n    const speed = baseSpeed - 10 * forceFactor;\n    const friction = baseFriction + forceFactor / 50;\n    preventScroll = false;\n    pointerIsDown = false;\n    dragEvents.clear();\n    scrollBody.useDuration(speed).useFriction(friction);\n    scrollTo.distance(force, !dragFree);\n    isMouse = false;\n    eventHandler.emit('pointerUp');\n  }\n  function click(evt) {\n    if (preventClick) {\n      evt.stopPropagation();\n      evt.preventDefault();\n      preventClick = false;\n    }\n  }\n  function pointerDown() {\n    return pointerIsDown;\n  }\n  const self = {\n    init,\n    destroy,\n    pointerDown\n  };\n  return self;\n}\n\nfunction DragTracker(axis, ownerWindow) {\n  const logInterval = 170;\n  let startEvent;\n  let lastEvent;\n  function readTime(evt) {\n    return evt.timeStamp;\n  }\n  function readPoint(evt, evtAxis) {\n    const property = evtAxis || axis.scroll;\n    const coord = `client${property === 'x' ? 'X' : 'Y'}`;\n    return (isMouseEvent(evt, ownerWindow) ? evt : evt.touches[0])[coord];\n  }\n  function pointerDown(evt) {\n    startEvent = evt;\n    lastEvent = evt;\n    return readPoint(evt);\n  }\n  function pointerMove(evt) {\n    const diff = readPoint(evt) - readPoint(lastEvent);\n    const expired = readTime(evt) - readTime(startEvent) > logInterval;\n    lastEvent = evt;\n    if (expired) startEvent = evt;\n    return diff;\n  }\n  function pointerUp(evt) {\n    if (!startEvent || !lastEvent) return 0;\n    const diffDrag = readPoint(lastEvent) - readPoint(startEvent);\n    const diffTime = readTime(evt) - readTime(startEvent);\n    const expired = readTime(evt) - readTime(lastEvent) > logInterval;\n    const force = diffDrag / diffTime;\n    const isFlick = diffTime && !expired && mathAbs(force) > 0.1;\n    return isFlick ? force : 0;\n  }\n  const self = {\n    pointerDown,\n    pointerMove,\n    pointerUp,\n    readPoint\n  };\n  return self;\n}\n\nfunction NodeRects() {\n  function measure(node) {\n    const {\n      offsetTop,\n      offsetLeft,\n      offsetWidth,\n      offsetHeight\n    } = node;\n    const offset = {\n      top: offsetTop,\n      right: offsetLeft + offsetWidth,\n      bottom: offsetTop + offsetHeight,\n      left: offsetLeft,\n      width: offsetWidth,\n      height: offsetHeight\n    };\n    return offset;\n  }\n  const self = {\n    measure\n  };\n  return self;\n}\n\nfunction PercentOfView(viewSize) {\n  function measure(n) {\n    return viewSize * (n / 100);\n  }\n  const self = {\n    measure\n  };\n  return self;\n}\n\nfunction ResizeHandler(container, eventHandler, ownerWindow, slides, axis, watchResize, nodeRects) {\n  const observeNodes = [container].concat(slides);\n  let resizeObserver;\n  let containerSize;\n  let slideSizes = [];\n  let destroyed = false;\n  function readSize(node) {\n    return axis.measureSize(nodeRects.measure(node));\n  }\n  function init(emblaApi) {\n    if (!watchResize) return;\n    containerSize = readSize(container);\n    slideSizes = slides.map(readSize);\n    function defaultCallback(entries) {\n      for (const entry of entries) {\n        if (destroyed) return;\n        const isContainer = entry.target === container;\n        const slideIndex = slides.indexOf(entry.target);\n        const lastSize = isContainer ? containerSize : slideSizes[slideIndex];\n        const newSize = readSize(isContainer ? container : slides[slideIndex]);\n        const diffSize = mathAbs(newSize - lastSize);\n        if (diffSize >= 0.5) {\n          emblaApi.reInit();\n          eventHandler.emit('resize');\n          break;\n        }\n      }\n    }\n    resizeObserver = new ResizeObserver(entries => {\n      if (isBoolean(watchResize) || watchResize(emblaApi, entries)) {\n        defaultCallback(entries);\n      }\n    });\n    ownerWindow.requestAnimationFrame(() => {\n      observeNodes.forEach(node => resizeObserver.observe(node));\n    });\n  }\n  function destroy() {\n    destroyed = true;\n    if (resizeObserver) resizeObserver.disconnect();\n  }\n  const self = {\n    init,\n    destroy\n  };\n  return self;\n}\n\nfunction ScrollBody(location, offsetLocation, previousLocation, target, baseDuration, baseFriction) {\n  let bodyVelocity = 0;\n  let scrollDirection = 0;\n  let scrollDuration = baseDuration;\n  let scrollFriction = baseFriction;\n  let rawLocation = location.get();\n  let rawLocationPrevious = 0;\n  function seek(timeStep) {\n    const fixedDeltaTimeSeconds = timeStep / 1000;\n    const duration = scrollDuration * fixedDeltaTimeSeconds;\n    const diff = target.get() - location.get();\n    const isInstant = !scrollDuration;\n    let directionDiff = 0;\n    if (isInstant) {\n      bodyVelocity = 0;\n      previousLocation.set(target);\n      location.set(target);\n      directionDiff = diff;\n    } else {\n      previousLocation.set(location);\n      bodyVelocity += diff / duration;\n      bodyVelocity *= scrollFriction;\n      rawLocation += bodyVelocity;\n      location.add(bodyVelocity * fixedDeltaTimeSeconds);\n      directionDiff = rawLocation - rawLocationPrevious;\n    }\n    scrollDirection = mathSign(directionDiff);\n    rawLocationPrevious = rawLocation;\n    return self;\n  }\n  function settled() {\n    const diff = target.get() - offsetLocation.get();\n    return mathAbs(diff) < 0.001;\n  }\n  function duration() {\n    return scrollDuration;\n  }\n  function direction() {\n    return scrollDirection;\n  }\n  function velocity() {\n    return bodyVelocity;\n  }\n  function useBaseDuration() {\n    return useDuration(baseDuration);\n  }\n  function useBaseFriction() {\n    return useFriction(baseFriction);\n  }\n  function useDuration(n) {\n    scrollDuration = n;\n    return self;\n  }\n  function useFriction(n) {\n    scrollFriction = n;\n    return self;\n  }\n  const self = {\n    direction,\n    duration,\n    velocity,\n    seek,\n    settled,\n    useBaseFriction,\n    useBaseDuration,\n    useFriction,\n    useDuration\n  };\n  return self;\n}\n\nfunction ScrollBounds(limit, location, target, scrollBody, percentOfView) {\n  const pullBackThreshold = percentOfView.measure(10);\n  const edgeOffsetTolerance = percentOfView.measure(50);\n  const frictionLimit = Limit(0.1, 0.99);\n  let disabled = false;\n  function shouldConstrain() {\n    if (disabled) return false;\n    if (!limit.reachedAny(target.get())) return false;\n    if (!limit.reachedAny(location.get())) return false;\n    return true;\n  }\n  function constrain(pointerDown) {\n    if (!shouldConstrain()) return;\n    const edge = limit.reachedMin(location.get()) ? 'min' : 'max';\n    const diffToEdge = mathAbs(limit[edge] - location.get());\n    const diffToTarget = target.get() - location.get();\n    const friction = frictionLimit.constrain(diffToEdge / edgeOffsetTolerance);\n    target.subtract(diffToTarget * friction);\n    if (!pointerDown && mathAbs(diffToTarget) < pullBackThreshold) {\n      target.set(limit.constrain(target.get()));\n      scrollBody.useDuration(25).useBaseFriction();\n    }\n  }\n  function toggleActive(active) {\n    disabled = !active;\n  }\n  const self = {\n    shouldConstrain,\n    constrain,\n    toggleActive\n  };\n  return self;\n}\n\nfunction ScrollContain(viewSize, contentSize, snapsAligned, containScroll, pixelTolerance) {\n  const scrollBounds = Limit(-contentSize + viewSize, 0);\n  const snapsBounded = measureBounded();\n  const scrollContainLimit = findScrollContainLimit();\n  const snapsContained = measureContained();\n  function usePixelTolerance(bound, snap) {\n    return deltaAbs(bound, snap) < 1;\n  }\n  function findScrollContainLimit() {\n    const startSnap = snapsBounded[0];\n    const endSnap = arrayLast(snapsBounded);\n    const min = snapsBounded.lastIndexOf(startSnap);\n    const max = snapsBounded.indexOf(endSnap) + 1;\n    return Limit(min, max);\n  }\n  function measureBounded() {\n    return snapsAligned.map((snapAligned, index) => {\n      const {\n        min,\n        max\n      } = scrollBounds;\n      const snap = scrollBounds.constrain(snapAligned);\n      const isFirst = !index;\n      const isLast = arrayIsLastIndex(snapsAligned, index);\n      if (isFirst) return max;\n      if (isLast) return min;\n      if (usePixelTolerance(min, snap)) return min;\n      if (usePixelTolerance(max, snap)) return max;\n      return snap;\n    }).map(scrollBound => parseFloat(scrollBound.toFixed(3)));\n  }\n  function measureContained() {\n    if (contentSize <= viewSize + pixelTolerance) return [scrollBounds.max];\n    if (containScroll === 'keepSnaps') return snapsBounded;\n    const {\n      min,\n      max\n    } = scrollContainLimit;\n    return snapsBounded.slice(min, max);\n  }\n  const self = {\n    snapsContained,\n    scrollContainLimit\n  };\n  return self;\n}\n\nfunction ScrollLimit(contentSize, scrollSnaps, loop) {\n  const max = scrollSnaps[0];\n  const min = loop ? max - contentSize : arrayLast(scrollSnaps);\n  const limit = Limit(min, max);\n  const self = {\n    limit\n  };\n  return self;\n}\n\nfunction ScrollLooper(contentSize, limit, location, vectors) {\n  const jointSafety = 0.1;\n  const min = limit.min + jointSafety;\n  const max = limit.max + jointSafety;\n  const {\n    reachedMin,\n    reachedMax\n  } = Limit(min, max);\n  function shouldLoop(direction) {\n    if (direction === 1) return reachedMax(location.get());\n    if (direction === -1) return reachedMin(location.get());\n    return false;\n  }\n  function loop(direction) {\n    if (!shouldLoop(direction)) return;\n    const loopDistance = contentSize * (direction * -1);\n    vectors.forEach(v => v.add(loopDistance));\n  }\n  const self = {\n    loop\n  };\n  return self;\n}\n\nfunction ScrollProgress(limit) {\n  const {\n    max,\n    length\n  } = limit;\n  function get(n) {\n    const currentLocation = n - max;\n    return length ? currentLocation / -length : 0;\n  }\n  const self = {\n    get\n  };\n  return self;\n}\n\nfunction ScrollSnaps(axis, alignment, containerRect, slideRects, slidesToScroll) {\n  const {\n    startEdge,\n    endEdge\n  } = axis;\n  const {\n    groupSlides\n  } = slidesToScroll;\n  const alignments = measureSizes().map(alignment.measure);\n  const snaps = measureUnaligned();\n  const snapsAligned = measureAligned();\n  function measureSizes() {\n    return groupSlides(slideRects).map(rects => arrayLast(rects)[endEdge] - rects[0][startEdge]).map(mathAbs);\n  }\n  function measureUnaligned() {\n    return slideRects.map(rect => containerRect[startEdge] - rect[startEdge]).map(snap => -mathAbs(snap));\n  }\n  function measureAligned() {\n    return groupSlides(snaps).map(g => g[0]).map((snap, index) => snap + alignments[index]);\n  }\n  const self = {\n    snaps,\n    snapsAligned\n  };\n  return self;\n}\n\nfunction SlideRegistry(containSnaps, containScroll, scrollSnaps, scrollContainLimit, slidesToScroll, slideIndexes) {\n  const {\n    groupSlides\n  } = slidesToScroll;\n  const {\n    min,\n    max\n  } = scrollContainLimit;\n  const slideRegistry = createSlideRegistry();\n  function createSlideRegistry() {\n    const groupedSlideIndexes = groupSlides(slideIndexes);\n    const doNotContain = !containSnaps || containScroll === 'keepSnaps';\n    if (scrollSnaps.length === 1) return [slideIndexes];\n    if (doNotContain) return groupedSlideIndexes;\n    return groupedSlideIndexes.slice(min, max).map((group, index, groups) => {\n      const isFirst = !index;\n      const isLast = arrayIsLastIndex(groups, index);\n      if (isFirst) {\n        const range = arrayLast(groups[0]) + 1;\n        return arrayFromNumber(range);\n      }\n      if (isLast) {\n        const range = arrayLastIndex(slideIndexes) - arrayLast(groups)[0] + 1;\n        return arrayFromNumber(range, arrayLast(groups)[0]);\n      }\n      return group;\n    });\n  }\n  const self = {\n    slideRegistry\n  };\n  return self;\n}\n\nfunction ScrollTarget(loop, scrollSnaps, contentSize, limit, targetVector) {\n  const {\n    reachedAny,\n    removeOffset,\n    constrain\n  } = limit;\n  function minDistance(distances) {\n    return distances.concat().sort((a, b) => mathAbs(a) - mathAbs(b))[0];\n  }\n  function findTargetSnap(target) {\n    const distance = loop ? removeOffset(target) : constrain(target);\n    const ascDiffsToSnaps = scrollSnaps.map((snap, index) => ({\n      diff: shortcut(snap - distance, 0),\n      index\n    })).sort((d1, d2) => mathAbs(d1.diff) - mathAbs(d2.diff));\n    const {\n      index\n    } = ascDiffsToSnaps[0];\n    return {\n      index,\n      distance\n    };\n  }\n  function shortcut(target, direction) {\n    const targets = [target, target + contentSize, target - contentSize];\n    if (!loop) return target;\n    if (!direction) return minDistance(targets);\n    const matchingTargets = targets.filter(t => mathSign(t) === direction);\n    if (matchingTargets.length) return minDistance(matchingTargets);\n    return arrayLast(targets) - contentSize;\n  }\n  function byIndex(index, direction) {\n    const diffToSnap = scrollSnaps[index] - targetVector.get();\n    const distance = shortcut(diffToSnap, direction);\n    return {\n      index,\n      distance\n    };\n  }\n  function byDistance(distance, snap) {\n    const target = targetVector.get() + distance;\n    const {\n      index,\n      distance: targetSnapDistance\n    } = findTargetSnap(target);\n    const reachedBound = !loop && reachedAny(target);\n    if (!snap || reachedBound) return {\n      index,\n      distance\n    };\n    const diffToSnap = scrollSnaps[index] - targetSnapDistance;\n    const snapDistance = distance + shortcut(diffToSnap, 0);\n    return {\n      index,\n      distance: snapDistance\n    };\n  }\n  const self = {\n    byDistance,\n    byIndex,\n    shortcut\n  };\n  return self;\n}\n\nfunction ScrollTo(animation, indexCurrent, indexPrevious, scrollBody, scrollTarget, targetVector, eventHandler) {\n  function scrollTo(target) {\n    const distanceDiff = target.distance;\n    const indexDiff = target.index !== indexCurrent.get();\n    targetVector.add(distanceDiff);\n    if (distanceDiff) {\n      if (scrollBody.duration()) {\n        animation.start();\n      } else {\n        animation.update();\n        animation.render(1);\n        animation.update();\n      }\n    }\n    if (indexDiff) {\n      indexPrevious.set(indexCurrent.get());\n      indexCurrent.set(target.index);\n      eventHandler.emit('select');\n    }\n  }\n  function distance(n, snap) {\n    const target = scrollTarget.byDistance(n, snap);\n    scrollTo(target);\n  }\n  function index(n, direction) {\n    const targetIndex = indexCurrent.clone().set(n);\n    const target = scrollTarget.byIndex(targetIndex.get(), direction);\n    scrollTo(target);\n  }\n  const self = {\n    distance,\n    index\n  };\n  return self;\n}\n\nfunction SlideFocus(root, slides, slideRegistry, scrollTo, scrollBody, eventStore, eventHandler, watchFocus) {\n  const focusListenerOptions = {\n    passive: true,\n    capture: true\n  };\n  let lastTabPressTime = 0;\n  function init(emblaApi) {\n    if (!watchFocus) return;\n    function defaultCallback(index) {\n      const nowTime = new Date().getTime();\n      const diffTime = nowTime - lastTabPressTime;\n      if (diffTime > 10) return;\n      eventHandler.emit('slideFocusStart');\n      root.scrollLeft = 0;\n      const group = slideRegistry.findIndex(group => group.includes(index));\n      if (!isNumber(group)) return;\n      scrollBody.useDuration(0);\n      scrollTo.index(group, 0);\n      eventHandler.emit('slideFocus');\n    }\n    eventStore.add(document, 'keydown', registerTabPress, false);\n    slides.forEach((slide, slideIndex) => {\n      eventStore.add(slide, 'focus', evt => {\n        if (isBoolean(watchFocus) || watchFocus(emblaApi, evt)) {\n          defaultCallback(slideIndex);\n        }\n      }, focusListenerOptions);\n    });\n  }\n  function registerTabPress(event) {\n    if (event.code === 'Tab') lastTabPressTime = new Date().getTime();\n  }\n  const self = {\n    init\n  };\n  return self;\n}\n\nfunction Vector1D(initialValue) {\n  let value = initialValue;\n  function get() {\n    return value;\n  }\n  function set(n) {\n    value = normalizeInput(n);\n  }\n  function add(n) {\n    value += normalizeInput(n);\n  }\n  function subtract(n) {\n    value -= normalizeInput(n);\n  }\n  function normalizeInput(n) {\n    return isNumber(n) ? n : n.get();\n  }\n  const self = {\n    get,\n    set,\n    add,\n    subtract\n  };\n  return self;\n}\n\nfunction Translate(axis, container) {\n  const translate = axis.scroll === 'x' ? x : y;\n  const containerStyle = container.style;\n  let disabled = false;\n  function x(n) {\n    return `translate3d(${n}px,0px,0px)`;\n  }\n  function y(n) {\n    return `translate3d(0px,${n}px,0px)`;\n  }\n  function to(target) {\n    if (disabled) return;\n    containerStyle.transform = translate(axis.direction(target));\n  }\n  function toggleActive(active) {\n    disabled = !active;\n  }\n  function clear() {\n    if (disabled) return;\n    containerStyle.transform = '';\n    if (!container.getAttribute('style')) container.removeAttribute('style');\n  }\n  const self = {\n    clear,\n    to,\n    toggleActive\n  };\n  return self;\n}\n\nfunction SlideLooper(axis, viewSize, contentSize, slideSizes, slideSizesWithGaps, snaps, scrollSnaps, location, slides) {\n  const roundingSafety = 0.5;\n  const ascItems = arrayKeys(slideSizesWithGaps);\n  const descItems = arrayKeys(slideSizesWithGaps).reverse();\n  const loopPoints = startPoints().concat(endPoints());\n  function removeSlideSizes(indexes, from) {\n    return indexes.reduce((a, i) => {\n      return a - slideSizesWithGaps[i];\n    }, from);\n  }\n  function slidesInGap(indexes, gap) {\n    return indexes.reduce((a, i) => {\n      const remainingGap = removeSlideSizes(a, gap);\n      return remainingGap > 0 ? a.concat([i]) : a;\n    }, []);\n  }\n  function findSlideBounds(offset) {\n    return snaps.map((snap, index) => ({\n      start: snap - slideSizes[index] + roundingSafety + offset,\n      end: snap + viewSize - roundingSafety + offset\n    }));\n  }\n  function findLoopPoints(indexes, offset, isEndEdge) {\n    const slideBounds = findSlideBounds(offset);\n    return indexes.map(index => {\n      const initial = isEndEdge ? 0 : -contentSize;\n      const altered = isEndEdge ? contentSize : 0;\n      const boundEdge = isEndEdge ? 'end' : 'start';\n      const loopPoint = slideBounds[index][boundEdge];\n      return {\n        index,\n        loopPoint,\n        slideLocation: Vector1D(-1),\n        translate: Translate(axis, slides[index]),\n        target: () => location.get() > loopPoint ? initial : altered\n      };\n    });\n  }\n  function startPoints() {\n    const gap = scrollSnaps[0];\n    const indexes = slidesInGap(descItems, gap);\n    return findLoopPoints(indexes, contentSize, false);\n  }\n  function endPoints() {\n    const gap = viewSize - scrollSnaps[0] - 1;\n    const indexes = slidesInGap(ascItems, gap);\n    return findLoopPoints(indexes, -contentSize, true);\n  }\n  function canLoop() {\n    return loopPoints.every(({\n      index\n    }) => {\n      const otherIndexes = ascItems.filter(i => i !== index);\n      return removeSlideSizes(otherIndexes, viewSize) <= 0.1;\n    });\n  }\n  function loop() {\n    loopPoints.forEach(loopPoint => {\n      const {\n        target,\n        translate,\n        slideLocation\n      } = loopPoint;\n      const shiftLocation = target();\n      if (shiftLocation === slideLocation.get()) return;\n      translate.to(shiftLocation);\n      slideLocation.set(shiftLocation);\n    });\n  }\n  function clear() {\n    loopPoints.forEach(loopPoint => loopPoint.translate.clear());\n  }\n  const self = {\n    canLoop,\n    clear,\n    loop,\n    loopPoints\n  };\n  return self;\n}\n\nfunction SlidesHandler(container, eventHandler, watchSlides) {\n  let mutationObserver;\n  let destroyed = false;\n  function init(emblaApi) {\n    if (!watchSlides) return;\n    function defaultCallback(mutations) {\n      for (const mutation of mutations) {\n        if (mutation.type === 'childList') {\n          emblaApi.reInit();\n          eventHandler.emit('slidesChanged');\n          break;\n        }\n      }\n    }\n    mutationObserver = new MutationObserver(mutations => {\n      if (destroyed) return;\n      if (isBoolean(watchSlides) || watchSlides(emblaApi, mutations)) {\n        defaultCallback(mutations);\n      }\n    });\n    mutationObserver.observe(container, {\n      childList: true\n    });\n  }\n  function destroy() {\n    if (mutationObserver) mutationObserver.disconnect();\n    destroyed = true;\n  }\n  const self = {\n    init,\n    destroy\n  };\n  return self;\n}\n\nfunction SlidesInView(container, slides, eventHandler, threshold) {\n  const intersectionEntryMap = {};\n  let inViewCache = null;\n  let notInViewCache = null;\n  let intersectionObserver;\n  let destroyed = false;\n  function init() {\n    intersectionObserver = new IntersectionObserver(entries => {\n      if (destroyed) return;\n      entries.forEach(entry => {\n        const index = slides.indexOf(entry.target);\n        intersectionEntryMap[index] = entry;\n      });\n      inViewCache = null;\n      notInViewCache = null;\n      eventHandler.emit('slidesInView');\n    }, {\n      root: container.parentElement,\n      threshold\n    });\n    slides.forEach(slide => intersectionObserver.observe(slide));\n  }\n  function destroy() {\n    if (intersectionObserver) intersectionObserver.disconnect();\n    destroyed = true;\n  }\n  function createInViewList(inView) {\n    return objectKeys(intersectionEntryMap).reduce((list, slideIndex) => {\n      const index = parseInt(slideIndex);\n      const {\n        isIntersecting\n      } = intersectionEntryMap[index];\n      const inViewMatch = inView && isIntersecting;\n      const notInViewMatch = !inView && !isIntersecting;\n      if (inViewMatch || notInViewMatch) list.push(index);\n      return list;\n    }, []);\n  }\n  function get(inView = true) {\n    if (inView && inViewCache) return inViewCache;\n    if (!inView && notInViewCache) return notInViewCache;\n    const slideIndexes = createInViewList(inView);\n    if (inView) inViewCache = slideIndexes;\n    if (!inView) notInViewCache = slideIndexes;\n    return slideIndexes;\n  }\n  const self = {\n    init,\n    destroy,\n    get\n  };\n  return self;\n}\n\nfunction SlideSizes(axis, containerRect, slideRects, slides, readEdgeGap, ownerWindow) {\n  const {\n    measureSize,\n    startEdge,\n    endEdge\n  } = axis;\n  const withEdgeGap = slideRects[0] && readEdgeGap;\n  const startGap = measureStartGap();\n  const endGap = measureEndGap();\n  const slideSizes = slideRects.map(measureSize);\n  const slideSizesWithGaps = measureWithGaps();\n  function measureStartGap() {\n    if (!withEdgeGap) return 0;\n    const slideRect = slideRects[0];\n    return mathAbs(containerRect[startEdge] - slideRect[startEdge]);\n  }\n  function measureEndGap() {\n    if (!withEdgeGap) return 0;\n    const style = ownerWindow.getComputedStyle(arrayLast(slides));\n    return parseFloat(style.getPropertyValue(`margin-${endEdge}`));\n  }\n  function measureWithGaps() {\n    return slideRects.map((rect, index, rects) => {\n      const isFirst = !index;\n      const isLast = arrayIsLastIndex(rects, index);\n      if (isFirst) return slideSizes[index] + startGap;\n      if (isLast) return slideSizes[index] + endGap;\n      return rects[index + 1][startEdge] - rect[startEdge];\n    }).map(mathAbs);\n  }\n  const self = {\n    slideSizes,\n    slideSizesWithGaps,\n    startGap,\n    endGap\n  };\n  return self;\n}\n\nfunction SlidesToScroll(axis, viewSize, slidesToScroll, loop, containerRect, slideRects, startGap, endGap, pixelTolerance) {\n  const {\n    startEdge,\n    endEdge,\n    direction\n  } = axis;\n  const groupByNumber = isNumber(slidesToScroll);\n  function byNumber(array, groupSize) {\n    return arrayKeys(array).filter(i => i % groupSize === 0).map(i => array.slice(i, i + groupSize));\n  }\n  function bySize(array) {\n    if (!array.length) return [];\n    return arrayKeys(array).reduce((groups, rectB, index) => {\n      const rectA = arrayLast(groups) || 0;\n      const isFirst = rectA === 0;\n      const isLast = rectB === arrayLastIndex(array);\n      const edgeA = containerRect[startEdge] - slideRects[rectA][startEdge];\n      const edgeB = containerRect[startEdge] - slideRects[rectB][endEdge];\n      const gapA = !loop && isFirst ? direction(startGap) : 0;\n      const gapB = !loop && isLast ? direction(endGap) : 0;\n      const chunkSize = mathAbs(edgeB - gapB - (edgeA + gapA));\n      if (index && chunkSize > viewSize + pixelTolerance) groups.push(rectB);\n      if (isLast) groups.push(array.length);\n      return groups;\n    }, []).map((currentSize, index, groups) => {\n      const previousSize = Math.max(groups[index - 1] || 0);\n      return array.slice(previousSize, currentSize);\n    });\n  }\n  function groupSlides(array) {\n    return groupByNumber ? byNumber(array, slidesToScroll) : bySize(array);\n  }\n  const self = {\n    groupSlides\n  };\n  return self;\n}\n\nfunction Engine(root, container, slides, ownerDocument, ownerWindow, options, eventHandler) {\n  // Options\n  const {\n    align,\n    axis: scrollAxis,\n    direction,\n    startIndex,\n    loop,\n    duration,\n    dragFree,\n    dragThreshold,\n    inViewThreshold,\n    slidesToScroll: groupSlides,\n    skipSnaps,\n    containScroll,\n    watchResize,\n    watchSlides,\n    watchDrag,\n    watchFocus\n  } = options;\n  // Measurements\n  const pixelTolerance = 2;\n  const nodeRects = NodeRects();\n  const containerRect = nodeRects.measure(container);\n  const slideRects = slides.map(nodeRects.measure);\n  const axis = Axis(scrollAxis, direction);\n  const viewSize = axis.measureSize(containerRect);\n  const percentOfView = PercentOfView(viewSize);\n  const alignment = Alignment(align, viewSize);\n  const containSnaps = !loop && !!containScroll;\n  const readEdgeGap = loop || !!containScroll;\n  const {\n    slideSizes,\n    slideSizesWithGaps,\n    startGap,\n    endGap\n  } = SlideSizes(axis, containerRect, slideRects, slides, readEdgeGap, ownerWindow);\n  const slidesToScroll = SlidesToScroll(axis, viewSize, groupSlides, loop, containerRect, slideRects, startGap, endGap, pixelTolerance);\n  const {\n    snaps,\n    snapsAligned\n  } = ScrollSnaps(axis, alignment, containerRect, slideRects, slidesToScroll);\n  const contentSize = -arrayLast(snaps) + arrayLast(slideSizesWithGaps);\n  const {\n    snapsContained,\n    scrollContainLimit\n  } = ScrollContain(viewSize, contentSize, snapsAligned, containScroll, pixelTolerance);\n  const scrollSnaps = containSnaps ? snapsContained : snapsAligned;\n  const {\n    limit\n  } = ScrollLimit(contentSize, scrollSnaps, loop);\n  // Indexes\n  const index = Counter(arrayLastIndex(scrollSnaps), startIndex, loop);\n  const indexPrevious = index.clone();\n  const slideIndexes = arrayKeys(slides);\n  // Animation\n  const update = ({\n    dragHandler,\n    scrollBody,\n    scrollBounds,\n    options: {\n      loop\n    }\n  }, timeStep) => {\n    if (!loop) scrollBounds.constrain(dragHandler.pointerDown());\n    scrollBody.seek(timeStep);\n  };\n  const render = ({\n    scrollBody,\n    translate,\n    location,\n    offsetLocation,\n    scrollLooper,\n    slideLooper,\n    dragHandler,\n    animation,\n    eventHandler,\n    scrollBounds,\n    options: {\n      loop\n    }\n  }, lagOffset) => {\n    const shouldSettle = scrollBody.settled();\n    const withinBounds = !scrollBounds.shouldConstrain();\n    const hasSettled = loop ? shouldSettle : shouldSettle && withinBounds;\n    if (hasSettled && !dragHandler.pointerDown()) {\n      animation.stop();\n      eventHandler.emit('settle');\n    }\n    if (!hasSettled) eventHandler.emit('scroll');\n    const interpolatedLocation = location.get() * lagOffset + previousLocation.get() * (1 - lagOffset);\n    offsetLocation.set(interpolatedLocation);\n    if (loop) {\n      scrollLooper.loop(scrollBody.direction());\n      slideLooper.loop();\n    }\n    translate.to(offsetLocation.get());\n  };\n  const animation = Animations(ownerDocument, ownerWindow, timeStep => update(engine, timeStep), lagOffset => render(engine, lagOffset));\n  // Shared\n  const friction = 0.68;\n  const startLocation = scrollSnaps[index.get()];\n  const location = Vector1D(startLocation);\n  const previousLocation = Vector1D(startLocation);\n  const offsetLocation = Vector1D(startLocation);\n  const target = Vector1D(startLocation);\n  const scrollBody = ScrollBody(location, offsetLocation, previousLocation, target, duration, friction);\n  const scrollTarget = ScrollTarget(loop, scrollSnaps, contentSize, limit, target);\n  const scrollTo = ScrollTo(animation, index, indexPrevious, scrollBody, scrollTarget, target, eventHandler);\n  const scrollProgress = ScrollProgress(limit);\n  const eventStore = EventStore();\n  const slidesInView = SlidesInView(container, slides, eventHandler, inViewThreshold);\n  const {\n    slideRegistry\n  } = SlideRegistry(containSnaps, containScroll, scrollSnaps, scrollContainLimit, slidesToScroll, slideIndexes);\n  const slideFocus = SlideFocus(root, slides, slideRegistry, scrollTo, scrollBody, eventStore, eventHandler, watchFocus);\n  // Engine\n  const engine = {\n    ownerDocument,\n    ownerWindow,\n    eventHandler,\n    containerRect,\n    slideRects,\n    animation,\n    axis,\n    dragHandler: DragHandler(axis, root, ownerDocument, ownerWindow, target, DragTracker(axis, ownerWindow), location, animation, scrollTo, scrollBody, scrollTarget, index, eventHandler, percentOfView, dragFree, dragThreshold, skipSnaps, friction, watchDrag),\n    eventStore,\n    percentOfView,\n    index,\n    indexPrevious,\n    limit,\n    location,\n    offsetLocation,\n    previousLocation,\n    options,\n    resizeHandler: ResizeHandler(container, eventHandler, ownerWindow, slides, axis, watchResize, nodeRects),\n    scrollBody,\n    scrollBounds: ScrollBounds(limit, offsetLocation, target, scrollBody, percentOfView),\n    scrollLooper: ScrollLooper(contentSize, limit, offsetLocation, [location, offsetLocation, previousLocation, target]),\n    scrollProgress,\n    scrollSnapList: scrollSnaps.map(scrollProgress.get),\n    scrollSnaps,\n    scrollTarget,\n    scrollTo,\n    slideLooper: SlideLooper(axis, viewSize, contentSize, slideSizes, slideSizesWithGaps, snaps, scrollSnaps, offsetLocation, slides),\n    slideFocus,\n    slidesHandler: SlidesHandler(container, eventHandler, watchSlides),\n    slidesInView,\n    slideIndexes,\n    slideRegistry,\n    slidesToScroll,\n    target,\n    translate: Translate(axis, container)\n  };\n  return engine;\n}\n\nfunction EventHandler() {\n  let listeners = {};\n  let api;\n  function init(emblaApi) {\n    api = emblaApi;\n  }\n  function getListeners(evt) {\n    return listeners[evt] || [];\n  }\n  function emit(evt) {\n    getListeners(evt).forEach(e => e(api, evt));\n    return self;\n  }\n  function on(evt, cb) {\n    listeners[evt] = getListeners(evt).concat([cb]);\n    return self;\n  }\n  function off(evt, cb) {\n    listeners[evt] = getListeners(evt).filter(e => e !== cb);\n    return self;\n  }\n  function clear() {\n    listeners = {};\n  }\n  const self = {\n    init,\n    emit,\n    off,\n    on,\n    clear\n  };\n  return self;\n}\n\nconst defaultOptions = {\n  align: 'center',\n  axis: 'x',\n  container: null,\n  slides: null,\n  containScroll: 'trimSnaps',\n  direction: 'ltr',\n  slidesToScroll: 1,\n  inViewThreshold: 0,\n  breakpoints: {},\n  dragFree: false,\n  dragThreshold: 10,\n  loop: false,\n  skipSnaps: false,\n  duration: 25,\n  startIndex: 0,\n  active: true,\n  watchDrag: true,\n  watchResize: true,\n  watchSlides: true,\n  watchFocus: true\n};\n\nfunction OptionsHandler(ownerWindow) {\n  function mergeOptions(optionsA, optionsB) {\n    return objectsMergeDeep(optionsA, optionsB || {});\n  }\n  function optionsAtMedia(options) {\n    const optionsAtMedia = options.breakpoints || {};\n    const matchedMediaOptions = objectKeys(optionsAtMedia).filter(media => ownerWindow.matchMedia(media).matches).map(media => optionsAtMedia[media]).reduce((a, mediaOption) => mergeOptions(a, mediaOption), {});\n    return mergeOptions(options, matchedMediaOptions);\n  }\n  function optionsMediaQueries(optionsList) {\n    return optionsList.map(options => objectKeys(options.breakpoints || {})).reduce((acc, mediaQueries) => acc.concat(mediaQueries), []).map(ownerWindow.matchMedia);\n  }\n  const self = {\n    mergeOptions,\n    optionsAtMedia,\n    optionsMediaQueries\n  };\n  return self;\n}\n\nfunction PluginsHandler(optionsHandler) {\n  let activePlugins = [];\n  function init(emblaApi, plugins) {\n    activePlugins = plugins.filter(({\n      options\n    }) => optionsHandler.optionsAtMedia(options).active !== false);\n    activePlugins.forEach(plugin => plugin.init(emblaApi, optionsHandler));\n    return plugins.reduce((map, plugin) => Object.assign(map, {\n      [plugin.name]: plugin\n    }), {});\n  }\n  function destroy() {\n    activePlugins = activePlugins.filter(plugin => plugin.destroy());\n  }\n  const self = {\n    init,\n    destroy\n  };\n  return self;\n}\n\nfunction EmblaCarousel(root, userOptions, userPlugins) {\n  const ownerDocument = root.ownerDocument;\n  const ownerWindow = ownerDocument.defaultView;\n  const optionsHandler = OptionsHandler(ownerWindow);\n  const pluginsHandler = PluginsHandler(optionsHandler);\n  const mediaHandlers = EventStore();\n  const eventHandler = EventHandler();\n  const {\n    mergeOptions,\n    optionsAtMedia,\n    optionsMediaQueries\n  } = optionsHandler;\n  const {\n    on,\n    off,\n    emit\n  } = eventHandler;\n  const reInit = reActivate;\n  let destroyed = false;\n  let engine;\n  let optionsBase = mergeOptions(defaultOptions, EmblaCarousel.globalOptions);\n  let options = mergeOptions(optionsBase);\n  let pluginList = [];\n  let pluginApis;\n  let container;\n  let slides;\n  function storeElements() {\n    const {\n      container: userContainer,\n      slides: userSlides\n    } = options;\n    const customContainer = isString(userContainer) ? root.querySelector(userContainer) : userContainer;\n    container = customContainer || root.children[0];\n    const customSlides = isString(userSlides) ? container.querySelectorAll(userSlides) : userSlides;\n    slides = [].slice.call(customSlides || container.children);\n  }\n  function createEngine(options) {\n    const engine = Engine(root, container, slides, ownerDocument, ownerWindow, options, eventHandler);\n    if (options.loop && !engine.slideLooper.canLoop()) {\n      const optionsWithoutLoop = Object.assign({}, options, {\n        loop: false\n      });\n      return createEngine(optionsWithoutLoop);\n    }\n    return engine;\n  }\n  function activate(withOptions, withPlugins) {\n    if (destroyed) return;\n    optionsBase = mergeOptions(optionsBase, withOptions);\n    options = optionsAtMedia(optionsBase);\n    pluginList = withPlugins || pluginList;\n    storeElements();\n    engine = createEngine(options);\n    optionsMediaQueries([optionsBase, ...pluginList.map(({\n      options\n    }) => options)]).forEach(query => mediaHandlers.add(query, 'change', reActivate));\n    if (!options.active) return;\n    engine.translate.to(engine.location.get());\n    engine.animation.init();\n    engine.slidesInView.init();\n    engine.slideFocus.init(self);\n    engine.eventHandler.init(self);\n    engine.resizeHandler.init(self);\n    engine.slidesHandler.init(self);\n    if (engine.options.loop) engine.slideLooper.loop();\n    if (container.offsetParent && slides.length) engine.dragHandler.init(self);\n    pluginApis = pluginsHandler.init(self, pluginList);\n  }\n  function reActivate(withOptions, withPlugins) {\n    const startIndex = selectedScrollSnap();\n    deActivate();\n    activate(mergeOptions({\n      startIndex\n    }, withOptions), withPlugins);\n    eventHandler.emit('reInit');\n  }\n  function deActivate() {\n    engine.dragHandler.destroy();\n    engine.eventStore.clear();\n    engine.translate.clear();\n    engine.slideLooper.clear();\n    engine.resizeHandler.destroy();\n    engine.slidesHandler.destroy();\n    engine.slidesInView.destroy();\n    engine.animation.destroy();\n    pluginsHandler.destroy();\n    mediaHandlers.clear();\n  }\n  function destroy() {\n    if (destroyed) return;\n    destroyed = true;\n    mediaHandlers.clear();\n    deActivate();\n    eventHandler.emit('destroy');\n    eventHandler.clear();\n  }\n  function scrollTo(index, jump, direction) {\n    if (!options.active || destroyed) return;\n    engine.scrollBody.useBaseFriction().useDuration(jump === true ? 0 : options.duration);\n    engine.scrollTo.index(index, direction || 0);\n  }\n  function scrollNext(jump) {\n    const next = engine.index.add(1).get();\n    scrollTo(next, jump, -1);\n  }\n  function scrollPrev(jump) {\n    const prev = engine.index.add(-1).get();\n    scrollTo(prev, jump, 1);\n  }\n  function canScrollNext() {\n    const next = engine.index.add(1).get();\n    return next !== selectedScrollSnap();\n  }\n  function canScrollPrev() {\n    const prev = engine.index.add(-1).get();\n    return prev !== selectedScrollSnap();\n  }\n  function scrollSnapList() {\n    return engine.scrollSnapList;\n  }\n  function scrollProgress() {\n    return engine.scrollProgress.get(engine.location.get());\n  }\n  function selectedScrollSnap() {\n    return engine.index.get();\n  }\n  function previousScrollSnap() {\n    return engine.indexPrevious.get();\n  }\n  function slidesInView() {\n    return engine.slidesInView.get();\n  }\n  function slidesNotInView() {\n    return engine.slidesInView.get(false);\n  }\n  function plugins() {\n    return pluginApis;\n  }\n  function internalEngine() {\n    return engine;\n  }\n  function rootNode() {\n    return root;\n  }\n  function containerNode() {\n    return container;\n  }\n  function slideNodes() {\n    return slides;\n  }\n  const self = {\n    canScrollNext,\n    canScrollPrev,\n    containerNode,\n    internalEngine,\n    destroy,\n    off,\n    on,\n    emit,\n    plugins,\n    previousScrollSnap,\n    reInit,\n    rootNode,\n    scrollNext,\n    scrollPrev,\n    scrollProgress,\n    scrollSnapList,\n    scrollTo,\n    selectedScrollSnap,\n    slideNodes,\n    slidesInView,\n    slidesNotInView\n  };\n  activate(userOptions, userPlugins);\n  setTimeout(() => eventHandler.emit('init'), 0);\n  return self;\n}\nEmblaCarousel.globalOptions = undefined;\n\n\n//# sourceMappingURL=embla-carousel.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/embla-carousel/esm/embla-carousel.esm.js\n");

/***/ })

};
;