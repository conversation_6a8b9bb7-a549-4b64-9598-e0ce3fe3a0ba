import Link from 'next/link';

import Ripple from '@/components/magicui/ripple';
import { But<PERSON> } from '@/components/ui/button';
import CoolModeProvider from '@/components/ui/CoolModeProvider';

export default function notFound() {
  return (
    <div className='relative flex h-screen w-full select-none flex-col items-center justify-center gap-10 overflow-hidden bg-black px-4 text-center'>
      <div className='flex flex-col gap-3'>
        <h1 className='z-10 text-7xl font-bold 2xl:text-[120px]'>
          404 Not Found
        </h1>
        <h1 className='z-10 mt-3 text-lg sm:text-2xl'>
          Hmmm Looks like this Link is Broken 🤔
        </h1>
      </div>
      <div className='z-10 flex items-center justify-center gap-6'>
        <Button asChild>
          <Link className='text-lg md:text-xl' href={'/'}>
            Home
          </Link>
        </Button>
        <Link className='text-lg md:text-xl' href={'/cool404'}>
          <span className='text-lg underline underline-offset-8 transition-all ease-in-out hover:cursor-pointer  hover:text-red-300 md:text-xl'>
            Cool 404?
          </span>{' '}
        </Link>
        <CoolModeProvider />
      </div>
      <Ripple />
    </div>
  );
}
