// pages/api/user/update-secret-key.ts
import { NextApiRequest, NextApiResponse } from "next";
import { getServerSession } from "next-auth/next";

import { authOptions } from "@/lib/auth";
import connectDB from "@/lib/mongodb";
import { User } from "@/model/User";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "POST") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({ message: "Unauthorized" });
  }

  try {
    await connectDB();
    const user = await User.findByIdAndUpdate(
      session.user.id,
      { $set: { secretKey: req.body.secretKey } },
      { new: true }
    );

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    return res.status(200).json({ message: "Secret key updated successfully" });
  } catch (error) {
    console.error("Update secret key error:", error);
    return res.status(500).json({ message: "Internal server error" });
  }
}