import { Bitter } from 'next/font/google';
import React from 'react';

const bitter = Bitter({ subsets: ['latin'] });

function Work() {
  return (
    <section className='mt-20 rounded-xl border-[0.5px] border-[#353535]	bg-black md:border-[#1f1f1f] '>
      <div className='flex h-3/6 w-full flex-col gap-10 rounded-3xl px-4 py-10  text-white md:py-14 '>
        <h2
          className={` ${bitter.className} text-center text-3xl md:text-4xl xl:text-5xl`}
        >
          Who Do We <span className='font-medium text-red-400'>Help</span>
        </h2>
        <div className='mt-4 flex flex-col justify-center gap-6 px-0 sm:mt-7 sm:px-8 lg:flex-row lg:text-4xl'>
          {/* Section 1: Developers & Tech Professionals */}
          {workData.map((data, index) => {
            return (
              <div
                key={index}
                className='flex flex-col gap-7 rounded-lg border-[0.5px] border-[#353535] bg-zinc-950 p-5 text-left transition-all duration-300 hover:border-collapse hover:cursor-pointer hover:bg-[#1F1F1F] active:border-collapse active:bg-[#1F1F1F] sm:gap-10 md:border-[#1f1f1f]'
              >
                <h2 className='text-3xl font-semibold text-red-400 md:text-4xl'>
                  {data.id}
                </h2>
                <p className='text-2xl capitalize md:text-3xl'>{data.title}</p>

                <p className='text-base'>{data.description}</p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}

export default Work;

const workData = [
  {
    id: '01',
    title: 'developers & tech professionals',
    description:
      'We assist developers and tech professionals by providing real-time coding assistance during interviews, ensuring they stay confident and focused without distractions.',
  },
  {
    id: '02',
    title: 'remote interview candidates',
    description:
      'Candidates attending remote interviews can rely on our discreet support system to handle complex coding challenges while maintaining a seamless meeting experience.',
  },
  {
    id: '03',
    title: 'coding bootcamp graduates',
    description:
      'Fresh graduates and bootcamp alumni use our platform to bridge the gap between learning and professional interviews, helping them showcase their true potential.',
  },
];