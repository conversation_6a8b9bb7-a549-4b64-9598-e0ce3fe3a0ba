# SBA - Landing Page

![SBA Banner](https://i.postimg.cc/jqJHkw5D/image.png)

## 🌐 Live Website
🔗 [www.sunilbhor.com](http://www.sunilbhor.com)

## 📌 About the Project
This repository contains the complete source code for the **Landing Page** I developed during my internship at **Sunil Bhor & Associates, Nashik**. The landing page is built with modern web technologies to ensure a seamless and responsive experience.

Additionally, I implemented an **Appointment System** to facilitate seamless scheduling and management of appointments.

## 🚀 Tech Stack
- **Next.js 15** - React framework for production-ready applications.
- **React 19** - Latest React version for component-based UI development.
- **Tailwind CSS** - Utility-first CSS framework for responsive design.
- **Appwrite** - Backend-as-a-Service (BaaS) for authentication and database management.
- **Zustand** - Lightweight state management solution.

## 📂 Project Structure
```
├── app/            # Next.js App Router setup
├── components/     # Reusable UI components
├── public/         # Static assets
├── styles/         # Global Tailwind styles
├── lib/            # Helper functions & utilities
├── api/            # API routes
└── README.md       # Documentation
```

## ⚡ Getting Started

### 1️⃣ Clone the Repository
```bash
git clone https://github.com/Arsenic-01/SBA.git
cd SBA
```

### 2️⃣ Install Dependencies
```bash
npm install
```

### 3️⃣ Run the Development Server
```bash
npm run dev
```
The app should now be running on `http://localhost:3000`

## 📸 Screenshots
### 📍 Landing Page
![Landing Page Preview](https://i.postimg.cc/jqJHkw5D/image.png)

## 🎯 Features
✅ **Fully Responsive** - Works on all devices.
✅ **Fast & SEO-Friendly** - Optimized for performance.
✅ **Appwrite Integration** - Secure authentication & database.
✅ **Reusable Components** - Modular and scalable design.
✅ **Appointment System** - Streamlined booking and scheduling.

## 🤝 Contribution
Feel free to fork, create an issue, or submit a pull request! Any feedback or improvements are welcome.

## 📬 Contact
For any inquiries, feel free to reach out:
📧 **Email:** [<EMAIL>](mailto:<EMAIL>)
🔗 **LinkedIn:** [Vedant Bhor](https://www.linkedin.com/in/vedant-bhor-39287828b/)

---

### ⭐ If you like this project, don't forget to **star** the repository! ⭐
