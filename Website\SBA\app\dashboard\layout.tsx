/* eslint-disable import/order */
"use client";

import { useUser, useClerk } from "@clerk/nextjs";
import {
  Navbar,
  NavbarBrand,
  NavbarContent,
  NavbarItem,
  Button,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  NavbarMenuToggle,
  NavbarMenu,
  NavbarMenuItem,
  Avatar,
  Tooltip,
  Card
} from "@nextui-org/react";
import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useRouter, usePathname } from "next/navigation";
import { ReactNode, useEffect, useState, useMemo } from "react";

// Icon imports for navigation items
import {
  FiHome,
  FiUser,
  FiCreditCard,
  FiDollarSign,
  FiKey,
  FiTag,
  FiBook,
  FiShield
} from "react-icons/fi";

// Import navigation items from SidebarNav
const navItems = [
  { title: "Home", href: "/", icon: <FiHome className="text-lg" /> },
  { title: "Profile", href: "/dashboard", icon: <FiUser className="text-lg" /> },
  { title: "Account Type", href: "/dashboard/account-type", icon: <FiCreditCard className="text-lg" /> },
  { title: "Payment History", href: "/dashboard/payment-history", icon: <FiDollarSign className="text-lg" /> },
  { title: "Secret Key", href: "/dashboard/secret-key", icon: <FiKey className="text-lg" /> },
  { title: "Pricing", href: "/pricing", icon: <FiTag className="text-lg" /> },
  { title: "Documentation", href: "/documentation", icon: <FiBook className="text-lg" /> },
];

// Animation variants defined outside component to prevent recreation
const navVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: -5 },
  visible: { opacity: 1, y: 0 }
};

export default function DashboardLayout({ children }: { children: ReactNode }) {
  const { isLoaded, isSignedIn, user } = useUser();
  useClerk();
  const router = useRouter();
  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoadingAdminStatus, setIsLoadingAdminStatus] = useState(true);

  // Add state for database user
  const [dbUser, setDbUser] = useState({
    name: "",
    email: "",
    role: ""
  });

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Fetch user data from MongoDB to check admin status
  useEffect(() => {
    const fetchUserData = async () => {
      if (!isSignedIn) return;

      setIsLoadingAdminStatus(true);
      try {
        console.log("Fetching user data to check admin status");
        const response = await fetch('/api/user/me');
        const data = await response.json();

        if (response.ok && data) {
          console.log("User admin status:", data.isAdmin);
          setIsAdmin(!!data.isAdmin);
          setDbUser({
            name: data.name,
            email: data.email,
            role: data.role || ""
          });
        } else {
          console.error('Failed to fetch user data:', data?.message || 'Unknown error');
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      } finally {
        setIsLoadingAdminStatus(false);
      }
    };

    if (isSignedIn) {
      fetchUserData();
    }
  }, [isSignedIn]);

  // Create full navigation items list, including Admin Dashboard if user is admin
  const adminItems = useMemo(() => [
    ...navItems.slice(0, 1), // Home
    ...(isAdmin ? [{
      title: "Admin Dashboard",
      href: "/dashboard/admin",
      icon: <FiShield className="text-lg" />
    }] : []),
    ...navItems.slice(1), // Rest of the items
  ], [isAdmin]);

  // Redirect to login if not signed in
  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/login');
    }
  }, [isLoaded, isSignedIn, router]);

  const handleLogout = async () => {
    try {
      console.log("Dashboard: Starting logout process...");

      // Instead of directly calling Clerk's signOut, redirect to our dedicated logout page
      // which handles thorough cleaning of all auth data
      router.push('/logout');
    } catch (error) {
      console.error("Logout error:", error);
      // Fallback in case of navigation error - force a hard redirect
      window.location.href = '/logout';
    }
  };

  // Show loading state when clerk is initializing
  if (!isLoaded || isLoadingAdminStatus) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-[#06081c]">
        <div className="text-center">
          <div className="relative inline-flex items-center justify-center">
            <div className="size-16 animate-spin rounded-full border-3 border-gray-300 border-t-[#24AE7C]"></div>
            <div className="absolute flex space-x-1.5">
              <div className="size-2 animate-[pulse_1.5s_ease-in-out_infinite_0.2s] rounded-full bg-[#24AE7C]"></div>
              <div className="size-2 animate-[pulse_1.5s_ease-in-out_infinite_0.4s] rounded-full bg-[#24AE7C]"></div>
              <div className="size-2 animate-[pulse_1.5s_ease-in-out_infinite_0.6s] rounded-full bg-[#24AE7C]"></div>
            </div>
          </div>
          <div className="mt-8">
            <p className="text-xl font-medium text-gray-100">Loading Dashboard</p>
            <p className="mt-3 text-sm text-gray-400">Please wait while we prepare your experience</p>
          </div>
        </div>
      </div>
    );
  }

  // If not signed in, show empty state (redirect will happen via useEffect)
  if (!isSignedIn) {
    return null;
  }

  return (
    <div className="relative flex min-h-screen flex-col overflow-hidden">
      {/* Background gradient elements */}
      <div className="radial-2 absolute inset-0 -z-10"></div>
      <div className="-z-10 absolute right-0 top-0 size-1/3 rounded-full bg-[#24AE7C]/10 blur-[100px]"></div>
      <div className="-z-10 absolute bottom-0 left-0 size-1/3 rounded-full bg-blue-500/10 blur-[100px]"></div>

      {/* Dashboard Navbar */}
      <Navbar
        maxWidth="full"
        isBordered
        className={`glassmorphism z-50 border-b border-slate-800/60 text-white shadow-md transition-all duration-300 ${scrolled ? 'py-1' : 'py-2'}`}
        position="sticky"
        isMenuOpen={isMenuOpen}
        onMenuOpenChange={setIsMenuOpen}
      >
        <NavbarContent className="gap-1 sm:gap-2 md:gap-4">
          <NavbarMenuToggle
            aria-label={isMenuOpen ? "Close menu" : "Open menu"}
            className="sm:hidden"
          />
          <NavbarBrand className="xs:max-w-[200px] max-w-[180px] shrink-0 sm:max-w-none">
            <Link href="/" className="flex items-center gap-1 sm:gap-2">
              <Image
                src="/logo.png"
                width={28}
                height={28}
                priority
                className="w-[28px] transition-all duration-300 hover:scale-110 sm:w-[32px]"
                alt="Company logo: Interview Cracker"
              />
              <h1 className="select-none truncate text-sm font-bold text-[#f8f9fa] sm:text-base md:text-lg lg:text-xl">
                <span className="text-[#24AE7C]">Interview</span>Cracker
              </h1>
            </Link>
          </NavbarBrand>
        </NavbarContent>

        {/* Desktop Navigation Links */}
        <NavbarContent justify="center" className="hidden gap-0 pl-0 sm:flex md:gap-1 md:pl-2 lg:gap-4 lg:pl-4">
          <motion.div
            className="flex flex-wrap justify-center gap-0 sm:gap-1 md:gap-2 lg:gap-3"
            variants={navVariants}
            initial="hidden"
            animate="visible"
          >
            {adminItems.map((item) => (
              <motion.div key={item.href} variants={itemVariants}>
                <Tooltip content={item.title} placement="bottom" delay={300}>
                  <NavbarItem isActive={pathname === item.href}>
                    <Link
                      href={item.href}
                      className={`flex items-center gap-1 rounded-lg px-1 py-1.5 transition-colors duration-200 hover:bg-white/10 sm:px-1.5 sm:py-2 md:px-2 lg:px-3 ${pathname === item.href
                        ? 'bg-[#24AE7C]/20 font-medium text-[#24AE7C]'
                        : 'text-gray-100'
                        }`}
                    >
                      <span>{item.icon}</span>
                      <span className="hidden text-xs md:inline-block md:text-sm lg:block lg:text-base">{item.title}</span>
                    </Link>
                  </NavbarItem>
                </Tooltip>
              </motion.div>
            ))}
          </motion.div>
        </NavbarContent>

        <NavbarContent justify="end" className="gap-4">
          {user?.imageUrl ? (
            <Dropdown placement="bottom-end">
              <DropdownTrigger>
                <Avatar
                  as="button"
                  size="sm"
                  src={user.imageUrl}
                  showFallback
                  className="cursor-pointer transition-transform hover:scale-110"
                  name={dbUser.name || user.firstName || "User"}
                />
              </DropdownTrigger>
              <DropdownMenu aria-label="User menu" className="p-2">
                <DropdownItem key="profile" textValue="Profile info" className="p-2">
                  <div className="flex flex-col gap-1">
                    <p className="font-medium">{dbUser.name || user.fullName || user.firstName}</p>
                    <p className="text-sm text-gray-400">{dbUser.email || user.primaryEmailAddress?.emailAddress}</p>
                  </div>
                </DropdownItem>
                <DropdownItem key="divider" className="my-1 h-px bg-gray-700" />
                <DropdownItem key="settings" textValue="Account settings">
                  <Link href="/dashboard" className="flex w-full">
                    Account Settings
                  </Link>
                </DropdownItem>
                <DropdownItem key="logout" textValue="Logout" className="text-danger" color="danger" onClick={handleLogout}>
                  Log Out
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          ) : (
            <NavbarItem>
              <Button
                color="danger"
                variant="flat"
                onClick={handleLogout}
                size="sm"
                className="bg-red-700/20 text-red-400 transition-all duration-200 hover:bg-red-700/40"
                radius="full"
              >
                Logout
              </Button>
            </NavbarItem>
          )}
        </NavbarContent>

        {/* Mobile Menu */}
        <NavbarMenu className="max-h-[85vh] overflow-y-auto bg-[#0f172a]/95 px-4 pt-6 backdrop-blur-xl">
          {user?.imageUrl && (
            <div className="mb-4 flex items-center gap-3 border-b border-gray-800/50 pb-4">
              <Avatar
                size="sm"
                src={user.imageUrl}
                showFallback
                className="cursor-pointer"
                name={dbUser.name || user.firstName || "User"}
              />
              <div className="overflow-hidden">
                <p className="truncate text-sm font-semibold text-white">{dbUser.name || user.fullName || user.firstName}</p>
                <p className="truncate text-xs text-gray-400">{dbUser.email || user.primaryEmailAddress?.emailAddress}</p>
              </div>
            </div>
          )}

          {adminItems.map((item, index) => (
            <NavbarMenuItem key={`${item.href}-${index}`} className="my-1.5">
              <Link
                href={item.href}
                className={`flex w-full items-center gap-2 rounded-lg px-3 py-2 text-sm transition-colors duration-200 ${pathname === item.href
                  ? 'bg-[#24AE7C]/20 font-semibold text-[#24AE7C]'
                  : 'text-gray-300 hover:bg-white/5'
                  }`}
                onClick={() => setIsMenuOpen(false)}
              >
                <span>{item.icon}</span>
                <span>{item.title}</span>
              </Link>
            </NavbarMenuItem>
          ))}
          <NavbarMenuItem className="mt-6">
            <Button
              color="danger"
              variant="flat"
              onClick={handleLogout}
              fullWidth
              size="sm"
              className="bg-red-700/20 text-red-400 transition-all duration-200 hover:bg-red-700/40"
            >
              Logout
            </Button>
          </NavbarMenuItem>
        </NavbarMenu>
      </Navbar>

      {/* Main Content Area */}
      <div className="flex grow flex-col">
        <motion.div
          className="container mx-auto grow px-4 py-8 md:px-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="cardglass relative overflow-hidden rounded-xl border border-slate-800/40 p-6 shadow-xl sm:p-8">
            {/* Subtle card decoration */}
            <div className="absolute -right-20 -top-20 size-40 rounded-full bg-[#24AE7C]/10 blur-[50px]"></div>
            <div className="absolute -bottom-20 -left-20 size-40 rounded-full bg-blue-500/10 blur-[50px]"></div>

            {/* Card content */}
            <div className="relative z-10">
              {children}
            </div>
          </Card>
        </motion.div>
      </div>

      {/* Footer */}
      <footer className="mt-auto border-t border-slate-800/40 px-4 py-3 backdrop-blur-sm sm:px-8 sm:py-4">
        <div className="container mx-auto flex flex-col items-center justify-between sm:flex-row">
          <div className="mb-3 text-center text-xs text-gray-400 sm:mb-0 sm:text-left sm:text-sm">
            © {new Date().getFullYear()} InterviewCracker. All rights reserved.
          </div>
          <div className="flex flex-wrap justify-center gap-3 sm:gap-4">
            <Link href="/privacy" className="text-xs text-gray-400 transition-colors hover:text-[#24AE7C] sm:text-sm">Privacy Policy</Link>
            <Link href="/terms&conditions" className="text-xs text-gray-400 transition-colors hover:text-[#24AE7C] sm:text-sm">Terms of Service</Link>
          </div>
        </div>
      </footer>
    </div>
  );
}