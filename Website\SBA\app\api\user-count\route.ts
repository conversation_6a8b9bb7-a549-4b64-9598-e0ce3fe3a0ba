import { NextResponse } from 'next/server';

import connectDB from '@/lib/mongodb';
import { User } from '@/model/User';

export async function GET() {
  try {
    await connectDB();
    const count = await User.countDocuments({});
    
    // Return a default value of 39 if count is 0
    const userCount = count > 0 ? count : 39;
    console.log("API: Retrieved user count:", userCount);
    
    return NextResponse.json({ count: userCount });
  } catch (error) {
    console.error('Error fetching user count:', error);
    return NextResponse.json({ count: 39 }, { status: 500 });
  }
}

// Adding a default export as well to ensure better route detection
export default { GET };
