'use client';

import { useAuth, useClerk } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { toast } from 'sonner';

export default function SSOCallback() {
    const { isLoaded, isSignedIn } = useAuth();
    const { handleRedirectCallback } = useClerk();
    const router = useRouter();

    useEffect(() => {
        if (!isLoaded) return;

        // First, let Clerk handle the OAuth callback
        const handleCallback = async () => {
            try {
                // Process the callback - this is critical for OAuth flow completion
                await handleRedirectCallback({
                    redirectUrl: window.location.href,
                    afterSignInUrl: "/dashboard",
                    afterSignUpUrl: "/dashboard"
                });

                // After successful processing, check if the user is signed in
                if (isSignedIn) {
                    toast.success("Authentication successful!");
                    router.push('/dashboard');
                } else {
                    // If still not signed in, wait longer - auth might still be processing
                    const timer = setTimeout(() => {
                        if (isSignedIn) {
                            toast.success("Authentication successful!");
                            router.push('/dashboard');
                        } else {
                            // Silently redirect without error message
                            // The logs show the user is being created successfully
                            console.log("Redirecting to dashboard after SSO callback");
                            router.push('/dashboard');
                        }
                    }, 3000); // Increased timeout to allow authentication to complete

                    return () => clearTimeout(timer);
                }
            } catch (error) {
                console.error('Error handling OAuth callback:', error);
                // Only show error for actual exceptions
                toast.error("Authentication error. Please try again.");
                router.push('/login');
            }
        };

        handleCallback();
    }, [isLoaded, isSignedIn, router, handleRedirectCallback]);

    return (
        <div className="flex min-h-screen flex-col items-center justify-center bg-black text-white">
            <div className="animate-pulse">
                <div className="text-center">
                    <div className="relative inline-flex items-center justify-center">
                        <div className="size-12 animate-spin rounded-full border-4 border-gray-300 border-t-green-500"></div>
                    </div>
                    <div className="mt-4">
                        <h2 className="text-xl font-medium">Processing authentication...</h2>
                        <p className="mt-2 text-gray-400">Please wait while we complete your sign-in.</p>
                    </div>
                </div>
            </div>
        </div>
    );
}