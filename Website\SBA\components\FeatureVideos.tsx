/* eslint-disable react-hooks/rules-of-hooks */
"use client";

import { motion, useAnimation } from "framer-motion";
import { Play, PauseCircle } from "lucide-react";
import React, { useState, useEffect } from "react";

import { cn } from "@/lib/utils";

interface FeatureVideoProps {
    title: string;
    description: string;
    videoSrc: string;
    className?: string;
}

const FeatureVideo = ({ title, description, videoSrc, className }: FeatureVideoProps) => {
    const [isHovered, setIsHovered] = useState(false);
    const [isPaused, setIsPaused] = useState(false);
    const controls = useAnimation();
    const [isVisible, setIsVisible] = useState(false);
    const videoRef = React.useRef<HTMLVideoElement>(null);

    const togglePlayPause = () => {
        if (videoRef.current) {
            if (isPaused) {
                videoRef.current.play();
            } else {
                videoRef.current.pause();
            }
            setIsPaused(!isPaused);
        }
    };

    useEffect(() => {
        // Floating animation
        controls.start({
            y: [0, -5, 0],
            transition: {
                duration: 4,
                repeat: Infinity,
                repeatType: "reverse",
                ease: "easeInOut"
            }
        });
    }, [controls]);

    return (
        <motion.div
            initial={{ opacity: 0, y: 30, rotateX: 5 }}
            whileInView={{ opacity: 1, y: 0, rotateX: 0 }}
            transition={{
                duration: 0.7,
                ease: [0.23, 1, 0.32, 1],
                delay: 0.1
            }}
            viewport={{ once: true, margin: "-50px 0px" }}
            onViewportEnter={() => setIsVisible(true)}
            animate={controls}
            whileHover={{
                scale: 1.02,
                boxShadow: "0 10px 30px -10px rgba(0, 150, 255, 0.3)"
            }}
            onHoverStart={() => setIsHovered(true)}
            onHoverEnd={() => setIsHovered(false)}
            className={cn(
                "flex flex-col rounded-lg bg-gradient-to-br from-gray-900 to-black/60 backdrop-blur-lg border border-gray-800 p-5 transition-all",
                isHovered && "border-blue-500/50",
                isVisible && "shadow-lg shadow-blue-900/20",
                className
            )}
            style={{
                transformStyle: "preserve-3d",
                perspective: "1000px"
            }}
        >
            <motion.h3
                className="mb-2 text-xl font-bold"
                animate={{ color: isHovered ? "#fff" : "#eee" }}
                transition={{ duration: 0.2 }}
            >
                <span className="bg-gradient-to-r from-blue-400 to-cyan-300 bg-clip-text text-transparent">
                    {title}
                </span>
            </motion.h3>

            <motion.p
                className="mb-4 text-sm text-gray-400"
                animate={{ color: isHovered ? "#cbd5e1" : "#94a3b8" }}
                transition={{ duration: 0.3 }}
            >
                {description}
            </motion.p>            <motion.div
                className="relative aspect-video overflow-hidden rounded-md"
                initial={{ scale: 0.95, opacity: 1 }}
                animate={{ scale: isHovered ? 1.02 : 1, opacity: 1 }}
                transition={{
                    type: "spring",
                    stiffness: 300,
                    damping: 20
                }}
                onClick={togglePlayPause}
                style={{
                    cursor: "pointer",
                    transformStyle: "preserve-3d",
                    transform: "translateZ(20px)",
                    boxShadow: isHovered
                        ? "0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 8px 10px -6px rgba(0, 0, 0, 0.2)"
                        : "0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -4px rgba(0, 0, 0, 0.1)"
                }}
            >
                {/* Video element */}                <video
                    ref={videoRef}
                    src={videoSrc}
                    className="size-full object-cover transition-transform duration-700"
                    style={{
                        transform: isHovered ? "scale(1.05)" : "scale(1)"
                    }}
                    autoPlay
                    muted
                    loop
                    playsInline
                    suppressHydrationWarning
                />

                {/* Video progress indicator */}
                <motion.div
                    className="to-purple-500 absolute bottom-0 left-0 h-1 bg-gradient-to-r from-blue-500 via-cyan-400"
                    initial={{ width: "0%" }}
                    animate={{ width: isHovered ? "100%" : "30%" }}
                    transition={{
                        duration: isHovered ? 2.5 : 1.5,
                        ease: "easeInOut",
                        repeat: Infinity,
                        repeatType: "reverse"
                    }}
                />

                {/* Interactive control hint */}
                <motion.div
                    className="absolute bottom-3 right-3 z-30 flex items-center gap-1 rounded-md bg-black/60 px-2 py-1 text-xs text-white/80"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: isHovered ? 1 : 0 }}
                    transition={{ duration: 0.2 }}
                >
                    {isPaused ? <Play className="size-3" /> : <PauseCircle className="size-3" />}
                    <span>{isPaused ? "Play" : "Pause"}</span>
                </motion.div>
            </motion.div>
        </motion.div>
    );
};

export default function FeatureVideos() {
    const features = [
        {
            title: "System Design Generator",
            description: "Generate System Designs with ease and precision",
            videoSrc: "/videos/System.mp4"
        },
        {
            title: "Always On Top",
            description: "Keep your Application Always On Top at all times",
            videoSrc: "/videos/Top.mp4"
        },
        {
            title: "Optimize Code Button",
            description: "Optimize your code with the click of a button",
            videoSrc: "/videos/Optimize.mp4"
        },
        {
            title: "Language And Model Selector",
            description: "Support for multiple languages and AI Models with seamless switching",
            videoSrc: "/videos/language-selector.mp4"
        }
    ];

    const additionalFeatures = [
        {
            title: "Invisibility in Task Manager",
            description: "Enhanced privacy features to remain undetectable",
            videoSrc: "/videos/Invisible.mp4"
        },
        {
            title: "Screen Capture and Processing",
            description: "High-quality Screen Capture and Processing With Just Two Commands",
            videoSrc: "/videos/capture.mp4"
        }
    ];

    const containerVariants = {
        hidden: {},
        visible: {
            transition: {
                staggerChildren: 0.3
            }
        }
    };

    const itemVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.6,
                ease: "easeOut"
            }
        }
    };

    return (
        <section className="w-full py-16">
            <div className="mb-8">                <motion.h3
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="mb-8 text-center text-3xl font-bold text-white md:text-4xl lg:text-5xl"
            ><span className="bg-gradient-to-r from-green-400 to-cyan-500 bg-clip-text text-transparent">
                    Core Features
                </span>
            </motion.h3>
                <motion.p
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    viewport={{ once: true }}
                    className="mx-auto mb-8 max-w-2xl text-center text-gray-400"
                >
                    Explore our premium capabilities designed for professional environments and advanced use cases
                </motion.p>
            </div>
            <motion.div
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{
                    duration: 0.8,
                    staggerChildren: 0.2,
                    delayChildren: 0.3
                }}
                viewport={{ once: true, margin: "-100px 0px" }}
                className="mb-16 grid grid-cols-1 gap-10 md:grid-cols-2"
            >
                {additionalFeatures.map((feature, index) => {
                    // eslint-disable-next-line react-hooks/rules-of-hooks
                    const [isHovered, setIsHovered] = useState(false);
                    // eslint-disable-next-line react-hooks/rules-of-hooks
                    const [isPaused, setIsPaused] = useState(false);
                    const videoRef = React.useRef<HTMLVideoElement>(null);

                    const togglePlayPause = () => {
                        if (videoRef.current) {
                            if (isPaused) {
                                videoRef.current.play();
                            } else {
                                videoRef.current.pause();
                            }
                            setIsPaused(!isPaused);
                        }
                    };

                    return (
                        <motion.div
                            key={index}
                            variants={{
                                hidden: { opacity: 0, y: 50, rotateY: 5 },
                                visible: {
                                    opacity: 1,
                                    y: 0,
                                    rotateY: 0,
                                    transition: {
                                        type: "spring",
                                        stiffness: 100,
                                        damping: 15,
                                        mass: 1
                                    }
                                }
                            }}
                            initial="hidden"
                            whileInView="visible"
                            viewport={{ once: true }}
                            whileHover={{
                                scale: 1.03,
                                boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.8)",
                                y: -5
                            }}
                            onHoverStart={() => setIsHovered(true)}
                            onHoverEnd={() => setIsHovered(false)}
                            className="relative overflow-hidden rounded-xl border border-gray-800 bg-gradient-to-br from-gray-900 via-black to-gray-900"
                            style={{
                                transformStyle: "preserve-3d",
                                transformOrigin: "center center"
                            }}
                        >
                            {/* Animated border */}
                            <motion.div
                                className="to-purple-600 absolute -inset-px z-0 rounded-xl bg-gradient-to-r from-blue-600 via-cyan-500"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: isHovered ? 1 : 0.3 }}
                                transition={{ duration: 0.4 }}
                            />

                            {/* Glowing effect */}
                            <motion.div
                                className="absolute inset-0 z-0"
                                animate={{
                                    boxShadow: isHovered
                                        ? "inset 0 0 30px 5px rgba(56, 189, 248, 0.3)"
                                        : "inset 0 0 0px 0px rgba(56, 189, 248, 0)"
                                }}
                                transition={{ duration: 1 }}
                            />

                            {/* Content container */}
                            <div className="relative z-10 m-px rounded-xl border border-gray-800 bg-gradient-to-br from-gray-900 via-black to-gray-900 p-6">
                                <motion.h3
                                    className="mb-3 text-2xl font-bold"
                                    animate={{
                                        color: isHovered ? "#ffffff" : "#f0f0f0",
                                        textShadow: isHovered ? "0 0 15px rgba(56, 189, 248, 0.5)" : "none"
                                    }}
                                    transition={{ duration: 0.4 }}
                                >
                                    <motion.span
                                        className="to-purple-400 bg-gradient-to-r from-cyan-400 bg-clip-text text-transparent"
                                        animate={{
                                            backgroundPosition: isHovered ? "100%" : "0%"
                                        }}
                                        transition={{ duration: 3, repeat: Infinity, repeatType: "reverse" }}
                                        style={{ backgroundSize: "200%" }}
                                    >
                                        {feature.title}
                                    </motion.span>
                                </motion.h3>

                                <motion.p
                                    className="mb-6 text-gray-300"
                                    animate={{ opacity: isHovered ? 1 : 0.7 }}
                                >
                                    {feature.description}
                                </motion.p>

                                <motion.div
                                    className="relative aspect-video overflow-hidden rounded-lg shadow-2xl"
                                    animate={{
                                        scale: isHovered ? 1.03 : 1,
                                        y: isHovered ? -5 : 0
                                    }}
                                    transition={{
                                        type: "spring",
                                        stiffness: 300,
                                        damping: 20
                                    }}
                                    onClick={togglePlayPause}
                                    style={{
                                        cursor: "pointer",
                                        transformStyle: "preserve-3d"
                                    }}
                                >
                                    {/* Fancy video overlay with animated gradient */}
                                    <motion.div
                                        className="pointer-events-none absolute inset-0 z-10 mix-blend-overlay"
                                        animate={{
                                            background: isHovered
                                                ? "linear-gradient(225deg, rgba(59, 130, 246, 0.3), rgba(139, 92, 246, 0.1))"
                                                : "linear-gradient(225deg, rgba(59, 130, 246, 0.2), rgba(236, 72, 153, 0.1))"
                                        }}
                                        transition={{ duration: 1.5 }}
                                    />

                                    {/* Video element */}                                    <video
                                        ref={videoRef}
                                        src={feature.videoSrc}
                                        className="size-full object-cover transition-transform duration-1000"
                                        style={{
                                            transform: isHovered ? "scale(1.05)" : "scale(1)"
                                        }}
                                        autoPlay
                                        muted
                                        loop
                                        playsInline
                                        suppressHydrationWarning
                                    />{/* Play/Pause overlay removed */}

                                    {/* Video progress indicator */}
                                    <motion.div
                                        className="to-purple-500 absolute bottom-0 left-0 h-1 bg-gradient-to-r from-blue-500 via-cyan-400"
                                        initial={{ width: "0%" }}
                                        animate={{ width: isHovered ? "100%" : "30%" }}
                                        transition={{
                                            duration: isHovered ? 2.5 : 1.5,
                                            ease: "easeInOut",
                                            repeat: Infinity,
                                            repeatType: "reverse"
                                        }}
                                    />

                                    {/* Interactive control hint */}
                                    <motion.div
                                        className="absolute bottom-3 right-3 z-30 flex items-center gap-1 rounded-md bg-black/60 px-2 py-1 text-xs text-white/80"
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: isHovered ? 1 : 0 }}
                                        transition={{ duration: 0.2 }}
                                    >
                                        {isPaused ? <Play className="size-3" /> : <PauseCircle className="size-3" />}
                                        <span>{isPaused ? "Play" : "Pause"}</span>
                                    </motion.div>
                                </motion.div>
                            </div>
                        </motion.div>
                    );
                })}
            </motion.div>

            <div className="mb-10 text-center">
                <motion.h2
                    initial={{ opacity: 0, y: -20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                    className="mb-4 text-3xl font-bold text-white md:text-4xl"
                >                <span className="bg-gradient-to-r from-blue-500 to-blue-400 bg-clip-text text-transparent">
                        Advanced Features
                    </span>
                </motion.h2>
                <motion.p
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    viewport={{ once: true }}
                    className="mx-auto max-w-2xl text-gray-400"
                >
                    Experience our powerful suite of tools designed to enhance your productivity
                </motion.p>
            </div>

            <motion.div
                variants={containerVariants}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                className="mb-12 grid grid-cols-1 gap-6 md:grid-cols-2"
            >
                {features.map((feature, index) => (
                    <motion.div key={index} variants={itemVariants}>
                        <FeatureVideo
                            title={feature.title}
                            description={feature.description}
                            videoSrc={feature.videoSrc}
                        />
                    </motion.div>
                ))}
            </motion.div>
        </section>
    );
}
