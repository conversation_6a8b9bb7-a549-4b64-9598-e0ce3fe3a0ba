'use client';

import { FiBell } from 'react-icons/fi';

import Announcements from '@/components/Announcements';
import MotionDiv from '@/components/motion/MotionDiv';

const AnnouncementsPage = () => {
  return (
    <div className='flex size-full flex-col bg-black pb-14 pt-24 sm:pb-20 sm:pt-28 xl:pb-6 2xl:pb-12'>
      {/* Hero section with subtle animation */}
      <div className="relative mb-8 w-full overflow-hidden sm:mb-12">
        <div className="absolute inset-0 z-0 bg-gradient-to-r from-[#24AE7C]/20 to-blue-900/10"></div>
        <div className="relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <MotionDiv
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            className="flex flex-col items-center py-12 text-center sm:py-16"
          >
            <div className="mb-4 inline-flex items-center justify-center rounded-full border border-gray-700/40 bg-gray-900/60 p-2 backdrop-blur-sm">
              <FiBell className="mr-2 text-[#24AE7C]" size={18} />
              <span className="text-sm text-white">Stay updated</span>
            </div>
            <h1 className="mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-4xl font-bold text-transparent md:text-5xl lg:text-6xl">
              Announcements
            </h1>
            <p className="max-w-3xl text-lg text-gray-300">
              Stay informed with the latest updates, features, and important information about our application.
            </p>

            {/* Decorative elements */}
            <div className="absolute right-10 top-20 size-64 rounded-full bg-[#24AE7C]/10 blur-3xl"></div>
            <div className="absolute bottom-10 left-10 size-48 rounded-full bg-blue-900/10 blur-3xl"></div>
          </MotionDiv>
        </div>
      </div>

      {/* Main content */}
      <div className="relative z-10 mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
        <Announcements />
      </div>

      {/* Bottom decorative element */}
      <div className="mt-16 h-px w-full bg-gradient-to-r from-transparent via-gray-800 to-transparent"></div>
    </div>
  );
};

export default AnnouncementsPage;