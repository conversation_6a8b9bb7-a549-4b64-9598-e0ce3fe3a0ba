// pages/api/auth/[...nextauth].ts
import type { NextApiRequest, NextApiResponse } from "next";
import NextAuth from "next-auth";

import { authOptions } from "@/lib/auth";

// Add enhanced logging for OAuth flow and session handling
console.log("NextAuth API route - Environment:", {
  NEXTAUTH_URL: process.env.NEXTAUTH_URL,
  NODE_ENV: process.env.NODE_ENV,
  hasSecret: !!process.env.NEXTAUTH_SECRET,
  hasGoogleClientId: !!process.env.GOOGLE_CLIENT_ID,
  hasGoogleClientSecret: !!process.env.GOOGLE_CLIENT_SECRET,
});

export default async function auth(req: NextApiRequest, res: NextApiResponse) {
  // Log important request details for debugging
  if (req.method === "POST") {
    console.log("Auth POST request:", {
      url: req.url,
      headers: {
        cookie: req.headers.cookie ? "Present" : "Missing",
        "content-type": req.headers["content-type"],
        referer: req.headers.referer,
      },
      body: req.body ? "Has body" : "No body",
    });
  }

  // Set CORS headers for authentication requests
  res.setHeader("Access-Control-Allow-Credentials", "true");
  res.setHeader("Access-Control-Allow-Origin", process.env.NEXTAUTH_URL || "https://interviewcracker.in");
  res.setHeader("Access-Control-Allow-Methods", "GET,POST");
  
  // Return NextAuth handler with our configured options
  return await NextAuth(req, res, authOptions);
}