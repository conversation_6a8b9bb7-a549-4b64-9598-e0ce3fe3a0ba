// @ts-nocheck
'use client';
import { motion } from 'framer-motion';
// eslint-disable-next-line no-unused-vars
import Link from 'next/link';
import { useRouter } from 'next/navigation';

// eslint-disable-next-line no-unused-vars
import { TransitionLink } from '../utils/TransitionLink';

interface Props {
  otherclassNames?: string;
}

const ShinyButton = ({ Props }: Props) => {
  const router = useRouter();
  return (
    <motion.button
      initial={{ '--x': '100%', scale: 1 }}
      animate={{ '--x': '-100%' }}
      whileTap={{ scale: 0.97 }}
      transition={{
        repeat: Infinity,
        repeatType: 'loop',
        repeatDelay: 1,
        type: 'spring',
        stiffness: 20,
        damping: 15,
        mass: 2,
        scale: {
          type: 'spring',
          stiffness: 10,
          damping: 5,
          mass: 0.1,
        },
      }}
      className={`radial-gradient relative z-10 mt-4 hidden rounded-md px-8 py-2 sm:mt-5 sm:px-10  sm:py-3 md:block ${Props && Props.otherclassNames}`}
      onClick={() => {
        router.push('/contact');
      }}
    >
      Contact Us
      <span className='linear-overlay absolute inset-0 block rounded-md p-px' />
    </motion.button>
  );
};

export default ShinyButton;
