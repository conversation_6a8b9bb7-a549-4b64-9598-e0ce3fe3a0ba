import { useState, useEffect } from "react";

type OS = 'Windows' | 'macOS' | 'Linux' | 'Unknown';

export function useOSDetection(): OS {
  const [os, setOS] = useState<OS>('Unknown');

  useEffect(() => {
    // Only run on client-side
    if (typeof window !== 'undefined') {
      const userAgent = window.navigator.userAgent;
      
      if (userAgent.indexOf('Windows') !== -1) {
        setOS('Windows');
      } else if (userAgent.indexOf('Mac') !== -1) {
        setOS('macOS');
      } else if (userAgent.indexOf('Linux') !== -1) {
        setOS('Linux');
      } else {
        setOS('Unknown');
      }
    }
  }, []);

  return os;
}