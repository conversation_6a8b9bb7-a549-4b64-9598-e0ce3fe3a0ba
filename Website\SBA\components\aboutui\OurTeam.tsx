import React from 'react';

import { organization } from '@/data/index';

const OurTeam = () => {
  return (
    <div className='mb-10  max-w-screen-2xl px-8'>
      <div className='mt-10 grid grid-cols-1  gap-7 sm:mt-16 sm:grid-cols-2 md:mt-24'>
        {organization.map((team, index) => {
          return (
            <div
              className='cursor-pointer  snap-y rounded-lg border-[0.5px]	border-[#353535] bg-black p-3 transition-all ease-in-out hover:border-collapse hover:bg-[#141414] hover:shadow-sm  active:border-collapse active:bg-[#1F1F1F]  md:border-[#1f1f1f]'
              key={index}
            >
              <div className='mt-3 flex flex-col items-baseline gap-1 text-wrap break-words'>
                <h2 className='mb-2 mt-1  px-2 text-xl font-bold'>
                  {team.name}
                </h2>

                <h2 className='rounded-xl bg-neutral-800/80 px-2 py-1 text-base text-white-100'>
                  {team.position}
                </h2>

                <p className='my-2 px-2 	text-base text-slate-300'>
                  {team.experience} of Experience
                </p>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default OurTeam;
