import { getAuth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";

import connectDB from "@/lib/mongodb";
import { User } from "@/model/User";

export async function POST(req: NextRequest) {
  try {
    // Authenticate request with Clerk
    const auth = getAuth(req);
    const { userId } = auth;
    
    if (!userId) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    await connectDB();
    
    // Verify admin status
    const adminUser = await User.findOne({ clerkId: userId });
    if (!adminUser || !adminUser.isAdmin) {
      return NextResponse.json({ message: "Unauthorized: Admin access required" }, { status: 403 });
    }
    
    // Get request data
    const { userId: targetUserId, subscriptionType, subscriptionPlan, subscriptionStartDate, subscriptionEndDate, promptsUsed } = await req.json();
    
    if (!targetUserId) {
      return NextResponse.json({ message: "User ID is required" }, { status: 400 });
    }

    // Update the user with validation
    const updateData: any = {};
    
    // Handle subscription type and related fields
    if (subscriptionType !== undefined) {
      updateData.subscriptionType = subscriptionType;
      
      // For free subscriptions, reset plan and end date
      if (subscriptionType === "free") {
        updateData.subscriptionPlan = null;
        updateData.subscriptionStartDate = null;
        updateData.subscriptionEndDate = null;
      } 
      // For premium subscriptions, validate plan and dates
      else if (subscriptionType === "premium") {
        // Validate subscription plan for premium users
        if (!subscriptionPlan || !["weekly", "monthly", "yearly"].includes(subscriptionPlan)) {
          return NextResponse.json({ 
            message: "Premium subscription requires a valid plan (weekly, monthly or yearly)" 
          }, { status: 400 });
        }
        updateData.subscriptionPlan = subscriptionPlan;
        
        // Validate start date for premium users
        if (!subscriptionStartDate) {
          return NextResponse.json({ 
            message: "Premium subscription requires a start date" 
          }, { status: 400 });
        }
        updateData.subscriptionStartDate = subscriptionStartDate;
        
        // Validate end date for premium users
        if (!subscriptionEndDate) {
          return NextResponse.json({ 
            message: "Premium subscription requires an end date" 
          }, { status: 400 });
        }
        updateData.subscriptionEndDate = subscriptionEndDate;
      }
    } else {
      // If only updating the subscription plan but not the type
      if (subscriptionPlan) {
        // Get current user to check their subscription type
        const currentUser = await User.findById(targetUserId);
        if (!currentUser) {
          return NextResponse.json({ message: "User not found" }, { status: 404 });
        }
        
        // Only allow plan updates for premium users
        if (currentUser.subscriptionType === "premium") {
          if (!["weekly", "monthly", "yearly"].includes(subscriptionPlan)) {
            return NextResponse.json({ 
              message: "Invalid subscription plan. Choose 'weekly', 'monthly' or 'yearly'" 
            }, { status: 400 });
          }
          updateData.subscriptionPlan = subscriptionPlan;
        } else {
          return NextResponse.json({ 
            message: "Cannot set subscription plan for free users" 
          }, { status: 400 });
        }
      }
      
      // If updating start date
      if (subscriptionStartDate !== undefined) {
        const currentUser = await User.findById(targetUserId);
        if (!currentUser) {
          return NextResponse.json({ message: "User not found" }, { status: 404 });
        }
        
        // Only allow start date updates for premium users
        if (currentUser.subscriptionType === "premium") {
          updateData.subscriptionStartDate = subscriptionStartDate;
        } else {
          return NextResponse.json({ 
            message: "Cannot set subscription start date for free users" 
          }, { status: 400 });
        }
      }
      
      // If updating end date
      if (subscriptionEndDate !== undefined) {
        const currentUser = await User.findById(targetUserId);
        if (!currentUser) {
          return NextResponse.json({ message: "User not found" }, { status: 404 });
        }
        
        // Only allow end date updates for premium users
        if (currentUser.subscriptionType === "premium") {
          updateData.subscriptionEndDate = subscriptionEndDate;
        } else {
          return NextResponse.json({ 
            message: "Cannot set subscription end date for free users" 
          }, { status: 400 });
        }
      }
    }
    
    if (promptsUsed !== undefined) {
      updateData.promptsUsed = promptsUsed;
    }

    const updatedUser = await User.findByIdAndUpdate(
      targetUserId,
      { $set: updateData },
      { new: true }
    );
    
    if (!updatedUser) {
      return NextResponse.json({ message: "User not found" }, { status: 404 });
    }

    return NextResponse.json({ 
      user: {
        id: updatedUser._id,
        name: updatedUser.name,
        email: updatedUser.email,
        subscriptionType: updatedUser.subscriptionType || "free",
        subscriptionPlan: updatedUser.subscriptionPlan || null,
        subscriptionStartDate: updatedUser.subscriptionStartDate,
        subscriptionEndDate: updatedUser.subscriptionEndDate,
        promptsUsed: updatedUser.promptsUsed || 0
      },
      status: 'success',
      message: 'User updated successfully'
    });
  } catch (error) {
    console.error("Admin update user error:", error);
    return NextResponse.json({ 
      message: "Internal server error", 
      error: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 });
  }
}