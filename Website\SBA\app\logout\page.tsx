"use client";

import { useClerk } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function LogoutPage() {
    const router = useRouter();
    const { signOut } = useClerk();
    const [isLoggingOut, setIsLoggingOut] = useState(true);

    useEffect(() => {
        // Directly handle logout without showing the page
        const handleLogout = async () => {
            try {
                setIsLoggingOut(true);
                console.log("Starting logout process...");

                // Clear localStorage and sessionStorage to remove any cached data
                try {
                    window.localStorage.clear();
                    window.sessionStorage.clear();
                    console.log("Cleared local and session storage");
                } catch (storageError) {
                    console.error("Error clearing storage:", storageError);
                }

                // Clear cookies with different paths to ensure complete removal
                const paths = ['/', '/dashboard', '/api', ''];
                const domains = [window.location.hostname, '', null, undefined];

                paths.forEach(path => {
                    domains.forEach(domain => {
                        document.cookie.split(";").forEach(c => {
                            const cookie = c.trim();
                            if (cookie) {
                                const cookieName = cookie.split("=")[0];
                                document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}${domain ? `; domain=${domain}` : ''}`;
                            }
                        });
                    });
                });
                console.log("Cleared cookies");

                // Make sure the browser actually processes the cookie clearing
                // before proceeding with Clerk sign-out
                await new Promise(resolve => setTimeout(resolve, 100));

                // Use Clerk's signOut for complete session termination with specific options
                await signOut({
                    redirectUrl: '/login',
                    sessionId: 'all' // Sign out from all sessions
                });

                console.log("Clerk signOut completed");

                // In case Clerk's redirect doesn't work, force a hard redirect after a delay
                setTimeout(() => {
                    console.log("Forcing redirect to login page");
                    setIsLoggingOut(false);
                    window.location.href = '/login'; // Use location.href for a hard redirect
                }, 2000);
            } catch (error) {
                console.error("Logout error:", error);

                // If an error occurs, do a hard refresh/redirect to the login page
                setIsLoggingOut(false);
                window.location.href = '/login';
            }
        };

        // Execute logout immediately when the page loads
        handleLogout();

        // Safety timeout - if nothing happens for 5 seconds, force redirect
        const safetyTimeout = setTimeout(() => {
            if (isLoggingOut) {
                console.log("Safety timeout triggered - forcing redirect");
                window.location.href = '/login';
            }
        }, 5000);

        return () => clearTimeout(safetyTimeout);
    }, [signOut, router]);

    return (
        <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-900">
            <div className="rounded-xl border border-gray-800 bg-black/50 p-8 text-center shadow-xl backdrop-blur-lg">
                <h1 className="mb-4 text-2xl font-bold text-white">Signing Out...</h1>
                <div className="mx-auto mb-4 size-12 animate-spin rounded-full border-2 border-gray-300 border-t-blue-500"></div>
                <p className="text-gray-400">Please wait while we securely log you out.</p>
            </div>
        </div>
    );
}