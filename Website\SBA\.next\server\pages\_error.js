"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_error";
exports.ids = ["pages/_error"];
exports.modules = {

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ pageWrapperTemplate),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps)\n/* harmony export */ });\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @sentry/nextjs */ \"@sentry/nextjs\");\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @clerk/nextjs */ \"@clerk/nextjs\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nfunction MyApp({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__.ClerkProvider, {\n        ...pageProps,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\pages\\\\_app.tsx\",\n            lineNumber: 8,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\pages\\\\_app.tsx\",\n        lineNumber: 7,\n        columnNumber: 9\n    }, this);\n}\n\nvar serverComponentModule = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    default: MyApp\n});\n\n/*\n * This file is a template for the code which will be substituted when our webpack loader handles non-API files in the\n * `pages/` directory.\n *\n * We use `__SENTRY_WRAPPING_TARGET_FILE__.cjs` as a placeholder for the path to the file being wrapped. Because it's not a real package,\n * this causes both TS and ESLint to complain, hence the pragma comments below.\n */\n\n\nconst userPageModule = serverComponentModule ;\n\nconst pageComponent = userPageModule ? userPageModule.default : undefined;\n\nconst origGetInitialProps = pageComponent ? pageComponent.getInitialProps : undefined;\nconst origGetStaticProps = userPageModule ? userPageModule.getStaticProps : undefined;\nconst origGetServerSideProps = userPageModule ? userPageModule.getServerSideProps : undefined;\n\n// Rollup will aggressively tree-shake what it perceives to be unused properties\n// on objects. Because the key that's used to index into this object (/_app)\n// is replaced during bundling, Rollup can't see that these properties are in fact\n// used. Using `Object.freeze` signals to Rollup that it should not tree-shake\n// this object.\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst getInitialPropsWrappers = Object.freeze({\n  '/_app': _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapAppGetInitialPropsWithSentry,\n  '/_document': _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapDocumentGetInitialPropsWithSentry,\n  '/_error': _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapErrorGetInitialPropsWithSentry,\n});\n\nconst getInitialPropsWrapper = getInitialPropsWrappers['/_app'] || _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapGetInitialPropsWithSentry;\n\nif (pageComponent && typeof origGetInitialProps === 'function') {\n  pageComponent.getInitialProps = getInitialPropsWrapper(origGetInitialProps) ;\n}\n\nconst getStaticProps =\n  typeof origGetStaticProps === 'function'\n    ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapGetStaticPropsWithSentry(origGetStaticProps, '/_app')\n    : undefined;\nconst getServerSideProps =\n  typeof origGetServerSideProps === 'function'\n    ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapGetServerSidePropsWithSentry(origGetServerSideProps, '/_app')\n    : undefined;\n\nconst pageWrapperTemplate = pageComponent ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapPageComponentWithSentry(pageComponent ) : pageComponent;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ pageWrapperTemplate),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps)\n/* harmony export */ });\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @sentry/nextjs */ \"@sentry/nextjs\");\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Html, {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Head, {}, void 0, false, {\n                fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"body\", {\n                suppressHydrationWarning: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Main, {}, void 0, false, {\n                        fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.NextScript, {}, void 0, false, {\n                        fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\pages\\\\_document.tsx\",\n                lineNumber: 7,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 9\n    }, this);\n}\n\nvar serverComponentModule = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    default: Document\n});\n\n/*\n * This file is a template for the code which will be substituted when our webpack loader handles non-API files in the\n * `pages/` directory.\n *\n * We use `__SENTRY_WRAPPING_TARGET_FILE__.cjs` as a placeholder for the path to the file being wrapped. Because it's not a real package,\n * this causes both TS and ESLint to complain, hence the pragma comments below.\n */\n\n\nconst userPageModule = serverComponentModule ;\n\nconst pageComponent = userPageModule ? userPageModule.default : undefined;\n\nconst origGetInitialProps = pageComponent ? pageComponent.getInitialProps : undefined;\nconst origGetStaticProps = userPageModule ? userPageModule.getStaticProps : undefined;\nconst origGetServerSideProps = userPageModule ? userPageModule.getServerSideProps : undefined;\n\n// Rollup will aggressively tree-shake what it perceives to be unused properties\n// on objects. Because the key that's used to index into this object (/_document)\n// is replaced during bundling, Rollup can't see that these properties are in fact\n// used. Using `Object.freeze` signals to Rollup that it should not tree-shake\n// this object.\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst getInitialPropsWrappers = Object.freeze({\n  '/_app': _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapAppGetInitialPropsWithSentry,\n  '/_document': _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapDocumentGetInitialPropsWithSentry,\n  '/_error': _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapErrorGetInitialPropsWithSentry,\n});\n\nconst getInitialPropsWrapper = getInitialPropsWrappers['/_document'] || _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapGetInitialPropsWithSentry;\n\nif (pageComponent && typeof origGetInitialProps === 'function') {\n  pageComponent.getInitialProps = getInitialPropsWrapper(origGetInitialProps) ;\n}\n\nconst getStaticProps =\n  typeof origGetStaticProps === 'function'\n    ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapGetStaticPropsWithSentry(origGetStaticProps, '/_document')\n    : undefined;\nconst getServerSideProps =\n  typeof origGetServerSideProps === 'function'\n    ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapGetServerSidePropsWithSentry(origGetServerSideProps, '/_document')\n    : undefined;\n\nconst pageWrapperTemplate = pageComponent ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapPageComponentWithSentry(pageComponent ) : pageComponent;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "./pages/_error.jsx":
/*!**************************!*\
  !*** ./pages/_error.jsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ pageWrapperTemplate),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps)\n/* harmony export */ });\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @sentry/nextjs */ \"@sentry/nextjs\");\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_error__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/error */ \"next/error\");\n/* harmony import */ var next_error__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_error__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nconst CustomErrorComponent = (props)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)((next_error__WEBPACK_IMPORTED_MODULE_2___default()), {\n        statusCode: props.statusCode\n    }, void 0, false, {\n        fileName: \"D:\\\\WebDevelopment\\\\coder\\\\Website\\\\SBA\\\\pages\\\\_error.jsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, undefined);\n};\nCustomErrorComponent.getInitialProps = async (contextData)=>{\n    // In case this is running in a serverless function, await this in order to give Sentry\n    // time to send the error before the lambda exits\n    await _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.captureUnderscoreErrorException(contextData);\n    // This will contain the status code of the response\n    return next_error__WEBPACK_IMPORTED_MODULE_2___default().getInitialProps(contextData);\n};\n\nvar serverComponentModule = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    default: CustomErrorComponent\n});\n\n/*\n * This file is a template for the code which will be substituted when our webpack loader handles non-API files in the\n * `pages/` directory.\n *\n * We use `__SENTRY_WRAPPING_TARGET_FILE__.cjs` as a placeholder for the path to the file being wrapped. Because it's not a real package,\n * this causes both TS and ESLint to complain, hence the pragma comments below.\n */\n\n\nconst userPageModule = serverComponentModule ;\n\nconst pageComponent = userPageModule ? userPageModule.default : undefined;\n\nconst origGetInitialProps = pageComponent ? pageComponent.getInitialProps : undefined;\nconst origGetStaticProps = userPageModule ? userPageModule.getStaticProps : undefined;\nconst origGetServerSideProps = userPageModule ? userPageModule.getServerSideProps : undefined;\n\n// Rollup will aggressively tree-shake what it perceives to be unused properties\n// on objects. Because the key that's used to index into this object (/_error)\n// is replaced during bundling, Rollup can't see that these properties are in fact\n// used. Using `Object.freeze` signals to Rollup that it should not tree-shake\n// this object.\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst getInitialPropsWrappers = Object.freeze({\n  '/_app': _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapAppGetInitialPropsWithSentry,\n  '/_document': _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapDocumentGetInitialPropsWithSentry,\n  '/_error': _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapErrorGetInitialPropsWithSentry,\n});\n\nconst getInitialPropsWrapper = getInitialPropsWrappers['/_error'] || _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapGetInitialPropsWithSentry;\n\nif (pageComponent && typeof origGetInitialProps === 'function') {\n  pageComponent.getInitialProps = getInitialPropsWrapper(origGetInitialProps) ;\n}\n\nconst getStaticProps =\n  typeof origGetStaticProps === 'function'\n    ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapGetStaticPropsWithSentry(origGetStaticProps, '/_error')\n    : undefined;\nconst getServerSideProps =\n  typeof origGetServerSideProps === 'function'\n    ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapGetServerSidePropsWithSentry(origGetServerSideProps, '/_error')\n    : undefined;\n\nconst pageWrapperTemplate = pageComponent ? _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapPageComponentWithSentry(pageComponent ) : pageComponent;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_error.jsx\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! private-next-pages/_error */ \"./pages/_error.jsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "@clerk/nextjs":
/*!********************************!*\
  !*** external "@clerk/nextjs" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("@clerk/nextjs");

/***/ }),

/***/ "@sentry/nextjs":
/*!*********************************!*\
  !*** external "@sentry/nextjs" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@sentry/nextjs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/error":
/*!*****************************!*\
  !*** external "next/error" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("next/error");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();