'use client';

import { <PERSON><PERSON> } from '@nextui-org/button';
import React from 'react';

import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/Tooltip';

import ModalContent from '../ModalDescription';


interface ServicesExpandedProps {
  Modaltitle: string | undefined;
  ModalDescription: string | undefined;
  id: number | undefined;
  isOpen: boolean;
  onClose: () => void;
  isInvalid?: boolean;
}

const ServicesExpanded: React.FC<ServicesExpandedProps> = ({
  Modaltitle,
  ModalDescription,
  id,
  isOpen,
  isInvalid,
  onClose,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-h-[80vh] max-w-7xl scrollbar-hide'>
        <DialogHeader className='mt-5'>
          <DialogTitle className='text-center text-3xl font-bold lg:px-5 lg:text-4xl'>
            {Modaltitle}
          </DialogTitle>
        </DialogHeader>
        <DialogDescription className='mt-5 text-center sm:mt-0 lg:px-5'>
          <span className='max-w-md text-white/40'>{ModalDescription}</span>
        </DialogDescription>
        <ModalContent id={id!} isInvalid={isInvalid} />
        <DialogFooter>
          <div className='flex flex-col gap-4 sm:flex-row sm:justify-end'>
            <TooltipProvider>
              <Tooltip>
                <TooltipContent>
                  Office Timing : 10:00 AM - 08:00 PM
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div>
                    <Button
                      color='secondary'
                      variant='shadow'
                      radius='lg'
                      className='w-full'
                      onClick={() => window.open('tel:+917879310513', '_blank')}
                    >
                      Call us 📞
                    </Button>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p className='text-center text-green-400'>
                    95% Response Rate ✅
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div>
                    <Button
                      color='secondary'
                      variant='shadow'
                      radius='lg'
                      className='w-full'
                      onClick={() =>
                        window.open('mailto:<EMAIL>', '_blank')
                      }
                    >
                      Email 📧
                    </Button>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p><EMAIL></p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <DialogClose asChild>
              <Button radius='lg' size='md' variant='ghost'>
                Close
              </Button>
            </DialogClose>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ServicesExpanded;
