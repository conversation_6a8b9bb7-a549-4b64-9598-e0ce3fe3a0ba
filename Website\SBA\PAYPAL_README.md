# PayPal Integration Guide

This document explains how the PayPal integration works in the application and how to troubleshoot common issues.

## Overview

The application uses PayPal's REST API to process payments for premium subscriptions. The integration supports both sandbox (development) and live (production) environments.

## Configuration

### Environment Variables

The following environment variables need to be set in the `.env` file:

```
PAYPAL_CLIENT_ID="your_paypal_client_id"
PAYPAL_CLIENT_SECRET="your_paypal_client_secret"
```

- For development, use sandbox credentials
- For production, use live credentials

### Environment Detection

The application automatically detects the environment based on the `NODE_ENV` variable:
- If `NODE_ENV` is set to "development", the sandbox environment is used
- If `NODE_ENV` is set to "production", the live environment is used

## Payment Flow

1. User selects a subscription plan and chooses PayPal as the payment method
2. The application creates a PayPal order via the `/api/paypal/create-order` endpoint
3. User is redirected to PayPal to approve the payment
4. After approval, PayPal redirects back to the application's capture endpoint
5. The application captures the payment via the `/api/paypal/capture-order` endpoint
6. The user's subscription is activated

## Troubleshooting

### Common Errors

#### "Client Authentication failed"

This error occurs when:
- The PayPal client ID or secret is incorrect
- The credentials don't match the environment (using sandbox credentials in production or vice versa)
- The credentials have expired or been revoked

**Solution:**
1. Verify that the correct credentials are set in the `.env` file
2. Make sure the credentials match the environment (sandbox/live)
3. Check if the credentials are still valid in the PayPal Developer Dashboard

#### "Failed to get PayPal approval link"

This error occurs when:
- The PayPal order was created but didn't return an approval URL
- There was an issue with the order creation process

**Solution:**
1. Check the server logs for more detailed error information
2. Verify that the order parameters are correct
3. Ensure the PayPal account is in good standing

## Testing

### Sandbox Testing

In the sandbox environment, you can use the following test accounts:
- Buyer: Use any sandbox test account
- Payment: Use the sandbox test credit cards

### Production Testing

Before going live:
1. Test the entire payment flow in the sandbox environment
2. Verify that webhooks are properly configured
3. Test currency conversion for international users

## Support

If you encounter issues with the PayPal integration:
1. Check the application logs for detailed error messages
2. Verify all environment variables are correctly set
3. Ensure the PayPal Developer account is in good standing
4. Contact PayPal Developer Support for API-specific issues
