// components/dashboard/UserProfileCard.tsx
"use client";

import { Card, CardBody, Tooltip } from "@nextui-org/react";
import { motion } from "framer-motion";
import Image from "next/image";
import { useState } from "react";
import { <PERSON>a<PERSON><PERSON>, <PERSON>aEnvelope, FaCheckCircle } from "react-icons/fa";

interface UserProfileProps {
    user: {
        name: string;
        email: string;
        image?: string;
    };
}

const UserProfileCard: React.FC<UserProfileProps> = ({ user }) => {
    const [isHovered, setIsHovered] = useState(false);

    // Animation variants
    const cardVariants = {
        hover: {
            scale: 1.02,
            transition: {
                duration: 0.3,
                ease: "easeInOut"
            }
        },
        initial: {
            scale: 1,
            transition: {
                duration: 0.3,
                ease: "easeInOut"
            }
        }
    };

    return (
        <motion.div
            initial="initial"
            animate={isHovered ? "hover" : "initial"}
            variants={cardVariants}
            onHoverStart={() => setIsHovered(true)}
            onHoverEnd={() => setIsHovered(false)}
        >
            <Card className="relative overflow-hidden border border-slate-800/60 bg-[#0f172a]/60 shadow-xl backdrop-blur-sm">
                {/* Decorative elements */}
                <div className="absolute -left-16 -top-16 size-40 rounded-full bg-[#24AE7C]/5 opacity-70 blur-2xl transition-all duration-700 group-hover:opacity-100"></div>
                <div className="absolute bottom-0 right-0 size-32 rounded-full bg-blue-500/5 opacity-60 blur-[30px]"></div>

                <CardBody className="relative z-10 p-6">
                    <h2 className="text-18-bold mb-5 flex items-center gap-2 text-white">
                        <span className="rounded-lg bg-gradient-to-br from-[#24AE7C]/20 to-[#24AE7C]/5 p-2">
                            <FaUser className="text-[#24AE7C]" />
                        </span>
                        Profile Information
                        <Tooltip content="Verified Account" placement="right">
                            <div className="ml-auto cursor-pointer">
                                <div>
                                    <FaCheckCircle className="text-[#24AE7C]" />
                                </div>
                            </div>
                        </Tooltip>
                    </h2>

                    <div className="flex flex-col items-center gap-6 md:flex-row md:items-start">
                        <div className="group relative shrink-0">
                            <div className="to-purple-500 absolute inset-0 rounded-full bg-gradient-to-br from-[#24AE7C] via-blue-500 opacity-70 blur-sm transition-opacity duration-300 group-hover:opacity-100"></div>
                            <div className="relative size-24 overflow-hidden rounded-full border-2 border-transparent bg-[#0f172a] p-1">
                                <Image
                                    src={user.image || "https://res.cloudinary.com/dtemmbo4i/image/upload/v1741714656/guest_ozaygm.png"}
                                    alt="User avatar"
                                    width={96}
                                    height={96}
                                    className="size-full rounded-full object-cover"
                                />
                            </div>
                            <div className="absolute bottom-0 right-0 rounded-full border-2 border-[#0f172a] bg-[#24AE7C] p-1 opacity-0 shadow-lg transition-opacity duration-300 group-hover:opacity-100">
                                <Tooltip content="Verified Profile" placement="bottom">
                                    <div className="cursor-pointer">
                                        <div>
                                            <FaCheckCircle className="text-xs text-white" />
                                        </div>
                                    </div>
                                </Tooltip>
                            </div>
                        </div>

                        <div className="grow space-y-4 text-center md:text-left">
                            <div className="group flex items-center gap-3 rounded-md border border-slate-700/40 bg-[#181f33]/80 p-4 backdrop-blur-sm transition-all duration-300 hover:border-[#24AE7C]/20">
                                <span className="rounded-md bg-[#24AE7C]/10 p-2 text-[#24AE7C]">
                                    <FaUser />
                                </span>
                                <div>
                                    <p className="text-14-medium mb-1 text-gray-400">Name</p>
                                    <p className="text-16-semibold text-white transition-colors duration-300 group-hover:text-[#24AE7C]">{user.name}</p>
                                </div>
                            </div>

                            <div className="group flex items-center gap-3 rounded-md border border-slate-700/40 bg-[#181f33]/80 p-4 backdrop-blur-sm transition-all duration-300 hover:border-[#24AE7C]/20">
                                <span className="rounded-md bg-[#24AE7C]/10 p-2 text-[#24AE7C]">
                                    <FaEnvelope />
                                </span>
                                <div>
                                    <p className="text-14-medium mb-1 text-gray-400">Email</p>
                                    <p className="text-16-semibold text-white transition-colors duration-300 group-hover:text-[#24AE7C]">{user.email}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </CardBody>
            </Card>
        </motion.div>
    );
};

export default UserProfileCard;