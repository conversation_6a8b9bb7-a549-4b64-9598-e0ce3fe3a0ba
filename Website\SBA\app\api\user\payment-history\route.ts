import { clerk<PERSON>lient } from "@clerk/clerk-sdk-node";
import { getAuth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";

import connectDB from "@/lib/mongodb";
import { Payment } from "@/model/Payment";
import { User } from "@/model/User";

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  console.log("Payment history API route triggered");
  
  try {
    // Get the authentication data from Clerk
    const auth = getAuth(req);
    const { userId } = auth;
    
    console.log("Auth check: userId =", userId);
    
    if (!userId) {
      console.log("No userId found in request");
      return NextResponse.json({ message: "Unauthorized", error: "No userId" }, { status: 401 });
    }

    // Connect to MongoDB
    console.log("Connecting to MongoDB");
    await connectDB();
    console.log("MongoDB connected");
    
    // Find the user by their Clerk ID
    console.log("Finding user with clerkId:", userId);
    let user = await User.findOne({ clerkId: userId });
    
    // If user doesn't exist, create a new one with data from Clerk
    if (!user) {
      console.log("User not found, fetching details from Clerk");
      
      try {
        // Get user data from Clerk
        const clerkUser = await clerkClient.users.getUser(userId);
        
        // Create a new user record
        user = new User({
          clerkId: userId,
          name: clerkUser.firstName
            ? `${clerkUser.firstName} ${clerkUser.lastName || ''}`
            : "New User",
          email: clerkUser.emailAddresses[0]?.emailAddress || "<EMAIL>",
          avatar: clerkUser.imageUrl,
          role: "user",
          subscriptionType: "free",
          promptsUsed: 0,
          createdAt: new Date()
        });
      } catch (clerkError) {
        console.error("Error fetching user from Clerk:", clerkError);
        
        // Fall back to creating a user with default values
        user = new User({
          clerkId: userId,
          name: "New User",
          email: "<EMAIL>",
          avatar: "/default-avatar.png",
          role: "user",
          subscriptionType: "free",
          promptsUsed: 0,
          createdAt: new Date()
        });
      }
      
      await user.save();
      console.log("New user created with ID:", user._id);
    }
    console.log("User found:", user._id);
    
    // Find payments using the Clerk ID directly instead of MongoDB ObjectId
    // This change matches our updated Payment model schema
    console.log("Finding payments for Clerk user:", userId);
    const payments = await Payment.find({ user: userId }).sort({ createdAt: -1 });
    console.log("Payments found:", payments.length);
    
    // Transform payments to match the expected format in the frontend
    const formattedPayments = payments.map(payment => ({
      id: payment._id.toString(),
      amount: payment.amount / 100, // Convert from cents to dollars/main unit
      currency: payment.currency,
      status: payment.status,
      date: payment.createdAt,
      method: payment.method,
      description: `${payment.plan.charAt(0).toUpperCase() + payment.plan.slice(1)} Plan Subscription`,
      invoice_url: null // You can add invoice URL generation here if needed
    }));
    
    // Return the payment data
    return NextResponse.json({ 
      payments: formattedPayments,
      status: 'success',
      message: 'Payment history fetched successfully'
    });
  } catch (error) {
    console.error("Payment history API error:", error);
    return NextResponse.json({ 
      message: "Internal server error", 
      error: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 });
  }
}