import bcrypt from "bcryptjs";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";

import { authOptions } from "@/lib/auth";
import connectDB from "@/lib/mongodb";
import { User } from "@/model/User"; // Ensure this matches model/User.ts


export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || !session.user?.email) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    await connectDB();
    const user = await User.findOne({ email: session.user.email });

    if (!user) {
      return NextResponse.json({ message: "User not found" }, { status: 404 });
    }

    if (user.provider !== "email") {
      return NextResponse.json({ message: "Password change not allowed for OAuth users" }, { status: 403 });
    }

    const { currentPassword, newPassword, confirmPassword } = await req.json();

    if (!currentPassword || !newPassword || !confirmPassword) {
      return NextResponse.json({ message: "All password fields are required" }, { status: 400 });
    }

    if (newPassword !== confirmPassword) {
      return NextResponse.json({ message: "New passwords do not match" }, { status: 400 });
    }

    // Debug: Check if comparePassword exists
    console.log("User methods:", Object.getOwnPropertyNames(Object.getPrototypeOf(user)));
    console.log("User object:", user);

    if (!user.password) {
      return NextResponse.json({ message: "Password not set for this user" }, { status: 400 });
    }
    
    const isMatch = await bcrypt.compare(currentPassword, user.password);
    if (!isMatch) {
      return NextResponse.json({ message: "Current password is incorrect" }, { status: 400 });
    }

    user.password = await bcrypt.hash(newPassword, 10);
    await user.save();

    return NextResponse.json({ message: "Password changed successfully" }, { status: 200 });
  } catch (error) {
    console.error("Update password error:", error);
    return NextResponse.json({ message: "Internal server error" }, { status: 500 });
  }
}