"use client";

import { useEffect, useState } from "react";

import FAQ from "@/components/Accordian";
import CallToAction from "@/components/CallToAction";
import UndetectableBadge from "@/components/componentsUndetectableBadge";
import Development from "@/components/Development";
import DownloadButtons from "@/components/DownloadButtons";
import FeatureVideos from "@/components/FeatureVideos";
import Grid from "@/components/Grid";
import Hero from "@/components/Hero";
import Testimonials from "@/components/Testimonials";
import InfiniteMovingCards from "@/components/ui/InfiniteCards";
// eslint-disable-next-line no-unused-vars
import VideoDemoSection from "@/components/VideoDemoSection";
import { companieslogo } from "@/data";

const videoSource1 = "/videos/video1.mp4";
const videoSource2 = "/videos/video2.mp4";

export default function Home() {
  const [, setIsMobile] = useState(true);

  useEffect(() => {
    // Check screen size on component mount and window resize
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 640); // 640px is the standard sm breakpoint in Tailwind
    };

    // Initial check
    checkScreenSize();

    // Add event listener for window resize
    window.addEventListener('resize', checkScreenSize);

    // Cleanup
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  return (
    <>
      <main className="relative mx-auto flex flex-col items-center justify-center overflow-hidden bg-black-100">
        <Hero /> {/* Mostly SSR */}

        {/* Positioned UndetectableBadge below Navbar and above other content */}
        <div className="z-20 my-2 flex w-full justify-center">
          <UndetectableBadge />
        </div>

        {/* Pricing Timeline Component */}
        {/* <PriceTimeline className="bg-black-100 z-20" />  */}

        <div className="relative z-20 -mt-16 mb-8 w-full">
          <VideoDemoSection videoSource1={videoSource1} videoSource2={videoSource2} />
        </div>

        <InfiniteMovingCards logos={companieslogo} />

        <div className="w-full max-w-7xl px-2 sm:px-10">
          {/* CoreFeatures component removed */}
          {/* <LampDemo /> */}
          <FeatureVideos />
        </div>
        <div className="w-full max-w-6xl px-5 sm:px-10 2xl:max-w-7xl">
          <Grid /> {/* Mostly Client */}
          <Development /> {/* SSR */}
        </div>
        <div className="w-full max-w-6xl px-5 sm:px-10 2xl:max-w-7xl">
          <Testimonials /> {/* Client */}
        </div>
        <div className="w-full max-w-7xl px-5 sm:px-10">
          <CallToAction />
        </div>
        <div className="w-full max-w-6xl px-5 sm:px-10">
          <FAQ />
        </div>
        <DownloadButtons />
      </main>
    </>
  );
}