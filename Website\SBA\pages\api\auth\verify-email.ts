import crypto from 'crypto'; // Import crypto

import { NextApiRequest, NextApiResponse } from "next";

import connectDB from "@/lib/mongodb";
import { PendingUser } from "@/model/PendingUser";
import { User } from "@/model/User";

// Helper function to generate consistent secret keys
const generateSecretKey = () => `sk_${crypto.randomBytes(16).toString("hex")}`;

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "POST") return res.status(405).json({ message: "Method not allowed" });

  await connectDB();
  const { email, code } = req.body;
  if (!email || !code) return res.status(400).json({ message: "Email and code are required" });

  const pendingUsers = await PendingUser.find({ email });
  if (pendingUsers.length !== 1) {
    return res.status(400).json({ message: "Invalid or expired verification code" });
  }
  const pendingUser = pendingUsers[0];
  if (pendingUser.verificationCode !== code || pendingUser.verificationCodeExpire < new Date()) {
    return res.status(400).json({ message: "Invalid or expired verification code" });
  }

  const newUser = new User({
    name: pendingUser.name,
    email: pendingUser.email,
    password: pendingUser.password,
    provider: "email",
    avatar: "/default-avatar.png",
    subscriptionType: "free",
    promptsUsed: 0,
    secretKey: generateSecretKey(), // Use the consistent format
  });

  await newUser.save();
  await PendingUser.deleteOne({ email });

  return res.status(200).json({ message: "Email verified and account created successfully" });
}