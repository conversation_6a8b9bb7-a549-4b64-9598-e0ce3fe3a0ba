import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';

// Define public routes that don't require authentication
const isPublicRoute = createRouteMatcher([
  '/',
  '/about(.*)',
  '/contact(.*)',
  '/login(.*)',
  '/register(.*)',
  '/announcements(.*)',
  '/pricing(.*)',
  '/forgot-password(.*)',
  '/reset-password(.*)',
  '/verify-email(.*)',
  '/privacy(.*)',
  '/terms&conditions(.*)',
  '/copyright(.*)',
  '/cool404(.*)',
  '/sso-callback(.*)',
  '/api/get-region(.*)',
  '/api/exchange-rates(.*)',
  '/api/proxy/connectivity-check(.*)',
  '/api/send(.*)',
  '/api/sentry-example-api(.*)',
  '/api/test-api(.*)'
]);

// Define protected routes that require authentication
const isProtectedRoute = createRouteMatcher([
  '/dashboard(.*)',
  '/api/user(.*)',
  '/api/admin(.*)',
  '/api/paypal(.*)',
  '/api/razorpay(.*)'
]);

export default clerkMiddleware(async (auth, req) => {
  // Check if the request is for a protected route
  if (isProtectedRoute(req)) {
    // Protect returns a Promise, so we await it
    await auth.protect();
  }
  // Return undefined for public routes (Clerk will continue processing)
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)'
  ]
};