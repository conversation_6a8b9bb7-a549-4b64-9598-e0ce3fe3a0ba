'use client';
import { motion, stagger, useAnimate } from 'framer-motion';
import { useEffect } from 'react';

import { cn } from '@/lib/utils';

export const TextGenerateEffect = ({
  words,
  className,
}: {
  words: string;
  className?: string;
}) => {
  const [scope, animate] = useAnimate();
  const wordsArray = words.split(' ');

  useEffect(() => {
    animate(
      'span',
      { opacity: 1 },
      {
        duration: 1.5,
        delay: stagger(0.1),
      }
    );
  }, [animate]); // Depend on animate, not scope.current

  const renderWords = () => {
    return (
      <motion.div ref={scope}>
        {wordsArray.map((word, idx) => (
          <motion.span
            key={word + idx}
            className={cn(
              'opacity-0',
              idx > 3 ? 'text-[#CBACF9]' : 'dark:text-white text-black'
            )}
          >
            {word}{' '}
          </motion.span>
        ))}
      </motion.div>
    );
  };

  return (
    <div className={cn('font-normal', className)}>
      <div className='my-4'>
        <div className='leading-snug tracking-wide text-black dark:text-white'>
          {renderWords()}
        </div>
      </div>
    </div>
  );
};