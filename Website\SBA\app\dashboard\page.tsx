"use client";

import { useUser, useAuth } from "@clerk/nextjs";
import { <PERSON><PERSON>, <PERSON>, Divider, Too<PERSON><PERSON> } from "@nextui-org/react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { useEffect, useContext, useState } from "react";
import { FaW<PERSON><PERSON>, FaApple, FaDownload, Fa<PERSON><PERSON>, <PERSON>a<PERSON>ey, FaShieldAlt } from "react-icons/fa";
import { toast } from "sonner";

import AccountDeletionSection from "@/components/dashboard/AccountDeletionSection";
import PasswordUpdateForm from "@/components/dashboard/PasswordUpdateForm";
import UserProfileCard from "@/components/dashboard/UserProfileCard";
import { UserContext } from "@/context/UserContext";

export default function Dashboard() {
    const { isLoaded: userIsLoaded, isSignedIn, user } = useUser();
    const { isLoaded: authIsLoaded, sessionId } = useAuth();
    const router = useRouter();
    const userContext = useContext(UserContext);

    // Add state for database user
    const [dbUser, setDbUser] = useState({
        name: "",
        email: "",
    });
    const [isLoading, setIsLoading] = useState(true);

    // Redirect to login if not authenticated
    useEffect(() => {
        if (userIsLoaded && authIsLoaded && !isSignedIn) {
            router.push("/login");
        }

        // Update user context if needed
        if (userContext && isSignedIn) {
            userContext.setIsLoggedIn(true);
        }
    }, [userIsLoaded, authIsLoaded, isSignedIn, router, userContext]);

    // Fetch user data from MongoDB
    useEffect(() => {
        const fetchUserData = async () => {
            if (!isSignedIn) {
                setIsLoading(false);
                return;
            }

            try {
                const response = await fetch('/api/user/me', {
                    // Add cache: 'no-store' to prevent browser caching
                    headers: {
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });
                const data = await response.json();

                if (response.ok && data.status === 'success') {
                    setDbUser({
                        name: data.name,
                        email: data.email
                    });

                    // Check if the message indicates a subscription expiration
                    if (data.message && data.message.includes('subscription has expired')) {
                        // Show toast notification about subscription expiration
                        toast.info(
                            "Your premium subscription has expired and has been updated to the free plan.",
                            {
                                duration: 6000,
                                position: "top-center"
                            }
                        );
                    }
                } else {
                    console.error('Failed to fetch user data:', data.message || 'Unknown error');
                    // Reset user data on error
                    setDbUser({
                        name: "",
                        email: ""
                    });
                }
            } catch (error) {
                console.error('Error fetching user data:', error);
                // Reset user data on error
                setDbUser({
                    name: "",
                    email: ""
                });
            } finally {
                setIsLoading(false);
            }
        };

        // Reset user data when fetch starts
        setDbUser({
            name: "",
            email: ""
        });
        setIsLoading(true);
        fetchUserData();

    }, [isSignedIn, user, sessionId]); // Add user and sessionId as dependencies to refresh when they change

    // Show loading state
    if (!userIsLoaded || !authIsLoaded || !isSignedIn || isLoading) {
        return (
            <div className="flex min-h-screen items-center justify-center bg-[#06081c]">
                <div className="text-center">
                    <div className="relative inline-flex items-center justify-center">
                        <div className="size-16 animate-spin rounded-full border-3 border-gray-300 border-t-[#24AE7C]"></div>
                        <div className="absolute flex space-x-1.5">
                            <div className="size-2 animate-[pulse_1.5s_ease-in-out_infinite_0.2s] rounded-full bg-[#24AE7C]"></div>
                            <div className="size-2 animate-[pulse_1.5s_ease-in-out_infinite_0.4s] rounded-full bg-[#24AE7C]"></div>
                            <div className="size-2 animate-[pulse_1.5s_ease-in-out_infinite_0.6s] rounded-full bg-[#24AE7C]"></div>
                        </div>
                    </div>
                    <div className="mt-8">
                        <p className="text-xl font-medium text-gray-100">Loading Dashboard</p>
                        <p className="mt-3 text-sm text-gray-400">Please wait while we prepare your account details</p>
                    </div>
                </div>
            </div>
        );
    }

    // Download handlers
    const handleWindowsDownload = () => {
        window.location.href =
            "https://github.com/VaibhavRaina/Pytorch/releases/download/v1.0.0/interviewCr-setup-win64.exe";
    };

    const handleMacDownload = () => {
        window.location.href =
            "https://github.com/VaibhavRaina/Pytorch/releases/download/v2.5.0/interview-cracker-setup-macOS.zip";
    };

    // Animation variants
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };

    const itemVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                type: "spring",
                stiffness: 100,
                damping: 15
            }
        }
    };

    return (
        <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="relative"
        >
            {/* Decorative element */}
            <div className="absolute -right-20 -top-10 -z-10 size-64 rounded-full bg-blue-500/5 blur-[60px]"></div>

            {/* Page Header */}
            <motion.div
                variants={itemVariants}
                className="mb-8"
            >
                <h1 className="text-32-bold flex items-center gap-3 text-white">
                    <span className="rounded-xl bg-gradient-to-br from-[#24AE7C]/20 to-[#24AE7C]/10 p-3">
                        <FaUser className="text-2xl text-[#24AE7C]" />
                    </span>
                    <span>My Account</span>
                </h1>
                <p className="ml-12 mt-2 text-gray-400">Manage your profile, account settings and applications</p>
            </motion.div>

            {/* Main Cards Section */}
            <motion.div
                variants={itemVariants}
                className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2"
            >
                {/* User Profile Card - Now using dbUser instead of Clerk user */}
                <UserProfileCard
                    user={{
                        name: dbUser.name || user?.fullName || user?.firstName || "Unknown",
                        email: dbUser.email || user?.primaryEmailAddress?.emailAddress || "Email not provided",
                        image: user?.imageUrl || undefined,
                    }}
                />

                {/* Desktop Applications Card */}
                {user && (
                    <Card className="group relative overflow-hidden border border-slate-800/60 bg-[#0f172a]/60 p-6 shadow-xl backdrop-blur-sm">
                        {/* Card decoration */}
                        <div className="absolute right-0 top-0 -z-0 size-40 rounded-full bg-[#24AE7C]/5 opacity-70 blur-2xl transition-all duration-700 group-hover:opacity-100"></div>

                        <div className="relative z-10">
                            <h2 className="text-18-bold mb-4 flex items-center gap-2 text-white">
                                <FaDownload className="text-[#24AE7C]" />
                                Desktop Applications
                            </h2>
                            <p className="text-14-regular mb-6 text-gray-400">
                                Download our desktop application for to crack you interviews with ease use the secret key and gmail to login in the app.
                            </p>
                            <div className="space-y-4">
                                <Tooltip content="Download our Windows application" placement="bottom">
                                    <Button
                                        variant="flat"
                                        radius="sm"
                                        onClick={handleWindowsDownload}
                                        startContent={<FaWindows size={18} />}
                                        className="w-full border border-slate-700/50 bg-[#181f33]/80 py-6 text-white transition-all duration-300 hover:border-[#24AE7C]/30 hover:bg-[#24AE7C]/20"
                                        endContent={<div className="rounded-md bg-[#24AE7C]/20 p-1"><FaDownload size={14} /></div>}
                                    >
                                        <span className="text-16-semibold">Download for Windows</span>
                                    </Button>
                                </Tooltip>

                                <Tooltip content="Download our macOS application" placement="bottom">
                                    <Button
                                        variant="flat"
                                        radius="sm"
                                        onClick={handleMacDownload}
                                        startContent={<FaApple size={18} />}
                                        className="w-full border border-slate-700/50 bg-[#181f33]/80 py-6 text-white transition-all duration-300 hover:border-[#24AE7C]/30 hover:bg-[#24AE7C]/20"
                                        endContent={<div className="rounded-md bg-[#24AE7C]/20 p-1"><FaDownload size={14} /></div>}
                                    >
                                        <span className="text-16-semibold">Download for Mac</span>
                                    </Button>
                                </Tooltip>
                            </div>
                        </div>
                    </Card>
                )}
            </motion.div>

            {/* Divider with decoration */}
            <motion.div
                variants={itemVariants}
                className="relative my-12"
            >
                <Divider className="bg-slate-800/60" />
                <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-[#0f172a] px-4">
                    <FaShieldAlt className="text-gray-500" />
                </div>
            </motion.div>

            {/* Account Security Section */}
            <motion.div
                variants={itemVariants}
                className="mb-4"
            >
                <h2 className="text-24-bold mb-6 flex items-center gap-3 text-white">
                    <span className="rounded-lg bg-gradient-to-br from-[#24AE7C]/20 to-[#24AE7C]/10 p-2">
                        <FaKey className="text-[#24AE7C]" />
                    </span>
                    <span>Account Security</span>
                </h2>
            </motion.div>

            {/* Security Forms Section */}
            <motion.div
                variants={containerVariants}
                className="grid grid-cols-1 gap-8"
            >
                <motion.div variants={itemVariants}>
                    <PasswordUpdateForm />
                </motion.div>
                <motion.div variants={itemVariants}>
                    <AccountDeletionSection />
                </motion.div>
            </motion.div>
        </motion.div>
    );
}