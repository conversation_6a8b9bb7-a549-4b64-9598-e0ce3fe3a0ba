'use client';
import React from 'react';

import { AnimatedTooltip } from '../ui/animated-tooltip';
const people = [
  {
    id: 1,
    name: '<PERSON>',
    designation: 'Software Engineer',
    image: '/users/user-1.png',
  },
  {
    id: 2,
    name: '<PERSON>',
    designation: 'Product Manager',
    image: '/users/user-2.png',
  },
  {
    id: 3,
    name: '<PERSON>',
    designation: 'Data Scientist',
    image: '/users/user-3.png',
  },
  {
    id: 4,
    name: '<PERSON>',
    designation: 'UX Designer',
    image: '/users/user-4.jpg',
  },
  {
    id: 5,
    name: '<PERSON> nah',
    designation: 'Front End Developer',
    image: '/users/user-5.jpg',
  },
];

export function AnimatedTooltipPreview() {
  return <AnimatedTooltip items={people} />;
}
