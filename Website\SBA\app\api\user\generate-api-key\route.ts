import { randomBytes } from "crypto";

import { clerkClient } from "@clerk/clerk-sdk-node";
import { getAuth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";

import connectDB from "@/lib/mongodb";
import { User } from "@/model/User";


export async function POST(req: NextRequest) {
  try {
    // Get the authentication data from Clerk
    const auth = getAuth(req);
    const { userId } = auth;
    
    console.log("Generate API key route triggered for user:", userId);
    
    if (!userId) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Connect to MongoDB
    await connectDB();
    
    // Find the user by their Clerk ID
    let user = await User.findOne({ clerkId: userId });
    
    // If user doesn't exist, create a new one with data from Clerk
    if (!user) {
      console.log("User not found, fetching details from Clerk");
      
      // Generate a random API key
      const secretKey = randomBytes(16).toString('hex');
      
      try {
        // Get user data from Clerk
        const clerkUser = await clerkClient.users.getUser(userId);
        
        // Extract name and email from Clerk user data
        const firstName = clerkUser.firstName || '';
        const lastName = clerkUser.lastName || '';
        const fullName = [firstName, lastName].filter(Boolean).join(' ') || 'New User';
        
        // Get primary email
        const primaryEmail = clerkUser.emailAddresses.find(email => 
          email.id === clerkUser.primaryEmailAddressId
        )?.emailAddress || '<EMAIL>';
        
        // Get profile image if available
        const profileImage = clerkUser.imageUrl || '/default-avatar.png';
        
        console.log("Creating new user with name:", fullName, "and email:", primaryEmail);
        
        // Create a new user with the Clerk ID and a secret key
        user = new User({
          clerkId: userId,
          name: fullName,
          email: primaryEmail,
          avatar: profileImage,
          role: "user",
          subscriptionType: "free",
          promptsUsed: 0,
          secretKey,
          createdAt: new Date()
        });
      } catch (clerkError) {
        console.error("Error fetching user from Clerk:", clerkError);
        
        // Fall back to creating a user with default values
        user = new User({
          clerkId: userId,
          name: "New User",
          email: "<EMAIL>",
          avatar: "/default-avatar.png",
          role: "user",
          subscriptionType: "free",
          promptsUsed: 0,
          secretKey: randomBytes(16).toString('hex'),
          createdAt: new Date()
        });
      }
      
      await user.save();
      console.log("New user created with ID and API key:", user._id);
      
      return NextResponse.json({
        secretKey: user.secretKey,
        message: "New user created with API key"
      });
    }
    
    // Generate a new secret key
    const newSecretKey = randomBytes(16).toString('hex');
    
    // Update the user's secret key
    user.secretKey = newSecretKey;
    await user.save();
    
    console.log("Generated new API key for user:", user._id);
    
    return NextResponse.json({
      secretKey: newSecretKey,
      message: "API key regenerated successfully"
    });
  } catch (error) {
    console.error("Error generating API key:", error);
    return NextResponse.json({ 
      message: "Internal server error", 
      error: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 });
  }
}