// components/WhatsAppButton.tsx
import React from 'react';
import { FaWhatsapp } from 'react-icons/fa6';

const WhatsAppButton: React.FC = () => {
  return (
    <a
      href='https://wa.me/+919797027481?text=Hello%2C%20I%20would%20like%20to%20know%20more%20about%20your%20services.'
      target='_blank'
      rel='noopener noreferrer'
      className='fixed bottom-6 right-5 flex size-14 items-center justify-center rounded-full bg-green-500 shadow-lg transition-transform ease-linear hover:scale-105 hover:bg-green-600 lg:bottom-8 lg:right-8 xl:bottom-10 xl:right-8 2xl:right-10 2xl:size-16'
      aria-label='Chat on WhatsApp'
    >
      <FaWhatsapp className='text-4xl text-white 2xl:text-4xl' />
    </a>
  );
};

export default WhatsAppButton;
