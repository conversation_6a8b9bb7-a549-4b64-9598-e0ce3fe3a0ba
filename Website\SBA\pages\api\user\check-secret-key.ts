// pages/api/user/check-secret-key.ts
import { NextApiRequest, NextApiResponse } from "next";

import connectDB from "@/lib/mongodb";
import { User } from "@/model/User";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { secretKey } = req.body;

  try {
    await connectDB();
    const user = await User.find({ secretKey });
    return res.status(200).json(!!user.length);
  } catch (error) {
    console.error("Error checking secret key:", error);
    return res.status(500).json(false);
  }
}