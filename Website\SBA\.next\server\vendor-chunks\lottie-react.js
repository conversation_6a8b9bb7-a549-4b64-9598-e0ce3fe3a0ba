"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lottie-react";
exports.ids = ["vendor-chunks/lottie-react"];
exports.modules = {

/***/ "(ssr)/./node_modules/lottie-react/build/index.es.js":
/*!*****************************************************!*\
  !*** ./node_modules/lottie-react/build/index.es.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LottiePlayer: () => (/* reexport default from dynamic */ lottie_web__WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   \"default\": () => (/* binding */ Lottie),\n/* harmony export */   useLottie: () => (/* binding */ useLottie),\n/* harmony export */   useLottieInteractivity: () => (/* binding */ useLottieInteractivity)\n/* harmony export */ });\n/* harmony import */ var lottie_web__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lottie-web */ \"(ssr)/./node_modules/lottie-web/build/player/lottie.js\");\n/* harmony import */ var lottie_web__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lottie_web__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (null != _i) {\n    var _s,\n      _e,\n      _x,\n      _r,\n      _arr = [],\n      _n = !0,\n      _d = !1;\n    try {\n      if (_x = (_i = _i.call(arr)).next, 0 === i) {\n        if (Object(_i) !== _i) return;\n        _n = !1;\n      } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0);\n    } catch (err) {\n      _d = !0, _e = err;\n    } finally {\n      try {\n        if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return;\n      } finally {\n        if (_d) throw _e;\n      }\n    }\n    return _arr;\n  }\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\n\nvar _excluded$1 = [\"animationData\", \"loop\", \"autoplay\", \"initialSegment\", \"onComplete\", \"onLoopComplete\", \"onEnterFrame\", \"onSegmentStart\", \"onConfigReady\", \"onDataReady\", \"onDataFailed\", \"onLoadedImages\", \"onDOMLoaded\", \"onDestroy\", \"lottieRef\", \"renderer\", \"name\", \"assetsPath\", \"rendererSettings\"];\nvar useLottie = function useLottie(props, style) {\n  var animationData = props.animationData,\n    loop = props.loop,\n    autoplay = props.autoplay,\n    initialSegment = props.initialSegment,\n    onComplete = props.onComplete,\n    onLoopComplete = props.onLoopComplete,\n    onEnterFrame = props.onEnterFrame,\n    onSegmentStart = props.onSegmentStart,\n    onConfigReady = props.onConfigReady,\n    onDataReady = props.onDataReady,\n    onDataFailed = props.onDataFailed,\n    onLoadedImages = props.onLoadedImages,\n    onDOMLoaded = props.onDOMLoaded,\n    onDestroy = props.onDestroy;\n    props.lottieRef;\n    props.renderer;\n    props.name;\n    props.assetsPath;\n    props.rendererSettings;\n    var rest = _objectWithoutProperties(props, _excluded$1);\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    animationLoaded = _useState2[0],\n    setAnimationLoaded = _useState2[1];\n  var animationInstanceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n  var animationContainer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n  /*\n        ======================================\n            INTERACTION METHODS\n        ======================================\n     */\n  /**\n   * Play\n   */\n  var play = function play() {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.play();\n  };\n  /**\n   * Stop\n   */\n  var stop = function stop() {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.stop();\n  };\n  /**\n   * Pause\n   */\n  var pause = function pause() {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.pause();\n  };\n  /**\n   * Set animation speed\n   * @param speed\n   */\n  var setSpeed = function setSpeed(speed) {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.setSpeed(speed);\n  };\n  /**\n   * Got to frame and play\n   * @param value\n   * @param isFrame\n   */\n  var goToAndPlay = function goToAndPlay(value, isFrame) {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.goToAndPlay(value, isFrame);\n  };\n  /**\n   * Got to frame and stop\n   * @param value\n   * @param isFrame\n   */\n  var goToAndStop = function goToAndStop(value, isFrame) {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.goToAndStop(value, isFrame);\n  };\n  /**\n   * Set animation direction\n   * @param direction\n   */\n  var setDirection = function setDirection(direction) {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.setDirection(direction);\n  };\n  /**\n   * Play animation segments\n   * @param segments\n   * @param forceFlag\n   */\n  var playSegments = function playSegments(segments, forceFlag) {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.playSegments(segments, forceFlag);\n  };\n  /**\n   * Set sub frames\n   * @param useSubFrames\n   */\n  var setSubframe = function setSubframe(useSubFrames) {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.setSubframe(useSubFrames);\n  };\n  /**\n   * Get animation duration\n   * @param inFrames\n   */\n  var getDuration = function getDuration(inFrames) {\n    var _a;\n    return (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.getDuration(inFrames);\n  };\n  /**\n   * Destroy animation\n   */\n  var destroy = function destroy() {\n    var _a;\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n    // Removing the reference to the animation so separate cleanups are skipped.\n    // Without it the internal `lottie-react` instance throws exceptions as it already cleared itself on destroy.\n    animationInstanceRef.current = undefined;\n  };\n  /*\n        ======================================\n            LOTTIE\n        ======================================\n     */\n  /**\n   * Load a new animation, and if it's the case, destroy the previous one\n   * @param {Object} forcedConfigs\n   */\n  var loadAnimation = function loadAnimation() {\n    var forcedConfigs = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var _a;\n    // Return if the container ref is null\n    if (!animationContainer.current) {\n      return;\n    }\n    // Destroy any previous instance\n    (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n    // Build the animation configuration\n    var config = _objectSpread2(_objectSpread2(_objectSpread2({}, props), forcedConfigs), {}, {\n      container: animationContainer.current\n    });\n    // Save the animation instance\n    animationInstanceRef.current = lottie_web__WEBPACK_IMPORTED_MODULE_0___default().loadAnimation(config);\n    setAnimationLoaded(!!animationInstanceRef.current);\n    // Return a function that will clean up\n    return function () {\n      var _a;\n      (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.destroy();\n      animationInstanceRef.current = undefined;\n    };\n  };\n  /**\n   * (Re)Initialize when animation data changed\n   */\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    var onUnmount = loadAnimation();\n    // Clean up on unmount\n    return function () {\n      return onUnmount === null || onUnmount === void 0 ? void 0 : onUnmount();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [animationData, loop]);\n  // Update the autoplay state\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (!animationInstanceRef.current) {\n      return;\n    }\n    animationInstanceRef.current.autoplay = !!autoplay;\n  }, [autoplay]);\n  // Update the initial segment state\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (!animationInstanceRef.current) {\n      return;\n    }\n    // When null should reset to default animation length\n    if (!initialSegment) {\n      animationInstanceRef.current.resetSegments(true);\n      return;\n    }\n    // If it's not a valid segment, do nothing\n    if (!Array.isArray(initialSegment) || !initialSegment.length) {\n      return;\n    }\n    // If the current position it's not in the new segment\n    // set the current position to start\n    if (animationInstanceRef.current.currentRawFrame < initialSegment[0] || animationInstanceRef.current.currentRawFrame > initialSegment[1]) {\n      animationInstanceRef.current.currentRawFrame = initialSegment[0];\n    }\n    // Update the segment\n    animationInstanceRef.current.setSegment(initialSegment[0], initialSegment[1]);\n  }, [initialSegment]);\n  /*\n        ======================================\n            EVENTS\n        ======================================\n     */\n  /**\n   * Reinitialize listener on change\n   */\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    var partialListeners = [{\n      name: \"complete\",\n      handler: onComplete\n    }, {\n      name: \"loopComplete\",\n      handler: onLoopComplete\n    }, {\n      name: \"enterFrame\",\n      handler: onEnterFrame\n    }, {\n      name: \"segmentStart\",\n      handler: onSegmentStart\n    }, {\n      name: \"config_ready\",\n      handler: onConfigReady\n    }, {\n      name: \"data_ready\",\n      handler: onDataReady\n    }, {\n      name: \"data_failed\",\n      handler: onDataFailed\n    }, {\n      name: \"loaded_images\",\n      handler: onLoadedImages\n    }, {\n      name: \"DOMLoaded\",\n      handler: onDOMLoaded\n    }, {\n      name: \"destroy\",\n      handler: onDestroy\n    }];\n    var listeners = partialListeners.filter(function (listener) {\n      return listener.handler != null;\n    });\n    if (!listeners.length) {\n      return;\n    }\n    var deregisterList = listeners.map(\n    /**\n     * Handle the process of adding an event listener\n     * @param {Listener} listener\n     * @return {Function} Function that deregister the listener\n     */\n    function (listener) {\n      var _a;\n      (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.addEventListener(listener.name, listener.handler);\n      // Return a function to deregister this listener\n      return function () {\n        var _a;\n        (_a = animationInstanceRef.current) === null || _a === void 0 ? void 0 : _a.removeEventListener(listener.name, listener.handler);\n      };\n    });\n    // Deregister listeners on unmount\n    return function () {\n      deregisterList.forEach(function (deregister) {\n        return deregister();\n      });\n    };\n  }, [onComplete, onLoopComplete, onEnterFrame, onSegmentStart, onConfigReady, onDataReady, onDataFailed, onLoadedImages, onDOMLoaded, onDestroy]);\n  /**\n   * Build the animation view\n   */\n  var View = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", _objectSpread2({\n    style: style,\n    ref: animationContainer\n  }, rest));\n  return {\n    View: View,\n    play: play,\n    stop: stop,\n    pause: pause,\n    setSpeed: setSpeed,\n    goToAndStop: goToAndStop,\n    goToAndPlay: goToAndPlay,\n    setDirection: setDirection,\n    playSegments: playSegments,\n    setSubframe: setSubframe,\n    getDuration: getDuration,\n    destroy: destroy,\n    animationContainerRef: animationContainer,\n    animationLoaded: animationLoaded,\n    animationItem: animationInstanceRef.current\n  };\n};\n\n// helpers\nfunction getContainerVisibility(container) {\n  var _container$getBoundin = container.getBoundingClientRect(),\n    top = _container$getBoundin.top,\n    height = _container$getBoundin.height;\n  var current = window.innerHeight - top;\n  var max = window.innerHeight + height;\n  return current / max;\n}\nfunction getContainerCursorPosition(container, cursorX, cursorY) {\n  var _container$getBoundin2 = container.getBoundingClientRect(),\n    top = _container$getBoundin2.top,\n    left = _container$getBoundin2.left,\n    width = _container$getBoundin2.width,\n    height = _container$getBoundin2.height;\n  var x = (cursorX - left) / width;\n  var y = (cursorY - top) / height;\n  return {\n    x: x,\n    y: y\n  };\n}\nvar useInitInteractivity = function useInitInteractivity(_ref) {\n  var wrapperRef = _ref.wrapperRef,\n    animationItem = _ref.animationItem,\n    mode = _ref.mode,\n    actions = _ref.actions;\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    var wrapper = wrapperRef.current;\n    if (!wrapper || !animationItem || !actions.length) {\n      return;\n    }\n    animationItem.stop();\n    var scrollModeHandler = function scrollModeHandler() {\n      var assignedSegment = null;\n      var scrollHandler = function scrollHandler() {\n        var currentPercent = getContainerVisibility(wrapper);\n        // Find the first action that satisfies the current position conditions\n        var action = actions.find(function (_ref2) {\n          var visibility = _ref2.visibility;\n          return visibility && currentPercent >= visibility[0] && currentPercent <= visibility[1];\n        });\n        // Skip if no matching action was found!\n        if (!action) {\n          return;\n        }\n        if (action.type === \"seek\" && action.visibility && action.frames.length === 2) {\n          // Seek: Go to a frame based on player scroll position action\n          var frameToGo = action.frames[0] + Math.ceil((currentPercent - action.visibility[0]) / (action.visibility[1] - action.visibility[0]) * action.frames[1]);\n          //! goToAndStop must be relative to the start of the current segment\n          animationItem.goToAndStop(frameToGo - animationItem.firstFrame - 1, true);\n        }\n        if (action.type === \"loop\") {\n          // Loop: Loop a given frames\n          if (assignedSegment === null) {\n            // if not playing any segments currently. play those segments and save to state\n            animationItem.playSegments(action.frames, true);\n            assignedSegment = action.frames;\n          } else {\n            // if playing any segments currently.\n            //check if segments in state are equal to the frames selected by action\n            if (assignedSegment !== action.frames) {\n              // if they are not equal. new segments are to be loaded\n              animationItem.playSegments(action.frames, true);\n              assignedSegment = action.frames;\n            } else if (animationItem.isPaused) {\n              // if they are equal the play method must be called only if lottie is paused\n              animationItem.playSegments(action.frames, true);\n              assignedSegment = action.frames;\n            }\n          }\n        }\n        if (action.type === \"play\" && animationItem.isPaused) {\n          // Play: Reset segments and continue playing full animation from current position\n          animationItem.resetSegments(true);\n          animationItem.play();\n        }\n        if (action.type === \"stop\") {\n          // Stop: Stop playback\n          animationItem.goToAndStop(action.frames[0] - animationItem.firstFrame - 1, true);\n        }\n      };\n      document.addEventListener(\"scroll\", scrollHandler);\n      return function () {\n        document.removeEventListener(\"scroll\", scrollHandler);\n      };\n    };\n    var cursorModeHandler = function cursorModeHandler() {\n      var handleCursor = function handleCursor(_x, _y) {\n        var x = _x;\n        var y = _y;\n        // Resolve cursor position if cursor is inside container\n        if (x !== -1 && y !== -1) {\n          // Get container cursor position\n          var pos = getContainerCursorPosition(wrapper, x, y);\n          // Use the resolved position\n          x = pos.x;\n          y = pos.y;\n        }\n        // Find the first action that satisfies the current position conditions\n        var action = actions.find(function (_ref3) {\n          var position = _ref3.position;\n          if (position && Array.isArray(position.x) && Array.isArray(position.y)) {\n            return x >= position.x[0] && x <= position.x[1] && y >= position.y[0] && y <= position.y[1];\n          }\n          if (position && !Number.isNaN(position.x) && !Number.isNaN(position.y)) {\n            return x === position.x && y === position.y;\n          }\n          return false;\n        });\n        // Skip if no matching action was found!\n        if (!action) {\n          return;\n        }\n        // Process action types:\n        if (action.type === \"seek\" && action.position && Array.isArray(action.position.x) && Array.isArray(action.position.y) && action.frames.length === 2) {\n          // Seek: Go to a frame based on player scroll position action\n          var xPercent = (x - action.position.x[0]) / (action.position.x[1] - action.position.x[0]);\n          var yPercent = (y - action.position.y[0]) / (action.position.y[1] - action.position.y[0]);\n          animationItem.playSegments(action.frames, true);\n          animationItem.goToAndStop(Math.ceil((xPercent + yPercent) / 2 * (action.frames[1] - action.frames[0])), true);\n        }\n        if (action.type === \"loop\") {\n          animationItem.playSegments(action.frames, true);\n        }\n        if (action.type === \"play\") {\n          // Play: Reset segments and continue playing full animation from current position\n          if (animationItem.isPaused) {\n            animationItem.resetSegments(false);\n          }\n          animationItem.playSegments(action.frames);\n        }\n        if (action.type === \"stop\") {\n          animationItem.goToAndStop(action.frames[0], true);\n        }\n      };\n      var mouseMoveHandler = function mouseMoveHandler(ev) {\n        handleCursor(ev.clientX, ev.clientY);\n      };\n      var mouseOutHandler = function mouseOutHandler() {\n        handleCursor(-1, -1);\n      };\n      wrapper.addEventListener(\"mousemove\", mouseMoveHandler);\n      wrapper.addEventListener(\"mouseout\", mouseOutHandler);\n      return function () {\n        wrapper.removeEventListener(\"mousemove\", mouseMoveHandler);\n        wrapper.removeEventListener(\"mouseout\", mouseOutHandler);\n      };\n    };\n    switch (mode) {\n      case \"scroll\":\n        return scrollModeHandler();\n      case \"cursor\":\n        return cursorModeHandler();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [mode, animationItem]);\n};\nvar useLottieInteractivity = function useLottieInteractivity(_ref4) {\n  var actions = _ref4.actions,\n    mode = _ref4.mode,\n    lottieObj = _ref4.lottieObj;\n  var animationItem = lottieObj.animationItem,\n    View = lottieObj.View,\n    animationContainerRef = lottieObj.animationContainerRef;\n  useInitInteractivity({\n    actions: actions,\n    animationItem: animationItem,\n    mode: mode,\n    wrapperRef: animationContainerRef\n  });\n  return View;\n};\n\nvar _excluded = [\"style\", \"interactivity\"];\nvar Lottie = function Lottie(props) {\n  var _a, _b, _c;\n  var style = props.style,\n    interactivity = props.interactivity,\n    lottieProps = _objectWithoutProperties(props, _excluded);\n  /**\n   * Initialize the 'useLottie' hook\n   */\n  var _useLottie = useLottie(lottieProps, style),\n    View = _useLottie.View,\n    play = _useLottie.play,\n    stop = _useLottie.stop,\n    pause = _useLottie.pause,\n    setSpeed = _useLottie.setSpeed,\n    goToAndStop = _useLottie.goToAndStop,\n    goToAndPlay = _useLottie.goToAndPlay,\n    setDirection = _useLottie.setDirection,\n    playSegments = _useLottie.playSegments,\n    setSubframe = _useLottie.setSubframe,\n    getDuration = _useLottie.getDuration,\n    destroy = _useLottie.destroy,\n    animationContainerRef = _useLottie.animationContainerRef,\n    animationLoaded = _useLottie.animationLoaded,\n    animationItem = _useLottie.animationItem;\n  /**\n   * Make the hook variables/methods available through the provided 'lottieRef'\n   */\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (props.lottieRef) {\n      props.lottieRef.current = {\n        play: play,\n        stop: stop,\n        pause: pause,\n        setSpeed: setSpeed,\n        goToAndPlay: goToAndPlay,\n        goToAndStop: goToAndStop,\n        setDirection: setDirection,\n        playSegments: playSegments,\n        setSubframe: setSubframe,\n        getDuration: getDuration,\n        destroy: destroy,\n        animationContainerRef: animationContainerRef,\n        animationLoaded: animationLoaded,\n        animationItem: animationItem\n      };\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [(_a = props.lottieRef) === null || _a === void 0 ? void 0 : _a.current]);\n  return useLottieInteractivity({\n    lottieObj: {\n      View: View,\n      play: play,\n      stop: stop,\n      pause: pause,\n      setSpeed: setSpeed,\n      goToAndStop: goToAndStop,\n      goToAndPlay: goToAndPlay,\n      setDirection: setDirection,\n      playSegments: playSegments,\n      setSubframe: setSubframe,\n      getDuration: getDuration,\n      destroy: destroy,\n      animationContainerRef: animationContainerRef,\n      animationLoaded: animationLoaded,\n      animationItem: animationItem\n    },\n    actions: (_b = interactivity === null || interactivity === void 0 ? void 0 : interactivity.actions) !== null && _b !== void 0 ? _b : [],\n    mode: (_c = interactivity === null || interactivity === void 0 ? void 0 : interactivity.mode) !== null && _c !== void 0 ? _c : \"scroll\"\n  });\n};\n\n\n//# sourceMappingURL=index.es.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lottie-react/build/index.es.js\n");

/***/ })

};
;