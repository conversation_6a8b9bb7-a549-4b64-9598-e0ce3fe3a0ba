"use client";

import Image from "next/image";
import { useState } from "react";

import { ClientLoginForm } from "@/components/forms/ClientLoginForm";
import { PasskeyModal } from "@/components/PasskeyModal";
import { SearchParamProps } from "@/types";

export default function Login({ searchParams }: SearchParamProps) {
  const [isLoading, setIsLoading] = useState(false);
  const isAdmin = searchParams?.admin === "true";
  const year = new Date().getFullYear();

  const handleLoading = (loading: boolean) => {
    setIsLoading(loading);
  };

  return (
    <div className="relative flex h-screen min-h-screen bg-black">
      {isAdmin && <PasskeyModal />}
      <section className="remove-scrollbar container my-auto">
        <div className="sub-container max-w-[496px]">
          <ClientLoginForm onLoading={handleLoading} />
          <div className="text-14-regular mt-20 flex justify-between">
            <p className="justify-items-end text-dark-600 xl:text-left">
              © {year} Interview Cracker
            </p>
          </div>
        </div>
      </section>
      <div className="hidden w-[50%] md:block">
        <Image
          src="/side_img.jpg"
          height={1739}
          width={1895}
          loading="eager"
          alt="Side image login"
          className="side-img pointer-events-none max-w-full select-none"
        />
      </div>
      {isLoading && (
        <div className="fixed inset-x-0 bottom-0 top-16 z-50 flex items-center justify-center bg-gray-900 bg-opacity-70">
          <div className="text-center">
            <div className="relative inline-flex items-center justify-center">
              <div className="size-10 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600"></div>
              <div className="absolute flex space-x-1">
                <div className="size-1 animate-[pulse_1.5s_ease-in-out_infinite_0.2s] rounded-full bg-gray-400"></div>
                <div className="size-1 animate-[pulse_1.5s_ease-in-out_infinite_0.4s] rounded-full bg-gray-400"></div>
                <div className="size-1 animate-[pulse_1.5s_ease-in-out_infinite_0.6s] rounded-full bg-gray-400"></div>
              </div>
            </div>
            <div className="mt-4">
              <p className="font-medium text-white">Processing Your Login</p>
              <p className="mt-1 text-sm text-gray-300">Please wait a moment</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}