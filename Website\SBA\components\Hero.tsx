"use client";
import { Star, StarHalf } from 'lucide-react';
import Image from 'next/image';
import { useState, useEffect } from 'react';

import { HeroButtonGetStarted } from './utils/Misc';


const Hero = () => {
  const phrases = [
    "F*ck interviews",
    "F*ck leetcode",


  ];

  const [currentPhraseIndex, setCurrentPhraseIndex] = useState(0);

  useEffect(() => {
    const intervalId = setInterval(() => {
      setCurrentPhraseIndex((prevIndex) => (prevIndex + 1) % phrases.length);
    }, 4000); // Change every 4 seconds

    return () => clearInterval(intervalId);
  }, []);

  const people = [
    {
      id: 1,
      name: '<PERSON><PERSON>',
      designation: 'Software Engineer',
      image: '/users/testimonials/user-3.png',
    },
    {
      id: 2,
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      designation: 'Full Stack Developer',
      image: '/users/testimonials/user-4.jpg',
    },

  ];

  return (
    <div className='relative my-10 flex w-full flex-col items-center justify-center gap-16'>

      <div className=''>
        {/* Radial gradient background */}
        <div className='absolute left-0 top-0 flex h-screen w-full items-center justify-center bg-white bg-dot-black/[0.05] dark:bg-black dark:bg-dot-white/[0.2]'>
          <div className='pointer-events-none absolute inset-0 flex items-center justify-center bg-white [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)] dark:bg-black'></div>
        </div>

        <div className='relative z-10 mb-6 mt-12 flex max-w-[80vw] items-center justify-center  sm:my-10 sm:justify-between md:my-20 lg:my-24 lg:mb-5 xl:my-16 xl:gap-x-20 2xl:my-[120px] 2xl:gap-x-40'>
          <div className='flex max-w-[100vw] flex-col items-center justify-center  sm:items-start md:max-w-2xl lg:w-full lg:max-w-[100vw] xl:max-w-[60vw] xl:text-left'>
            <span className='mt-2 text-[42px] leading-tight sm:mt-5 md:leading-tight lg:text-5xl 2xl:text-6xl'>
              <div className='font-medium text-[#ED5F5F] '>
                {phrases[currentPhraseIndex]}
              </div>
              <span className='font-normal'> Focus on things </span>
              <br className='hidden md:block' />
              <span className='font-normal'> that matter.</span>
            </span>

            <div className='mt-4 hidden text-pretty font-light text-white/70 sm:block md:text-base lg:max-w-[80vw] xl:max-w-[45vw]'>
              We Provide AI Tool to help you perform &apos;Better&apos; in your interviews.
              <br />
              <div className='items-center justify-center'>
                Available on windows and mac.</div>
            </div>

            <div className='mt-8 flex flex-col items-center justify-center gap-10 sm:flex-row sm:gap-20 md:mt-10'>
              <HeroButtonGetStarted />

              <div className='flex flex-col items-center gap-5 sm:flex-row'>
                <div className='flex -space-x-3'>
                  {people.map((person) => (
                    <Image
                      key={person.id}
                      src={person.image}
                      alt={person.name}
                      width={40}
                      height={40}
                      loading='eager'
                      className='pointer-events-none inline-block size-full select-none rounded-full object-cover'
                    />
                  ))}
                </div>

                <div className='flex flex-col items-center justify-between sm:items-start'>
                  <div className='flex gap-0.5'>
                    <Star className='size-4 fill-red-500 text-red-500' />
                    <Star className='size-4 fill-red-500 text-red-500' />
                    <Star className='size-4 fill-red-500 text-red-500' />
                    <Star className='size-4 fill-red-500 text-red-500' />
                    <StarHalf className='size-4 fill-red-500 text-red-500' />
                  </div>
                  <p>
                    <span className='font-semibold'></span> Highly Rated
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Construction image */}
        </div>
      </div>
    </div>
  );
};

export default Hero;