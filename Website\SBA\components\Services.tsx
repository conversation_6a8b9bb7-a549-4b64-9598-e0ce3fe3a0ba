import { But<PERSON> } from '@nextui-org/button';
import { EmblaOptionsType } from 'embla-carousel';
import Link from 'next/link';
import React from 'react';

import EmblaCarousel from './ui/EmblaCarousel';

import '../app/embla.css';
import { Link } from 'lucide-react';



const OPTIONS: EmblaOptionsType = { dragFree: true };
const SLIDE_COUNT = 5;
const SLIDES = Array.from(Array(SLIDE_COUNT).keys());

const Services = () => {
  return (
    <>
      <div className='-mt-48 flex flex-col px-4  pb-10 md:p-10 lg:mb-28'>
        <div className='flex flex-col justify-center gap-16 lg:flex-row xl:gap-20'>
          <EmblaCarousel slides={SLIDES} options={OPTIONS} />
          <div className='flex flex-col gap-9'>
            <p className='z-10 text-pretty pt-10 text-base sm:text-base'>
              Our AI-powered coding assistant revolutionizes technical interviews by offering real-time code with optimization, instant problem-solving suggestions without being visible to interviewer, system design problem solution, edge case detection and handling, and interview cracking. Our tool helps you focus on sweet talking the interviewer while we handle the code.
            </p>
            <div className='flex gap-4'>
              <Button
                color='primary'
                variant='shadow'
                radius='full'
                className='w-24'
              >
                <Link href={'/services'}>Features</Link>
              </Button>
              <Button
                color='primary'
                variant='shadow'
                radius='full'
                className='w-[165px]'
              >
                <Link href={'/dashboard'}>Start Free Trial</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Services;