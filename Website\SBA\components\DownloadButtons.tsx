"use client";

import { <PERSON><PERSON> } from "@nextui-org/button";
import { useRouter } from "next/navigation";
import <PERSON>rip<PERSON> from "next/script";
import React from "react";
import { FaWindows, FaApple } from "react-icons/fa6";

const DownloadButtons = () => {
    // eslint-disable-next-line no-unused-vars
    const router = useRouter();

    const handleWindowsDownload = () => {
        // Fire Twitter Ads pixel event for download
        if (window.twq) {
            window.twq("event", "tw-p9l1d-p9l1g", {});
            console.log("Twitter Pixel: Windows download event fired");
        }
        // Trigger the download
        window.location.href =
            "https://github.com/VaibhavRaina/Pytorch/releases/download/v1.0.0/interviewCr-setup-win64.exe";
    };

    const handleMacDownload = () => {
        // Fire Twitter Ads pixel event for download
        if (window.twq) {
            window.twq("event", "tw-p9l1d-p9l1g", {});
            console.log("Twitter Pixel: Mac download event fired");
        }
        // Trigger the download
        window.location.href =
            "https://github.com/VaibhavRaina/Pytorch/releases/download/v2.5.0/interview-cracker-setup-macOS.zip";
    };

    return (
        <section className="w-full bg-gradient-to-b from-black via-slate-900/80 to-black py-20">
            {/* Twitter Ads Pixel Script */}
            <Script
                src="https://static.ads-twitter.com/uwt.js"
                strategy="afterInteractive"
                onLoad={() => {
                    // Initialize twq if it doesn't exist
                    if (!window.twq) {
                        window.twq = function () {
                            window.twq.exe
                                ? window.twq.exe.apply(window.twq, arguments)
                                : window.twq.queue.push(arguments);
                        };
                        window.twq.version = "1.1";
                        window.twq.queue = [];
                    }
                    // Configure the pixel with your pixel ID
                    window.twq("config", "p9l1d");
                    console.log("Twitter Pixel loaded and configured");
                }}
            />

            <div className="mx-auto max-w-6xl text-center">
                {/* Heading with Gradient and Glow */}
                <h3 className="to-purple-500 mb-10 bg-gradient-to-r from-green-400 via-blue-400 bg-clip-text text-4xl font-extrabold text-transparent drop-shadow-lg md:text-5xl">
                    Take the Fast Lane
                    <br />
                    <span className="text-white">Download Interview Cracker Now</span>
                </h3>

                {/* Subtext */}
                <p className="mx-auto mb-12 max-w-2xl text-lg text-gray-300 md:text-xl">
                    Ace your interviews with the ultimate code generation tool at your fingertips.
                </p>

                {/* Download Buttons */}
                <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
                    <Button
                        color="primary"
                        variant="shadow"
                        radius="full"
                        className="relative w-56 overflow-visible px-6 py-3 text-sm font-medium"
                        aria-description="Download for Windows Button"
                        onClick={handleWindowsDownload}
                    >
                        <span className="absolute right-0 top-0">
                            <span className="relative flex size-3">
                                <span className="absolute inline-flex size-full animate-ping rounded-full bg-teal-600 opacity-75"></span>
                                <span className="relative inline-flex size-3 rounded-full bg-[#0B879C]/90"></span>
                            </span>
                        </span>
                        <FaWindows className="mr-2" /> Download for Windows
                    </Button>

                    <Button
                        color="primary"
                        variant="shadow"
                        radius="full"
                        className="relative w-56 overflow-visible px-6 py-3 text-sm font-medium"
                        aria-description="Download for Mac Button"
                        onClick={handleMacDownload}
                    >
                        <span className="absolute right-0 top-0">
                            <span className="relative flex size-3">
                                <span className="absolute inline-flex size-full animate-ping rounded-full bg-teal-600 opacity-75"></span>
                                <span className="relative inline-flex size-3 rounded-full bg-[#0B879C]/90"></span>
                            </span>
                        </span>
                        <FaApple className="mr-2" /> Download for Mac
                    </Button>
                </div>
            </div>
        </section>
    );
};

export default DownloadButtons;