import { getAuth } from '@clerk/nextjs/server';
import paypal from '@paypal/checkout-server-sdk';
import { NextApiRequest, NextApiResponse } from 'next';

import { sendSubscriptionConfirmation } from '@/lib/email';
import connectDB from '@/lib/mongodb';
import { Payment } from '@/model/Payment';
import { User } from '@/model/User';


// Validate PayPal credentials
const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID;
const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET;

if (!PAYPAL_CLIENT_ID || !PAYPAL_CLIENT_SECRET) {
  console.error('PayPal credentials are missing. Check your environment variables.');
}

// Log the first few characters of credentials for debugging (don't log full credentials)
console.log('PayPal Client ID (first 5 chars):', PAYPAL_CLIENT_ID ? PAYPAL_CLIENT_ID.substring(0, 5) + '...' : 'undefined');
console.log('PayPal Client Secret (first 5 chars):', PAYPAL_CLIENT_SECRET ? PAYPAL_CLIENT_SECRET.substring(0, 5) + '...' : 'undefined');

// Use sandbox environment for development, live for production
const isDev = process.env.NODE_ENV === 'development';
console.log('PayPal Environment:', isDev ? 'Sandbox' : 'Live');

let environment: paypal.core.SandboxEnvironment | paypal.core.LiveEnvironment;
let client: paypal.core.PayPalHttpClient | null = null;

try {
  if (isDev) {
    environment = new paypal.core.SandboxEnvironment(PAYPAL_CLIENT_ID!, PAYPAL_CLIENT_SECRET!);
  } else {
    environment = new paypal.core.LiveEnvironment(PAYPAL_CLIENT_ID!, PAYPAL_CLIENT_SECRET!);
  }
  client = new paypal.core.PayPalHttpClient(environment);
} catch (error) {
  console.error('Error initializing PayPal client:', error);
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token, os } = req.query;
  if (!token || typeof token !== 'string') {
    console.error('Invalid token:', req.query);
    res.redirect(302, '/pricing?payment=error&error=invalid_token');
    return;
  }

  // Check if PayPal client is initialized
  if (!client) {
    console.error('PayPal client is not initialized');
    res.redirect(302, '/pricing?payment=error&error=paypal_configuration_error');
    return;
  }

  // Get the base URL from the request or environment variable
  const protocol = req.headers['x-forwarded-proto'] || 'http';
  const host = req.headers.host || 'localhost:3000';
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || `${protocol}://${host}`;

  try {
    await connectDB();

    // Use getAuth for API routes
    const auth = getAuth(req);
    const userId = auth.userId;

    // For payment return flow, userId might not be available yet
    // PayPal callback handling might need special treatment
    // If no userId, we'll still try to capture the order but will need user info later

    const request = new paypal.orders.OrdersCaptureRequest(token);
    request.requestBody({});
    const response = await client.execute(request);
    const order = response.result;

    if (order.status !== 'COMPLETED') {
      console.error('Order not completed:', order);
      res.redirect(302, `${baseUrl}/pricing?payment=failed&error=order_not_completed`);
      return;
    }

    const purchaseUnit = order.purchase_units[0];
    const capture = purchaseUnit.payments.captures[0];
    const value = parseFloat(capture.amount.value);

    // Define pricing for plans - MUST match the pricing in create-order.ts
    const pricing = {
      weekly: 7,
      monthly: 7,
      yearly: 150
    };

    // Find the plan based on the payment amount
    const plan = Object.keys(pricing).find((p) => pricing[p as keyof typeof pricing] === value);

    if (!plan) {
      console.error('Unrecognized amount:', value);
      console.error('Expected one of:', Object.values(pricing));
      res.redirect(302, `${baseUrl}/pricing?payment=error&error=unrecognized_amount`);
      return;
    }

    // Store payment information even without userId
    // userId can be associated later if needed
    if (userId) {
      const user = await User.findOne({ clerkId: userId });
      if (!user) {
        console.error('User not found:', userId);
        res.redirect(302, `${baseUrl}/pricing?payment=error&error=user_not_found`);
        return;
      }

      const newPayment = new Payment({
        user: userId,
        paymentId: capture.id,
        orderId: order.id,
        amount: value * 100,
        currency: capture.amount.currency_code,
        status: order.status,
        method: 'paypal',
        plan,
        metadata: { os: os || 'Unknown' },
      });
      await newPayment.save();

      const now = new Date();
      const endDate =
        plan === 'weekly' ? new Date(now.setDate(now.getDate() + 7)) : plan === 'monthly' ? new Date(now.setMonth(now.getMonth() + 1)) : new Date(now.setFullYear(now.getFullYear() + 1));

      user.subscriptionType = 'premium';
      user.subscriptionPlan = plan;
      user.subscriptionStartDate = new Date();
      user.subscriptionEndDate = endDate;
      user.promptsUsed = 0;
      await user.save();

      await sendSubscriptionConfirmation(user.email, user.name || 'Valued Customer', plan, value * 100, capture.amount.currency_code, endDate, os as string);
    } else {
      // Store the payment without a user for now
      // This handles the case where PayPal callback comes without authentication
      const newPayment = new Payment({
        paymentId: capture.id,
        orderId: order.id,
        amount: value * 100,
        currency: capture.amount.currency_code,
        status: order.status,
        method: 'paypal',
        plan,
        metadata: {
          os: os || 'Unknown',
          pendingUserAssociation: true // Flag that this payment needs to be associated with a user
        },
      });
      await newPayment.save();
    }

    res.redirect(302, `${baseUrl}/payment-success?method=paypal&plan=${plan}&os=${encodeURIComponent(os as string)}`);
    return;
  } catch (error) {
    console.error('Capture PayPal order error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.redirect(302, `${baseUrl}/pricing?payment=error&error=${encodeURIComponent(errorMessage)}`);
    return;
  }
}