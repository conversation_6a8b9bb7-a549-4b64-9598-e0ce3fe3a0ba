/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/import-in-the-middle";
exports.ids = ["vendor-chunks/import-in-the-middle"];
exports.modules = {

/***/ "(ssr)/./node_modules/import-in-the-middle/index.js":
/*!****************************************************!*\
  !*** ./node_modules/import-in-the-middle/index.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Unless explicitly stated otherwise all files in this repository are licensed under the Apache 2.0 License.\n//\n// This product includes software developed at Datadog (https://www.datadoghq.com/). Copyright 2021 Datadog, Inc.\n\nconst path = __webpack_require__(/*! path */ \"path\")\nconst parse = __webpack_require__(/*! module-details-from-path */ \"(ssr)/./node_modules/module-details-from-path/index.js\")\nconst { fileURLToPath } = __webpack_require__(/*! url */ \"url\")\nconst { MessageChannel } = __webpack_require__(/*! worker_threads */ \"worker_threads\")\n\nconst {\n  importHooks,\n  specifiers,\n  toHook\n} = __webpack_require__(/*! ./lib/register */ \"(ssr)/./node_modules/import-in-the-middle/lib/register.js\")\n\nfunction addHook (hook) {\n  importHooks.push(hook)\n  toHook.forEach(([name, namespace]) => hook(name, namespace))\n}\n\nfunction removeHook (hook) {\n  const index = importHooks.indexOf(hook)\n  if (index > -1) {\n    importHooks.splice(index, 1)\n  }\n}\n\nfunction callHookFn (hookFn, namespace, name, baseDir) {\n  const newDefault = hookFn(namespace, name, baseDir)\n  if (newDefault && newDefault !== namespace) {\n    namespace.default = newDefault\n  }\n}\n\nlet sendModulesToLoader\n\n/**\n * EXPERIMENTAL\n * This feature is experimental and may change in minor versions.\n * **NOTE** This feature is incompatible with the {internals: true} Hook option.\n *\n * Creates a message channel with a port that can be used to add hooks to the\n * list of exclusively included modules.\n *\n * This can be used to only wrap modules that are Hook'ed, however modules need\n * to be hooked before they are imported.\n *\n * ```ts\n * import { register } from 'module'\n * import { Hook, createAddHookMessageChannel } from 'import-in-the-middle'\n *\n * const { registerOptions, waitForAllMessagesAcknowledged } = createAddHookMessageChannel()\n *\n * register('import-in-the-middle/hook.mjs', import.meta.url, registerOptions)\n *\n * Hook(['fs'], (exported, name, baseDir) => {\n *   // Instrument the fs module\n * })\n *\n * // Ensure that the loader has acknowledged all the modules\n * // before we allow execution to continue\n * await waitForAllMessagesAcknowledged()\n * ```\n */\nfunction createAddHookMessageChannel () {\n  const { port1, port2 } = new MessageChannel()\n  let pendingAckCount = 0\n  let resolveFn\n\n  sendModulesToLoader = (modules) => {\n    pendingAckCount++\n    port1.postMessage(modules)\n  }\n\n  port1.on('message', () => {\n    pendingAckCount--\n\n    if (resolveFn && pendingAckCount <= 0) {\n      resolveFn()\n    }\n  }).unref()\n\n  function waitForAllMessagesAcknowledged () {\n    // This timer is to prevent the process from exiting with code 13:\n    // 13: Unsettled Top-Level Await.\n    const timer = setInterval(() => { }, 1000)\n    const promise = new Promise((resolve) => {\n      resolveFn = resolve\n    }).then(() => { clearInterval(timer) })\n\n    if (pendingAckCount === 0) {\n      resolveFn()\n    }\n\n    return promise\n  }\n\n  const addHookMessagePort = port2\n  const registerOptions = { data: { addHookMessagePort, include: [] }, transferList: [addHookMessagePort] }\n\n  return { registerOptions, addHookMessagePort, waitForAllMessagesAcknowledged }\n}\n\nfunction Hook (modules, options, hookFn) {\n  if ((this instanceof Hook) === false) return new Hook(modules, options, hookFn)\n  if (typeof modules === 'function') {\n    hookFn = modules\n    modules = null\n    options = null\n  } else if (typeof options === 'function') {\n    hookFn = options\n    options = null\n  }\n  const internals = options ? options.internals === true : false\n\n  if (sendModulesToLoader && Array.isArray(modules)) {\n    sendModulesToLoader(modules)\n  }\n\n  this._iitmHook = (name, namespace) => {\n    const filename = name\n    const isBuiltin = name.startsWith('node:')\n    let baseDir\n\n    if (isBuiltin) {\n      name = name.replace(/^node:/, '')\n    } else {\n      if (name.startsWith('file://')) {\n        try {\n          name = fileURLToPath(name)\n        } catch (e) {}\n      }\n      const details = parse(name)\n      if (details) {\n        name = details.name\n        baseDir = details.basedir\n      }\n    }\n\n    if (modules) {\n      for (const moduleName of modules) {\n        if (moduleName === name) {\n          if (baseDir) {\n            if (internals) {\n              name = name + path.sep + path.relative(baseDir, fileURLToPath(filename))\n            } else {\n              if (!baseDir.endsWith(specifiers.get(filename))) continue\n            }\n          }\n          callHookFn(hookFn, namespace, name, baseDir)\n        }\n      }\n    } else {\n      callHookFn(hookFn, namespace, name, baseDir)\n    }\n  }\n\n  addHook(this._iitmHook)\n}\n\nHook.prototype.unhook = function () {\n  removeHook(this._iitmHook)\n}\n\nmodule.exports = Hook\nmodule.exports.Hook = Hook\nmodule.exports.addHook = addHook\nmodule.exports.removeHook = removeHook\nmodule.exports.createAddHookMessageChannel = createAddHookMessageChannel\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/import-in-the-middle/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/import-in-the-middle/lib/register.js":
/*!***********************************************************!*\
  !*** ./node_modules/import-in-the-middle/lib/register.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("// Unless explicitly stated otherwise all files in this repository are licensed under the Apache 2.0 License.\n//\n// This product includes software developed at Datadog (https://www.datadoghq.com/). Copyright 2021 Datadog, Inc.\n\nconst importHooks = [] // TODO should this be a Set?\nconst setters = new WeakMap()\nconst getters = new WeakMap()\nconst specifiers = new Map()\nconst toHook = []\n\nconst proxyHandler = {\n  set (target, name, value) {\n    return setters.get(target)[name](value)\n  },\n\n  get (target, name) {\n    if (name === Symbol.toStringTag) {\n      return 'Module'\n    }\n\n    const getter = getters.get(target)[name]\n\n    if (typeof getter === 'function') {\n      return getter()\n    }\n  },\n\n  defineProperty (target, property, descriptor) {\n    if ((!('value' in descriptor))) {\n      throw new Error('Getters/setters are not supported for exports property descriptors.')\n    }\n\n    return setters.get(target)[property](descriptor.value)\n  }\n}\n\nfunction register (name, namespace, set, get, specifier) {\n  specifiers.set(name, specifier)\n  setters.set(namespace, set)\n  getters.set(namespace, get)\n  const proxy = new Proxy(namespace, proxyHandler)\n  importHooks.forEach(hook => hook(name, proxy))\n  toHook.push([name, proxy])\n}\n\nexports.register = register\nexports.importHooks = importHooks\nexports.specifiers = specifiers\nexports.toHook = toHook\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/import-in-the-middle/lib/register.js\n");

/***/ }),

/***/ "(instrument)/./node_modules/import-in-the-middle/index.js":
/*!****************************************************!*\
  !*** ./node_modules/import-in-the-middle/index.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Unless explicitly stated otherwise all files in this repository are licensed under the Apache 2.0 License.\n//\n// This product includes software developed at Datadog (https://www.datadoghq.com/). Copyright 2021 Datadog, Inc.\n\nconst path = __webpack_require__(/*! path */ \"path\")\nconst parse = __webpack_require__(/*! module-details-from-path */ \"(instrument)/./node_modules/module-details-from-path/index.js\")\nconst { fileURLToPath } = __webpack_require__(/*! url */ \"url\")\nconst { MessageChannel } = __webpack_require__(/*! worker_threads */ \"worker_threads\")\n\nconst {\n  importHooks,\n  specifiers,\n  toHook\n} = __webpack_require__(/*! ./lib/register */ \"(instrument)/./node_modules/import-in-the-middle/lib/register.js\")\n\nfunction addHook (hook) {\n  importHooks.push(hook)\n  toHook.forEach(([name, namespace]) => hook(name, namespace))\n}\n\nfunction removeHook (hook) {\n  const index = importHooks.indexOf(hook)\n  if (index > -1) {\n    importHooks.splice(index, 1)\n  }\n}\n\nfunction callHookFn (hookFn, namespace, name, baseDir) {\n  const newDefault = hookFn(namespace, name, baseDir)\n  if (newDefault && newDefault !== namespace) {\n    namespace.default = newDefault\n  }\n}\n\nlet sendModulesToLoader\n\n/**\n * EXPERIMENTAL\n * This feature is experimental and may change in minor versions.\n * **NOTE** This feature is incompatible with the {internals: true} Hook option.\n *\n * Creates a message channel with a port that can be used to add hooks to the\n * list of exclusively included modules.\n *\n * This can be used to only wrap modules that are Hook'ed, however modules need\n * to be hooked before they are imported.\n *\n * ```ts\n * import { register } from 'module'\n * import { Hook, createAddHookMessageChannel } from 'import-in-the-middle'\n *\n * const { registerOptions, waitForAllMessagesAcknowledged } = createAddHookMessageChannel()\n *\n * register('import-in-the-middle/hook.mjs', import.meta.url, registerOptions)\n *\n * Hook(['fs'], (exported, name, baseDir) => {\n *   // Instrument the fs module\n * })\n *\n * // Ensure that the loader has acknowledged all the modules\n * // before we allow execution to continue\n * await waitForAllMessagesAcknowledged()\n * ```\n */\nfunction createAddHookMessageChannel () {\n  const { port1, port2 } = new MessageChannel()\n  let pendingAckCount = 0\n  let resolveFn\n\n  sendModulesToLoader = (modules) => {\n    pendingAckCount++\n    port1.postMessage(modules)\n  }\n\n  port1.on('message', () => {\n    pendingAckCount--\n\n    if (resolveFn && pendingAckCount <= 0) {\n      resolveFn()\n    }\n  }).unref()\n\n  function waitForAllMessagesAcknowledged () {\n    // This timer is to prevent the process from exiting with code 13:\n    // 13: Unsettled Top-Level Await.\n    const timer = setInterval(() => { }, 1000)\n    const promise = new Promise((resolve) => {\n      resolveFn = resolve\n    }).then(() => { clearInterval(timer) })\n\n    if (pendingAckCount === 0) {\n      resolveFn()\n    }\n\n    return promise\n  }\n\n  const addHookMessagePort = port2\n  const registerOptions = { data: { addHookMessagePort, include: [] }, transferList: [addHookMessagePort] }\n\n  return { registerOptions, addHookMessagePort, waitForAllMessagesAcknowledged }\n}\n\nfunction Hook (modules, options, hookFn) {\n  if ((this instanceof Hook) === false) return new Hook(modules, options, hookFn)\n  if (typeof modules === 'function') {\n    hookFn = modules\n    modules = null\n    options = null\n  } else if (typeof options === 'function') {\n    hookFn = options\n    options = null\n  }\n  const internals = options ? options.internals === true : false\n\n  if (sendModulesToLoader && Array.isArray(modules)) {\n    sendModulesToLoader(modules)\n  }\n\n  this._iitmHook = (name, namespace) => {\n    const filename = name\n    const isBuiltin = name.startsWith('node:')\n    let baseDir\n\n    if (isBuiltin) {\n      name = name.replace(/^node:/, '')\n    } else {\n      if (name.startsWith('file://')) {\n        try {\n          name = fileURLToPath(name)\n        } catch (e) {}\n      }\n      const details = parse(name)\n      if (details) {\n        name = details.name\n        baseDir = details.basedir\n      }\n    }\n\n    if (modules) {\n      for (const moduleName of modules) {\n        if (moduleName === name) {\n          if (baseDir) {\n            if (internals) {\n              name = name + path.sep + path.relative(baseDir, fileURLToPath(filename))\n            } else {\n              if (!baseDir.endsWith(specifiers.get(filename))) continue\n            }\n          }\n          callHookFn(hookFn, namespace, name, baseDir)\n        }\n      }\n    } else {\n      callHookFn(hookFn, namespace, name, baseDir)\n    }\n  }\n\n  addHook(this._iitmHook)\n}\n\nHook.prototype.unhook = function () {\n  removeHook(this._iitmHook)\n}\n\nmodule.exports = Hook\nmodule.exports.Hook = Hook\nmodule.exports.addHook = addHook\nmodule.exports.removeHook = removeHook\nmodule.exports.createAddHookMessageChannel = createAddHookMessageChannel\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/./node_modules/import-in-the-middle/index.js\n");

/***/ }),

/***/ "(instrument)/./node_modules/import-in-the-middle/lib/register.js":
/*!***********************************************************!*\
  !*** ./node_modules/import-in-the-middle/lib/register.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("// Unless explicitly stated otherwise all files in this repository are licensed under the Apache 2.0 License.\n//\n// This product includes software developed at Datadog (https://www.datadoghq.com/). Copyright 2021 Datadog, Inc.\n\nconst importHooks = [] // TODO should this be a Set?\nconst setters = new WeakMap()\nconst getters = new WeakMap()\nconst specifiers = new Map()\nconst toHook = []\n\nconst proxyHandler = {\n  set (target, name, value) {\n    return setters.get(target)[name](value)\n  },\n\n  get (target, name) {\n    if (name === Symbol.toStringTag) {\n      return 'Module'\n    }\n\n    const getter = getters.get(target)[name]\n\n    if (typeof getter === 'function') {\n      return getter()\n    }\n  },\n\n  defineProperty (target, property, descriptor) {\n    if ((!('value' in descriptor))) {\n      throw new Error('Getters/setters are not supported for exports property descriptors.')\n    }\n\n    return setters.get(target)[property](descriptor.value)\n  }\n}\n\nfunction register (name, namespace, set, get, specifier) {\n  specifiers.set(name, specifier)\n  setters.set(namespace, set)\n  getters.set(namespace, get)\n  const proxy = new Proxy(namespace, proxyHandler)\n  importHooks.forEach(hook => hook(name, proxy))\n  toHook.push([name, proxy])\n}\n\nexports.register = register\nexports.importHooks = importHooks\nexports.specifiers = specifiers\nexports.toHook = toHook\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/./node_modules/import-in-the-middle/lib/register.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/import-in-the-middle/index.js":
/*!****************************************************!*\
  !*** ./node_modules/import-in-the-middle/index.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Unless explicitly stated otherwise all files in this repository are licensed under the Apache 2.0 License.\n//\n// This product includes software developed at Datadog (https://www.datadoghq.com/). Copyright 2021 Datadog, Inc.\n\nconst path = __webpack_require__(/*! path */ \"path\")\nconst parse = __webpack_require__(/*! module-details-from-path */ \"(rsc)/./node_modules/module-details-from-path/index.js\")\nconst { fileURLToPath } = __webpack_require__(/*! url */ \"url\")\nconst { MessageChannel } = __webpack_require__(/*! worker_threads */ \"worker_threads\")\n\nconst {\n  importHooks,\n  specifiers,\n  toHook\n} = __webpack_require__(/*! ./lib/register */ \"(rsc)/./node_modules/import-in-the-middle/lib/register.js\")\n\nfunction addHook (hook) {\n  importHooks.push(hook)\n  toHook.forEach(([name, namespace]) => hook(name, namespace))\n}\n\nfunction removeHook (hook) {\n  const index = importHooks.indexOf(hook)\n  if (index > -1) {\n    importHooks.splice(index, 1)\n  }\n}\n\nfunction callHookFn (hookFn, namespace, name, baseDir) {\n  const newDefault = hookFn(namespace, name, baseDir)\n  if (newDefault && newDefault !== namespace) {\n    namespace.default = newDefault\n  }\n}\n\nlet sendModulesToLoader\n\n/**\n * EXPERIMENTAL\n * This feature is experimental and may change in minor versions.\n * **NOTE** This feature is incompatible with the {internals: true} Hook option.\n *\n * Creates a message channel with a port that can be used to add hooks to the\n * list of exclusively included modules.\n *\n * This can be used to only wrap modules that are Hook'ed, however modules need\n * to be hooked before they are imported.\n *\n * ```ts\n * import { register } from 'module'\n * import { Hook, createAddHookMessageChannel } from 'import-in-the-middle'\n *\n * const { registerOptions, waitForAllMessagesAcknowledged } = createAddHookMessageChannel()\n *\n * register('import-in-the-middle/hook.mjs', import.meta.url, registerOptions)\n *\n * Hook(['fs'], (exported, name, baseDir) => {\n *   // Instrument the fs module\n * })\n *\n * // Ensure that the loader has acknowledged all the modules\n * // before we allow execution to continue\n * await waitForAllMessagesAcknowledged()\n * ```\n */\nfunction createAddHookMessageChannel () {\n  const { port1, port2 } = new MessageChannel()\n  let pendingAckCount = 0\n  let resolveFn\n\n  sendModulesToLoader = (modules) => {\n    pendingAckCount++\n    port1.postMessage(modules)\n  }\n\n  port1.on('message', () => {\n    pendingAckCount--\n\n    if (resolveFn && pendingAckCount <= 0) {\n      resolveFn()\n    }\n  }).unref()\n\n  function waitForAllMessagesAcknowledged () {\n    // This timer is to prevent the process from exiting with code 13:\n    // 13: Unsettled Top-Level Await.\n    const timer = setInterval(() => { }, 1000)\n    const promise = new Promise((resolve) => {\n      resolveFn = resolve\n    }).then(() => { clearInterval(timer) })\n\n    if (pendingAckCount === 0) {\n      resolveFn()\n    }\n\n    return promise\n  }\n\n  const addHookMessagePort = port2\n  const registerOptions = { data: { addHookMessagePort, include: [] }, transferList: [addHookMessagePort] }\n\n  return { registerOptions, addHookMessagePort, waitForAllMessagesAcknowledged }\n}\n\nfunction Hook (modules, options, hookFn) {\n  if ((this instanceof Hook) === false) return new Hook(modules, options, hookFn)\n  if (typeof modules === 'function') {\n    hookFn = modules\n    modules = null\n    options = null\n  } else if (typeof options === 'function') {\n    hookFn = options\n    options = null\n  }\n  const internals = options ? options.internals === true : false\n\n  if (sendModulesToLoader && Array.isArray(modules)) {\n    sendModulesToLoader(modules)\n  }\n\n  this._iitmHook = (name, namespace) => {\n    const filename = name\n    const isBuiltin = name.startsWith('node:')\n    let baseDir\n\n    if (isBuiltin) {\n      name = name.replace(/^node:/, '')\n    } else {\n      if (name.startsWith('file://')) {\n        try {\n          name = fileURLToPath(name)\n        } catch (e) {}\n      }\n      const details = parse(name)\n      if (details) {\n        name = details.name\n        baseDir = details.basedir\n      }\n    }\n\n    if (modules) {\n      for (const moduleName of modules) {\n        if (moduleName === name) {\n          if (baseDir) {\n            if (internals) {\n              name = name + path.sep + path.relative(baseDir, fileURLToPath(filename))\n            } else {\n              if (!baseDir.endsWith(specifiers.get(filename))) continue\n            }\n          }\n          callHookFn(hookFn, namespace, name, baseDir)\n        }\n      }\n    } else {\n      callHookFn(hookFn, namespace, name, baseDir)\n    }\n  }\n\n  addHook(this._iitmHook)\n}\n\nHook.prototype.unhook = function () {\n  removeHook(this._iitmHook)\n}\n\nmodule.exports = Hook\nmodule.exports.Hook = Hook\nmodule.exports.addHook = addHook\nmodule.exports.removeHook = removeHook\nmodule.exports.createAddHookMessageChannel = createAddHookMessageChannel\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/import-in-the-middle/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/import-in-the-middle/lib/register.js":
/*!***********************************************************!*\
  !*** ./node_modules/import-in-the-middle/lib/register.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("// Unless explicitly stated otherwise all files in this repository are licensed under the Apache 2.0 License.\n//\n// This product includes software developed at Datadog (https://www.datadoghq.com/). Copyright 2021 Datadog, Inc.\n\nconst importHooks = [] // TODO should this be a Set?\nconst setters = new WeakMap()\nconst getters = new WeakMap()\nconst specifiers = new Map()\nconst toHook = []\n\nconst proxyHandler = {\n  set (target, name, value) {\n    return setters.get(target)[name](value)\n  },\n\n  get (target, name) {\n    if (name === Symbol.toStringTag) {\n      return 'Module'\n    }\n\n    const getter = getters.get(target)[name]\n\n    if (typeof getter === 'function') {\n      return getter()\n    }\n  },\n\n  defineProperty (target, property, descriptor) {\n    if ((!('value' in descriptor))) {\n      throw new Error('Getters/setters are not supported for exports property descriptors.')\n    }\n\n    return setters.get(target)[property](descriptor.value)\n  }\n}\n\nfunction register (name, namespace, set, get, specifier) {\n  specifiers.set(name, specifier)\n  setters.set(namespace, set)\n  getters.set(namespace, get)\n  const proxy = new Proxy(namespace, proxyHandler)\n  importHooks.forEach(hook => hook(name, proxy))\n  toHook.push([name, proxy])\n}\n\nexports.register = register\nexports.importHooks = importHooks\nexports.specifiers = specifiers\nexports.toHook = toHook\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/import-in-the-middle/lib/register.js\n");

/***/ })

};
;