"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/razorpay/create-order";
exports.ids = ["pages/api/razorpay/create-order"];
exports.modules = {

/***/ "@clerk/nextjs/server":
/*!***************************************!*\
  !*** external "@clerk/nextjs/server" ***!
  \***************************************/
/***/ ((module) => {

module.exports = require("@clerk/nextjs/server");

/***/ }),

/***/ "@sentry/nextjs":
/*!*********************************!*\
  !*** external "@sentry/nextjs" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@sentry/nextjs");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "razorpay":
/*!***************************!*\
  !*** external "razorpay" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("razorpay");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "(api)/./pages/api/razorpay/create-order.ts":
/*!********************************************!*\
  !*** ./pages/api/razorpay/create-order.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (/* binding */ wrappedHandler$1)\n/* harmony export */ });\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @sentry/nextjs */ \"@sentry/nextjs\");\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _clerk_nextjs_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @clerk/nextjs/server */ \"@clerk/nextjs/server\");\n/* harmony import */ var _clerk_nextjs_server__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_clerk_nextjs_server__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var razorpay__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! razorpay */ \"razorpay\");\n/* harmony import */ var razorpay__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(razorpay__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/mongodb */ \"(api)/./lib/mongodb.ts\");\n/* harmony import */ var _model_User__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/model/User */ \"(api)/./model/User.ts\");\n\n\n\n\n\n\n// Validate environment variables\nconst RAZORPAY_KEY_ID = process.env.RAZORPAY_KEY_ID;\nconst RAZORPAY_KEY_SECRET = process.env.RAZORPAY_KEY_SECRET;\nif (!RAZORPAY_KEY_ID || !RAZORPAY_KEY_SECRET) {\n    console.error(\"Razorpay configuration missing. Check RAZORPAY_KEY_ID and RAZORPAY_KEY_SECRET env variables.\");\n}\n// Create Razorpay instance\nconst razorpay = RAZORPAY_KEY_ID && RAZORPAY_KEY_SECRET ? new (razorpay__WEBPACK_IMPORTED_MODULE_2___default())({\n    key_id: RAZORPAY_KEY_ID,\n    key_secret: RAZORPAY_KEY_SECRET\n}) : null;\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            success: false,\n            message: \"Method not allowed\"\n        });\n    }\n    if (!razorpay) {\n        return res.status(500).json({\n            success: false,\n            message: \"Payment gateway configuration error. Please contact support.\"\n        });\n    }\n    // Ensure this runs on the server using the Pages Router compatible approach\n    const { userId } = (0,_clerk_nextjs_server__WEBPACK_IMPORTED_MODULE_1__.getAuth)(req);\n    if (!userId) {\n        return res.status(401).json({\n            success: false,\n            message: \"Unauthorized\"\n        });\n    }\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n        const { plan, os } = req.body;\n        if (!plan) {\n            return res.status(400).json({\n                success: false,\n                message: \"Plan is required\"\n            });\n        }\n        const usdToInr = 85.26;\n        let amountInUsd;\n        // Pricing logic (simplified since it's the same for all OS in your example)\n        switch(plan){\n            case \"weekly\":\n                amountInUsd = 7;\n                break;\n            case \"monthly\":\n                amountInUsd = 7;\n                break;\n            case \"yearly\":\n                amountInUsd = 150;\n                break;\n            default:\n                return res.status(400).json({\n                    success: false,\n                    message: \"Invalid plan\"\n                });\n        }\n        // Get user to check if they're from India\n        const user = await _model_User__WEBPACK_IMPORTED_MODULE_4__.User.findOne({\n            clerkId: userId\n        });\n        const isIndianUser = user?.countryCode === \"IN\";\n        console.log(\"User country code:\", user?.countryCode, \"isIndianUser:\", isIndianUser);\n        // For Indian users, we'll show prices in INR\n        const amountInPaise = Math.round(amountInUsd * usdToInr * 100);\n        const order = await razorpay.orders.create({\n            amount: amountInPaise,\n            currency: \"INR\",\n            receipt: `receipt_${Date.now()}`,\n            payment_capture: true\n        });\n        return res.status(200).json({\n            success: true,\n            orderId: order.id,\n            amount: isIndianUser ? Math.round(amountInUsd * usdToInr) : amountInUsd,\n            currency: isIndianUser ? \"INR\" : \"USD\",\n            plan,\n            os\n        });\n    } catch (error) {\n        console.error(\"Create order error:\", error);\n        return res.status(500).json({\n            success: false,\n            message: \"Internal server error\",\n            error: error.message\n        });\n    }\n}\n// Explicitly mark this file as server-only (optional, for clarity)\nconst config$1 = {\n    api: {\n    }\n};\n\nvar serverComponentModule = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    config: config$1,\n    default: handler\n});\n\n/*\n * This file is a template for the code which will be substituted when our webpack loader handles API files in the\n * `pages/` directory.\n *\n * We use `__SENTRY_WRAPPING_TARGET_FILE__.cjs` as a placeholder for the path to the file being wrapped. Because it's not a real package,\n * this causes both TS and ESLint to complain, hence the pragma comments below.\n */\n\n\nconst userApiModule = serverComponentModule ;\n\n// Default to undefined. It's possible for Next.js users to not define any exports/handlers in an API route. If that is\n// the case Next.js will crash during runtime but the Sentry SDK should definitely not crash so we need to handle it.\nlet userProvidedHandler = undefined;\n\nif ('default' in userApiModule && typeof userApiModule.default === 'function') {\n  // Handle when user defines via ESM export: `export default myFunction;`\n  userProvidedHandler = userApiModule.default;\n} else if (typeof userApiModule === 'function') {\n  // Handle when user defines via CJS export: \"module.exports = myFunction;\"\n  userProvidedHandler = userApiModule;\n}\n\nconst origConfig = userApiModule.config || {};\n\n// Setting `externalResolver` to `true` prevents nextjs from throwing a warning in dev about API routes resolving\n// without sending a response. It's a false positive (a response is sent, but only after we flush our send queue), and\n// we throw a warning of our own to tell folks that, but it's better if we just don't have to deal with it in the first\n// place.\nconst config = {\n  ...origConfig,\n  api: {\n    ...origConfig.api,\n    externalResolver: true,\n  },\n};\n\nlet wrappedHandler = userProvidedHandler;\n\nif (wrappedHandler && undefined) {}\n\nif (wrappedHandler) {\n  wrappedHandler = _sentry_nextjs__WEBPACK_IMPORTED_MODULE_0__.wrapApiHandlerWithSentry(wrappedHandler, '/api/razorpay/create-order');\n}\n\nconst wrappedHandler$1 = wrappedHandler;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/razorpay/create-order.ts\n");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Frazorpay%2Fcreate-order&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Crazorpay%5Ccreate-order.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Frazorpay%2Fcreate-order&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Crazorpay%5Ccreate-order.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_razorpay_create_order_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\razorpay\\create-order.ts */ \"(api)/./pages/api/razorpay/create-order.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_razorpay_create_order_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_razorpay_create_order_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/razorpay/create-order\",\n        pathname: \"/api/razorpay/create-order\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_razorpay_create_order_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Frazorpay%2Fcreate-order&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Crazorpay%5Ccreate-order.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/mongodb.ts":
/*!************************!*\
  !*** ./lib/mongodb.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI || \"\";\nif (!MONGODB_URI) {\n    throw new Error(\"Please define the MONGODB_URI environment variable\");\n}\nlet cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, {\n            bufferCommands: false\n        }).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    cached.conn = await cached.promise;\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWIvbW9uZ29kYi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFFaEMsTUFBTUMsY0FBY0MsUUFBUUMsR0FBRyxDQUFDRixXQUFXLElBQUk7QUFFL0MsSUFBSSxDQUFDQSxhQUFhO0lBQ2hCLE1BQU0sSUFBSUcsTUFBTTtBQUNsQjtBQUVBLElBQUlDLFNBQVMsT0FBZ0JMLFFBQVE7QUFFckMsSUFBSSxDQUFDSyxRQUFRO0lBQ1hBLFNBQVMsT0FBZ0JMLFFBQVEsR0FBRztRQUFFTyxNQUFNO1FBQU1DLFNBQVM7SUFBSztBQUNsRTtBQUVBLGVBQWVDO0lBQ2IsSUFBSUosT0FBT0UsSUFBSSxFQUFFO1FBQ2YsT0FBT0YsT0FBT0UsSUFBSTtJQUNwQjtJQUVBLElBQUksQ0FBQ0YsT0FBT0csT0FBTyxFQUFFO1FBQ25CSCxPQUFPRyxPQUFPLEdBQUdSLHVEQUFnQixDQUFDQyxhQUFhO1lBQzdDVSxnQkFBZ0I7UUFDbEIsR0FBR0MsSUFBSSxDQUFDLENBQUNaO1lBQ1AsT0FBT0E7UUFDVDtJQUNGO0lBQ0FLLE9BQU9FLElBQUksR0FBRyxNQUFNRixPQUFPRyxPQUFPO0lBQ2xDLE9BQU9ILE9BQU9FLElBQUk7QUFDcEI7QUFFQSxpRUFBZUUsU0FBU0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGVydmlld2NyYWNrZXIvLi9saWIvbW9uZ29kYi50cz8wNWJkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBtb25nb29zZSBmcm9tIFwibW9uZ29vc2VcIjtcblxuY29uc3QgTU9OR09EQl9VUkkgPSBwcm9jZXNzLmVudi5NT05HT0RCX1VSSSB8fCBcIlwiO1xuXG5pZiAoIU1PTkdPREJfVVJJKSB7XG4gIHRocm93IG5ldyBFcnJvcihcIlBsZWFzZSBkZWZpbmUgdGhlIE1PTkdPREJfVVJJIGVudmlyb25tZW50IHZhcmlhYmxlXCIpO1xufVxuXG5sZXQgY2FjaGVkID0gKGdsb2JhbCBhcyBhbnkpLm1vbmdvb3NlO1xuXG5pZiAoIWNhY2hlZCkge1xuICBjYWNoZWQgPSAoZ2xvYmFsIGFzIGFueSkubW9uZ29vc2UgPSB7IGNvbm46IG51bGwsIHByb21pc2U6IG51bGwgfTtcbn1cblxuYXN5bmMgZnVuY3Rpb24gY29ubmVjdERCKCkge1xuICBpZiAoY2FjaGVkLmNvbm4pIHtcbiAgICByZXR1cm4gY2FjaGVkLmNvbm47XG4gIH1cblxuICBpZiAoIWNhY2hlZC5wcm9taXNlKSB7XG4gICAgY2FjaGVkLnByb21pc2UgPSBtb25nb29zZS5jb25uZWN0KE1PTkdPREJfVVJJLCB7XG4gICAgICBidWZmZXJDb21tYW5kczogZmFsc2UsXG4gICAgfSkudGhlbigobW9uZ29vc2UpID0+IHtcbiAgICAgIHJldHVybiBtb25nb29zZTtcbiAgICB9KTtcbiAgfVxuICBjYWNoZWQuY29ubiA9IGF3YWl0IGNhY2hlZC5wcm9taXNlO1xuICByZXR1cm4gY2FjaGVkLmNvbm47XG59XG5cbmV4cG9ydCBkZWZhdWx0IGNvbm5lY3REQjsiXSwibmFtZXMiOlsibW9uZ29vc2UiLCJNT05HT0RCX1VSSSIsInByb2Nlc3MiLCJlbnYiLCJFcnJvciIsImNhY2hlZCIsImdsb2JhbCIsImNvbm4iLCJwcm9taXNlIiwiY29ubmVjdERCIiwiY29ubmVjdCIsImJ1ZmZlckNvbW1hbmRzIiwidGhlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./lib/mongodb.ts\n");

/***/ }),

/***/ "(api)/./model/User.ts":
/*!***********************!*\
  !*** ./model/User.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   User: () => (/* binding */ User)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Helper function to generate a random string for secretKey\nconst generateRandomKey = ()=>{\n    return crypto__WEBPACK_IMPORTED_MODULE_1___default().randomBytes(32).toString(\"hex\");\n};\nconst userSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    clerkId: {\n        type: String,\n        unique: true,\n        sparse: true\n    },\n    name: {\n        type: String\n    },\n    email: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    password: {\n        type: String\n    },\n    provider: {\n        type: String,\n        default: \"clerk\"\n    },\n    avatar: {\n        type: String,\n        default: \"/default-avatar.png\"\n    },\n    countryCode: {\n        type: String,\n        default: \"US\"\n    },\n    subscriptionType: {\n        type: String,\n        default: \"free\"\n    },\n    subscriptionPlan: {\n        type: String,\n        default: \"\"\n    },\n    subscriptionStartDate: {\n        type: Date\n    },\n    subscriptionEndDate: {\n        type: Date\n    },\n    promptsUsed: {\n        type: Number,\n        default: 0\n    },\n    lastResetDate: {\n        type: Date\n    },\n    secretKey: {\n        type: String,\n        default: generateRandomKey,\n        unique: true\n    },\n    isAdmin: {\n        type: Boolean,\n        default: false\n    },\n    resetCode: {\n        type: String\n    },\n    resetCodeExpires: {\n        type: Date\n    }\n});\nconst User = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"User\", userSchema);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./model/User.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Frazorpay%2Fcreate-order&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Crazorpay%5Ccreate-order.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();