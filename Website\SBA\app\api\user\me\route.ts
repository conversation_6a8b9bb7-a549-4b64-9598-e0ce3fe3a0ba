import { clerkClient } from "@clerk/clerk-sdk-node";
import { getAuth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";

import connectDB from "@/lib/mongodb";
import { checkAndUpdateExpiredSubscription } from "@/lib/subscription-utils";
import { User } from "@/model/User";

// Helper function to detect user's country
async function detectUserCountry(req: NextRequest): Promise<string> {
  try {
    // Get client IP with multiple fallbacks
    let ip = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || '';

    // Only use fallback IPs if we're on localhost and can't determine the real IP
    if (!ip || ip === '::1' || ip === '127.0.0.1') {
      // Check if we should use an Indian IP for testing (via environment variable)
      const useIndianIP = process.env.USE_INDIAN_IP === 'true';

      if (useIndianIP) {
        // Use an Indian IP address for testing INR pricing
        ip = '**************'; // Indian IP
        console.log('Using Indian IP for user country detection:', ip);
      } else {
        // Use a public IP lookup service to get the real external IP
        try {
          const ipifyResponse = await fetch('https://api.ipify.org?format=json', { cache: 'no-store' });
          if (ipifyResponse.ok) {
            const ipData = await ipifyResponse.json();
            ip = ipData.ip;
            console.log('Retrieved real external IP for user country detection:', ip);
          } else {
            // Fallback to US IP if we can't determine the real IP
            ip = '*******';
            console.log('Could not determine real IP for user country detection, using fallback:', ip);
          }
        } catch (ipError) {
          // Fallback to US IP if the service fails
          ip = '*******';
          console.log('Error getting real IP for user country detection, using fallback:', ip);
        }
      }
    } else {
      console.log('Using client IP for user country detection:', ip);
    }

    // If we have multiple IPs (from proxies), use the first one
    if (ip.includes(',')) {
      ip = ip.split(',')[0].trim();
      console.log('Using first IP from proxy chain for user country detection:', ip);
    }

    const apiKey = process.env.IPGEOLOCATION_API_KEY;

    if (apiKey) {
      const geoResponse = await fetch(`https://api.ipgeolocation.io/ipgeo?apiKey=${apiKey}&ip=${ip}`, {
        headers: { 'Accept': 'application/json' },
        cache: 'no-store'
      });

      if (geoResponse.ok) {
        const geoData = await geoResponse.json();
        const countryCode = geoData.country_code2 || 'US';
        console.log("Detected user country:", countryCode);
        return countryCode;
      }
    }
  } catch (geoError) {
    console.error("Error detecting user location:", geoError);
  }

  return 'US'; // Default to US
}

// Explicitly mark this route as dynamic since it uses request headers
export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  console.log("User me API route triggered");

  try {
    // Get the authentication data from Clerk
    const auth = getAuth(req);
    const { userId } = auth;

    console.log("Auth check: userId =", userId);

    if (!userId) {
      console.log("No userId found in request");
      return NextResponse.json({ message: "Unauthorized", error: "No userId" }, { status: 401 });
    }

    // Connect to MongoDB
    console.log("Connecting to MongoDB");
    await connectDB();
    console.log("MongoDB connected");

    try {
      // Get the current user from Clerk to determine authentication method
      const clerkUser = await clerkClient.users.getUser(userId);

      // Extract primary email from Clerk user data
      const primaryEmail = clerkUser.emailAddresses.find(email =>
        email.id === clerkUser.primaryEmailAddressId
      )?.emailAddress;

      if (!primaryEmail) {
        console.log("No primary email found for user:", userId);
        return NextResponse.json({ message: "User email not found" }, { status: 400 });
      }

      console.log("Got email from Clerk:", primaryEmail);

      // Determine the current auth method
      // Check if the user is using OAuth (Google)
      const isUsingOAuth = clerkUser.externalAccounts.some(account => account.provider === "google");
      console.log("User is using OAuth:", isUsingOAuth);

      // Find the user by their Clerk ID
      console.log("Finding user with clerkId:", userId);
      let user = await User.findOne({ clerkId: userId });

      // If no user found by Clerk ID, try finding by email but respect the current auth method
      if (!user) {
        console.log("User not found by clerkId, checking by email:", primaryEmail);
        const existingUserByEmail = await User.findOne({ email: primaryEmail });

        if (existingUserByEmail) {
          // If user exists with this email but with different auth method
          if (isUsingOAuth && existingUserByEmail.provider === 'email') {
            console.log("Found traditional account with same email, but user is using OAuth now");
            // Create a new record for OAuth login to separate from traditional login
            // Get user's country from IP address
            const countryCode = await detectUserCountry(req);

            user = new User({
              clerkId: userId,
              name: clerkUser.firstName ? `${clerkUser.firstName} ${clerkUser.lastName || ''}` : 'New User',
              email: primaryEmail,
              avatar: clerkUser.imageUrl || '/default-avatar.png',
              role: "user",
              subscriptionType: "free",
              promptsUsed: 0,
              isAdmin: false,
              provider: "google", // Mark as Google provider
              countryCode,
              createdAt: new Date()
            });
            await user.save();
            console.log("Created separate OAuth user record for:", primaryEmail);
          } else {
            // Link existing account by updating the ClerkID
            existingUserByEmail.clerkId = userId;
            if (isUsingOAuth) {
              existingUserByEmail.provider = "google";
            }
            await existingUserByEmail.save();
            user = existingUserByEmail;
            console.log("Updated existing user with new ClerkID:", userId);
          }
        } else {
          // Create a new user with the Clerk ID and user details
          console.log("No existing user found, creating new user record");
          // Get user's country from IP address
          const countryCode = await detectUserCountry(req);

          user = new User({
            clerkId: userId,
            name: clerkUser.firstName ? `${clerkUser.firstName} ${clerkUser.lastName || ''}` : 'New User',
            email: primaryEmail,
            avatar: clerkUser.imageUrl || '/default-avatar.png',
            role: "user",
            subscriptionType: "free",
            promptsUsed: 0,
            isAdmin: false,
            provider: isUsingOAuth ? "google" : "clerk",
            countryCode,
            createdAt: new Date()
          });
          await user.save();
          console.log("New user created with ID:", user._id);
        }
      } else {
        console.log("User found:", user._id);

        // Update provider information if needed
        if (isUsingOAuth && user.provider !== "google") {
          user.provider = "google";
          await user.save();
          console.log("Updated user provider to Google");
        }

        // Update user details if they're missing
        if (user.name === "New User" || user.email === "<EMAIL>" || !user.countryCode) {
          console.log("Updating user with current Clerk details");

          // Extract name from Clerk user data
          const firstName = clerkUser.firstName || '';
          const lastName = clerkUser.lastName || '';
          const fullName = [firstName, lastName].filter(Boolean).join(' ');

          if (fullName && user.name === "New User") {
            user.name = fullName;
          }

          if (primaryEmail && user.email === "<EMAIL>") {
            user.email = primaryEmail;
          }

          // Get profile image if available
          if (clerkUser.imageUrl && user.avatar === "/default-avatar.png") {
            user.avatar = clerkUser.imageUrl;
          }

          // Set country code if not already set
          if (!user.countryCode) {
            user.countryCode = await detectUserCountry(req);
            console.log("Updated user country code to:", user.countryCode);
          }

          await user.save();
          console.log("User details updated from Clerk");
        }
      }

      // Check if the user's subscription has expired and update if needed
      const wasSubscriptionUpdated = await checkAndUpdateExpiredSubscription(user);
      if (wasSubscriptionUpdated) {
        console.log("User subscription was expired and updated to free plan:", user._id);
      }

      // Return the user data
      return NextResponse.json({
        ...user.toJSON(),
        status: 'success',
        message: wasSubscriptionUpdated
          ? 'User data fetched successfully. Your premium subscription has expired and has been updated to the free plan.'
          : 'User data fetched successfully'
      });
    } catch (clerkError) {
      console.error("Error with Clerk authentication:", clerkError);
      return NextResponse.json({
        message: "Error processing authentication",
        error: clerkError instanceof Error ? clerkError.message : "Clerk API error"
      }, { status: 500 });
    }
  } catch (error) {
    console.error("User me API error:", error);
    return NextResponse.json({
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}