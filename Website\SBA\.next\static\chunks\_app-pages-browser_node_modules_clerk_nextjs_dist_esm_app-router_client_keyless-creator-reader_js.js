"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_clerk_nextjs_dist_esm_app-router_client_keyless-creator-reader_js"],{

/***/ "(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js ***!
  \*****************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KeylessCreatorOrReader: function() { return /* binding */ KeylessCreatorOrReader; }\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _keyless_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../keyless-actions */ \"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\");\n\n\n\n\nconst KeylessCreatorOrReader = (props) => {\n  var _a;\n  const { children } = props;\n  const segments = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.useSelectedLayoutSegments)();\n  const isNotFoundRoute = ((_a = segments[0]) == null ? void 0 : _a.startsWith(\"/_not-found\")) || false;\n  const [state, fetchKeys] = react__WEBPACK_IMPORTED_MODULE_1___default().useActionState(_keyless_actions__WEBPACK_IMPORTED_MODULE_2__.createOrReadKeylessAction, null);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    if (isNotFoundRoute) {\n      return;\n    }\n    react__WEBPACK_IMPORTED_MODULE_1___default().startTransition(() => {\n      fetchKeys();\n    });\n  }, [isNotFoundRoute]);\n  if (!react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(children)) {\n    return children;\n  }\n  return react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(children, {\n    key: state == null ? void 0 : state.publishableKey,\n    publishableKey: state == null ? void 0 : state.publishableKey,\n    __internal_keyless_claimKeylessApplicationUrl: state == null ? void 0 : state.claimUrl,\n    __internal_keyless_copyInstanceKeysUrl: state == null ? void 0 : state.apiKeysUrl,\n    __internal_bypassMissingPublishableKey: true\n  });\n};\n\n//# sourceMappingURL=keyless-creator-reader.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   createOrReadKeylessAction: function() { return /* binding */ createOrReadKeylessAction; },
/* harmony export */   deleteKeylessAction: function() { return /* binding */ deleteKeylessAction; },
/* harmony export */   syncKeylessConfigAction: function() { return /* binding */ syncKeylessConfigAction; }
/* harmony export */ });
/* harmony import */ var next_dist_client_app_call_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/client/app-call-server */ "(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js");
/* harmony import */ var next_dist_client_app_call_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_app_call_server__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js");



function __build_action__(action, args) {
  return (0,next_dist_client_app_call_server__WEBPACK_IMPORTED_MODULE_0__.callServer)(action.$$id, args)
}

/* __next_internal_action_entry_do_not_use__ {"0a99371a463eea10cd136fa6e633e2cacd20618e":"deleteKeylessAction","b3c000e2e8fc2ff57492e20cd6b82bd9b2957bb2":"syncKeylessConfigAction","bf1663972672a8cd7891516db68d6e6020dcc70e":"createOrReadKeylessAction"} */ var syncKeylessConfigAction = (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_1__.createServerReference)("b3c000e2e8fc2ff57492e20cd6b82bd9b2957bb2");

var createOrReadKeylessAction = (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_1__.createServerReference)("bf1663972672a8cd7891516db68d6e6020dcc70e");
var deleteKeylessAction = (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_1__.createServerReference)("0a99371a463eea10cd136fa6e633e2cacd20618e");



/***/ })

}]);