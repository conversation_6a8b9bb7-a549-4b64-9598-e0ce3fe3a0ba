"use client";

import { useSignUp } from "@clerk/nextjs";
import { motion } from 'framer-motion';
import { useRouter, useSearchParams } from "next/navigation";
import { useState, useEffect } from "react";
import { FiMail, FiCheck, FiAlertCircle, FiRefreshCw } from 'react-icons/fi';
import { toast } from "sonner";

export default function VerifyEmail() {
    const [code, setCode] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [isResending, setIsResending] = useState(false);
    const [timeLeft, setTimeLeft] = useState(60);
    const [canResend, setCanResend] = useState(false);

    const router = useRouter();
    const searchParams = useSearchParams();
    const email = searchParams?.get("email") || "";
    const fullName = searchParams?.get("fullName") || "";
    const { signUp, setActive } = useSignUp();

    // Countdown timer for resend functionality
    useEffect(() => {
        if (!canResend && timeLeft > 0) {
            const timer = setTimeout(() => {
                setTimeLeft(timeLeft - 1);
            }, 1000);

            return () => clearTimeout(timer);
        } else if (timeLeft === 0) {
            setCanResend(true);
        }
    }, [timeLeft, canResend]);

    // Handle form submission to verify the code using Clerk
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!signUp) {
            toast.error("Sign up session not found. Please try registering again.");
            router.push("/register");
            return;
        }

        setIsLoading(true);

        try {
            // Attempt verification with Clerk using the code
            const completeSignUp = await signUp.attemptEmailAddressVerification({
                code
            });

            // Check the status of the verification
            if (completeSignUp.status === "complete") {
                // Set the session as active if verification was successful
                await setActive({ session: completeSignUp.createdSessionId });

                // Save user data to MongoDB only after successful verification
                if (fullName) {
                    try {
                        // Create user in your database with their name after successful verification
                        const response = await fetch("/api/user/create", {
                            method: "POST",
                            headers: { "Content-Type": "application/json" },
                            body: JSON.stringify({ fullName }),
                        });

                        if (!response.ok) {
                            console.error("Failed to save user data:", await response.text());
                        }
                    } catch (dbError) {
                        console.error("Database error:", dbError);
                        // Continue even if DB save fails - user is still authenticated
                    }
                }

                toast.success("Email verified successfully! Redirecting to dashboard...");
                router.push("/dashboard");
            } else {
                // Handle incomplete verification
                console.log("Verification status:", completeSignUp);
                toast.error("Verification incomplete. Please try again.");
            }
        } catch (error: any) {
            console.error("Verification error:", error);
            toast.error(error?.errors?.[0]?.message || "Failed to verify email. Please check the code and try again.");
        } finally {
            setIsLoading(false);
        }
    };

    // Handle resend verification code button click using Clerk
    const handleResendCode = async () => {
        if (!canResend) return;

        if (!signUp) {
            toast.error("Sign up session not found. Please try registering again.");
            router.push("/register");
            return;
        }

        setIsResending(true);

        try {
            // Resend the verification email through Clerk
            await signUp.prepareEmailAddressVerification({
                strategy: "email_code"
            });

            toast.success("Verification code resent successfully. Please check your email.");
            setTimeLeft(60);
            setCanResend(false);
        } catch (error: any) {
            console.error("Resend error:", error);
            toast.error(error?.errors?.[0]?.message || "Failed to resend verification code.");
        } finally {
            setIsResending(false);
        }
    };

    // Split the input into individual characters for cleaner UI
    const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.replace(/[^0-9A-Za-z]/g, '').toUpperCase();
        if (value.length <= 8) {
            setCode(value);
        }
    };

    return (
        <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-900 px-4 text-white">
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="relative w-full max-w-md overflow-hidden"
            >
                {/* Abstract design elements */}
                <div className="absolute right-0 top-0 -z-10 size-64 -translate-y-1/2 translate-x-1/2 rounded-full bg-blue-500/10 blur-3xl"></div>
                <div className="bg-purple-500/10 absolute bottom-0 left-0 -z-10 size-64 -translate-x-1/2 translate-y-1/2 rounded-full blur-3xl"></div>

                <div className="overflow-hidden rounded-2xl border border-gray-800 bg-black/60 shadow-2xl backdrop-blur-sm">
                    <div className="p-8">
                        <div className="mb-8 text-center">
                            <div className="mx-auto mb-4 flex size-16 items-center justify-center rounded-full bg-blue-500/20">
                                <FiMail className="text-2xl text-blue-400" />
                            </div>
                            <h2 className="mb-2 text-2xl font-bold text-white">Verify Your Email</h2>
                            <p className="text-sm text-gray-400">
                                We&apos;ve sent a verification code to <span className="font-medium text-white">{email}</span>
                            </p>
                        </div>

                        <form onSubmit={handleSubmit} className="space-y-5">
                            <div className="space-y-2">
                                <label className="text-sm text-gray-400">Verification Code</label>
                                <div className="relative">
                                    <div className="flex flex-1 justify-center">
                                        <input
                                            type="text"
                                            value={code}
                                            onChange={handleCodeChange}
                                            className="w-full rounded-lg border border-gray-700 bg-gray-800/80 px-4 py-3 text-center font-mono text-xl tracking-[0.3em] text-white focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                                            placeholder="ENTER CODE"
                                            maxLength={8}
                                            autoFocus
                                            required
                                        />
                                    </div>
                                </div>
                            </div>

                            <button
                                type="submit"
                                disabled={isLoading || code.length < 6}
                                className="flex w-full items-center justify-center gap-2 rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 px-4 py-3 font-medium text-white transition duration-200 hover:-translate-y-px hover:from-blue-700 hover:to-indigo-700 disabled:transform-none disabled:cursor-not-allowed disabled:opacity-60"
                            >
                                {isLoading ? (
                                    <>
                                        <svg className="-ml-1 mr-2 size-4 animate-spin text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Verifying...
                                    </>
                                ) : (
                                    <>
                                        Verify Email <FiCheck className="ml-1" />
                                    </>
                                )}
                            </button>

                            <div className="rounded-lg border border-gray-700/50 bg-gray-800/40 p-4">
                                <p className="flex items-start text-sm text-gray-300">
                                    <FiAlertCircle className="mr-2 mt-0.5 shrink-0 text-amber-400" />
                                    <span>If you don&apos;t see the email in your inbox, please check your spam or junk folder. It may take a few minutes to arrive.</span>
                                </p>
                            </div>
                        </form>

                        <div className="mt-6 border-t border-gray-800 pt-4 text-center">
                            <p className="mb-3 text-sm text-gray-400">
                                Didn&apos;t receive the code?
                            </p>
                            <button
                                onClick={handleResendCode}
                                disabled={isResending || !canResend}
                                className={`mx-auto flex items-center justify-center gap-2 rounded-full px-4 py-2 ${canResend
                                    ? "text-blue-400 hover:bg-blue-500/10 hover:text-blue-300"
                                    : "cursor-not-allowed text-gray-500"
                                    } border transition-all duration-200 ${canResend ? "border-blue-500/30" : "border-gray-700"
                                    }`}
                            >
                                {isResending ? (
                                    <>
                                        <svg className="size-4 animate-spin text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Sending...
                                    </>
                                ) : canResend ? (
                                    <>
                                        <FiRefreshCw className="size-4" />
                                        Resend Code
                                    </>
                                ) : (
                                    <>
                                        <FiRefreshCw className="size-4" />
                                        Resend in {timeLeft}s
                                    </>
                                )}
                            </button>
                        </div>

                        <div className="mt-6 text-center">
                            <button
                                onClick={() => router.push('/register')}
                                className="mx-auto flex items-center justify-center text-sm text-gray-400 hover:text-gray-300"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" className="mr-1 size-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                </svg>
                                Back to Registration
                            </button>
                        </div>
                    </div>
                </div>

                <p className="mt-6 text-center text-xs text-gray-500">
                    © {new Date().getFullYear()} Interview Cracker. All rights reserved.
                </p>
            </motion.div>
        </div>
    );
}