// components/UndetectableBadge.tsx
"use client";

import React from "react";

const UndetectableBadge: React.FC = () => {
    // Get the current date
    const today = new Date();
    const month = String(today.getMonth() + 1).padStart(2, "0"); // Months are 0-based, so +1
    const day = String(today.getDate()).padStart(2, "0");
    const year = String(today.getFullYear()).slice(-2); // Last two digits of the year
    const formattedDate = `${month}/${day}/${year}`; // Format as MM/DD/YY

    return (
        <div className="flex justify-center items-center py-1">
            <div className="relative inline-block px-6 py-2 bg-gray-900 text-white text-sm font-semibold rounded-full border-2 border-transparent">
                <span>Undetectable as of {formattedDate}</span>
                {/* Animated outline */}
                <div className="absolute inset-0 rounded-full border-2 border-red-500 animate-pulse-outline"></div>
            </div>
        </div>
    );
};

export default UndetectableBadge;