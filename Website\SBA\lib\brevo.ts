import SibApiV3Sdk from "sib-api-v3-sdk";

// Configure the API client with your API key
const defaultClient = SibApiV3Sdk.ApiClient.instance;
const apiKey = defaultClient.authentications["api-key"];

// More robust error handling for missing environment variables
if (!process.env.BREVO_API_KEY) {
  console.error("=================================================================");
  console.error("ERROR: BREVO_API_KEY is not set in environment variables");
  console.error("This will cause all email sending functions to fail");
  console.error("Please set this in your .env or environment configuration");
  console.error("=================================================================");
  
  // In development, we'll throw an error to make it obvious
  if (process.env.NODE_ENV === "development") {
    throw new Error("BREVO_API_KEY is not set in environment variables");
  }
}

// Set the API key if available
if (process.env.BREVO_API_KEY) {
  apiKey.apiKey = process.env.BREVO_API_KEY;
}

// Export the transactional emails API instance
export const transactionalEmailsApi = new SibApiV3Sdk.TransactionalEmailsApi();

// Helper function to check if Brevo is properly configured
export const isBrevoConfigured = () => {
  return !!process.env.BREVO_API_KEY;
};