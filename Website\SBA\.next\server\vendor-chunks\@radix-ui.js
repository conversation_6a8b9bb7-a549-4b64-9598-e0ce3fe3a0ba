"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSx1RUFBdUUsa0NBQWtDLElBQUk7QUFDN0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50ZXJ2aWV3Y3JhY2tlci8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcHJpbWl0aXZlL2Rpc3QvaW5kZXgubWpzP2FhM2UiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvY29yZS9wcmltaXRpdmUvc3JjL3ByaW1pdGl2ZS50c3hcbmZ1bmN0aW9uIGNvbXBvc2VFdmVudEhhbmRsZXJzKG9yaWdpbmFsRXZlbnRIYW5kbGVyLCBvdXJFdmVudEhhbmRsZXIsIHsgY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkID0gdHJ1ZSB9ID0ge30pIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIGhhbmRsZUV2ZW50KGV2ZW50KSB7XG4gICAgb3JpZ2luYWxFdmVudEhhbmRsZXI/LihldmVudCk7XG4gICAgaWYgKGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9PT0gZmFsc2UgfHwgIWV2ZW50LmRlZmF1bHRQcmV2ZW50ZWQpIHtcbiAgICAgIHJldHVybiBvdXJFdmVudEhhbmRsZXI/LihldmVudCk7XG4gICAgfVxuICB9O1xufVxuZXhwb3J0IHtcbiAgY29tcG9zZUV2ZW50SGFuZGxlcnNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-alert-dialog/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-alert-dialog/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Action: () => (/* binding */ Action),\n/* harmony export */   AlertDialog: () => (/* binding */ AlertDialog),\n/* harmony export */   AlertDialogAction: () => (/* binding */ AlertDialogAction),\n/* harmony export */   AlertDialogCancel: () => (/* binding */ AlertDialogCancel),\n/* harmony export */   AlertDialogContent: () => (/* binding */ AlertDialogContent),\n/* harmony export */   AlertDialogDescription: () => (/* binding */ AlertDialogDescription),\n/* harmony export */   AlertDialogOverlay: () => (/* binding */ AlertDialogOverlay),\n/* harmony export */   AlertDialogPortal: () => (/* binding */ AlertDialogPortal),\n/* harmony export */   AlertDialogTitle: () => (/* binding */ AlertDialogTitle),\n/* harmony export */   AlertDialogTrigger: () => (/* binding */ AlertDialogTrigger),\n/* harmony export */   Cancel: () => (/* binding */ Cancel),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Description: () => (/* binding */ Description2),\n/* harmony export */   Overlay: () => (/* binding */ Overlay2),\n/* harmony export */   Portal: () => (/* binding */ Portal2),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Title: () => (/* binding */ Title2),\n/* harmony export */   Trigger: () => (/* binding */ Trigger2),\n/* harmony export */   createAlertDialogScope: () => (/* binding */ createAlertDialogScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Action,AlertDialog,AlertDialogAction,AlertDialogCancel,AlertDialogContent,AlertDialogDescription,AlertDialogOverlay,AlertDialogPortal,AlertDialogTitle,AlertDialogTrigger,Cancel,Content,Description,Overlay,Portal,Root,Title,Trigger,createAlertDialogScope auto */ // packages/react/alert-dialog/src/AlertDialog.tsx\n\n\n\n\n\n\n\n\nvar ROOT_NAME = \"AlertDialog\";\nvar [createAlertDialogContext, createAlertDialogScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(ROOT_NAME, [\n    _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.createDialogScope\n]);\nvar useDialogScope = (0,_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.createDialogScope)();\nvar AlertDialog = (props)=>{\n    const { __scopeAlertDialog, ...alertDialogProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ...dialogScope,\n        ...alertDialogProps,\n        modal: true\n    });\n};\nAlertDialog.displayName = ROOT_NAME;\nvar TRIGGER_NAME = \"AlertDialogTrigger\";\nvar AlertDialogTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAlertDialog, ...triggerProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        ...dialogScope,\n        ...triggerProps,\n        ref: forwardedRef\n    });\n});\nAlertDialogTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"AlertDialogPortal\";\nvar AlertDialogPortal = (props)=>{\n    const { __scopeAlertDialog, ...portalProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        ...dialogScope,\n        ...portalProps\n    });\n};\nAlertDialogPortal.displayName = PORTAL_NAME;\nvar OVERLAY_NAME = \"AlertDialogOverlay\";\nvar AlertDialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAlertDialog, ...overlayProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay, {\n        ...dialogScope,\n        ...overlayProps,\n        ref: forwardedRef\n    });\n});\nAlertDialogOverlay.displayName = OVERLAY_NAME;\nvar CONTENT_NAME = \"AlertDialogContent\";\nvar [AlertDialogContentProvider, useAlertDialogContentContext] = createAlertDialogContext(CONTENT_NAME);\nvar AlertDialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAlertDialog, children, ...contentProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, contentRef);\n    const cancelRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.WarningProvider, {\n        contentName: CONTENT_NAME,\n        titleName: TITLE_NAME,\n        docsSlug: \"alert-dialog\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AlertDialogContentProvider, {\n            scope: __scopeAlertDialog,\n            cancelRef,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                role: \"alertdialog\",\n                ...dialogScope,\n                ...contentProps,\n                ref: composedRefs,\n                onOpenAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(contentProps.onOpenAutoFocus, (event)=>{\n                    event.preventDefault();\n                    cancelRef.current?.focus({\n                        preventScroll: true\n                    });\n                }),\n                onPointerDownOutside: (event)=>event.preventDefault(),\n                onInteractOutside: (event)=>event.preventDefault(),\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_6__.Slottable, {\n                        children\n                    }),\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DescriptionWarning, {\n                        contentRef\n                    })\n                ]\n            })\n        })\n    });\n});\nAlertDialogContent.displayName = CONTENT_NAME;\nvar TITLE_NAME = \"AlertDialogTitle\";\nvar AlertDialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAlertDialog, ...titleProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title, {\n        ...dialogScope,\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nAlertDialogTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"AlertDialogDescription\";\nvar AlertDialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAlertDialog, ...descriptionProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description, {\n        ...dialogScope,\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nAlertDialogDescription.displayName = DESCRIPTION_NAME;\nvar ACTION_NAME = \"AlertDialogAction\";\nvar AlertDialogAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAlertDialog, ...actionProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close, {\n        ...dialogScope,\n        ...actionProps,\n        ref: forwardedRef\n    });\n});\nAlertDialogAction.displayName = ACTION_NAME;\nvar CANCEL_NAME = \"AlertDialogCancel\";\nvar AlertDialogCancel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAlertDialog, ...cancelProps } = props;\n    const { cancelRef } = useAlertDialogContentContext(CANCEL_NAME, __scopeAlertDialog);\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, cancelRef);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close, {\n        ...dialogScope,\n        ...cancelProps,\n        ref\n    });\n});\nAlertDialogCancel.displayName = CANCEL_NAME;\nvar DescriptionWarning = ({ contentRef })=>{\n    const MESSAGE = `\\`${CONTENT_NAME}\\` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the \\`${CONTENT_NAME}\\` by passing a \\`${DESCRIPTION_NAME}\\` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an \\`id\\` and passing the same value to the \\`aria-describedby\\` prop in \\`${CONTENT_NAME}\\`. If the description is confusing or duplicative for sighted users, you can use the \\`@radix-ui/react-visually-hidden\\` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const hasDescription = document.getElementById(contentRef.current?.getAttribute(\"aria-describedby\"));\n        if (!hasDescription) console.warn(MESSAGE);\n    }, [\n        MESSAGE,\n        contentRef\n    ]);\n    return null;\n};\nvar Root2 = AlertDialog;\nvar Trigger2 = AlertDialogTrigger;\nvar Portal2 = AlertDialogPortal;\nvar Overlay2 = AlertDialogOverlay;\nvar Content2 = AlertDialogContent;\nvar Action = AlertDialogAction;\nvar Cancel = AlertDialogCancel;\nvar Title2 = AlertDialogTitle;\nvar Description2 = AlertDialogDescription;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-alert-dialog/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-collection/dist/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollection: () => (/* binding */ createCollection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-collection/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ createCollection auto */ // packages/react/collection/src/Collection.tsx\n\n\n\n\n\nfunction createCollection(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionRef: {\n            current: null\n        },\n        itemMap: /* @__PURE__ */ new Map()\n    });\n    const CollectionProvider = (props)=>{\n        const { scope, children } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const itemMap = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Map()).current;\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            scope,\n            itemMap,\n            collectionRef: ref,\n            children\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            context.itemMap.set(ref, {\n                ref,\n                ...itemData\n            });\n            return ()=>void context.itemMap.delete(ref);\n        });\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useCollection(scope) {\n        const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n        const getItems = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            const collectionNode = context.collectionRef.current;\n            if (!collectionNode) return [];\n            const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n            const items = Array.from(context.itemMap.values());\n            const orderedItems = items.sort((a, b)=>orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current));\n            return orderedItems;\n        }, [\n            context.collectionRef,\n            context.itemMap\n        ]);\n        return getItems;\n    }\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        useCollection,\n        createCollectionScope\n    ];\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-collection/node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-collection/node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/createContext.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  function Provider(props) {\n    const { children, ...context } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n  }\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  Provider.displayName = rootComponentName + \"Provider\";\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    function Provider(props) {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName][index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n    }\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName][index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    Provider.displayName = rootComponentName + \"Provider\";\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWNvbGxlY3Rpb24vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1jb250ZXh0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUMrQjtBQUNTO0FBQ3hDO0FBQ0Esa0JBQWtCLGdEQUFtQjtBQUNyQztBQUNBLFlBQVksdUJBQXVCO0FBQ25DLGtCQUFrQiwwQ0FBYTtBQUMvQiwyQkFBMkIsc0RBQUcscUJBQXFCLGlCQUFpQjtBQUNwRTtBQUNBO0FBQ0Esb0JBQW9CLDZDQUFnQjtBQUNwQztBQUNBO0FBQ0EseUJBQXlCLGFBQWEsMkJBQTJCLGtCQUFrQjtBQUNuRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixnREFBbUI7QUFDM0M7QUFDQTtBQUNBO0FBQ0EsY0FBYyw4QkFBOEI7QUFDNUM7QUFDQSxvQkFBb0IsMENBQWE7QUFDakMsNkJBQTZCLHNEQUFHLHFCQUFxQixpQkFBaUI7QUFDdEU7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDZDQUFnQjtBQUN0QztBQUNBO0FBQ0EsMkJBQTJCLGFBQWEsMkJBQTJCLGtCQUFrQjtBQUNyRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGdEQUFtQjtBQUNoQyxLQUFLO0FBQ0w7QUFDQTtBQUNBLGFBQWEsMENBQWE7QUFDMUIsaUJBQWlCLFdBQVcsVUFBVSxNQUFNLG1DQUFtQztBQUMvRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsMkRBQTJELHFCQUFxQjtBQUNoRjtBQUNBLGtEQUFrRCxVQUFVO0FBQzVELGlCQUFpQjtBQUNqQixPQUFPLElBQUk7QUFDWCxhQUFhLDBDQUFhLFVBQVUsV0FBVyxvQkFBb0IsZ0JBQWdCO0FBQ25GO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFJRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50ZXJ2aWV3Y3JhY2tlci8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtY29sbGVjdGlvbi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWNvbnRleHQvZGlzdC9pbmRleC5tanM/M2RiZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC9jb250ZXh0L3NyYy9jcmVhdGVDb250ZXh0LnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmZ1bmN0aW9uIGNyZWF0ZUNvbnRleHQyKHJvb3RDb21wb25lbnROYW1lLCBkZWZhdWx0Q29udGV4dCkge1xuICBjb25zdCBDb250ZXh0ID0gUmVhY3QuY3JlYXRlQ29udGV4dChkZWZhdWx0Q29udGV4dCk7XG4gIGZ1bmN0aW9uIFByb3ZpZGVyKHByb3BzKSB7XG4gICAgY29uc3QgeyBjaGlsZHJlbiwgLi4uY29udGV4dCB9ID0gcHJvcHM7XG4gICAgY29uc3QgdmFsdWUgPSBSZWFjdC51c2VNZW1vKCgpID0+IGNvbnRleHQsIE9iamVjdC52YWx1ZXMoY29udGV4dCkpO1xuICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KENvbnRleHQuUHJvdmlkZXIsIHsgdmFsdWUsIGNoaWxkcmVuIH0pO1xuICB9XG4gIGZ1bmN0aW9uIHVzZUNvbnRleHQyKGNvbnN1bWVyTmFtZSkge1xuICAgIGNvbnN0IGNvbnRleHQgPSBSZWFjdC51c2VDb250ZXh0KENvbnRleHQpO1xuICAgIGlmIChjb250ZXh0KSByZXR1cm4gY29udGV4dDtcbiAgICBpZiAoZGVmYXVsdENvbnRleHQgIT09IHZvaWQgMCkgcmV0dXJuIGRlZmF1bHRDb250ZXh0O1xuICAgIHRocm93IG5ldyBFcnJvcihgXFxgJHtjb25zdW1lck5hbWV9XFxgIG11c3QgYmUgdXNlZCB3aXRoaW4gXFxgJHtyb290Q29tcG9uZW50TmFtZX1cXGBgKTtcbiAgfVxuICBQcm92aWRlci5kaXNwbGF5TmFtZSA9IHJvb3RDb21wb25lbnROYW1lICsgXCJQcm92aWRlclwiO1xuICByZXR1cm4gW1Byb3ZpZGVyLCB1c2VDb250ZXh0Ml07XG59XG5mdW5jdGlvbiBjcmVhdGVDb250ZXh0U2NvcGUoc2NvcGVOYW1lLCBjcmVhdGVDb250ZXh0U2NvcGVEZXBzID0gW10pIHtcbiAgbGV0IGRlZmF1bHRDb250ZXh0cyA9IFtdO1xuICBmdW5jdGlvbiBjcmVhdGVDb250ZXh0Myhyb290Q29tcG9uZW50TmFtZSwgZGVmYXVsdENvbnRleHQpIHtcbiAgICBjb25zdCBCYXNlQ29udGV4dCA9IFJlYWN0LmNyZWF0ZUNvbnRleHQoZGVmYXVsdENvbnRleHQpO1xuICAgIGNvbnN0IGluZGV4ID0gZGVmYXVsdENvbnRleHRzLmxlbmd0aDtcbiAgICBkZWZhdWx0Q29udGV4dHMgPSBbLi4uZGVmYXVsdENvbnRleHRzLCBkZWZhdWx0Q29udGV4dF07XG4gICAgZnVuY3Rpb24gUHJvdmlkZXIocHJvcHMpIHtcbiAgICAgIGNvbnN0IHsgc2NvcGUsIGNoaWxkcmVuLCAuLi5jb250ZXh0IH0gPSBwcm9wcztcbiAgICAgIGNvbnN0IENvbnRleHQgPSBzY29wZT8uW3Njb3BlTmFtZV1baW5kZXhdIHx8IEJhc2VDb250ZXh0O1xuICAgICAgY29uc3QgdmFsdWUgPSBSZWFjdC51c2VNZW1vKCgpID0+IGNvbnRleHQsIE9iamVjdC52YWx1ZXMoY29udGV4dCkpO1xuICAgICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goQ29udGV4dC5Qcm92aWRlciwgeyB2YWx1ZSwgY2hpbGRyZW4gfSk7XG4gICAgfVxuICAgIGZ1bmN0aW9uIHVzZUNvbnRleHQyKGNvbnN1bWVyTmFtZSwgc2NvcGUpIHtcbiAgICAgIGNvbnN0IENvbnRleHQgPSBzY29wZT8uW3Njb3BlTmFtZV1baW5kZXhdIHx8IEJhc2VDb250ZXh0O1xuICAgICAgY29uc3QgY29udGV4dCA9IFJlYWN0LnVzZUNvbnRleHQoQ29udGV4dCk7XG4gICAgICBpZiAoY29udGV4dCkgcmV0dXJuIGNvbnRleHQ7XG4gICAgICBpZiAoZGVmYXVsdENvbnRleHQgIT09IHZvaWQgMCkgcmV0dXJuIGRlZmF1bHRDb250ZXh0O1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBcXGAke2NvbnN1bWVyTmFtZX1cXGAgbXVzdCBiZSB1c2VkIHdpdGhpbiBcXGAke3Jvb3RDb21wb25lbnROYW1lfVxcYGApO1xuICAgIH1cbiAgICBQcm92aWRlci5kaXNwbGF5TmFtZSA9IHJvb3RDb21wb25lbnROYW1lICsgXCJQcm92aWRlclwiO1xuICAgIHJldHVybiBbUHJvdmlkZXIsIHVzZUNvbnRleHQyXTtcbiAgfVxuICBjb25zdCBjcmVhdGVTY29wZSA9ICgpID0+IHtcbiAgICBjb25zdCBzY29wZUNvbnRleHRzID0gZGVmYXVsdENvbnRleHRzLm1hcCgoZGVmYXVsdENvbnRleHQpID0+IHtcbiAgICAgIHJldHVybiBSZWFjdC5jcmVhdGVDb250ZXh0KGRlZmF1bHRDb250ZXh0KTtcbiAgICB9KTtcbiAgICByZXR1cm4gZnVuY3Rpb24gdXNlU2NvcGUoc2NvcGUpIHtcbiAgICAgIGNvbnN0IGNvbnRleHRzID0gc2NvcGU/LltzY29wZU5hbWVdIHx8IHNjb3BlQ29udGV4dHM7XG4gICAgICByZXR1cm4gUmVhY3QudXNlTWVtbyhcbiAgICAgICAgKCkgPT4gKHsgW2BfX3Njb3BlJHtzY29wZU5hbWV9YF06IHsgLi4uc2NvcGUsIFtzY29wZU5hbWVdOiBjb250ZXh0cyB9IH0pLFxuICAgICAgICBbc2NvcGUsIGNvbnRleHRzXVxuICAgICAgKTtcbiAgICB9O1xuICB9O1xuICBjcmVhdGVTY29wZS5zY29wZU5hbWUgPSBzY29wZU5hbWU7XG4gIHJldHVybiBbY3JlYXRlQ29udGV4dDMsIGNvbXBvc2VDb250ZXh0U2NvcGVzKGNyZWF0ZVNjb3BlLCAuLi5jcmVhdGVDb250ZXh0U2NvcGVEZXBzKV07XG59XG5mdW5jdGlvbiBjb21wb3NlQ29udGV4dFNjb3BlcyguLi5zY29wZXMpIHtcbiAgY29uc3QgYmFzZVNjb3BlID0gc2NvcGVzWzBdO1xuICBpZiAoc2NvcGVzLmxlbmd0aCA9PT0gMSkgcmV0dXJuIGJhc2VTY29wZTtcbiAgY29uc3QgY3JlYXRlU2NvcGUgPSAoKSA9PiB7XG4gICAgY29uc3Qgc2NvcGVIb29rcyA9IHNjb3Blcy5tYXAoKGNyZWF0ZVNjb3BlMikgPT4gKHtcbiAgICAgIHVzZVNjb3BlOiBjcmVhdGVTY29wZTIoKSxcbiAgICAgIHNjb3BlTmFtZTogY3JlYXRlU2NvcGUyLnNjb3BlTmFtZVxuICAgIH0pKTtcbiAgICByZXR1cm4gZnVuY3Rpb24gdXNlQ29tcG9zZWRTY29wZXMob3ZlcnJpZGVTY29wZXMpIHtcbiAgICAgIGNvbnN0IG5leHRTY29wZXMgPSBzY29wZUhvb2tzLnJlZHVjZSgobmV4dFNjb3BlczIsIHsgdXNlU2NvcGUsIHNjb3BlTmFtZSB9KSA9PiB7XG4gICAgICAgIGNvbnN0IHNjb3BlUHJvcHMgPSB1c2VTY29wZShvdmVycmlkZVNjb3Blcyk7XG4gICAgICAgIGNvbnN0IGN1cnJlbnRTY29wZSA9IHNjb3BlUHJvcHNbYF9fc2NvcGUke3Njb3BlTmFtZX1gXTtcbiAgICAgICAgcmV0dXJuIHsgLi4ubmV4dFNjb3BlczIsIC4uLmN1cnJlbnRTY29wZSB9O1xuICAgICAgfSwge30pO1xuICAgICAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4gKHsgW2BfX3Njb3BlJHtiYXNlU2NvcGUuc2NvcGVOYW1lfWBdOiBuZXh0U2NvcGVzIH0pLCBbbmV4dFNjb3Blc10pO1xuICAgIH07XG4gIH07XG4gIGNyZWF0ZVNjb3BlLnNjb3BlTmFtZSA9IGJhc2VTY29wZS5zY29wZU5hbWU7XG4gIHJldHVybiBjcmVhdGVTY29wZTtcbn1cbmV4cG9ydCB7XG4gIGNyZWF0ZUNvbnRleHQyIGFzIGNyZWF0ZUNvbnRleHQsXG4gIGNyZWF0ZUNvbnRleHRTY29wZVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-collection/node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/composeRefs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => refs.forEach((ref) => setRef(ref, node));\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmcy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLDhDQUFpQjtBQUMxQjtBQUlFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRlcnZpZXdjcmFja2VyLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1jb21wb3NlLXJlZnMvZGlzdC9pbmRleC5tanM/MWQxNCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC9jb21wb3NlLXJlZnMvc3JjL2NvbXBvc2VSZWZzLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiBzZXRSZWYocmVmLCB2YWx1ZSkge1xuICBpZiAodHlwZW9mIHJlZiA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgcmVmKHZhbHVlKTtcbiAgfSBlbHNlIGlmIChyZWYgIT09IG51bGwgJiYgcmVmICE9PSB2b2lkIDApIHtcbiAgICByZWYuY3VycmVudCA9IHZhbHVlO1xuICB9XG59XG5mdW5jdGlvbiBjb21wb3NlUmVmcyguLi5yZWZzKSB7XG4gIHJldHVybiAobm9kZSkgPT4gcmVmcy5mb3JFYWNoKChyZWYpID0+IHNldFJlZihyZWYsIG5vZGUpKTtcbn1cbmZ1bmN0aW9uIHVzZUNvbXBvc2VkUmVmcyguLi5yZWZzKSB7XG4gIHJldHVybiBSZWFjdC51c2VDYWxsYmFjayhjb21wb3NlUmVmcyguLi5yZWZzKSwgcmVmcyk7XG59XG5leHBvcnQge1xuICBjb21wb3NlUmVmcyxcbiAgdXNlQ29tcG9zZWRSZWZzXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/createContext.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dialog/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger),\n/* harmony export */   Overlay: () => (/* binding */ Overlay),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   WarningProvider: () => (/* binding */ WarningProvider),\n/* harmony export */   createDialogScope: () => (/* binding */ createDialogScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Close,Content,Description,Dialog,DialogClose,DialogContent,DialogDescription,DialogOverlay,DialogPortal,DialogTitle,DialogTrigger,Overlay,Portal,Root,Title,Trigger,WarningProvider,createDialogScope auto */ // packages/react/dialog/src/Dialog.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar DIALOG_NAME = \"Dialog\";\nvar [createDialogContext, createDialogScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(DIALOG_NAME);\nvar [DialogProvider, useDialogContext] = createDialogContext(DIALOG_NAME);\nvar Dialog = (props)=>{\n    const { __scopeDialog, children, open: openProp, defaultOpen, onOpenChange, modal = true } = props;\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogProvider, {\n        scope: __scopeDialog,\n        triggerRef,\n        contentRef,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        titleId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        descriptionId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        open,\n        onOpenChange: setOpen,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setOpen((prevOpen)=>!prevOpen), [\n            setOpen\n        ]),\n        modal,\n        children\n    });\n};\nDialog.displayName = DIALOG_NAME;\nvar TRIGGER_NAME = \"DialogTrigger\";\nvar DialogTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.triggerRef);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n});\nDialogTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DialogPortal\";\nvar [PortalProvider, usePortalContext] = createDialogContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar DialogPortal = (props)=>{\n    const { __scopeDialog, forceMount, children, container } = props;\n    const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeDialog,\n        forceMount,\n        children: react__WEBPACK_IMPORTED_MODULE_0__.Children.map(children, (child)=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n                present: forceMount || context.open,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__.Portal, {\n                    asChild: true,\n                    container,\n                    children: child\n                })\n            }))\n    });\n};\nDialogPortal.displayName = PORTAL_NAME;\nvar OVERLAY_NAME = \"DialogOverlay\";\nvar DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogOverlayImpl, {\n            ...overlayProps,\n            ref: forwardedRef\n        })\n    }) : null;\n});\nDialogOverlay.displayName = OVERLAY_NAME;\nvar DialogOverlayImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return(// Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n    // ie. when `Overlay` and `Content` are siblings\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        as: _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot,\n        allowPinchZoom: true,\n        shards: [\n            context.contentRef\n        ],\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.div, {\n            \"data-state\": getState(context.open),\n            ...overlayProps,\n            ref: forwardedRef,\n            style: {\n                pointerEvents: \"auto\",\n                ...overlayProps.style\n            }\n        })\n    }));\n});\nvar CONTENT_NAME = \"DialogContent\";\nvar DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentModal, {\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentNonModal, {\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nDialogContent.displayName = CONTENT_NAME;\nvar DialogContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.contentRef, contentRef);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const content = contentRef.current;\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_12__.hideOthers)(content);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: true,\n        onCloseAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onCloseAutoFocus, (event)=>{\n            event.preventDefault();\n            context.triggerRef.current?.focus();\n        }),\n        onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n            const originalEvent = event.detail.originalEvent;\n            const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n            const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n            if (isRightClick) event.preventDefault();\n        }),\n        onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault())\n    });\n});\nvar DialogContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerDownOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event)=>{\n            props.onCloseAutoFocus?.(event);\n            if (!event.defaultPrevented) {\n                if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n                event.preventDefault();\n            }\n            hasInteractedOutsideRef.current = false;\n            hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event)=>{\n            props.onInteractOutside?.(event);\n            if (!event.defaultPrevented) {\n                hasInteractedOutsideRef.current = true;\n                if (event.detail.originalEvent.type === \"pointerdown\") {\n                    hasPointerDownOutsideRef.current = true;\n                }\n            }\n            const target = event.target;\n            const targetIsTrigger = context.triggerRef.current?.contains(target);\n            if (targetIsTrigger) event.preventDefault();\n            if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n                event.preventDefault();\n            }\n        }\n    });\n});\nvar DialogContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, contentRef);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__.useFocusGuards)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__.FocusScope, {\n                asChild: true,\n                loop: true,\n                trapped: trapFocus,\n                onMountAutoFocus: onOpenAutoFocus,\n                onUnmountAutoFocus: onCloseAutoFocus,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__.DismissableLayer, {\n                    role: \"dialog\",\n                    id: context.contentId,\n                    \"aria-describedby\": context.descriptionId,\n                    \"aria-labelledby\": context.titleId,\n                    \"data-state\": getState(context.open),\n                    ...contentProps,\n                    ref: composedRefs,\n                    onDismiss: ()=>context.onOpenChange(false)\n                })\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TitleWarning, {\n                        titleId: context.titleId\n                    }),\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DescriptionWarning, {\n                        contentRef,\n                        descriptionId: context.descriptionId\n                    })\n                ]\n            })\n        ]\n    });\n});\nvar TITLE_NAME = \"DialogTitle\";\nvar DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.h2, {\n        id: context.titleId,\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nDialogTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"DialogDescription\";\nvar DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.p, {\n        id: context.descriptionId,\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nDialogDescription.displayName = DESCRIPTION_NAME;\nvar CLOSE_NAME = \"DialogClose\";\nvar DialogClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, ()=>context.onOpenChange(false))\n    });\n});\nDialogClose.displayName = CLOSE_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar TITLE_WARNING_NAME = \"DialogTitleWarning\";\nvar [WarningProvider, useWarningContext] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContext)(TITLE_WARNING_NAME, {\n    contentName: CONTENT_NAME,\n    titleName: TITLE_NAME,\n    docsSlug: \"dialog\"\n});\nvar TitleWarning = ({ titleId })=>{\n    const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n    const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (titleId) {\n            const hasTitle = document.getElementById(titleId);\n            if (!hasTitle) console.error(MESSAGE);\n        }\n    }, [\n        MESSAGE,\n        titleId\n    ]);\n    return null;\n};\nvar DESCRIPTION_WARNING_NAME = \"DialogDescriptionWarning\";\nvar DescriptionWarning = ({ contentRef, descriptionId })=>{\n    const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n    const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const describedById = contentRef.current?.getAttribute(\"aria-describedby\");\n        if (descriptionId && describedById) {\n            const hasDescription = document.getElementById(descriptionId);\n            if (!hasDescription) console.warn(MESSAGE);\n        }\n    }, [\n        MESSAGE,\n        contentRef,\n        descriptionId\n    ]);\n    return null;\n};\nvar Root = Dialog;\nvar Trigger = DialogTrigger;\nvar Portal = DialogPortal;\nvar Overlay = DialogOverlay;\nvar Content = DialogContent;\nvar Title = DialogTitle;\nvar Description = DialogDescription;\nvar Close = DialogClose;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-direction/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectionProvider: () => (/* binding */ DirectionProvider),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   useDirection: () => (/* binding */ useDirection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/direction/src/Direction.tsx\n\n\nvar DirectionContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQytCO0FBQ1M7QUFDeEMsdUJBQXVCLGdEQUFtQjtBQUMxQztBQUNBLFVBQVUsZ0JBQWdCO0FBQzFCLHlCQUF5QixzREFBRyw4QkFBOEIsc0JBQXNCO0FBQ2hGO0FBQ0E7QUFDQSxvQkFBb0IsNkNBQWdCO0FBQ3BDO0FBQ0E7QUFDQTtBQUtFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRlcnZpZXdjcmFja2VyLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1kaXJlY3Rpb24vZGlzdC9pbmRleC5tanM/NGM0NiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC9kaXJlY3Rpb24vc3JjL0RpcmVjdGlvbi50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG52YXIgRGlyZWN0aW9uQ29udGV4dCA9IFJlYWN0LmNyZWF0ZUNvbnRleHQodm9pZCAwKTtcbnZhciBEaXJlY3Rpb25Qcm92aWRlciA9IChwcm9wcykgPT4ge1xuICBjb25zdCB7IGRpciwgY2hpbGRyZW4gfSA9IHByb3BzO1xuICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChEaXJlY3Rpb25Db250ZXh0LlByb3ZpZGVyLCB7IHZhbHVlOiBkaXIsIGNoaWxkcmVuIH0pO1xufTtcbmZ1bmN0aW9uIHVzZURpcmVjdGlvbihsb2NhbERpcikge1xuICBjb25zdCBnbG9iYWxEaXIgPSBSZWFjdC51c2VDb250ZXh0KERpcmVjdGlvbkNvbnRleHQpO1xuICByZXR1cm4gbG9jYWxEaXIgfHwgZ2xvYmFsRGlyIHx8IFwibHRyXCI7XG59XG52YXIgUHJvdmlkZXIgPSBEaXJlY3Rpb25Qcm92aWRlcjtcbmV4cG9ydCB7XG4gIERpcmVjdGlvblByb3ZpZGVyLFxuICBQcm92aWRlcixcbiAgdXNlRGlyZWN0aW9uXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // packages/react/dismissable-layer/src/DismissableLayer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event)=>{\n        const target = event.target;\n        const isPointerDownOnBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n        onPointerDownOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event)=>{\n        const target = event.target;\n        const isFocusInBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (isFocusInBranch) return;\n        onFocusOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)((event)=>{\n        const isHighestLayer = index === context.layers.size - 1;\n        if (!isHighestLayer) return;\n        onEscapeKeyDown?.(event);\n        if (!event.defaultPrevented && onDismiss) {\n            event.preventDefault();\n            onDismiss();\n        }\n    }, ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!node) return;\n        if (disableOutsidePointerEvents) {\n            if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                ownerDocument.body.style.pointerEvents = \"none\";\n            }\n            context.layersWithOutsidePointerEventsDisabled.add(node);\n        }\n        context.layers.add(node);\n        dispatchUpdate();\n        return ()=>{\n            if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n            }\n        };\n    }, [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            if (!node) return;\n            context.layers.delete(node);\n            context.layersWithOutsidePointerEventsDisabled.delete(node);\n            dispatchUpdate();\n        };\n    }, [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleUpdate = ()=>force({});\n        document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n        return ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const node = ref.current;\n        if (node) {\n            context.branches.add(node);\n            return ()=>{\n                context.branches.delete(node);\n            };\n        }\n    }, [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{});\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handlePointerDown = (event)=>{\n            if (event.target && !isPointerInsideReactTreeRef.current) {\n                let handleAndDispatchPointerDownOutsideEvent2 = function() {\n                    handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                        discrete: true\n                    });\n                };\n                var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                const eventDetail = {\n                    originalEvent: event\n                };\n                if (event.pointerType === \"touch\") {\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                    ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                        once: true\n                    });\n                } else {\n                    handleAndDispatchPointerDownOutsideEvent2();\n                }\n            } else {\n                ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n            }\n            isPointerInsideReactTreeRef.current = false;\n        };\n        const timerId = window.setTimeout(()=>{\n            ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n        }, 0);\n        return ()=>{\n            window.clearTimeout(timerId);\n            ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n        };\n    }, [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleFocus = (event)=>{\n            if (event.target && !isFocusInsideReactTreeRef.current) {\n                const eventDetail = {\n                    originalEvent: event\n                };\n                handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                    discrete: false\n                });\n            }\n        };\n        ownerDocument.addEventListener(\"focusin\", handleFocus);\n        return ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus);\n    }, [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpc21pc3NhYmxlLWxheWVyL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUF1QjtBQUNjO0FBQ2tCO0FBQ3ZCO0FBQ0Q7QUFDRTtBQXFKM0I7QUEvSU4sSUFBTVEseUJBQXlCO0FBQy9CLElBQU1DLGlCQUFpQjtBQUN2QixJQUFNQyx1QkFBdUI7QUFDN0IsSUFBTUMsZ0JBQWdCO0FBRXRCLElBQUlDO0FBRUosSUFBTUMsd0NBQWdDYixnREFBQSxDQUFjO0lBQ2xEZSxRQUFRLG9CQUFJQztJQUNaQyx3Q0FBd0Msb0JBQUlEO0lBQzVDRSxVQUFVLG9CQUFJRjtBQUNoQjtBQXNDQSxJQUFNRyxpQ0FBeUJuQiw2Q0FBQSxDQUM3QixDQUFDcUIsT0FBT0M7SUFDTixNQUFNLEVBQ0pDLDhCQUE4QixPQUM5QkMsZUFBQSxFQUNBQyxvQkFBQSxFQUNBQyxjQUFBLEVBQ0FDLGlCQUFBLEVBQ0FDLFNBQUEsRUFDQSxHQUFHQyxZQUNMLEdBQUlSO0lBQ0osTUFBTVMsVUFBZ0I5Qiw2Q0FBQSxDQUFXYTtJQUNqQyxNQUFNLENBQUNtQixNQUFNQyxRQUFPLEdBQVVqQywyQ0FBQSxDQUF5QztJQUN2RSxNQUFNbUMsZ0JBQWdCSCxNQUFNRyxpQkFBaUJDLFlBQVlDO0lBQ3pELE1BQU0sR0FBR0MsTUFBSyxHQUFVdEMsMkNBQUEsQ0FBUyxDQUFDO0lBQ2xDLE1BQU11QyxlQUFlbkMsNkVBQWVBLENBQUNrQixjQUFjLENBQUNVLFFBQVNDLFFBQVFEO0lBQ3JFLE1BQU1qQixTQUFTeUIsTUFBTUMsSUFBQSxDQUFLWCxRQUFRZixNQUFNO0lBQ3hDLE1BQU0sQ0FBQzJCLDZDQUE0QyxHQUFJO1dBQUlaLFFBQVFiLHNDQUFzQztLQUFBLENBQUUwQixLQUFBLENBQU07SUFDakgsTUFBTUMsb0RBQW9EN0IsT0FBTzhCLE9BQUEsQ0FBUUg7SUFDekUsTUFBTUksUUFBUWQsT0FBT2pCLE9BQU84QixPQUFBLENBQVFiLFFBQVE7SUFDNUMsTUFBTWUsOEJBQThCakIsUUFBUWIsc0NBQUEsQ0FBdUMrQixJQUFBLEdBQU87SUFDMUYsTUFBTUMseUJBQXlCSCxTQUFTRjtJQUV4QyxNQUFNTSxxQkFBcUJDLHNCQUFzQixDQUFDQztRQUNoRCxNQUFNQyxTQUFTRCxNQUFNQyxNQUFBO1FBQ3JCLE1BQU1DLHdCQUF3QjtlQUFJeEIsUUFBUVosUUFBUTtTQUFBLENBQUVxQyxJQUFBLENBQUssQ0FBQ0MsU0FBV0EsT0FBT0MsUUFBQSxDQUFTSjtRQUNyRixJQUFJLENBQUNKLDBCQUEwQkssdUJBQXVCO1FBQ3REN0IsdUJBQXVCMkI7UUFDdkJ6QixvQkFBb0J5QjtRQUNwQixJQUFJLENBQUNBLE1BQU1NLGdCQUFBLEVBQWtCOUI7SUFDL0IsR0FBR087SUFFSCxNQUFNd0IsZUFBZUMsZ0JBQWdCLENBQUNSO1FBQ3BDLE1BQU1DLFNBQVNELE1BQU1DLE1BQUE7UUFDckIsTUFBTVEsa0JBQWtCO2VBQUkvQixRQUFRWixRQUFRO1NBQUEsQ0FBRXFDLElBQUEsQ0FBSyxDQUFDQyxTQUFXQSxPQUFPQyxRQUFBLENBQVNKO1FBQy9FLElBQUlRLGlCQUFpQjtRQUNyQm5DLGlCQUFpQjBCO1FBQ2pCekIsb0JBQW9CeUI7UUFDcEIsSUFBSSxDQUFDQSxNQUFNTSxnQkFBQSxFQUFrQjlCO0lBQy9CLEdBQUdPO0lBRUg3QixvRkFBZ0JBLENBQUMsQ0FBQzhDO1FBQ2hCLE1BQU1VLGlCQUFpQmhCLFVBQVVoQixRQUFRZixNQUFBLENBQU9pQyxJQUFBLEdBQU87UUFDdkQsSUFBSSxDQUFDYyxnQkFBZ0I7UUFDckJ0QyxrQkFBa0I0QjtRQUNsQixJQUFJLENBQUNBLE1BQU1NLGdCQUFBLElBQW9COUIsV0FBVztZQUN4Q3dCLE1BQU1XLGNBQUE7WUFDTm5DO1FBQ0Y7SUFDRixHQUFHTztJQUVHbkMsNENBQUEsQ0FBVTtRQUNkLElBQUksQ0FBQ2dDLE1BQU07UUFDWCxJQUFJVCw2QkFBNkI7WUFDL0IsSUFBSU8sUUFBUWIsc0NBQUEsQ0FBdUMrQixJQUFBLEtBQVMsR0FBRztnQkFDN0RwQyw0QkFBNEJ1QixjQUFjOEIsSUFBQSxDQUFLQyxLQUFBLENBQU1DLGFBQUE7Z0JBQ3JEaEMsY0FBYzhCLElBQUEsQ0FBS0MsS0FBQSxDQUFNQyxhQUFBLEdBQWdCO1lBQzNDO1lBQ0FyQyxRQUFRYixzQ0FBQSxDQUF1Q21ELEdBQUEsQ0FBSXBDO1FBQ3JEO1FBQ0FGLFFBQVFmLE1BQUEsQ0FBT3FELEdBQUEsQ0FBSXBDO1FBQ25CcUM7UUFDQSxPQUFPO1lBQ0wsSUFDRTlDLCtCQUNBTyxRQUFRYixzQ0FBQSxDQUF1QytCLElBQUEsS0FBUyxHQUN4RDtnQkFDQWIsY0FBYzhCLElBQUEsQ0FBS0MsS0FBQSxDQUFNQyxhQUFBLEdBQWdCdkQ7WUFDM0M7UUFDRjtJQUNGLEdBQUc7UUFBQ29CO1FBQU1HO1FBQWVaO1FBQTZCTztLQUFRO0lBUXhEOUIsNENBQUEsQ0FBVTtRQUNkLE9BQU87WUFDTCxJQUFJLENBQUNnQyxNQUFNO1lBQ1hGLFFBQVFmLE1BQUEsQ0FBT3VELE1BQUEsQ0FBT3RDO1lBQ3RCRixRQUFRYixzQ0FBQSxDQUF1Q3FELE1BQUEsQ0FBT3RDO1lBQ3REcUM7UUFDRjtJQUNGLEdBQUc7UUFBQ3JDO1FBQU1GO0tBQVE7SUFFWjlCLDRDQUFBLENBQVU7UUFDZCxNQUFNdUUsZUFBZSxJQUFNakMsTUFBTSxDQUFDO1FBQ2xDRCxTQUFTbUMsZ0JBQUEsQ0FBaUIvRCxnQkFBZ0I4RDtRQUMxQyxPQUFPLElBQU1sQyxTQUFTb0MsbUJBQUEsQ0FBb0JoRSxnQkFBZ0I4RDtJQUM1RCxHQUFHLEVBQUU7SUFFTCxPQUNFLGdCQUFBaEUsc0RBQUFBLENBQUNMLGdFQUFTQSxDQUFDd0UsR0FBQSxFQUFWO1FBQ0UsR0FBRzdDLFVBQUE7UUFDSjhDLEtBQUtwQztRQUNMMkIsT0FBTztZQUNMQyxlQUFlcEIsOEJBQ1hFLHlCQUNFLFNBQ0EsU0FDRjtZQUNKLEdBQUc1QixNQUFNNkMsS0FBQTtRQUNYO1FBQ0FVLGdCQUFnQjNFLHlFQUFvQkEsQ0FBQ29CLE1BQU11RCxjQUFBLEVBQWdCakIsYUFBYWlCLGNBQWM7UUFDdEZDLGVBQWU1RSx5RUFBb0JBLENBQUNvQixNQUFNd0QsYUFBQSxFQUFlbEIsYUFBYWtCLGFBQWE7UUFDbkZDLHNCQUFzQjdFLHlFQUFvQkEsQ0FDeENvQixNQUFNeUQsb0JBQUEsRUFDTjVCLG1CQUFtQjRCLG9CQUFBO0lBQ3JCO0FBR047QUFHRjNELGlCQUFpQjRELFdBQUEsR0FBY3ZFO0FBTS9CLElBQU13RSxjQUFjO0FBS3BCLElBQU1DLHVDQUErQmpGLDZDQUFBLENBR25DLENBQUNxQixPQUFPQztJQUNSLE1BQU1RLFVBQWdCOUIsNkNBQUEsQ0FBV2E7SUFDakMsTUFBTThELE1BQVkzRSx5Q0FBQSxDQUFzQztJQUN4RCxNQUFNdUMsZUFBZW5DLDZFQUFlQSxDQUFDa0IsY0FBY3FEO0lBRTdDM0UsNENBQUEsQ0FBVTtRQUNkLE1BQU1nQyxPQUFPMkMsSUFBSVEsT0FBQTtRQUNqQixJQUFJbkQsTUFBTTtZQUNSRixRQUFRWixRQUFBLENBQVNrRCxHQUFBLENBQUlwQztZQUNyQixPQUFPO2dCQUNMRixRQUFRWixRQUFBLENBQVNvRCxNQUFBLENBQU90QztZQUMxQjtRQUNGO0lBQ0YsR0FBRztRQUFDRixRQUFRWixRQUFRO0tBQUM7SUFFckIsT0FBTyxnQkFBQVgsc0RBQUFBLENBQUNMLGdFQUFTQSxDQUFDd0UsR0FBQSxFQUFWO1FBQWUsR0FBR3JELEtBQUE7UUFBT3NELEtBQUtwQztJQUFBO0FBQ3hDO0FBRUEwQyx1QkFBdUJGLFdBQUEsR0FBY0M7QUFZckMsU0FBUzdCLHNCQUNQMUIsb0JBQUEsRUFDQVUsZ0JBQTBCQyxZQUFZQyxRQUFBO0lBRXRDLE1BQU0rQywyQkFBMkIvRSxnRkFBY0EsQ0FBQ29CO0lBQ2hELE1BQU00RCw4QkFBb0NyRix5Q0FBQSxDQUFPO0lBQ2pELE1BQU1zRixpQkFBdUJ0Rix5Q0FBQSxDQUFPLEtBQU87SUFFckNBLDRDQUFBLENBQVU7UUFDZCxNQUFNdUYsb0JBQW9CLENBQUNuQztZQUN6QixJQUFJQSxNQUFNQyxNQUFBLElBQVUsQ0FBQ2dDLDRCQUE0QkYsT0FBQSxFQUFTO2dCQUd4RCxJQUFTSyw0Q0FBVDtvQkFDRUMsNkJBQ0UvRSxzQkFDQTBFLDBCQUNBTSxhQUNBO3dCQUFFQyxVQUFVO29CQUFLO2dCQUVyQjtnQkFQUyxJQUFBSCwyQ0FBQUE7Z0JBRlQsTUFBTUUsY0FBYztvQkFBRUUsZUFBZXhDO2dCQUFNO2dCQXVCM0MsSUFBSUEsTUFBTXlDLFdBQUEsS0FBZ0IsU0FBUztvQkFDakMxRCxjQUFjc0MsbUJBQUEsQ0FBb0IsU0FBU2EsZUFBZUgsT0FBTztvQkFDakVHLGVBQWVILE9BQUEsR0FBVUs7b0JBQ3pCckQsY0FBY3FDLGdCQUFBLENBQWlCLFNBQVNjLGVBQWVILE9BQUEsRUFBUzt3QkFBRVcsTUFBTTtvQkFBSztnQkFDL0UsT0FBTztvQkFDTE47Z0JBQ0Y7WUFDRixPQUFPO2dCQUdMckQsY0FBY3NDLG1CQUFBLENBQW9CLFNBQVNhLGVBQWVILE9BQU87WUFDbkU7WUFDQUUsNEJBQTRCRixPQUFBLEdBQVU7UUFDeEM7UUFjQSxNQUFNWSxVQUFVQyxPQUFPQyxVQUFBLENBQVc7WUFDaEM5RCxjQUFjcUMsZ0JBQUEsQ0FBaUIsZUFBZWU7UUFDaEQsR0FBRztRQUNILE9BQU87WUFDTFMsT0FBT0UsWUFBQSxDQUFhSDtZQUNwQjVELGNBQWNzQyxtQkFBQSxDQUFvQixlQUFlYztZQUNqRHBELGNBQWNzQyxtQkFBQSxDQUFvQixTQUFTYSxlQUFlSCxPQUFPO1FBQ25FO0lBQ0YsR0FBRztRQUFDaEQ7UUFBZWlEO0tBQXlCO0lBRTVDLE9BQU87UUFBQTtRQUVMTixzQkFBc0IsSUFBT08sNEJBQTRCRixPQUFBLEdBQVU7SUFDckU7QUFDRjtBQU1BLFNBQVN2QixnQkFDUGxDLGNBQUEsRUFDQVMsZ0JBQTBCQyxZQUFZQyxRQUFBO0lBRXRDLE1BQU04RCxxQkFBcUI5RixnRkFBY0EsQ0FBQ3FCO0lBQzFDLE1BQU0wRSw0QkFBa0NwRyx5Q0FBQSxDQUFPO0lBRXpDQSw0Q0FBQSxDQUFVO1FBQ2QsTUFBTXFHLGNBQWMsQ0FBQ2pEO1lBQ25CLElBQUlBLE1BQU1DLE1BQUEsSUFBVSxDQUFDK0MsMEJBQTBCakIsT0FBQSxFQUFTO2dCQUN0RCxNQUFNTyxjQUFjO29CQUFFRSxlQUFleEM7Z0JBQU07Z0JBQzNDcUMsNkJBQTZCOUUsZUFBZXdGLG9CQUFvQlQsYUFBYTtvQkFDM0VDLFVBQVU7Z0JBQ1o7WUFDRjtRQUNGO1FBQ0F4RCxjQUFjcUMsZ0JBQUEsQ0FBaUIsV0FBVzZCO1FBQzFDLE9BQU8sSUFBTWxFLGNBQWNzQyxtQkFBQSxDQUFvQixXQUFXNEI7SUFDNUQsR0FBRztRQUFDbEU7UUFBZWdFO0tBQW1CO0lBRXRDLE9BQU87UUFDTHZCLGdCQUFnQixJQUFPd0IsMEJBQTBCakIsT0FBQSxHQUFVO1FBQzNETixlQUFlLElBQU91QiwwQkFBMEJqQixPQUFBLEdBQVU7SUFDNUQ7QUFDRjtBQUVBLFNBQVNkO0lBQ1AsTUFBTWpCLFFBQVEsSUFBSWtELFlBQVk3RjtJQUM5QjRCLFNBQVNrRSxhQUFBLENBQWNuRDtBQUN6QjtBQUVBLFNBQVNxQyw2QkFDUGUsSUFBQSxFQUNBQyxPQUFBLEVBQ0FDLE1BQUEsRUFDQSxFQUFFZixRQUFBLEVBQVM7SUFFWCxNQUFNdEMsU0FBU3FELE9BQU9kLGFBQUEsQ0FBY3ZDLE1BQUE7SUFDcEMsTUFBTUQsUUFBUSxJQUFJa0QsWUFBWUUsTUFBTTtRQUFFRyxTQUFTO1FBQU9DLFlBQVk7UUFBTUY7SUFBTztJQUMvRSxJQUFJRCxTQUFTcEQsT0FBT21CLGdCQUFBLENBQWlCZ0MsTUFBTUMsU0FBMEI7UUFBRVgsTUFBTTtJQUFLO0lBRWxGLElBQUlILFVBQVU7UUFDWnhGLHNGQUEyQkEsQ0FBQ2tELFFBQVFEO0lBQ3RDLE9BQU87UUFDTEMsT0FBT2tELGFBQUEsQ0FBY25EO0lBQ3ZCO0FBQ0Y7QUFFQSxJQUFNeUQsT0FBTzFGO0FBQ2IsSUFBTTJGLFNBQVM3QiIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGVydmlld2NyYWNrZXIvLi4vc3JjL0Rpc21pc3NhYmxlTGF5ZXIudHN4PzViZTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY29tcG9zZUV2ZW50SGFuZGxlcnMgfSBmcm9tICdAcmFkaXgtdWkvcHJpbWl0aXZlJztcbmltcG9ydCB7IFByaW1pdGl2ZSwgZGlzcGF0Y2hEaXNjcmV0ZUN1c3RvbUV2ZW50IH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZSc7XG5pbXBvcnQgeyB1c2VDb21wb3NlZFJlZnMgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtY29tcG9zZS1yZWZzJztcbmltcG9ydCB7IHVzZUNhbGxiYWNrUmVmIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYnO1xuaW1wb3J0IHsgdXNlRXNjYXBlS2V5ZG93biB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC11c2UtZXNjYXBlLWtleWRvd24nO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBEaXNtaXNzYWJsZUxheWVyXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IERJU01JU1NBQkxFX0xBWUVSX05BTUUgPSAnRGlzbWlzc2FibGVMYXllcic7XG5jb25zdCBDT05URVhUX1VQREFURSA9ICdkaXNtaXNzYWJsZUxheWVyLnVwZGF0ZSc7XG5jb25zdCBQT0lOVEVSX0RPV05fT1VUU0lERSA9ICdkaXNtaXNzYWJsZUxheWVyLnBvaW50ZXJEb3duT3V0c2lkZSc7XG5jb25zdCBGT0NVU19PVVRTSURFID0gJ2Rpc21pc3NhYmxlTGF5ZXIuZm9jdXNPdXRzaWRlJztcblxubGV0IG9yaWdpbmFsQm9keVBvaW50ZXJFdmVudHM6IHN0cmluZztcblxuY29uc3QgRGlzbWlzc2FibGVMYXllckNvbnRleHQgPSBSZWFjdC5jcmVhdGVDb250ZXh0KHtcbiAgbGF5ZXJzOiBuZXcgU2V0PERpc21pc3NhYmxlTGF5ZXJFbGVtZW50PigpLFxuICBsYXllcnNXaXRoT3V0c2lkZVBvaW50ZXJFdmVudHNEaXNhYmxlZDogbmV3IFNldDxEaXNtaXNzYWJsZUxheWVyRWxlbWVudD4oKSxcbiAgYnJhbmNoZXM6IG5ldyBTZXQ8RGlzbWlzc2FibGVMYXllckJyYW5jaEVsZW1lbnQ+KCksXG59KTtcblxudHlwZSBEaXNtaXNzYWJsZUxheWVyRWxlbWVudCA9IFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIFByaW1pdGl2ZS5kaXY+O1xudHlwZSBQcmltaXRpdmVEaXZQcm9wcyA9IFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgUHJpbWl0aXZlLmRpdj47XG5pbnRlcmZhY2UgRGlzbWlzc2FibGVMYXllclByb3BzIGV4dGVuZHMgUHJpbWl0aXZlRGl2UHJvcHMge1xuICAvKipcbiAgICogV2hlbiBgdHJ1ZWAsIGhvdmVyL2ZvY3VzL2NsaWNrIGludGVyYWN0aW9ucyB3aWxsIGJlIGRpc2FibGVkIG9uIGVsZW1lbnRzIG91dHNpZGVcbiAgICogdGhlIGBEaXNtaXNzYWJsZUxheWVyYC4gVXNlcnMgd2lsbCBuZWVkIHRvIGNsaWNrIHR3aWNlIG9uIG91dHNpZGUgZWxlbWVudHMgdG9cbiAgICogaW50ZXJhY3Qgd2l0aCB0aGVtOiBvbmNlIHRvIGNsb3NlIHRoZSBgRGlzbWlzc2FibGVMYXllcmAsIGFuZCBhZ2FpbiB0byB0cmlnZ2VyIHRoZSBlbGVtZW50LlxuICAgKi9cbiAgZGlzYWJsZU91dHNpZGVQb2ludGVyRXZlbnRzPzogYm9vbGVhbjtcbiAgLyoqXG4gICAqIEV2ZW50IGhhbmRsZXIgY2FsbGVkIHdoZW4gdGhlIGVzY2FwZSBrZXkgaXMgZG93bi5cbiAgICogQ2FuIGJlIHByZXZlbnRlZC5cbiAgICovXG4gIG9uRXNjYXBlS2V5RG93bj86IChldmVudDogS2V5Ym9hcmRFdmVudCkgPT4gdm9pZDtcbiAgLyoqXG4gICAqIEV2ZW50IGhhbmRsZXIgY2FsbGVkIHdoZW4gdGhlIGEgYHBvaW50ZXJkb3duYCBldmVudCBoYXBwZW5zIG91dHNpZGUgb2YgdGhlIGBEaXNtaXNzYWJsZUxheWVyYC5cbiAgICogQ2FuIGJlIHByZXZlbnRlZC5cbiAgICovXG4gIG9uUG9pbnRlckRvd25PdXRzaWRlPzogKGV2ZW50OiBQb2ludGVyRG93bk91dHNpZGVFdmVudCkgPT4gdm9pZDtcbiAgLyoqXG4gICAqIEV2ZW50IGhhbmRsZXIgY2FsbGVkIHdoZW4gdGhlIGZvY3VzIG1vdmVzIG91dHNpZGUgb2YgdGhlIGBEaXNtaXNzYWJsZUxheWVyYC5cbiAgICogQ2FuIGJlIHByZXZlbnRlZC5cbiAgICovXG4gIG9uRm9jdXNPdXRzaWRlPzogKGV2ZW50OiBGb2N1c091dHNpZGVFdmVudCkgPT4gdm9pZDtcbiAgLyoqXG4gICAqIEV2ZW50IGhhbmRsZXIgY2FsbGVkIHdoZW4gYW4gaW50ZXJhY3Rpb24gaGFwcGVucyBvdXRzaWRlIHRoZSBgRGlzbWlzc2FibGVMYXllcmAuXG4gICAqIFNwZWNpZmljYWxseSwgd2hlbiBhIGBwb2ludGVyZG93bmAgZXZlbnQgaGFwcGVucyBvdXRzaWRlIG9yIGZvY3VzIG1vdmVzIG91dHNpZGUgb2YgaXQuXG4gICAqIENhbiBiZSBwcmV2ZW50ZWQuXG4gICAqL1xuICBvbkludGVyYWN0T3V0c2lkZT86IChldmVudDogUG9pbnRlckRvd25PdXRzaWRlRXZlbnQgfCBGb2N1c091dHNpZGVFdmVudCkgPT4gdm9pZDtcbiAgLyoqXG4gICAqIEhhbmRsZXIgY2FsbGVkIHdoZW4gdGhlIGBEaXNtaXNzYWJsZUxheWVyYCBzaG91bGQgYmUgZGlzbWlzc2VkXG4gICAqL1xuICBvbkRpc21pc3M/OiAoKSA9PiB2b2lkO1xufVxuXG5jb25zdCBEaXNtaXNzYWJsZUxheWVyID0gUmVhY3QuZm9yd2FyZFJlZjxEaXNtaXNzYWJsZUxheWVyRWxlbWVudCwgRGlzbWlzc2FibGVMYXllclByb3BzPihcbiAgKHByb3BzLCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICBjb25zdCB7XG4gICAgICBkaXNhYmxlT3V0c2lkZVBvaW50ZXJFdmVudHMgPSBmYWxzZSxcbiAgICAgIG9uRXNjYXBlS2V5RG93bixcbiAgICAgIG9uUG9pbnRlckRvd25PdXRzaWRlLFxuICAgICAgb25Gb2N1c091dHNpZGUsXG4gICAgICBvbkludGVyYWN0T3V0c2lkZSxcbiAgICAgIG9uRGlzbWlzcyxcbiAgICAgIC4uLmxheWVyUHJvcHNcbiAgICB9ID0gcHJvcHM7XG4gICAgY29uc3QgY29udGV4dCA9IFJlYWN0LnVzZUNvbnRleHQoRGlzbWlzc2FibGVMYXllckNvbnRleHQpO1xuICAgIGNvbnN0IFtub2RlLCBzZXROb2RlXSA9IFJlYWN0LnVzZVN0YXRlPERpc21pc3NhYmxlTGF5ZXJFbGVtZW50IHwgbnVsbD4obnVsbCk7XG4gICAgY29uc3Qgb3duZXJEb2N1bWVudCA9IG5vZGU/Lm93bmVyRG9jdW1lbnQgPz8gZ2xvYmFsVGhpcz8uZG9jdW1lbnQ7XG4gICAgY29uc3QgWywgZm9yY2VdID0gUmVhY3QudXNlU3RhdGUoe30pO1xuICAgIGNvbnN0IGNvbXBvc2VkUmVmcyA9IHVzZUNvbXBvc2VkUmVmcyhmb3J3YXJkZWRSZWYsIChub2RlKSA9PiBzZXROb2RlKG5vZGUpKTtcbiAgICBjb25zdCBsYXllcnMgPSBBcnJheS5mcm9tKGNvbnRleHQubGF5ZXJzKTtcbiAgICBjb25zdCBbaGlnaGVzdExheWVyV2l0aE91dHNpZGVQb2ludGVyRXZlbnRzRGlzYWJsZWRdID0gWy4uLmNvbnRleHQubGF5ZXJzV2l0aE91dHNpZGVQb2ludGVyRXZlbnRzRGlzYWJsZWRdLnNsaWNlKC0xKTsgLy8gcHJldHRpZXItaWdub3JlXG4gICAgY29uc3QgaGlnaGVzdExheWVyV2l0aE91dHNpZGVQb2ludGVyRXZlbnRzRGlzYWJsZWRJbmRleCA9IGxheWVycy5pbmRleE9mKGhpZ2hlc3RMYXllcldpdGhPdXRzaWRlUG9pbnRlckV2ZW50c0Rpc2FibGVkKTsgLy8gcHJldHRpZXItaWdub3JlXG4gICAgY29uc3QgaW5kZXggPSBub2RlID8gbGF5ZXJzLmluZGV4T2Yobm9kZSkgOiAtMTtcbiAgICBjb25zdCBpc0JvZHlQb2ludGVyRXZlbnRzRGlzYWJsZWQgPSBjb250ZXh0LmxheWVyc1dpdGhPdXRzaWRlUG9pbnRlckV2ZW50c0Rpc2FibGVkLnNpemUgPiAwO1xuICAgIGNvbnN0IGlzUG9pbnRlckV2ZW50c0VuYWJsZWQgPSBpbmRleCA+PSBoaWdoZXN0TGF5ZXJXaXRoT3V0c2lkZVBvaW50ZXJFdmVudHNEaXNhYmxlZEluZGV4O1xuXG4gICAgY29uc3QgcG9pbnRlckRvd25PdXRzaWRlID0gdXNlUG9pbnRlckRvd25PdXRzaWRlKChldmVudCkgPT4ge1xuICAgICAgY29uc3QgdGFyZ2V0ID0gZXZlbnQudGFyZ2V0IGFzIEhUTUxFbGVtZW50O1xuICAgICAgY29uc3QgaXNQb2ludGVyRG93bk9uQnJhbmNoID0gWy4uLmNvbnRleHQuYnJhbmNoZXNdLnNvbWUoKGJyYW5jaCkgPT4gYnJhbmNoLmNvbnRhaW5zKHRhcmdldCkpO1xuICAgICAgaWYgKCFpc1BvaW50ZXJFdmVudHNFbmFibGVkIHx8IGlzUG9pbnRlckRvd25PbkJyYW5jaCkgcmV0dXJuO1xuICAgICAgb25Qb2ludGVyRG93bk91dHNpZGU/LihldmVudCk7XG4gICAgICBvbkludGVyYWN0T3V0c2lkZT8uKGV2ZW50KTtcbiAgICAgIGlmICghZXZlbnQuZGVmYXVsdFByZXZlbnRlZCkgb25EaXNtaXNzPy4oKTtcbiAgICB9LCBvd25lckRvY3VtZW50KTtcblxuICAgIGNvbnN0IGZvY3VzT3V0c2lkZSA9IHVzZUZvY3VzT3V0c2lkZSgoZXZlbnQpID0+IHtcbiAgICAgIGNvbnN0IHRhcmdldCA9IGV2ZW50LnRhcmdldCBhcyBIVE1MRWxlbWVudDtcbiAgICAgIGNvbnN0IGlzRm9jdXNJbkJyYW5jaCA9IFsuLi5jb250ZXh0LmJyYW5jaGVzXS5zb21lKChicmFuY2gpID0+IGJyYW5jaC5jb250YWlucyh0YXJnZXQpKTtcbiAgICAgIGlmIChpc0ZvY3VzSW5CcmFuY2gpIHJldHVybjtcbiAgICAgIG9uRm9jdXNPdXRzaWRlPy4oZXZlbnQpO1xuICAgICAgb25JbnRlcmFjdE91dHNpZGU/LihldmVudCk7XG4gICAgICBpZiAoIWV2ZW50LmRlZmF1bHRQcmV2ZW50ZWQpIG9uRGlzbWlzcz8uKCk7XG4gICAgfSwgb3duZXJEb2N1bWVudCk7XG5cbiAgICB1c2VFc2NhcGVLZXlkb3duKChldmVudCkgPT4ge1xuICAgICAgY29uc3QgaXNIaWdoZXN0TGF5ZXIgPSBpbmRleCA9PT0gY29udGV4dC5sYXllcnMuc2l6ZSAtIDE7XG4gICAgICBpZiAoIWlzSGlnaGVzdExheWVyKSByZXR1cm47XG4gICAgICBvbkVzY2FwZUtleURvd24/LihldmVudCk7XG4gICAgICBpZiAoIWV2ZW50LmRlZmF1bHRQcmV2ZW50ZWQgJiYgb25EaXNtaXNzKSB7XG4gICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgIG9uRGlzbWlzcygpO1xuICAgICAgfVxuICAgIH0sIG93bmVyRG9jdW1lbnQpO1xuXG4gICAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgIGlmICghbm9kZSkgcmV0dXJuO1xuICAgICAgaWYgKGRpc2FibGVPdXRzaWRlUG9pbnRlckV2ZW50cykge1xuICAgICAgICBpZiAoY29udGV4dC5sYXllcnNXaXRoT3V0c2lkZVBvaW50ZXJFdmVudHNEaXNhYmxlZC5zaXplID09PSAwKSB7XG4gICAgICAgICAgb3JpZ2luYWxCb2R5UG9pbnRlckV2ZW50cyA9IG93bmVyRG9jdW1lbnQuYm9keS5zdHlsZS5wb2ludGVyRXZlbnRzO1xuICAgICAgICAgIG93bmVyRG9jdW1lbnQuYm9keS5zdHlsZS5wb2ludGVyRXZlbnRzID0gJ25vbmUnO1xuICAgICAgICB9XG4gICAgICAgIGNvbnRleHQubGF5ZXJzV2l0aE91dHNpZGVQb2ludGVyRXZlbnRzRGlzYWJsZWQuYWRkKG5vZGUpO1xuICAgICAgfVxuICAgICAgY29udGV4dC5sYXllcnMuYWRkKG5vZGUpO1xuICAgICAgZGlzcGF0Y2hVcGRhdGUoKTtcbiAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIGlmIChcbiAgICAgICAgICBkaXNhYmxlT3V0c2lkZVBvaW50ZXJFdmVudHMgJiZcbiAgICAgICAgICBjb250ZXh0LmxheWVyc1dpdGhPdXRzaWRlUG9pbnRlckV2ZW50c0Rpc2FibGVkLnNpemUgPT09IDFcbiAgICAgICAgKSB7XG4gICAgICAgICAgb3duZXJEb2N1bWVudC5ib2R5LnN0eWxlLnBvaW50ZXJFdmVudHMgPSBvcmlnaW5hbEJvZHlQb2ludGVyRXZlbnRzO1xuICAgICAgICB9XG4gICAgICB9O1xuICAgIH0sIFtub2RlLCBvd25lckRvY3VtZW50LCBkaXNhYmxlT3V0c2lkZVBvaW50ZXJFdmVudHMsIGNvbnRleHRdKTtcblxuICAgIC8qKlxuICAgICAqIFdlIHB1cnBvc2VmdWxseSBwcmV2ZW50IGNvbWJpbmluZyB0aGlzIGVmZmVjdCB3aXRoIHRoZSBgZGlzYWJsZU91dHNpZGVQb2ludGVyRXZlbnRzYCBlZmZlY3RcbiAgICAgKiBiZWNhdXNlIGEgY2hhbmdlIHRvIGBkaXNhYmxlT3V0c2lkZVBvaW50ZXJFdmVudHNgIHdvdWxkIHJlbW92ZSB0aGlzIGxheWVyIGZyb20gdGhlIHN0YWNrXG4gICAgICogYW5kIGFkZCBpdCB0byB0aGUgZW5kIGFnYWluIHNvIHRoZSBsYXllcmluZyBvcmRlciB3b3VsZG4ndCBiZSBfY3JlYXRpb24gb3JkZXJfLlxuICAgICAqIFdlIG9ubHkgd2FudCB0aGVtIHRvIGJlIHJlbW92ZWQgZnJvbSBjb250ZXh0IHN0YWNrcyB3aGVuIHVubW91bnRlZC5cbiAgICAgKi9cbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgaWYgKCFub2RlKSByZXR1cm47XG4gICAgICAgIGNvbnRleHQubGF5ZXJzLmRlbGV0ZShub2RlKTtcbiAgICAgICAgY29udGV4dC5sYXllcnNXaXRoT3V0c2lkZVBvaW50ZXJFdmVudHNEaXNhYmxlZC5kZWxldGUobm9kZSk7XG4gICAgICAgIGRpc3BhdGNoVXBkYXRlKCk7XG4gICAgICB9O1xuICAgIH0sIFtub2RlLCBjb250ZXh0XSk7XG5cbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgY29uc3QgaGFuZGxlVXBkYXRlID0gKCkgPT4gZm9yY2Uoe30pO1xuICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcihDT05URVhUX1VQREFURSwgaGFuZGxlVXBkYXRlKTtcbiAgICAgIHJldHVybiAoKSA9PiBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKENPTlRFWFRfVVBEQVRFLCBoYW5kbGVVcGRhdGUpO1xuICAgIH0sIFtdKTtcblxuICAgIHJldHVybiAoXG4gICAgICA8UHJpbWl0aXZlLmRpdlxuICAgICAgICB7Li4ubGF5ZXJQcm9wc31cbiAgICAgICAgcmVmPXtjb21wb3NlZFJlZnN9XG4gICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgcG9pbnRlckV2ZW50czogaXNCb2R5UG9pbnRlckV2ZW50c0Rpc2FibGVkXG4gICAgICAgICAgICA/IGlzUG9pbnRlckV2ZW50c0VuYWJsZWRcbiAgICAgICAgICAgICAgPyAnYXV0bydcbiAgICAgICAgICAgICAgOiAnbm9uZSdcbiAgICAgICAgICAgIDogdW5kZWZpbmVkLFxuICAgICAgICAgIC4uLnByb3BzLnN0eWxlLFxuICAgICAgICB9fVxuICAgICAgICBvbkZvY3VzQ2FwdHVyZT17Y29tcG9zZUV2ZW50SGFuZGxlcnMocHJvcHMub25Gb2N1c0NhcHR1cmUsIGZvY3VzT3V0c2lkZS5vbkZvY3VzQ2FwdHVyZSl9XG4gICAgICAgIG9uQmx1ckNhcHR1cmU9e2NvbXBvc2VFdmVudEhhbmRsZXJzKHByb3BzLm9uQmx1ckNhcHR1cmUsIGZvY3VzT3V0c2lkZS5vbkJsdXJDYXB0dXJlKX1cbiAgICAgICAgb25Qb2ludGVyRG93bkNhcHR1cmU9e2NvbXBvc2VFdmVudEhhbmRsZXJzKFxuICAgICAgICAgIHByb3BzLm9uUG9pbnRlckRvd25DYXB0dXJlLFxuICAgICAgICAgIHBvaW50ZXJEb3duT3V0c2lkZS5vblBvaW50ZXJEb3duQ2FwdHVyZVxuICAgICAgICApfVxuICAgICAgLz5cbiAgICApO1xuICB9XG4pO1xuXG5EaXNtaXNzYWJsZUxheWVyLmRpc3BsYXlOYW1lID0gRElTTUlTU0FCTEVfTEFZRVJfTkFNRTtcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogRGlzbWlzc2FibGVMYXllckJyYW5jaFxuICogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG5jb25zdCBCUkFOQ0hfTkFNRSA9ICdEaXNtaXNzYWJsZUxheWVyQnJhbmNoJztcblxudHlwZSBEaXNtaXNzYWJsZUxheWVyQnJhbmNoRWxlbWVudCA9IFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIFByaW1pdGl2ZS5kaXY+O1xuaW50ZXJmYWNlIERpc21pc3NhYmxlTGF5ZXJCcmFuY2hQcm9wcyBleHRlbmRzIFByaW1pdGl2ZURpdlByb3BzIHt9XG5cbmNvbnN0IERpc21pc3NhYmxlTGF5ZXJCcmFuY2ggPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBEaXNtaXNzYWJsZUxheWVyQnJhbmNoRWxlbWVudCxcbiAgRGlzbWlzc2FibGVMYXllckJyYW5jaFByb3BzXG4+KChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gIGNvbnN0IGNvbnRleHQgPSBSZWFjdC51c2VDb250ZXh0KERpc21pc3NhYmxlTGF5ZXJDb250ZXh0KTtcbiAgY29uc3QgcmVmID0gUmVhY3QudXNlUmVmPERpc21pc3NhYmxlTGF5ZXJCcmFuY2hFbGVtZW50PihudWxsKTtcbiAgY29uc3QgY29tcG9zZWRSZWZzID0gdXNlQ29tcG9zZWRSZWZzKGZvcndhcmRlZFJlZiwgcmVmKTtcblxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IG5vZGUgPSByZWYuY3VycmVudDtcbiAgICBpZiAobm9kZSkge1xuICAgICAgY29udGV4dC5icmFuY2hlcy5hZGQobm9kZSk7XG4gICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICBjb250ZXh0LmJyYW5jaGVzLmRlbGV0ZShub2RlKTtcbiAgICAgIH07XG4gICAgfVxuICB9LCBbY29udGV4dC5icmFuY2hlc10pO1xuXG4gIHJldHVybiA8UHJpbWl0aXZlLmRpdiB7Li4ucHJvcHN9IHJlZj17Y29tcG9zZWRSZWZzfSAvPjtcbn0pO1xuXG5EaXNtaXNzYWJsZUxheWVyQnJhbmNoLmRpc3BsYXlOYW1lID0gQlJBTkNIX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxudHlwZSBQb2ludGVyRG93bk91dHNpZGVFdmVudCA9IEN1c3RvbUV2ZW50PHsgb3JpZ2luYWxFdmVudDogUG9pbnRlckV2ZW50IH0+O1xudHlwZSBGb2N1c091dHNpZGVFdmVudCA9IEN1c3RvbUV2ZW50PHsgb3JpZ2luYWxFdmVudDogRm9jdXNFdmVudCB9PjtcblxuLyoqXG4gKiBMaXN0ZW5zIGZvciBgcG9pbnRlcmRvd25gIG91dHNpZGUgYSByZWFjdCBzdWJ0cmVlLiBXZSB1c2UgYHBvaW50ZXJkb3duYCByYXRoZXIgdGhhbiBgcG9pbnRlcnVwYFxuICogdG8gbWltaWMgbGF5ZXIgZGlzbWlzc2luZyBiZWhhdmlvdXIgcHJlc2VudCBpbiBPUy5cbiAqIFJldHVybnMgcHJvcHMgdG8gcGFzcyB0byB0aGUgbm9kZSB3ZSB3YW50IHRvIGNoZWNrIGZvciBvdXRzaWRlIGV2ZW50cy5cbiAqL1xuZnVuY3Rpb24gdXNlUG9pbnRlckRvd25PdXRzaWRlKFxuICBvblBvaW50ZXJEb3duT3V0c2lkZT86IChldmVudDogUG9pbnRlckRvd25PdXRzaWRlRXZlbnQpID0+IHZvaWQsXG4gIG93bmVyRG9jdW1lbnQ6IERvY3VtZW50ID0gZ2xvYmFsVGhpcz8uZG9jdW1lbnRcbikge1xuICBjb25zdCBoYW5kbGVQb2ludGVyRG93bk91dHNpZGUgPSB1c2VDYWxsYmFja1JlZihvblBvaW50ZXJEb3duT3V0c2lkZSkgYXMgRXZlbnRMaXN0ZW5lcjtcbiAgY29uc3QgaXNQb2ludGVySW5zaWRlUmVhY3RUcmVlUmVmID0gUmVhY3QudXNlUmVmKGZhbHNlKTtcbiAgY29uc3QgaGFuZGxlQ2xpY2tSZWYgPSBSZWFjdC51c2VSZWYoKCkgPT4ge30pO1xuXG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlUG9pbnRlckRvd24gPSAoZXZlbnQ6IFBvaW50ZXJFdmVudCkgPT4ge1xuICAgICAgaWYgKGV2ZW50LnRhcmdldCAmJiAhaXNQb2ludGVySW5zaWRlUmVhY3RUcmVlUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgY29uc3QgZXZlbnREZXRhaWwgPSB7IG9yaWdpbmFsRXZlbnQ6IGV2ZW50IH07XG5cbiAgICAgICAgZnVuY3Rpb24gaGFuZGxlQW5kRGlzcGF0Y2hQb2ludGVyRG93bk91dHNpZGVFdmVudCgpIHtcbiAgICAgICAgICBoYW5kbGVBbmREaXNwYXRjaEN1c3RvbUV2ZW50KFxuICAgICAgICAgICAgUE9JTlRFUl9ET1dOX09VVFNJREUsXG4gICAgICAgICAgICBoYW5kbGVQb2ludGVyRG93bk91dHNpZGUsXG4gICAgICAgICAgICBldmVudERldGFpbCxcbiAgICAgICAgICAgIHsgZGlzY3JldGU6IHRydWUgfVxuICAgICAgICAgICk7XG4gICAgICAgIH1cblxuICAgICAgICAvKipcbiAgICAgICAgICogT24gdG91Y2ggZGV2aWNlcywgd2UgbmVlZCB0byB3YWl0IGZvciBhIGNsaWNrIGV2ZW50IGJlY2F1c2UgYnJvd3NlcnMgaW1wbGVtZW50XG4gICAgICAgICAqIGEgfjM1MG1zIGRlbGF5IGJldHdlZW4gdGhlIHRpbWUgdGhlIHVzZXIgc3RvcHMgdG91Y2hpbmcgdGhlIGRpc3BsYXkgYW5kIHdoZW4gdGhlXG4gICAgICAgICAqIGJyb3dzZXIgZXhlY3V0cmVzIGV2ZW50cy4gV2UgbmVlZCB0byBlbnN1cmUgd2UgZG9uJ3QgcmVhY3RpdmF0ZSBwb2ludGVyLWV2ZW50cyB3aXRoaW5cbiAgICAgICAgICogdGhpcyB0aW1lZnJhbWUgb3RoZXJ3aXNlIHRoZSBicm93c2VyIG1heSBleGVjdXRlIGV2ZW50cyB0aGF0IHNob3VsZCBoYXZlIGJlZW4gcHJldmVudGVkLlxuICAgICAgICAgKlxuICAgICAgICAgKiBBZGRpdGlvbmFsbHksIHRoaXMgYWxzbyBsZXRzIHVzIGRlYWwgYXV0b21hdGljYWxseSB3aXRoIGNhbmNlbGxhdGlvbnMgd2hlbiBhIGNsaWNrIGV2ZW50XG4gICAgICAgICAqIGlzbid0IHJhaXNlZCBiZWNhdXNlIHRoZSBwYWdlIHdhcyBjb25zaWRlcmVkIHNjcm9sbGVkL2RyYWctc2Nyb2xsZWQsIGxvbmctcHJlc3NlZCwgZXRjLlxuICAgICAgICAgKlxuICAgICAgICAgKiBUaGlzIGlzIHdoeSB3ZSBhbHNvIGNvbnRpbnVvdXNseSByZW1vdmUgdGhlIHByZXZpb3VzIGxpc3RlbmVyLCBiZWNhdXNlIHdlIGNhbm5vdCBiZVxuICAgICAgICAgKiBjZXJ0YWluIHRoYXQgaXQgd2FzIHJhaXNlZCwgYW5kIHRoZXJlZm9yZSBjbGVhbmVkLXVwLlxuICAgICAgICAgKi9cbiAgICAgICAgaWYgKGV2ZW50LnBvaW50ZXJUeXBlID09PSAndG91Y2gnKSB7XG4gICAgICAgICAgb3duZXJEb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdjbGljaycsIGhhbmRsZUNsaWNrUmVmLmN1cnJlbnQpO1xuICAgICAgICAgIGhhbmRsZUNsaWNrUmVmLmN1cnJlbnQgPSBoYW5kbGVBbmREaXNwYXRjaFBvaW50ZXJEb3duT3V0c2lkZUV2ZW50O1xuICAgICAgICAgIG93bmVyRG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCBoYW5kbGVDbGlja1JlZi5jdXJyZW50LCB7IG9uY2U6IHRydWUgfSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgaGFuZGxlQW5kRGlzcGF0Y2hQb2ludGVyRG93bk91dHNpZGVFdmVudCgpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBXZSBuZWVkIHRvIHJlbW92ZSB0aGUgZXZlbnQgbGlzdGVuZXIgaW4gY2FzZSB0aGUgb3V0c2lkZSBjbGljayBoYXMgYmVlbiBjYW5jZWxlZC5cbiAgICAgICAgLy8gU2VlOiBodHRwczovL2dpdGh1Yi5jb20vcmFkaXgtdWkvcHJpbWl0aXZlcy9pc3N1ZXMvMjE3MVxuICAgICAgICBvd25lckRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgaGFuZGxlQ2xpY2tSZWYuY3VycmVudCk7XG4gICAgICB9XG4gICAgICBpc1BvaW50ZXJJbnNpZGVSZWFjdFRyZWVSZWYuY3VycmVudCA9IGZhbHNlO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogaWYgdGhpcyBob29rIGV4ZWN1dGVzIGluIGEgY29tcG9uZW50IHRoYXQgbW91bnRzIHZpYSBhIGBwb2ludGVyZG93bmAgZXZlbnQsIHRoZSBldmVudFxuICAgICAqIHdvdWxkIGJ1YmJsZSB1cCB0byB0aGUgZG9jdW1lbnQgYW5kIHRyaWdnZXIgYSBgcG9pbnRlckRvd25PdXRzaWRlYCBldmVudC4gV2UgYXZvaWRcbiAgICAgKiB0aGlzIGJ5IGRlbGF5aW5nIHRoZSBldmVudCBsaXN0ZW5lciByZWdpc3RyYXRpb24gb24gdGhlIGRvY3VtZW50LlxuICAgICAqIFRoaXMgaXMgbm90IFJlYWN0IHNwZWNpZmljLCBidXQgcmF0aGVyIGhvdyB0aGUgRE9NIHdvcmtzLCBpZTpcbiAgICAgKiBgYGBcbiAgICAgKiBidXR0b24uYWRkRXZlbnRMaXN0ZW5lcigncG9pbnRlcmRvd24nLCAoKSA9PiB7XG4gICAgICogICBjb25zb2xlLmxvZygnSSB3aWxsIGxvZycpO1xuICAgICAqICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcigncG9pbnRlcmRvd24nLCAoKSA9PiB7XG4gICAgICogICAgIGNvbnNvbGUubG9nKCdJIHdpbGwgYWxzbyBsb2cnKTtcbiAgICAgKiAgIH0pXG4gICAgICogfSk7XG4gICAgICovXG4gICAgY29uc3QgdGltZXJJZCA9IHdpbmRvdy5zZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIG93bmVyRG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcigncG9pbnRlcmRvd24nLCBoYW5kbGVQb2ludGVyRG93bik7XG4gICAgfSwgMCk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHdpbmRvdy5jbGVhclRpbWVvdXQodGltZXJJZCk7XG4gICAgICBvd25lckRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3BvaW50ZXJkb3duJywgaGFuZGxlUG9pbnRlckRvd24pO1xuICAgICAgb3duZXJEb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdjbGljaycsIGhhbmRsZUNsaWNrUmVmLmN1cnJlbnQpO1xuICAgIH07XG4gIH0sIFtvd25lckRvY3VtZW50LCBoYW5kbGVQb2ludGVyRG93bk91dHNpZGVdKTtcblxuICByZXR1cm4ge1xuICAgIC8vIGVuc3VyZXMgd2UgY2hlY2sgUmVhY3QgY29tcG9uZW50IHRyZWUgKG5vdCBqdXN0IERPTSB0cmVlKVxuICAgIG9uUG9pbnRlckRvd25DYXB0dXJlOiAoKSA9PiAoaXNQb2ludGVySW5zaWRlUmVhY3RUcmVlUmVmLmN1cnJlbnQgPSB0cnVlKSxcbiAgfTtcbn1cblxuLyoqXG4gKiBMaXN0ZW5zIGZvciB3aGVuIGZvY3VzIGhhcHBlbnMgb3V0c2lkZSBhIHJlYWN0IHN1YnRyZWUuXG4gKiBSZXR1cm5zIHByb3BzIHRvIHBhc3MgdG8gdGhlIHJvb3QgKG5vZGUpIG9mIHRoZSBzdWJ0cmVlIHdlIHdhbnQgdG8gY2hlY2suXG4gKi9cbmZ1bmN0aW9uIHVzZUZvY3VzT3V0c2lkZShcbiAgb25Gb2N1c091dHNpZGU/OiAoZXZlbnQ6IEZvY3VzT3V0c2lkZUV2ZW50KSA9PiB2b2lkLFxuICBvd25lckRvY3VtZW50OiBEb2N1bWVudCA9IGdsb2JhbFRoaXM/LmRvY3VtZW50XG4pIHtcbiAgY29uc3QgaGFuZGxlRm9jdXNPdXRzaWRlID0gdXNlQ2FsbGJhY2tSZWYob25Gb2N1c091dHNpZGUpIGFzIEV2ZW50TGlzdGVuZXI7XG4gIGNvbnN0IGlzRm9jdXNJbnNpZGVSZWFjdFRyZWVSZWYgPSBSZWFjdC51c2VSZWYoZmFsc2UpO1xuXG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlRm9jdXMgPSAoZXZlbnQ6IEZvY3VzRXZlbnQpID0+IHtcbiAgICAgIGlmIChldmVudC50YXJnZXQgJiYgIWlzRm9jdXNJbnNpZGVSZWFjdFRyZWVSZWYuY3VycmVudCkge1xuICAgICAgICBjb25zdCBldmVudERldGFpbCA9IHsgb3JpZ2luYWxFdmVudDogZXZlbnQgfTtcbiAgICAgICAgaGFuZGxlQW5kRGlzcGF0Y2hDdXN0b21FdmVudChGT0NVU19PVVRTSURFLCBoYW5kbGVGb2N1c091dHNpZGUsIGV2ZW50RGV0YWlsLCB7XG4gICAgICAgICAgZGlzY3JldGU6IGZhbHNlLFxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9O1xuICAgIG93bmVyRG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignZm9jdXNpbicsIGhhbmRsZUZvY3VzKTtcbiAgICByZXR1cm4gKCkgPT4gb3duZXJEb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdmb2N1c2luJywgaGFuZGxlRm9jdXMpO1xuICB9LCBbb3duZXJEb2N1bWVudCwgaGFuZGxlRm9jdXNPdXRzaWRlXSk7XG5cbiAgcmV0dXJuIHtcbiAgICBvbkZvY3VzQ2FwdHVyZTogKCkgPT4gKGlzRm9jdXNJbnNpZGVSZWFjdFRyZWVSZWYuY3VycmVudCA9IHRydWUpLFxuICAgIG9uQmx1ckNhcHR1cmU6ICgpID0+IChpc0ZvY3VzSW5zaWRlUmVhY3RUcmVlUmVmLmN1cnJlbnQgPSBmYWxzZSksXG4gIH07XG59XG5cbmZ1bmN0aW9uIGRpc3BhdGNoVXBkYXRlKCkge1xuICBjb25zdCBldmVudCA9IG5ldyBDdXN0b21FdmVudChDT05URVhUX1VQREFURSk7XG4gIGRvY3VtZW50LmRpc3BhdGNoRXZlbnQoZXZlbnQpO1xufVxuXG5mdW5jdGlvbiBoYW5kbGVBbmREaXNwYXRjaEN1c3RvbUV2ZW50PEUgZXh0ZW5kcyBDdXN0b21FdmVudCwgT3JpZ2luYWxFdmVudCBleHRlbmRzIEV2ZW50PihcbiAgbmFtZTogc3RyaW5nLFxuICBoYW5kbGVyOiAoKGV2ZW50OiBFKSA9PiB2b2lkKSB8IHVuZGVmaW5lZCxcbiAgZGV0YWlsOiB7IG9yaWdpbmFsRXZlbnQ6IE9yaWdpbmFsRXZlbnQgfSAmIChFIGV4dGVuZHMgQ3VzdG9tRXZlbnQ8aW5mZXIgRD4gPyBEIDogbmV2ZXIpLFxuICB7IGRpc2NyZXRlIH06IHsgZGlzY3JldGU6IGJvb2xlYW4gfVxuKSB7XG4gIGNvbnN0IHRhcmdldCA9IGRldGFpbC5vcmlnaW5hbEV2ZW50LnRhcmdldDtcbiAgY29uc3QgZXZlbnQgPSBuZXcgQ3VzdG9tRXZlbnQobmFtZSwgeyBidWJibGVzOiBmYWxzZSwgY2FuY2VsYWJsZTogdHJ1ZSwgZGV0YWlsIH0pO1xuICBpZiAoaGFuZGxlcikgdGFyZ2V0LmFkZEV2ZW50TGlzdGVuZXIobmFtZSwgaGFuZGxlciBhcyBFdmVudExpc3RlbmVyLCB7IG9uY2U6IHRydWUgfSk7XG5cbiAgaWYgKGRpc2NyZXRlKSB7XG4gICAgZGlzcGF0Y2hEaXNjcmV0ZUN1c3RvbUV2ZW50KHRhcmdldCwgZXZlbnQpO1xuICB9IGVsc2Uge1xuICAgIHRhcmdldC5kaXNwYXRjaEV2ZW50KGV2ZW50KTtcbiAgfVxufVxuXG5jb25zdCBSb290ID0gRGlzbWlzc2FibGVMYXllcjtcbmNvbnN0IEJyYW5jaCA9IERpc21pc3NhYmxlTGF5ZXJCcmFuY2g7XG5cbmV4cG9ydCB7XG4gIERpc21pc3NhYmxlTGF5ZXIsXG4gIERpc21pc3NhYmxlTGF5ZXJCcmFuY2gsXG4gIC8vXG4gIFJvb3QsXG4gIEJyYW5jaCxcbn07XG5leHBvcnQgdHlwZSB7IERpc21pc3NhYmxlTGF5ZXJQcm9wcyB9O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY29tcG9zZUV2ZW50SGFuZGxlcnMiLCJQcmltaXRpdmUiLCJkaXNwYXRjaERpc2NyZXRlQ3VzdG9tRXZlbnQiLCJ1c2VDb21wb3NlZFJlZnMiLCJ1c2VDYWxsYmFja1JlZiIsInVzZUVzY2FwZUtleWRvd24iLCJqc3giLCJESVNNSVNTQUJMRV9MQVlFUl9OQU1FIiwiQ09OVEVYVF9VUERBVEUiLCJQT0lOVEVSX0RPV05fT1VUU0lERSIsIkZPQ1VTX09VVFNJREUiLCJvcmlnaW5hbEJvZHlQb2ludGVyRXZlbnRzIiwiRGlzbWlzc2FibGVMYXllckNvbnRleHQiLCJjcmVhdGVDb250ZXh0IiwibGF5ZXJzIiwiU2V0IiwibGF5ZXJzV2l0aE91dHNpZGVQb2ludGVyRXZlbnRzRGlzYWJsZWQiLCJicmFuY2hlcyIsIkRpc21pc3NhYmxlTGF5ZXIiLCJmb3J3YXJkUmVmIiwicHJvcHMiLCJmb3J3YXJkZWRSZWYiLCJkaXNhYmxlT3V0c2lkZVBvaW50ZXJFdmVudHMiLCJvbkVzY2FwZUtleURvd24iLCJvblBvaW50ZXJEb3duT3V0c2lkZSIsIm9uRm9jdXNPdXRzaWRlIiwib25JbnRlcmFjdE91dHNpZGUiLCJvbkRpc21pc3MiLCJsYXllclByb3BzIiwiY29udGV4dCIsInVzZUNvbnRleHQiLCJub2RlIiwic2V0Tm9kZSIsInVzZVN0YXRlIiwib3duZXJEb2N1bWVudCIsImdsb2JhbFRoaXMiLCJkb2N1bWVudCIsImZvcmNlIiwiY29tcG9zZWRSZWZzIiwiQXJyYXkiLCJmcm9tIiwiaGlnaGVzdExheWVyV2l0aE91dHNpZGVQb2ludGVyRXZlbnRzRGlzYWJsZWQiLCJzbGljZSIsImhpZ2hlc3RMYXllcldpdGhPdXRzaWRlUG9pbnRlckV2ZW50c0Rpc2FibGVkSW5kZXgiLCJpbmRleE9mIiwiaW5kZXgiLCJpc0JvZHlQb2ludGVyRXZlbnRzRGlzYWJsZWQiLCJzaXplIiwiaXNQb2ludGVyRXZlbnRzRW5hYmxlZCIsInBvaW50ZXJEb3duT3V0c2lkZSIsInVzZVBvaW50ZXJEb3duT3V0c2lkZSIsImV2ZW50IiwidGFyZ2V0IiwiaXNQb2ludGVyRG93bk9uQnJhbmNoIiwic29tZSIsImJyYW5jaCIsImNvbnRhaW5zIiwiZGVmYXVsdFByZXZlbnRlZCIsImZvY3VzT3V0c2lkZSIsInVzZUZvY3VzT3V0c2lkZSIsImlzRm9jdXNJbkJyYW5jaCIsImlzSGlnaGVzdExheWVyIiwicHJldmVudERlZmF1bHQiLCJ1c2VFZmZlY3QiLCJib2R5Iiwic3R5bGUiLCJwb2ludGVyRXZlbnRzIiwiYWRkIiwiZGlzcGF0Y2hVcGRhdGUiLCJkZWxldGUiLCJoYW5kbGVVcGRhdGUiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImRpdiIsInJlZiIsIm9uRm9jdXNDYXB0dXJlIiwib25CbHVyQ2FwdHVyZSIsIm9uUG9pbnRlckRvd25DYXB0dXJlIiwiZGlzcGxheU5hbWUiLCJCUkFOQ0hfTkFNRSIsIkRpc21pc3NhYmxlTGF5ZXJCcmFuY2giLCJ1c2VSZWYiLCJjdXJyZW50IiwiaGFuZGxlUG9pbnRlckRvd25PdXRzaWRlIiwiaXNQb2ludGVySW5zaWRlUmVhY3RUcmVlUmVmIiwiaGFuZGxlQ2xpY2tSZWYiLCJoYW5kbGVQb2ludGVyRG93biIsImhhbmRsZUFuZERpc3BhdGNoUG9pbnRlckRvd25PdXRzaWRlRXZlbnQiLCJoYW5kbGVBbmREaXNwYXRjaEN1c3RvbUV2ZW50IiwiZXZlbnREZXRhaWwiLCJkaXNjcmV0ZSIsIm9yaWdpbmFsRXZlbnQiLCJwb2ludGVyVHlwZSIsIm9uY2UiLCJ0aW1lcklkIiwid2luZG93Iiwic2V0VGltZW91dCIsImNsZWFyVGltZW91dCIsImhhbmRsZUZvY3VzT3V0c2lkZSIsImlzRm9jdXNJbnNpZGVSZWFjdFRyZWVSZWYiLCJoYW5kbGVGb2N1cyIsIkN1c3RvbUV2ZW50IiwiZGlzcGF0Y2hFdmVudCIsIm5hbWUiLCJoYW5kbGVyIiwiZGV0YWlsIiwiYnViYmxlcyIsImNhbmNlbGFibGUiLCJSb290IiwiQnJhbmNoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-focus-guards/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusGuards: () => (/* binding */ FocusGuards),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   useFocusGuards: () => (/* binding */ useFocusGuards)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ FocusGuards,Root,useFocusGuards auto */ // packages/react/focus-guards/src/FocusGuards.tsx\n\nvar count = 0;\nfunction FocusGuards(props) {\n    useFocusGuards();\n    return props.children;\n}\nfunction useFocusGuards() {\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const edgeGuards = document.querySelectorAll(\"[data-radix-focus-guard]\");\n        document.body.insertAdjacentElement(\"afterbegin\", edgeGuards[0] ?? createFocusGuard());\n        document.body.insertAdjacentElement(\"beforeend\", edgeGuards[1] ?? createFocusGuard());\n        count++;\n        return ()=>{\n            if (count === 1) {\n                document.querySelectorAll(\"[data-radix-focus-guard]\").forEach((node)=>node.remove());\n            }\n            count--;\n        };\n    }, []);\n}\nfunction createFocusGuard() {\n    const element = document.createElement(\"span\");\n    element.setAttribute(\"data-radix-focus-guard\", \"\");\n    element.tabIndex = 0;\n    element.style.outline = \"none\";\n    element.style.opacity = \"0\";\n    element.style.position = \"fixed\";\n    element.style.pointerEvents = \"none\";\n    return element;\n}\nvar Root = FocusGuards;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-focus-scope/dist/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusScope: () => (/* binding */ FocusScope),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ FocusScope,Root auto */ // packages/react/focus-scope/src/FocusScope.tsx\n\n\n\n\n\nvar AUTOFOCUS_ON_MOUNT = \"focusScope.autoFocusOnMount\";\nvar AUTOFOCUS_ON_UNMOUNT = \"focusScope.autoFocusOnUnmount\";\nvar EVENT_OPTIONS = {\n    bubbles: false,\n    cancelable: true\n};\nvar FOCUS_SCOPE_NAME = \"FocusScope\";\nvar FocusScope = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { loop = false, trapped = false, onMountAutoFocus: onMountAutoFocusProp, onUnmountAutoFocus: onUnmountAutoFocusProp, ...scopeProps } = props;\n    const [container, setContainer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const onMountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onMountAutoFocusProp);\n    const onUnmountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onUnmountAutoFocusProp);\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setContainer(node));\n    const focusScope = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        paused: false,\n        pause () {\n            this.paused = true;\n        },\n        resume () {\n            this.paused = false;\n        }\n    }).current;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (trapped) {\n            let handleFocusIn2 = function(event) {\n                if (focusScope.paused || !container) return;\n                const target = event.target;\n                if (container.contains(target)) {\n                    lastFocusedElementRef.current = target;\n                } else {\n                    focus(lastFocusedElementRef.current, {\n                        select: true\n                    });\n                }\n            }, handleFocusOut2 = function(event) {\n                if (focusScope.paused || !container) return;\n                const relatedTarget = event.relatedTarget;\n                if (relatedTarget === null) return;\n                if (!container.contains(relatedTarget)) {\n                    focus(lastFocusedElementRef.current, {\n                        select: true\n                    });\n                }\n            }, handleMutations2 = function(mutations) {\n                const focusedElement = document.activeElement;\n                if (focusedElement !== document.body) return;\n                for (const mutation of mutations){\n                    if (mutation.removedNodes.length > 0) focus(container);\n                }\n            };\n            var handleFocusIn = handleFocusIn2, handleFocusOut = handleFocusOut2, handleMutations = handleMutations2;\n            document.addEventListener(\"focusin\", handleFocusIn2);\n            document.addEventListener(\"focusout\", handleFocusOut2);\n            const mutationObserver = new MutationObserver(handleMutations2);\n            if (container) mutationObserver.observe(container, {\n                childList: true,\n                subtree: true\n            });\n            return ()=>{\n                document.removeEventListener(\"focusin\", handleFocusIn2);\n                document.removeEventListener(\"focusout\", handleFocusOut2);\n                mutationObserver.disconnect();\n            };\n        }\n    }, [\n        trapped,\n        container,\n        focusScope.paused\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (container) {\n            focusScopesStack.add(focusScope);\n            const previouslyFocusedElement = document.activeElement;\n            const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n            if (!hasFocusedCandidate) {\n                const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n                container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                container.dispatchEvent(mountEvent);\n                if (!mountEvent.defaultPrevented) {\n                    focusFirst(removeLinks(getTabbableCandidates(container)), {\n                        select: true\n                    });\n                    if (document.activeElement === previouslyFocusedElement) {\n                        focus(container);\n                    }\n                }\n            }\n            return ()=>{\n                container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                setTimeout(()=>{\n                    const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n                    container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                    container.dispatchEvent(unmountEvent);\n                    if (!unmountEvent.defaultPrevented) {\n                        focus(previouslyFocusedElement ?? document.body, {\n                            select: true\n                        });\n                    }\n                    container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                    focusScopesStack.remove(focusScope);\n                }, 0);\n            };\n        }\n    }, [\n        container,\n        onMountAutoFocus,\n        onUnmountAutoFocus,\n        focusScope\n    ]);\n    const handleKeyDown = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        if (!loop && !trapped) return;\n        if (focusScope.paused) return;\n        const isTabKey = event.key === \"Tab\" && !event.altKey && !event.ctrlKey && !event.metaKey;\n        const focusedElement = document.activeElement;\n        if (isTabKey && focusedElement) {\n            const container2 = event.currentTarget;\n            const [first, last] = getTabbableEdges(container2);\n            const hasTabbableElementsInside = first && last;\n            if (!hasTabbableElementsInside) {\n                if (focusedElement === container2) event.preventDefault();\n            } else {\n                if (!event.shiftKey && focusedElement === last) {\n                    event.preventDefault();\n                    if (loop) focus(first, {\n                        select: true\n                    });\n                } else if (event.shiftKey && focusedElement === first) {\n                    event.preventDefault();\n                    if (loop) focus(last, {\n                        select: true\n                    });\n                }\n            }\n        }\n    }, [\n        loop,\n        trapped,\n        focusScope.paused\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        tabIndex: -1,\n        ...scopeProps,\n        ref: composedRefs,\n        onKeyDown: handleKeyDown\n    });\n});\nFocusScope.displayName = FOCUS_SCOPE_NAME;\nfunction focusFirst(candidates, { select = false } = {}) {\n    const previouslyFocusedElement = document.activeElement;\n    for (const candidate of candidates){\n        focus(candidate, {\n            select\n        });\n        if (document.activeElement !== previouslyFocusedElement) return;\n    }\n}\nfunction getTabbableEdges(container) {\n    const candidates = getTabbableCandidates(container);\n    const first = findVisible(candidates, container);\n    const last = findVisible(candidates.reverse(), container);\n    return [\n        first,\n        last\n    ];\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction findVisible(elements, container) {\n    for (const element of elements){\n        if (!isHidden(element, {\n            upTo: container\n        })) return element;\n    }\n}\nfunction isHidden(node, { upTo }) {\n    if (getComputedStyle(node).visibility === \"hidden\") return true;\n    while(node){\n        if (upTo !== void 0 && node === upTo) return false;\n        if (getComputedStyle(node).display === \"none\") return true;\n        node = node.parentElement;\n    }\n    return false;\n}\nfunction isSelectableInput(element) {\n    return element instanceof HTMLInputElement && \"select\" in element;\n}\nfunction focus(element, { select = false } = {}) {\n    if (element && element.focus) {\n        const previouslyFocusedElement = document.activeElement;\n        element.focus({\n            preventScroll: true\n        });\n        if (element !== previouslyFocusedElement && isSelectableInput(element) && select) element.select();\n    }\n}\nvar focusScopesStack = createFocusScopesStack();\nfunction createFocusScopesStack() {\n    let stack = [];\n    return {\n        add (focusScope) {\n            const activeFocusScope = stack[0];\n            if (focusScope !== activeFocusScope) {\n                activeFocusScope?.pause();\n            }\n            stack = arrayRemove(stack, focusScope);\n            stack.unshift(focusScope);\n        },\n        remove (focusScope) {\n            stack = arrayRemove(stack, focusScope);\n            stack[0]?.resume();\n        }\n    };\n}\nfunction arrayRemove(array, item) {\n    const updatedArray = [\n        ...array\n    ];\n    const index = updatedArray.indexOf(item);\n    if (index !== -1) {\n        updatedArray.splice(index, 1);\n    }\n    return updatedArray;\n}\nfunction removeLinks(items) {\n    return items.filter((item)=>item.tagName !== \"A\");\n}\nvar Root = FocusScope;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@radix-ui/react-id/dist/index.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useId: () => (/* binding */ useId)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/id/src/id.tsx\n\n\nvar useReactId = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\"useId\".toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(useReactId());\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWlkL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUMrQjtBQUNxQztBQUNwRSxpQkFBaUIseUxBQUs7QUFDdEI7QUFDQTtBQUNBLHNCQUFzQiwyQ0FBYztBQUNwQyxFQUFFLGtGQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILDJDQUEyQyxHQUFHO0FBQzlDO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGVydmlld2NyYWNrZXIvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWlkL2Rpc3QvaW5kZXgubWpzPzBjZTIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvaWQvc3JjL2lkLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyB1c2VMYXlvdXRFZmZlY3QgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0XCI7XG52YXIgdXNlUmVhY3RJZCA9IFJlYWN0W1widXNlSWRcIi50b1N0cmluZygpXSB8fCAoKCkgPT4gdm9pZCAwKTtcbnZhciBjb3VudCA9IDA7XG5mdW5jdGlvbiB1c2VJZChkZXRlcm1pbmlzdGljSWQpIHtcbiAgY29uc3QgW2lkLCBzZXRJZF0gPSBSZWFjdC51c2VTdGF0ZSh1c2VSZWFjdElkKCkpO1xuICB1c2VMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghZGV0ZXJtaW5pc3RpY0lkKSBzZXRJZCgocmVhY3RJZCkgPT4gcmVhY3RJZCA/PyBTdHJpbmcoY291bnQrKykpO1xuICB9LCBbZGV0ZXJtaW5pc3RpY0lkXSk7XG4gIHJldHVybiBkZXRlcm1pbmlzdGljSWQgfHwgKGlkID8gYHJhZGl4LSR7aWR9YCA6IFwiXCIpO1xufVxuZXhwb3J0IHtcbiAgdXNlSWRcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Indicator: () => (/* binding */ Indicator),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   Link: () => (/* binding */ Link),\n/* harmony export */   List: () => (/* binding */ List),\n/* harmony export */   NavigationMenu: () => (/* binding */ NavigationMenu),\n/* harmony export */   NavigationMenuContent: () => (/* binding */ NavigationMenuContent),\n/* harmony export */   NavigationMenuIndicator: () => (/* binding */ NavigationMenuIndicator),\n/* harmony export */   NavigationMenuItem: () => (/* binding */ NavigationMenuItem),\n/* harmony export */   NavigationMenuLink: () => (/* binding */ NavigationMenuLink),\n/* harmony export */   NavigationMenuList: () => (/* binding */ NavigationMenuList),\n/* harmony export */   NavigationMenuSub: () => (/* binding */ NavigationMenuSub),\n/* harmony export */   NavigationMenuTrigger: () => (/* binding */ NavigationMenuTrigger),\n/* harmony export */   NavigationMenuViewport: () => (/* binding */ NavigationMenuViewport),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Sub: () => (/* binding */ Sub),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createNavigationMenuScope: () => (/* binding */ createNavigationMenuScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Content,Indicator,Item,Link,List,NavigationMenu,NavigationMenuContent,NavigationMenuIndicator,NavigationMenuItem,NavigationMenuLink,NavigationMenuList,NavigationMenuSub,NavigationMenuTrigger,NavigationMenuViewport,Root,Sub,Trigger,Viewport,createNavigationMenuScope auto */ // packages/react/navigation-menu/src/NavigationMenu.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar NAVIGATION_MENU_NAME = \"NavigationMenu\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(NAVIGATION_MENU_NAME);\nvar [FocusGroupCollection, useFocusGroupCollection, createFocusGroupCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(NAVIGATION_MENU_NAME);\nvar [createNavigationMenuContext, createNavigationMenuScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(NAVIGATION_MENU_NAME, [\n    createCollectionScope,\n    createFocusGroupCollectionScope\n]);\nvar [NavigationMenuProviderImpl, useNavigationMenuContext] = createNavigationMenuContext(NAVIGATION_MENU_NAME);\nvar [ViewportContentProvider, useViewportContentContext] = createNavigationMenuContext(NAVIGATION_MENU_NAME);\nvar NavigationMenu = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, value: valueProp, onValueChange, defaultValue, delayDuration = 200, skipDelayDuration = 300, orientation = \"horizontal\", dir, ...NavigationMenuProps } = props;\n    const [navigationMenu, setNavigationMenu] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, (node)=>setNavigationMenu(node));\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__.useDirection)(dir);\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const closeTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const skipDelayTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const [isOpenDelayed, setIsOpenDelayed] = react__WEBPACK_IMPORTED_MODULE_0__.useState(true);\n    const [value = \"\", setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: valueProp,\n        onChange: (value2)=>{\n            const isOpen = value2 !== \"\";\n            const hasSkipDelayDuration = skipDelayDuration > 0;\n            if (isOpen) {\n                window.clearTimeout(skipDelayTimerRef.current);\n                if (hasSkipDelayDuration) setIsOpenDelayed(false);\n            } else {\n                window.clearTimeout(skipDelayTimerRef.current);\n                skipDelayTimerRef.current = window.setTimeout(()=>setIsOpenDelayed(true), skipDelayDuration);\n            }\n            onValueChange?.(value2);\n        },\n        defaultProp: defaultValue\n    });\n    const startCloseTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(closeTimerRef.current);\n        closeTimerRef.current = window.setTimeout(()=>setValue(\"\"), 150);\n    }, [\n        setValue\n    ]);\n    const handleOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((itemValue)=>{\n        window.clearTimeout(closeTimerRef.current);\n        setValue(itemValue);\n    }, [\n        setValue\n    ]);\n    const handleDelayedOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((itemValue)=>{\n        const isOpenItem = value === itemValue;\n        if (isOpenItem) {\n            window.clearTimeout(closeTimerRef.current);\n        } else {\n            openTimerRef.current = window.setTimeout(()=>{\n                window.clearTimeout(closeTimerRef.current);\n                setValue(itemValue);\n            }, delayDuration);\n        }\n    }, [\n        value,\n        setValue,\n        delayDuration\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            window.clearTimeout(openTimerRef.current);\n            window.clearTimeout(closeTimerRef.current);\n            window.clearTimeout(skipDelayTimerRef.current);\n        };\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuProvider, {\n        scope: __scopeNavigationMenu,\n        isRootMenu: true,\n        value,\n        dir: direction,\n        orientation,\n        rootNavigationMenu: navigationMenu,\n        onTriggerEnter: (itemValue)=>{\n            window.clearTimeout(openTimerRef.current);\n            if (isOpenDelayed) handleDelayedOpen(itemValue);\n            else handleOpen(itemValue);\n        },\n        onTriggerLeave: ()=>{\n            window.clearTimeout(openTimerRef.current);\n            startCloseTimer();\n        },\n        onContentEnter: ()=>window.clearTimeout(closeTimerRef.current),\n        onContentLeave: startCloseTimer,\n        onItemSelect: (itemValue)=>{\n            setValue((prevValue)=>prevValue === itemValue ? \"\" : itemValue);\n        },\n        onItemDismiss: ()=>setValue(\"\"),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.nav, {\n            \"aria-label\": \"Main\",\n            \"data-orientation\": orientation,\n            dir: direction,\n            ...NavigationMenuProps,\n            ref: composedRef\n        })\n    });\n});\nNavigationMenu.displayName = NAVIGATION_MENU_NAME;\nvar SUB_NAME = \"NavigationMenuSub\";\nvar NavigationMenuSub = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, value: valueProp, onValueChange, defaultValue, orientation = \"horizontal\", ...subProps } = props;\n    const context = useNavigationMenuContext(SUB_NAME, __scopeNavigationMenu);\n    const [value = \"\", setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: valueProp,\n        onChange: onValueChange,\n        defaultProp: defaultValue\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuProvider, {\n        scope: __scopeNavigationMenu,\n        isRootMenu: false,\n        value,\n        dir: context.dir,\n        orientation,\n        rootNavigationMenu: context.rootNavigationMenu,\n        onTriggerEnter: (itemValue)=>setValue(itemValue),\n        onItemSelect: (itemValue)=>setValue(itemValue),\n        onItemDismiss: ()=>setValue(\"\"),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n            \"data-orientation\": orientation,\n            ...subProps,\n            ref: forwardedRef\n        })\n    });\n});\nNavigationMenuSub.displayName = SUB_NAME;\nvar NavigationMenuProvider = (props)=>{\n    const { scope, isRootMenu, rootNavigationMenu, dir, orientation, children, value, onItemSelect, onItemDismiss, onTriggerEnter, onTriggerLeave, onContentEnter, onContentLeave } = props;\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewportContent, setViewportContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(/* @__PURE__ */ new Map());\n    const [indicatorTrack, setIndicatorTrack] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuProviderImpl, {\n        scope,\n        isRootMenu,\n        rootNavigationMenu,\n        value,\n        previousValue: (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_9__.usePrevious)(value),\n        baseId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)(),\n        dir,\n        orientation,\n        viewport,\n        onViewportChange: setViewport,\n        indicatorTrack,\n        onIndicatorTrackChange: setIndicatorTrack,\n        onTriggerEnter: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onTriggerEnter),\n        onTriggerLeave: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onTriggerLeave),\n        onContentEnter: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onContentEnter),\n        onContentLeave: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onContentLeave),\n        onItemSelect: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onItemSelect),\n        onItemDismiss: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onItemDismiss),\n        onViewportContentChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((contentValue, contentData)=>{\n            setViewportContent((prevContent)=>{\n                prevContent.set(contentValue, contentData);\n                return new Map(prevContent);\n            });\n        }, []),\n        onViewportContentRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((contentValue)=>{\n            setViewportContent((prevContent)=>{\n                if (!prevContent.has(contentValue)) return prevContent;\n                prevContent.delete(contentValue);\n                return new Map(prevContent);\n            });\n        }, []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n            scope,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ViewportContentProvider, {\n                scope,\n                items: viewportContent,\n                children\n            })\n        })\n    });\n};\nvar LIST_NAME = \"NavigationMenuList\";\nvar NavigationMenuList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, ...listProps } = props;\n    const context = useNavigationMenuContext(LIST_NAME, __scopeNavigationMenu);\n    const list = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.ul, {\n        \"data-orientation\": context.orientation,\n        ...listProps,\n        ref: forwardedRef\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n        style: {\n            position: \"relative\"\n        },\n        ref: context.onIndicatorTrackChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n            scope: __scopeNavigationMenu,\n            children: context.isRootMenu ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroup, {\n                asChild: true,\n                children: list\n            }) : list\n        })\n    });\n});\nNavigationMenuList.displayName = LIST_NAME;\nvar ITEM_NAME = \"NavigationMenuItem\";\nvar [NavigationMenuItemContextProvider, useNavigationMenuItemContext] = createNavigationMenuContext(ITEM_NAME);\nvar NavigationMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, value: valueProp, ...itemProps } = props;\n    const autoValue = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)();\n    const value = valueProp || autoValue || \"LEGACY_REACT_AUTO_VALUE\";\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const focusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const restoreContentTabOrderRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{});\n    const wasEscapeCloseRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleContentEntry = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((side = \"start\")=>{\n        if (contentRef.current) {\n            restoreContentTabOrderRef.current();\n            const candidates = getTabbableCandidates(contentRef.current);\n            if (candidates.length) focusFirst(side === \"start\" ? candidates : candidates.reverse());\n        }\n    }, []);\n    const handleContentExit = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (contentRef.current) {\n            const candidates = getTabbableCandidates(contentRef.current);\n            if (candidates.length) restoreContentTabOrderRef.current = removeFromTabOrder(candidates);\n        }\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuItemContextProvider, {\n        scope: __scopeNavigationMenu,\n        value,\n        triggerRef,\n        contentRef,\n        focusProxyRef,\n        wasEscapeCloseRef,\n        onEntryKeyDown: handleContentEntry,\n        onFocusProxyEnter: handleContentEntry,\n        onRootContentClose: handleContentExit,\n        onContentFocusOutside: handleContentExit,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.li, {\n            ...itemProps,\n            ref: forwardedRef\n        })\n    });\n});\nNavigationMenuItem.displayName = ITEM_NAME;\nvar TRIGGER_NAME = \"NavigationMenuTrigger\";\nvar NavigationMenuTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, disabled, ...triggerProps } = props;\n    const context = useNavigationMenuContext(TRIGGER_NAME, props.__scopeNavigationMenu);\n    const itemContext = useNavigationMenuItemContext(TRIGGER_NAME, props.__scopeNavigationMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(ref, itemContext.triggerRef, forwardedRef);\n    const triggerId = makeTriggerId(context.baseId, itemContext.value);\n    const contentId = makeContentId(context.baseId, itemContext.value);\n    const hasPointerMoveOpenedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const wasClickCloseRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const open = itemContext.value === context.value;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n                scope: __scopeNavigationMenu,\n                value: itemContext.value,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupItem, {\n                    asChild: true,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.button, {\n                        id: triggerId,\n                        disabled,\n                        \"data-disabled\": disabled ? \"\" : void 0,\n                        \"data-state\": getOpenState(open),\n                        \"aria-expanded\": open,\n                        \"aria-controls\": contentId,\n                        ...triggerProps,\n                        ref: composedRefs,\n                        onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerEnter, ()=>{\n                            wasClickCloseRef.current = false;\n                            itemContext.wasEscapeCloseRef.current = false;\n                        }),\n                        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse(()=>{\n                            if (disabled || wasClickCloseRef.current || itemContext.wasEscapeCloseRef.current || hasPointerMoveOpenedRef.current) return;\n                            context.onTriggerEnter(itemContext.value);\n                            hasPointerMoveOpenedRef.current = true;\n                        })),\n                        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse(()=>{\n                            if (disabled) return;\n                            context.onTriggerLeave();\n                            hasPointerMoveOpenedRef.current = false;\n                        })),\n                        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, ()=>{\n                            context.onItemSelect(itemContext.value);\n                            wasClickCloseRef.current = open;\n                        }),\n                        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                            const verticalEntryKey = context.dir === \"rtl\" ? \"ArrowLeft\" : \"ArrowRight\";\n                            const entryKey = {\n                                horizontal: \"ArrowDown\",\n                                vertical: verticalEntryKey\n                            }[context.orientation];\n                            if (open && event.key === entryKey) {\n                                itemContext.onEntryKeyDown();\n                                event.preventDefault();\n                            }\n                        })\n                    })\n                })\n            }),\n            open && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                        \"aria-hidden\": true,\n                        tabIndex: 0,\n                        ref: itemContext.focusProxyRef,\n                        onFocus: (event)=>{\n                            const content = itemContext.contentRef.current;\n                            const prevFocusedElement = event.relatedTarget;\n                            const wasTriggerFocused = prevFocusedElement === ref.current;\n                            const wasFocusFromContent = content?.contains(prevFocusedElement);\n                            if (wasTriggerFocused || !wasFocusFromContent) {\n                                itemContext.onFocusProxyEnter(wasTriggerFocused ? \"start\" : \"end\");\n                            }\n                        }\n                    }),\n                    context.viewport && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"span\", {\n                        \"aria-owns\": contentId\n                    })\n                ]\n            })\n        ]\n    });\n});\nNavigationMenuTrigger.displayName = TRIGGER_NAME;\nvar LINK_NAME = \"NavigationMenuLink\";\nvar LINK_SELECT = \"navigationMenu.linkSelect\";\nvar NavigationMenuLink = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, active, onSelect, ...linkProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupItem, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.a, {\n            \"data-active\": active ? \"\" : void 0,\n            \"aria-current\": active ? \"page\" : void 0,\n            ...linkProps,\n            ref: forwardedRef,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, (event)=>{\n                const target = event.target;\n                const linkSelectEvent = new CustomEvent(LINK_SELECT, {\n                    bubbles: true,\n                    cancelable: true\n                });\n                target.addEventListener(LINK_SELECT, (event2)=>onSelect?.(event2), {\n                    once: true\n                });\n                (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.dispatchDiscreteCustomEvent)(target, linkSelectEvent);\n                if (!linkSelectEvent.defaultPrevented && !event.metaKey) {\n                    const rootContentDismissEvent = new CustomEvent(ROOT_CONTENT_DISMISS, {\n                        bubbles: true,\n                        cancelable: true\n                    });\n                    (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.dispatchDiscreteCustomEvent)(target, rootContentDismissEvent);\n                }\n            }, {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nNavigationMenuLink.displayName = LINK_NAME;\nvar INDICATOR_NAME = \"NavigationMenuIndicator\";\nvar NavigationMenuIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...indicatorProps } = props;\n    const context = useNavigationMenuContext(INDICATOR_NAME, props.__scopeNavigationMenu);\n    const isVisible = Boolean(context.value);\n    return context.indicatorTrack ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__.Presence, {\n        present: forceMount || isVisible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuIndicatorImpl, {\n            ...indicatorProps,\n            ref: forwardedRef\n        })\n    }), context.indicatorTrack) : null;\n});\nNavigationMenuIndicator.displayName = INDICATOR_NAME;\nvar NavigationMenuIndicatorImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, ...indicatorProps } = props;\n    const context = useNavigationMenuContext(INDICATOR_NAME, __scopeNavigationMenu);\n    const getItems = useCollection(__scopeNavigationMenu);\n    const [activeTrigger, setActiveTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [position, setPosition] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const isHorizontal = context.orientation === \"horizontal\";\n    const isVisible = Boolean(context.value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const items = getItems();\n        const triggerNode = items.find((item)=>item.value === context.value)?.ref.current;\n        if (triggerNode) setActiveTrigger(triggerNode);\n    }, [\n        getItems,\n        context.value\n    ]);\n    const handlePositionChange = ()=>{\n        if (activeTrigger) {\n            setPosition({\n                size: isHorizontal ? activeTrigger.offsetWidth : activeTrigger.offsetHeight,\n                offset: isHorizontal ? activeTrigger.offsetLeft : activeTrigger.offsetTop\n            });\n        }\n    };\n    useResizeObserver(activeTrigger, handlePositionChange);\n    useResizeObserver(context.indicatorTrack, handlePositionChange);\n    return position ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n        \"aria-hidden\": true,\n        \"data-state\": isVisible ? \"visible\" : \"hidden\",\n        \"data-orientation\": context.orientation,\n        ...indicatorProps,\n        ref: forwardedRef,\n        style: {\n            position: \"absolute\",\n            ...isHorizontal ? {\n                left: 0,\n                width: position.size + \"px\",\n                transform: `translateX(${position.offset}px)`\n            } : {\n                top: 0,\n                height: position.size + \"px\",\n                transform: `translateY(${position.offset}px)`\n            },\n            ...indicatorProps.style\n        }\n    }) : null;\n});\nvar CONTENT_NAME = \"NavigationMenuContent\";\nvar NavigationMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...contentProps } = props;\n    const context = useNavigationMenuContext(CONTENT_NAME, props.__scopeNavigationMenu);\n    const itemContext = useNavigationMenuItemContext(CONTENT_NAME, props.__scopeNavigationMenu);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(itemContext.contentRef, forwardedRef);\n    const open = itemContext.value === context.value;\n    const commonProps = {\n        value: itemContext.value,\n        triggerRef: itemContext.triggerRef,\n        focusProxyRef: itemContext.focusProxyRef,\n        wasEscapeCloseRef: itemContext.wasEscapeCloseRef,\n        onContentFocusOutside: itemContext.onContentFocusOutside,\n        onRootContentClose: itemContext.onRootContentClose,\n        ...contentProps\n    };\n    return !context.viewport ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__.Presence, {\n        present: forceMount || open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuContentImpl, {\n            \"data-state\": getOpenState(open),\n            ...commonProps,\n            ref: composedRefs,\n            onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerEnter, context.onContentEnter),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse(context.onContentLeave)),\n            style: {\n                // Prevent interaction when animating out\n                pointerEvents: !open && context.isRootMenu ? \"none\" : void 0,\n                ...commonProps.style\n            }\n        })\n    }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ViewportContentMounter, {\n        forceMount,\n        ...commonProps,\n        ref: composedRefs\n    });\n});\nNavigationMenuContent.displayName = CONTENT_NAME;\nvar ViewportContentMounter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useNavigationMenuContext(CONTENT_NAME, props.__scopeNavigationMenu);\n    const { onViewportContentChange, onViewportContentRemove } = context;\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_15__.useLayoutEffect)(()=>{\n        onViewportContentChange(props.value, {\n            ref: forwardedRef,\n            ...props\n        });\n    }, [\n        props,\n        forwardedRef,\n        onViewportContentChange\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_15__.useLayoutEffect)(()=>{\n        return ()=>onViewportContentRemove(props.value);\n    }, [\n        props.value,\n        onViewportContentRemove\n    ]);\n    return null;\n});\nvar ROOT_CONTENT_DISMISS = \"navigationMenu.rootContentDismiss\";\nvar NavigationMenuContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, value, triggerRef, focusProxyRef, wasEscapeCloseRef, onRootContentClose, onContentFocusOutside, ...contentProps } = props;\n    const context = useNavigationMenuContext(CONTENT_NAME, __scopeNavigationMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(ref, forwardedRef);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const getItems = useCollection(__scopeNavigationMenu);\n    const prevMotionAttributeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { onItemDismiss } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const content = ref.current;\n        if (context.isRootMenu && content) {\n            const handleClose = ()=>{\n                onItemDismiss();\n                onRootContentClose();\n                if (content.contains(document.activeElement)) triggerRef.current?.focus();\n            };\n            content.addEventListener(ROOT_CONTENT_DISMISS, handleClose);\n            return ()=>content.removeEventListener(ROOT_CONTENT_DISMISS, handleClose);\n        }\n    }, [\n        context.isRootMenu,\n        props.value,\n        triggerRef,\n        onItemDismiss,\n        onRootContentClose\n    ]);\n    const motionAttribute = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const items = getItems();\n        const values = items.map((item)=>item.value);\n        if (context.dir === \"rtl\") values.reverse();\n        const index = values.indexOf(context.value);\n        const prevIndex = values.indexOf(context.previousValue);\n        const isSelected = value === context.value;\n        const wasSelected = prevIndex === values.indexOf(value);\n        if (!isSelected && !wasSelected) return prevMotionAttributeRef.current;\n        const attribute = (()=>{\n            if (index !== prevIndex) {\n                if (isSelected && prevIndex !== -1) return index > prevIndex ? \"from-end\" : \"from-start\";\n                if (wasSelected && index !== -1) return index > prevIndex ? \"to-start\" : \"to-end\";\n            }\n            return null;\n        })();\n        prevMotionAttributeRef.current = attribute;\n        return attribute;\n    }, [\n        context.previousValue,\n        context.value,\n        context.dir,\n        getItems,\n        value\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroup, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_16__.DismissableLayer, {\n            id: contentId,\n            \"aria-labelledby\": triggerId,\n            \"data-motion\": motionAttribute,\n            \"data-orientation\": context.orientation,\n            ...contentProps,\n            ref: composedRefs,\n            disableOutsidePointerEvents: false,\n            onDismiss: ()=>{\n                const rootContentDismissEvent = new Event(ROOT_CONTENT_DISMISS, {\n                    bubbles: true,\n                    cancelable: true\n                });\n                ref.current?.dispatchEvent(rootContentDismissEvent);\n            },\n            onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocusOutside, (event)=>{\n                onContentFocusOutside();\n                const target = event.target;\n                if (context.rootNavigationMenu?.contains(target)) event.preventDefault();\n            }),\n            onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n                const target = event.target;\n                const isTrigger = getItems().some((item)=>item.ref.current?.contains(target));\n                const isRootViewport = context.isRootMenu && context.viewport?.contains(target);\n                if (isTrigger || isRootViewport || !context.isRootMenu) event.preventDefault();\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n                const isTabKey = event.key === \"Tab\" && !isMetaKey;\n                if (isTabKey) {\n                    const candidates = getTabbableCandidates(event.currentTarget);\n                    const focusedElement = document.activeElement;\n                    const index = candidates.findIndex((candidate)=>candidate === focusedElement);\n                    const isMovingBackwards = event.shiftKey;\n                    const nextCandidates = isMovingBackwards ? candidates.slice(0, index).reverse() : candidates.slice(index + 1, candidates.length);\n                    if (focusFirst(nextCandidates)) {\n                        event.preventDefault();\n                    } else {\n                        focusProxyRef.current?.focus();\n                    }\n                }\n            }),\n            onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onEscapeKeyDown, (event)=>{\n                wasEscapeCloseRef.current = true;\n            })\n        })\n    });\n});\nvar VIEWPORT_NAME = \"NavigationMenuViewport\";\nvar NavigationMenuViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...viewportProps } = props;\n    const context = useNavigationMenuContext(VIEWPORT_NAME, props.__scopeNavigationMenu);\n    const open = Boolean(context.value);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__.Presence, {\n        present: forceMount || open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuViewportImpl, {\n            ...viewportProps,\n            ref: forwardedRef\n        })\n    });\n});\nNavigationMenuViewport.displayName = VIEWPORT_NAME;\nvar NavigationMenuViewportImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, children, ...viewportImplProps } = props;\n    const context = useNavigationMenuContext(VIEWPORT_NAME, __scopeNavigationMenu);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.onViewportChange);\n    const viewportContentContext = useViewportContentContext(CONTENT_NAME, props.__scopeNavigationMenu);\n    const [size, setSize] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const viewportWidth = size ? size?.width + \"px\" : void 0;\n    const viewportHeight = size ? size?.height + \"px\" : void 0;\n    const open = Boolean(context.value);\n    const activeContentValue = open ? context.value : context.previousValue;\n    const handleSizeChange = ()=>{\n        if (content) setSize({\n            width: content.offsetWidth,\n            height: content.offsetHeight\n        });\n    };\n    useResizeObserver(content, handleSizeChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n        \"data-state\": getOpenState(open),\n        \"data-orientation\": context.orientation,\n        ...viewportImplProps,\n        ref: composedRefs,\n        style: {\n            // Prevent interaction when animating out\n            pointerEvents: !open && context.isRootMenu ? \"none\" : void 0,\n            [\"--radix-navigation-menu-viewport-width\"]: viewportWidth,\n            [\"--radix-navigation-menu-viewport-height\"]: viewportHeight,\n            ...viewportImplProps.style\n        },\n        onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerEnter, context.onContentEnter),\n        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse(context.onContentLeave)),\n        children: Array.from(viewportContentContext.items).map(([value, { ref, forceMount, ...props2 }])=>{\n            const isActive = activeContentValue === value;\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__.Presence, {\n                present: forceMount || isActive,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuContentImpl, {\n                    ...props2,\n                    ref: (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.composeRefs)(ref, (node)=>{\n                        if (isActive && node) setContent(node);\n                    })\n                })\n            }, value);\n        })\n    });\n});\nvar FOCUS_GROUP_NAME = \"FocusGroup\";\nvar FocusGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, ...groupProps } = props;\n    const context = useNavigationMenuContext(FOCUS_GROUP_NAME, __scopeNavigationMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupCollection.Provider, {\n        scope: __scopeNavigationMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupCollection.Slot, {\n            scope: __scopeNavigationMenu,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n                dir: context.dir,\n                ...groupProps,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nvar ARROW_KEYS = [\n    \"ArrowRight\",\n    \"ArrowLeft\",\n    \"ArrowUp\",\n    \"ArrowDown\"\n];\nvar FOCUS_GROUP_ITEM_NAME = \"FocusGroupItem\";\nvar FocusGroupItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, ...groupProps } = props;\n    const getItems = useFocusGroupCollection(__scopeNavigationMenu);\n    const context = useNavigationMenuContext(FOCUS_GROUP_ITEM_NAME, __scopeNavigationMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupCollection.ItemSlot, {\n        scope: __scopeNavigationMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.button, {\n            ...groupProps,\n            ref: forwardedRef,\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                const isFocusNavigationKey = [\n                    \"Home\",\n                    \"End\",\n                    ...ARROW_KEYS\n                ].includes(event.key);\n                if (isFocusNavigationKey) {\n                    let candidateNodes = getItems().map((item)=>item.ref.current);\n                    const prevItemKey = context.dir === \"rtl\" ? \"ArrowRight\" : \"ArrowLeft\";\n                    const prevKeys = [\n                        prevItemKey,\n                        \"ArrowUp\",\n                        \"End\"\n                    ];\n                    if (prevKeys.includes(event.key)) candidateNodes.reverse();\n                    if (ARROW_KEYS.includes(event.key)) {\n                        const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                        candidateNodes = candidateNodes.slice(currentIndex + 1);\n                    }\n                    setTimeout(()=>focusFirst(candidateNodes));\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction focusFirst(candidates) {\n    const previouslyFocusedElement = document.activeElement;\n    return candidates.some((candidate)=>{\n        if (candidate === previouslyFocusedElement) return true;\n        candidate.focus();\n        return document.activeElement !== previouslyFocusedElement;\n    });\n}\nfunction removeFromTabOrder(candidates) {\n    candidates.forEach((candidate)=>{\n        candidate.dataset.tabindex = candidate.getAttribute(\"tabindex\") || \"\";\n        candidate.setAttribute(\"tabindex\", \"-1\");\n    });\n    return ()=>{\n        candidates.forEach((candidate)=>{\n            const prevTabIndex = candidate.dataset.tabindex;\n            candidate.setAttribute(\"tabindex\", prevTabIndex);\n        });\n    };\n}\nfunction useResizeObserver(element, onResize) {\n    const handleResize = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onResize);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_15__.useLayoutEffect)(()=>{\n        let rAF = 0;\n        if (element) {\n            const resizeObserver = new ResizeObserver(()=>{\n                cancelAnimationFrame(rAF);\n                rAF = window.requestAnimationFrame(handleResize);\n            });\n            resizeObserver.observe(element);\n            return ()=>{\n                window.cancelAnimationFrame(rAF);\n                resizeObserver.unobserve(element);\n            };\n        }\n    }, [\n        element,\n        handleResize\n    ]);\n}\nfunction getOpenState(open) {\n    return open ? \"open\" : \"closed\";\n}\nfunction makeTriggerId(baseId, value) {\n    return `${baseId}-trigger-${value}`;\n}\nfunction makeContentId(baseId, value) {\n    return `${baseId}-content-${value}`;\n}\nfunction whenMouse(handler) {\n    return (event)=>event.pointerType === \"mouse\" ? handler(event) : void 0;\n}\nvar Root2 = NavigationMenu;\nvar Sub = NavigationMenuSub;\nvar List = NavigationMenuList;\nvar Item = NavigationMenuItem;\nvar Trigger = NavigationMenuTrigger;\nvar Link = NavigationMenuLink;\nvar Indicator = NavigationMenuIndicator;\nvar Content = NavigationMenuContent;\nvar Viewport = NavigationMenuViewport;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Portal,Root auto */ // packages/react/portal/src/Portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { container: containerProp, ...portalProps } = props;\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(()=>setMounted(true), []);\n    const container = containerProp || mounted && globalThis?.document?.body;\n    return container ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...portalProps,\n        ref: forwardedRef\n    }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence auto */ // packages/react/presence/src/Presence.tsx\n\n\n\n// packages/react/presence/src/useStateMachine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState ?? state;\n    }, initialState);\n}\n// packages/react/presence/src/Presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef({});\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n    }, [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        const styles = stylesRef.current;\n        const wasPresent = prevPresentRef.current;\n        const hasPresentChanged = wasPresent !== present;\n        if (hasPresentChanged) {\n            const prevAnimationName = prevAnimationNameRef.current;\n            const currentAnimationName = getAnimationName(styles);\n            if (present) {\n                send(\"MOUNT\");\n            } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                send(\"UNMOUNT\");\n            } else {\n                const isAnimating = prevAnimationName !== currentAnimationName;\n                if (wasPresent && isAnimating) {\n                    send(\"ANIMATION_OUT\");\n                } else {\n                    send(\"UNMOUNT\");\n                }\n            }\n            prevPresentRef.current = present;\n        }\n    }, [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        if (node) {\n            let timeoutId;\n            const ownerWindow = node.ownerDocument.defaultView ?? window;\n            const handleAnimationEnd = (event)=>{\n                const currentAnimationName = getAnimationName(stylesRef.current);\n                const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                if (event.target === node && isCurrentAnimation) {\n                    send(\"ANIMATION_END\");\n                    if (!prevPresentRef.current) {\n                        const currentFillMode = node.style.animationFillMode;\n                        node.style.animationFillMode = \"forwards\";\n                        timeoutId = ownerWindow.setTimeout(()=>{\n                            if (node.style.animationFillMode === \"forwards\") {\n                                node.style.animationFillMode = currentFillMode;\n                            }\n                        });\n                    }\n                }\n            };\n            const handleAnimationStart = (event)=>{\n                if (event.target === node) {\n                    prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                }\n            };\n            node.addEventListener(\"animationstart\", handleAnimationStart);\n            node.addEventListener(\"animationcancel\", handleAnimationEnd);\n            node.addEventListener(\"animationend\", handleAnimationEnd);\n            return ()=>{\n                ownerWindow.clearTimeout(timeoutId);\n                node.removeEventListener(\"animationstart\", handleAnimationStart);\n                node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                node.removeEventListener(\"animationend\", handleAnimationEnd);\n            };\n        } else {\n            send(\"ANIMATION_END\");\n        }\n    }, [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node2)=>{\n            if (node2) stylesRef.current = getComputedStyle(node2);\n            setNode(node2);\n        }, [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/primitive/src/Primitive.tsx\n\n\n\n\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Node = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUE7QUFDK0I7QUFDTztBQUNNO0FBQ0o7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLDZDQUFnQjtBQUMvQixZQUFZLDZCQUE2QjtBQUN6QywyQkFBMkIsc0RBQUk7QUFDL0I7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLHNEQUFHLFNBQVMsc0NBQXNDO0FBQzdFLEdBQUc7QUFDSCxrQ0FBa0MsS0FBSztBQUN2QyxXQUFXO0FBQ1gsQ0FBQyxJQUFJO0FBQ0w7QUFDQSxjQUFjLGdEQUFrQjtBQUNoQztBQUNBO0FBS0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGVydmlld2NyYWNrZXIvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcz9lZGRiIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3ByaW1pdGl2ZS9zcmMvUHJpbWl0aXZlLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgKiBhcyBSZWFjdERPTSBmcm9tIFwicmVhY3QtZG9tXCI7XG5pbXBvcnQgeyBTbG90IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1zbG90XCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbnZhciBOT0RFUyA9IFtcbiAgXCJhXCIsXG4gIFwiYnV0dG9uXCIsXG4gIFwiZGl2XCIsXG4gIFwiZm9ybVwiLFxuICBcImgyXCIsXG4gIFwiaDNcIixcbiAgXCJpbWdcIixcbiAgXCJpbnB1dFwiLFxuICBcImxhYmVsXCIsXG4gIFwibGlcIixcbiAgXCJuYXZcIixcbiAgXCJvbFwiLFxuICBcInBcIixcbiAgXCJzcGFuXCIsXG4gIFwic3ZnXCIsXG4gIFwidWxcIlxuXTtcbnZhciBQcmltaXRpdmUgPSBOT0RFUy5yZWR1Y2UoKHByaW1pdGl2ZSwgbm9kZSkgPT4ge1xuICBjb25zdCBOb2RlID0gUmVhY3QuZm9yd2FyZFJlZigocHJvcHMsIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHsgYXNDaGlsZCwgLi4ucHJpbWl0aXZlUHJvcHMgfSA9IHByb3BzO1xuICAgIGNvbnN0IENvbXAgPSBhc0NoaWxkID8gU2xvdCA6IG5vZGU7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgIHdpbmRvd1tTeW1ib2wuZm9yKFwicmFkaXgtdWlcIildID0gdHJ1ZTtcbiAgICB9XG4gICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goQ29tcCwgeyAuLi5wcmltaXRpdmVQcm9wcywgcmVmOiBmb3J3YXJkZWRSZWYgfSk7XG4gIH0pO1xuICBOb2RlLmRpc3BsYXlOYW1lID0gYFByaW1pdGl2ZS4ke25vZGV9YDtcbiAgcmV0dXJuIHsgLi4ucHJpbWl0aXZlLCBbbm9kZV06IE5vZGUgfTtcbn0sIHt9KTtcbmZ1bmN0aW9uIGRpc3BhdGNoRGlzY3JldGVDdXN0b21FdmVudCh0YXJnZXQsIGV2ZW50KSB7XG4gIGlmICh0YXJnZXQpIFJlYWN0RE9NLmZsdXNoU3luYygoKSA9PiB0YXJnZXQuZGlzcGF0Y2hFdmVudChldmVudCkpO1xufVxudmFyIFJvb3QgPSBQcmltaXRpdmU7XG5leHBvcnQge1xuICBQcmltaXRpdmUsXG4gIFJvb3QsXG4gIGRpc3BhdGNoRGlzY3JldGVDdXN0b21FdmVudFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/slot/src/Slot.tsx\n\n\n\nvar Slot = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n  if (slottable) {\n    const newElement = slottable.props.children;\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n        return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n      } else {\n        return child;\n      }\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n});\nSlot.displayName = \"Slot\";\nvar SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      // @ts-ignore\n      ref: forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n});\nSlotClone.displayName = \"SlotClone\";\nvar Slottable = ({ children }) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n};\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && child.type === Slottable;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Slot;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/useCallbackRef.tsx\n\nfunction useCallbackRef(callback) {\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBLHNCQUFzQix5Q0FBWTtBQUNsQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50ZXJ2aWV3Y3JhY2tlci8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZi9kaXN0L2luZGV4Lm1qcz8yODdhIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1jYWxsYmFjay1yZWYvc3JjL3VzZUNhbGxiYWNrUmVmLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VDYWxsYmFja1JlZihjYWxsYmFjaykge1xuICBjb25zdCBjYWxsYmFja1JlZiA9IFJlYWN0LnVzZVJlZihjYWxsYmFjayk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY2FsbGJhY2tSZWYuY3VycmVudCA9IGNhbGxiYWNrO1xuICB9KTtcbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4gKC4uLmFyZ3MpID0+IGNhbGxiYWNrUmVmLmN1cnJlbnQ/LiguLi5hcmdzKSwgW10pO1xufVxuZXhwb3J0IHtcbiAgdXNlQ2FsbGJhY2tSZWZcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-controllable-state/src/useControllableState.tsx\n\n\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  }\n}) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({ defaultProp, onChange });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onChange);\n  const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const setter = nextValue;\n        const value2 = typeof nextValue === \"function\" ? setter(prop) : nextValue;\n        if (value2 !== prop) handleChange(value2);\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, handleChange]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const uncontrolledState = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n  const handleChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onChange);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n  return uncontrolledState;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jb250cm9sbGFibGUtc3RhdGUvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDK0I7QUFDbUM7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCx5RUFBeUUsdUJBQXVCO0FBQ2hHO0FBQ0E7QUFDQSx1QkFBdUIsZ0ZBQWM7QUFDckMsbUJBQW1CLDhDQUFpQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0QsNEJBQTRCLDJDQUFjO0FBQzFDO0FBQ0EsdUJBQXVCLHlDQUFZO0FBQ25DLHVCQUF1QixnRkFBYztBQUNyQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50ZXJ2aWV3Y3JhY2tlci8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWNvbnRyb2xsYWJsZS1zdGF0ZS9kaXN0L2luZGV4Lm1qcz9jZjY3Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1jb250cm9sbGFibGUtc3RhdGUvc3JjL3VzZUNvbnRyb2xsYWJsZVN0YXRlLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyB1c2VDYWxsYmFja1JlZiB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZlwiO1xuZnVuY3Rpb24gdXNlQ29udHJvbGxhYmxlU3RhdGUoe1xuICBwcm9wLFxuICBkZWZhdWx0UHJvcCxcbiAgb25DaGFuZ2UgPSAoKSA9PiB7XG4gIH1cbn0pIHtcbiAgY29uc3QgW3VuY29udHJvbGxlZFByb3AsIHNldFVuY29udHJvbGxlZFByb3BdID0gdXNlVW5jb250cm9sbGVkU3RhdGUoeyBkZWZhdWx0UHJvcCwgb25DaGFuZ2UgfSk7XG4gIGNvbnN0IGlzQ29udHJvbGxlZCA9IHByb3AgIT09IHZvaWQgMDtcbiAgY29uc3QgdmFsdWUgPSBpc0NvbnRyb2xsZWQgPyBwcm9wIDogdW5jb250cm9sbGVkUHJvcDtcbiAgY29uc3QgaGFuZGxlQ2hhbmdlID0gdXNlQ2FsbGJhY2tSZWYob25DaGFuZ2UpO1xuICBjb25zdCBzZXRWYWx1ZSA9IFJlYWN0LnVzZUNhbGxiYWNrKFxuICAgIChuZXh0VmFsdWUpID0+IHtcbiAgICAgIGlmIChpc0NvbnRyb2xsZWQpIHtcbiAgICAgICAgY29uc3Qgc2V0dGVyID0gbmV4dFZhbHVlO1xuICAgICAgICBjb25zdCB2YWx1ZTIgPSB0eXBlb2YgbmV4dFZhbHVlID09PSBcImZ1bmN0aW9uXCIgPyBzZXR0ZXIocHJvcCkgOiBuZXh0VmFsdWU7XG4gICAgICAgIGlmICh2YWx1ZTIgIT09IHByb3ApIGhhbmRsZUNoYW5nZSh2YWx1ZTIpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0VW5jb250cm9sbGVkUHJvcChuZXh0VmFsdWUpO1xuICAgICAgfVxuICAgIH0sXG4gICAgW2lzQ29udHJvbGxlZCwgcHJvcCwgc2V0VW5jb250cm9sbGVkUHJvcCwgaGFuZGxlQ2hhbmdlXVxuICApO1xuICByZXR1cm4gW3ZhbHVlLCBzZXRWYWx1ZV07XG59XG5mdW5jdGlvbiB1c2VVbmNvbnRyb2xsZWRTdGF0ZSh7XG4gIGRlZmF1bHRQcm9wLFxuICBvbkNoYW5nZVxufSkge1xuICBjb25zdCB1bmNvbnRyb2xsZWRTdGF0ZSA9IFJlYWN0LnVzZVN0YXRlKGRlZmF1bHRQcm9wKTtcbiAgY29uc3QgW3ZhbHVlXSA9IHVuY29udHJvbGxlZFN0YXRlO1xuICBjb25zdCBwcmV2VmFsdWVSZWYgPSBSZWFjdC51c2VSZWYodmFsdWUpO1xuICBjb25zdCBoYW5kbGVDaGFuZ2UgPSB1c2VDYWxsYmFja1JlZihvbkNoYW5nZSk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHByZXZWYWx1ZVJlZi5jdXJyZW50ICE9PSB2YWx1ZSkge1xuICAgICAgaGFuZGxlQ2hhbmdlKHZhbHVlKTtcbiAgICAgIHByZXZWYWx1ZVJlZi5jdXJyZW50ID0gdmFsdWU7XG4gICAgfVxuICB9LCBbdmFsdWUsIHByZXZWYWx1ZVJlZiwgaGFuZGxlQ2hhbmdlXSk7XG4gIHJldHVybiB1bmNvbnRyb2xsZWRTdGF0ZTtcbn1cbmV4cG9ydCB7XG4gIHVzZUNvbnRyb2xsYWJsZVN0YXRlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ useEscapeKeydown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-escape-keydown/src/useEscapeKeydown.tsx\n\n\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lc2NhcGUta2V5ZG93bi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUNtQztBQUNsRTtBQUNBLDBCQUEwQixnRkFBYztBQUN4QyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrREFBK0QsZUFBZTtBQUM5RSwrRUFBK0UsZUFBZTtBQUM5RixHQUFHO0FBQ0g7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50ZXJ2aWV3Y3JhY2tlci8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWVzY2FwZS1rZXlkb3duL2Rpc3QvaW5kZXgubWpzPzJmYzYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWVzY2FwZS1rZXlkb3duL3NyYy91c2VFc2NhcGVLZXlkb3duLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyB1c2VDYWxsYmFja1JlZiB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZlwiO1xuZnVuY3Rpb24gdXNlRXNjYXBlS2V5ZG93bihvbkVzY2FwZUtleURvd25Qcm9wLCBvd25lckRvY3VtZW50ID0gZ2xvYmFsVGhpcz8uZG9jdW1lbnQpIHtcbiAgY29uc3Qgb25Fc2NhcGVLZXlEb3duID0gdXNlQ2FsbGJhY2tSZWYob25Fc2NhcGVLZXlEb3duUHJvcCk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlS2V5RG93biA9IChldmVudCkgPT4ge1xuICAgICAgaWYgKGV2ZW50LmtleSA9PT0gXCJFc2NhcGVcIikge1xuICAgICAgICBvbkVzY2FwZUtleURvd24oZXZlbnQpO1xuICAgICAgfVxuICAgIH07XG4gICAgb3duZXJEb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKFwia2V5ZG93blwiLCBoYW5kbGVLZXlEb3duLCB7IGNhcHR1cmU6IHRydWUgfSk7XG4gICAgcmV0dXJuICgpID0+IG93bmVyRG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImtleWRvd25cIiwgaGFuZGxlS2V5RG93biwgeyBjYXB0dXJlOiB0cnVlIH0pO1xuICB9LCBbb25Fc2NhcGVLZXlEb3duLCBvd25lckRvY3VtZW50XSk7XG59XG5leHBvcnQge1xuICB1c2VFc2NhcGVLZXlkb3duXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/useLayoutEffect.tsx\n\nvar useLayoutEffect2 = Boolean(globalThis?.document) ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : () => {\n};\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDK0I7QUFDL0IsdURBQXVELGtEQUFxQjtBQUM1RTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRlcnZpZXdjcmFja2VyLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdC9kaXN0L2luZGV4Lm1qcz9hYzU2Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1sYXlvdXQtZWZmZWN0L3NyYy91c2VMYXlvdXRFZmZlY3QudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbnZhciB1c2VMYXlvdXRFZmZlY3QyID0gQm9vbGVhbihnbG9iYWxUaGlzPy5kb2N1bWVudCkgPyBSZWFjdC51c2VMYXlvdXRFZmZlY3QgOiAoKSA9PiB7XG59O1xuZXhwb3J0IHtcbiAgdXNlTGF5b3V0RWZmZWN0MiBhcyB1c2VMYXlvdXRFZmZlY3Rcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-previous/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePrevious: () => (/* binding */ usePrevious)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-previous/src/usePrevious.tsx\n\nfunction usePrevious(value) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef({ value, previous: value });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1wcmV2aW91cy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQytCO0FBQy9CO0FBQ0EsY0FBYyx5Q0FBWSxHQUFHLHdCQUF3QjtBQUNyRCxTQUFTLDBDQUFhO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW50ZXJ2aWV3Y3JhY2tlci8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLXByZXZpb3VzL2Rpc3QvaW5kZXgubWpzP2VlODkiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLXByZXZpb3VzL3NyYy91c2VQcmV2aW91cy50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlUHJldmlvdXModmFsdWUpIHtcbiAgY29uc3QgcmVmID0gUmVhY3QudXNlUmVmKHsgdmFsdWUsIHByZXZpb3VzOiB2YWx1ZSB9KTtcbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4ge1xuICAgIGlmIChyZWYuY3VycmVudC52YWx1ZSAhPT0gdmFsdWUpIHtcbiAgICAgIHJlZi5jdXJyZW50LnByZXZpb3VzID0gcmVmLmN1cnJlbnQudmFsdWU7XG4gICAgICByZWYuY3VycmVudC52YWx1ZSA9IHZhbHVlO1xuICAgIH1cbiAgICByZXR1cm4gcmVmLmN1cnJlbnQucHJldmlvdXM7XG4gIH0sIFt2YWx1ZV0pO1xufVxuZXhwb3J0IHtcbiAgdXNlUHJldmlvdXNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   VisuallyHidden: () => (/* binding */ VisuallyHidden)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/visually-hidden/src/VisuallyHidden.tsx\n\n\n\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.span,\n      {\n        ...props,\n        ref: forwardedRef,\n        style: {\n          // See: https://github.com/twbs/bootstrap/blob/master/scss/mixins/_screen-reader.scss\n          position: \"absolute\",\n          border: 0,\n          width: 1,\n          height: 1,\n          padding: 0,\n          margin: -1,\n          overflow: \"hidden\",\n          clip: \"rect(0, 0, 0, 0)\",\n          whiteSpace: \"nowrap\",\n          wordWrap: \"normal\",\n          ...props.style\n        }\n      }\n    );\n  }\n);\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXZpc3VhbGx5LWhpZGRlbi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQytCO0FBQ3VCO0FBQ2Q7QUFDeEM7QUFDQSxxQkFBcUIsNkNBQWdCO0FBQ3JDO0FBQ0EsMkJBQTJCLHNEQUFHO0FBQzlCLE1BQU0sZ0VBQVM7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBSUU7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2ludGVydmlld2NyYWNrZXIvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXZpc3VhbGx5LWhpZGRlbi9kaXN0L2luZGV4Lm1qcz8zMDYyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3Zpc3VhbGx5LWhpZGRlbi9zcmMvVmlzdWFsbHlIaWRkZW4udHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IFByaW1pdGl2ZSB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtcHJpbWl0aXZlXCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbnZhciBOQU1FID0gXCJWaXN1YWxseUhpZGRlblwiO1xudmFyIFZpc3VhbGx5SGlkZGVuID0gUmVhY3QuZm9yd2FyZFJlZihcbiAgKHByb3BzLCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChcbiAgICAgIFByaW1pdGl2ZS5zcGFuLFxuICAgICAge1xuICAgICAgICAuLi5wcm9wcyxcbiAgICAgICAgcmVmOiBmb3J3YXJkZWRSZWYsXG4gICAgICAgIHN0eWxlOiB7XG4gICAgICAgICAgLy8gU2VlOiBodHRwczovL2dpdGh1Yi5jb20vdHdicy9ib290c3RyYXAvYmxvYi9tYXN0ZXIvc2Nzcy9taXhpbnMvX3NjcmVlbi1yZWFkZXIuc2Nzc1xuICAgICAgICAgIHBvc2l0aW9uOiBcImFic29sdXRlXCIsXG4gICAgICAgICAgYm9yZGVyOiAwLFxuICAgICAgICAgIHdpZHRoOiAxLFxuICAgICAgICAgIGhlaWdodDogMSxcbiAgICAgICAgICBwYWRkaW5nOiAwLFxuICAgICAgICAgIG1hcmdpbjogLTEsXG4gICAgICAgICAgb3ZlcmZsb3c6IFwiaGlkZGVuXCIsXG4gICAgICAgICAgY2xpcDogXCJyZWN0KDAsIDAsIDAsIDApXCIsXG4gICAgICAgICAgd2hpdGVTcGFjZTogXCJub3dyYXBcIixcbiAgICAgICAgICB3b3JkV3JhcDogXCJub3JtYWxcIixcbiAgICAgICAgICAuLi5wcm9wcy5zdHlsZVxuICAgICAgICB9XG4gICAgICB9XG4gICAgKTtcbiAgfVxuKTtcblZpc3VhbGx5SGlkZGVuLmRpc3BsYXlOYW1lID0gTkFNRTtcbnZhciBSb290ID0gVmlzdWFsbHlIaWRkZW47XG5leHBvcnQge1xuICBSb290LFxuICBWaXN1YWxseUhpZGRlblxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n// packages/react/compose-refs/src/composeRefs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => refs.forEach((ref) => setRef(ref, node));\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmcy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLDhDQUFpQjtBQUMxQjtBQUlFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnRlcnZpZXdjcmFja2VyLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1jb21wb3NlLXJlZnMvZGlzdC9pbmRleC5tanM/ZmJkYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC9jb21wb3NlLXJlZnMvc3JjL2NvbXBvc2VSZWZzLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiBzZXRSZWYocmVmLCB2YWx1ZSkge1xuICBpZiAodHlwZW9mIHJlZiA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgcmVmKHZhbHVlKTtcbiAgfSBlbHNlIGlmIChyZWYgIT09IG51bGwgJiYgcmVmICE9PSB2b2lkIDApIHtcbiAgICByZWYuY3VycmVudCA9IHZhbHVlO1xuICB9XG59XG5mdW5jdGlvbiBjb21wb3NlUmVmcyguLi5yZWZzKSB7XG4gIHJldHVybiAobm9kZSkgPT4gcmVmcy5mb3JFYWNoKChyZWYpID0+IHNldFJlZihyZWYsIG5vZGUpKTtcbn1cbmZ1bmN0aW9uIHVzZUNvbXBvc2VkUmVmcyguLi5yZWZzKSB7XG4gIHJldHVybiBSZWFjdC51c2VDYWxsYmFjayhjb21wb3NlUmVmcyguLi5yZWZzKSwgcmVmcyk7XG59XG5leHBvcnQge1xuICBjb21wb3NlUmVmcyxcbiAgdXNlQ29tcG9zZWRSZWZzXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(rsc)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\n// packages/react/slot/src/Slot.tsx\n\n\n\nvar Slot = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n  if (slottable) {\n    const newElement = slottable.props.children;\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n        return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n      } else {\n        return child;\n      }\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n});\nSlot.displayName = \"Slot\";\nvar SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      // @ts-ignore\n      ref: forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n});\nSlotClone.displayName = \"SlotClone\";\nvar Slottable = ({ children }) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n};\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && child.type === Slottable;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Slot;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ })

};
;