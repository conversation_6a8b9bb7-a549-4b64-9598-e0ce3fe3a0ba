"use client";

import { useUser } from "@clerk/nextjs";
import { Button, Input, Card, Table, TableHeader, TableColumn, TableBody, TableRow, TableCell, Pagination, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, useDisclosure, Switch, Select, SelectItem } from "@nextui-org/react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { FiSearch, FiX, FiEdit, FiTrash2, FiUsers, FiDollarSign, FiStar, FiBell } from "react-icons/fi";
import { toast } from "sonner";



export default function AdminPage() {
    // eslint-disable-next-line no-unused-vars
    const { isLoaded, user, isSignedIn } = useUser();
    const router = useRouter();
    const [searchTerm, setSearchTerm] = useState("");
    const [searchResults, setSearchResults] = useState<any[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [page, setPage] = useState(1);
    const rowsPerPage = 10;
    const [isAdmin, setIsAdmin] = useState(false);
    const [checkingAdmin, setCheckingAdmin] = useState(true);
    const [selectedSubscriptionType, setSelectedSubscriptionType] = useState("all");
    const [userStats, setUserStats] = useState({
        totalUsers: 0,
        premiumUsers: 0,
        freeUsers: 0,
        monthlyPlanUsers: 0,
        yearlyPlanUsers: 0,
        weeklyPlanUsers: 0
    });

    const { isOpen, onOpen, onClose } = useDisclosure();
    const [currentUser, setCurrentUser] = useState<any>(null);
    const [isPremium, setIsPremium] = useState(false);
    const [subscriptionPlan, setSubscriptionPlan] = useState("");
    const [startDate, setStartDate] = useState("");
    const [endDate, setEndDate] = useState("");
    const [promptsUsed, setPromptsUsed] = useState(0);

    const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();
    const [userToDelete, setUserToDelete] = useState<any>(null);

    useEffect(() => {
        // Ensure we only run on the client side
        if (typeof window === 'undefined') return;

        const checkAdminStatus = async () => {
            if (!isLoaded || !isSignedIn) return;

            try {
                // Add cache-busting parameter and specific headers
                const response = await fetch('/api/user/me?' + new URLSearchParams({
                    _ts: Date.now().toString()
                }), {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-invoke-app-router': 'true'
                    },
                    credentials: 'include'
                });

                if (response.ok) {
                    const userData = await response.json();
                    if (userData && userData.isAdmin === true) {
                        setIsAdmin(true);
                        // Fetch user statistics once verified as admin
                        fetchUserStats();
                    } else {
                        router.push("/dashboard");
                    }
                } else {
                    router.push("/dashboard");
                }
            } catch (error) {
                console.error("Error checking admin status:", error);
                router.push("/dashboard");
            } finally {
                setCheckingAdmin(false);
            }
        };

        if (isLoaded && isSignedIn) {
            checkAdminStatus();
        } else if (isLoaded && !isSignedIn) {
            router.push("/login");
        }
    }, [isLoaded, isSignedIn, router]);

    const fetchUserStats = async () => {
        try {
            const response = await fetch('/api/admin/user-stats');
            if (response.ok) {
                const data = await response.json();
                setUserStats({
                    totalUsers: data.totalUsers || 0,
                    premiumUsers: data.premiumUsers || 0,
                    freeUsers: data.freeUsers || 0,
                    monthlyPlanUsers: data.monthlyPlanUsers || 0,
                    yearlyPlanUsers: data.yearlyPlanUsers || 0,
                    weeklyPlanUsers: data.weeklyPlanUsers || 0
                });
            } else {
                console.error("Failed to fetch user statistics");
            }
        } catch (error) {
            console.error("Error fetching user statistics:", error);
        }
    };

    const handleSearch = async () => {
        // Allow empty search when filtering by subscription type
        if (!searchTerm.trim() && selectedSubscriptionType === "all") return;

        setIsLoading(true);
        try {
            const params = new URLSearchParams();

            // Add search term if provided
            if (searchTerm.trim()) {
                params.append("query", searchTerm);
            } else {
                // If no search term, show all users
                params.append("showAll", "true");
            }

            // Add subscription type filter if not "all"
            if (selectedSubscriptionType !== "all") {
                params.append("subscriptionType", selectedSubscriptionType);
            }

            const response = await fetch(`/api/admin/search-users?${params.toString()}`);
            if (response.ok) {
                const data = await response.json();
                setSearchResults(data.users || []);
            } else {
                console.error("Search failed:", await response.text());
                setSearchResults([]);
            }
        } catch (error) {
            console.error("Search error:", error);
            setSearchResults([]);
        } finally {
            setIsLoading(false);
        }
    };

    const clearSearch = () => {
        setSearchTerm("");
        setSelectedSubscriptionType("all");
        setSearchResults([]);
    };

    const handleShowAllUsers = async () => {
        setSearchTerm("");
        setSelectedSubscriptionType("all");
        setIsLoading(true);

        try {
            const response = await fetch(`/api/admin/search-users?showAll=true`);
            if (response.ok) {
                const data = await response.json();
                setSearchResults(data.users || []);
            } else {
                console.error("Failed to fetch all users:", await response.text());
                setSearchResults([]);
            }
        } catch (error) {
            console.error("Error fetching all users:", error);
            setSearchResults([]);
        } finally {
            setIsLoading(false);
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === "Enter") {
            handleSearch();
        }
    };

    const handleEditUser = (user: any) => {
        setCurrentUser(user);
        setIsPremium(user.subscriptionType === "premium");
        setSubscriptionPlan(user.subscriptionPlan || "monthly");
        setStartDate(user.subscriptionStartDate || "");
        setEndDate(user.subscriptionEndDate || "");
        setPromptsUsed(user.promptsUsed || 0);
        onOpen();
    };

    const handleSaveUser = async () => {
        if (!currentUser) return;

        setIsLoading(true);
        try {
            const userData = {
                userId: currentUser.id,
                subscriptionType: isPremium ? "premium" : "free",
                promptsUsed: parseInt(promptsUsed.toString()),
                subscriptionStartDate: startDate
            };

            // Only include subscription plan and end date for premium users
            if (isPremium) {
                Object.assign(userData, {
                    subscriptionPlan,
                    subscriptionStartDate: startDate,
                    subscriptionEndDate: endDate
                });
            }

            const response = await fetch('/api/admin/update-user', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(userData),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to update user');
            }

            const updatedResults = searchResults.map((user: any) => {
                if (user.id === currentUser.id) {
                    return {
                        ...user,
                        subscriptionType: isPremium ? "premium" : "free",
                        subscriptionPlan: isPremium ? subscriptionPlan : null,
                        subscriptionStartDate: isPremium ? startDate : null,
                        subscriptionEndDate: isPremium ? endDate : null,
                        promptsUsed
                    };
                }
                return user;
            });

            setSearchResults(updatedResults);
            toast.success("User updated successfully!");
            onClose();
        } catch (error) {
            console.error("Error updating user:", error);
            toast.error(error instanceof Error ? error.message : "Failed to update user");
        } finally {
            setIsLoading(false);
        }
    };

    const handleDeleteConfirm = (user: any) => {
        setUserToDelete(user);
        onDeleteOpen();
    };

    const handleDeleteUser = async () => {
        if (!userToDelete) return;

        setIsLoading(true);
        try {
            const response = await fetch('/api/admin/delete-account?userId=' + userToDelete.id, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to delete user');
            }

            const updatedResults = searchResults.filter((user: any) => user.id !== userToDelete.id);
            setSearchResults(updatedResults);
            toast.success("User deleted successfully!");
            onDeleteClose();
        } catch (error) {
            console.error("Error deleting user:", error);
            toast.error("Failed to delete user");
        } finally {
            setIsLoading(false);
        }
    };

    if (!isLoaded || checkingAdmin || !isAdmin) {
        return (
            <div className="flex h-full items-center justify-center">
                <div className="text-center">
                    <div className="relative inline-flex items-center justify-center">
                        <div className="size-12 animate-spin rounded-full border-3 border-gray-300 border-t-[#24AE7C]"></div>
                    </div>
                    <div className="mt-4">
                        <p className="font-medium text-gray-100">Verifying admin access...</p>
                    </div>
                </div>
            </div>
        );
    }

    const pages = Math.ceil(searchResults.length / rowsPerPage);
    const items = searchResults.slice((page - 1) * rowsPerPage, page * rowsPerPage);

    return (
        <div className="space-y-8">
            <div className="mb-6 flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
                <h1 className="text-2xl font-bold text-white">Admin Dashboard</h1>
                <div className="flex flex-wrap gap-3">
                    <Link href="/dashboard/announcements">
                        <Button
                            color="primary"
                            variant="flat"
                            className="border border-indigo-700/50 bg-indigo-700/40 text-indigo-300"
                            startContent={<FiBell size={16} />}
                        >
                            Manage Announcements
                        </Button>
                    </Link>
                </div>
            </div>

            {/* User Statistics Cards */}
            <div className="mb-8 grid grid-cols-1 gap-4 md:grid-cols-3">
                <Card className="border border-blue-800/50 bg-gradient-to-br from-blue-900/70 to-blue-800/40 p-4 backdrop-blur-sm">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-gray-400">Total Users</p>
                            <h3 className="text-3xl font-bold text-white">{userStats.totalUsers}</h3>
                            <p className="mt-2 text-sm text-blue-300">Registered accounts</p>
                        </div>
                        <div className="rounded-full bg-blue-700/40 p-3">
                            <FiUsers size={24} className="text-blue-300" />
                        </div>
                    </div>
                </Card>

                <Card className="border border-green-800/50 bg-gradient-to-br from-green-900/70 to-green-800/40 p-4 backdrop-blur-sm">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-gray-400">Premium Users</p>
                            <h3 className="text-3xl font-bold text-white">{userStats.premiumUsers}</h3>
                            <div className="mt-2 flex flex-wrap gap-2 text-xs">
                                <span className="text-green-300">Weekly: {userStats.weeklyPlanUsers}</span>
                                <span className="text-green-300">Monthly: {userStats.monthlyPlanUsers}</span>
                                <span className="text-green-300">Yearly: {userStats.yearlyPlanUsers}</span>
                            </div>
                        </div>
                        <div className="rounded-full bg-green-700/40 p-3">
                            <FiDollarSign size={24} className="text-green-300" />
                        </div>
                    </div>
                </Card>

                <Card className="border border-indigo-800/50 bg-gradient-to-br from-indigo-900/70 to-indigo-800/40 p-4 backdrop-blur-sm">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-gray-400">Free Users</p>
                            <h3 className="text-3xl font-bold text-white">{userStats.freeUsers}</h3>
                            <p className="mt-2 text-sm text-indigo-300">
                                {userStats.totalUsers > 0
                                    ? `${Math.round((userStats.freeUsers / userStats.totalUsers) * 100)}% of total users`
                                    : '0% of total users'
                                }
                            </p>
                        </div>
                        <div className="rounded-full bg-indigo-700/40 p-3">
                            <FiStar size={24} className="text-indigo-300" />
                        </div>
                    </div>
                </Card>
            </div>

            <Card className="border border-slate-800/60 bg-[#0f172a]/60 p-6 backdrop-blur-sm">
                <h2 className="mb-4 text-xl font-semibold text-white">User Management</h2>
                <div className="mb-4 flex flex-wrap items-center gap-2">
                    <div className="relative max-w-md grow">
                        <Input
                            type="text"
                            placeholder="Search users by email or name (partial match)"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            onKeyDown={handleKeyDown}
                            aria-label="Search users"
                            startContent={<FiSearch className="text-gray-400" />}
                            endContent={
                                searchTerm && (
                                    <FiX
                                        className="cursor-pointer text-gray-400 hover:text-gray-200"
                                        onClick={clearSearch}
                                        aria-label="Clear search"
                                    />
                                )
                            }
                            variant="bordered"
                            classNames={{
                                input: "text-white",
                                inputWrapper: "border-gray-600 hover:border-[#24AE7C]"
                            }}
                        />
                    </div>
                    <Select
                        placeholder="Filter by subscription"
                        value={selectedSubscriptionType}
                        onChange={(e) => setSelectedSubscriptionType(e.target.value)}
                        className="w-48"
                        variant="bordered"
                        aria-label="Filter users by subscription type"
                        classNames={{
                            trigger: "border-gray-600 text-white h-10",
                            value: "text-white"
                        }}
                    >
                        <SelectItem key="all" value="all">All Users</SelectItem>
                        <SelectItem key="free" value="free">Free Users</SelectItem>
                        <SelectItem key="premium" value="premium">All Premium</SelectItem>
                        <SelectItem key="weekly" value="weekly">Weekly Plan</SelectItem>
                        <SelectItem key="monthly" value="monthly">Monthly Plan</SelectItem>
                        <SelectItem key="yearly" value="yearly">Yearly Plan</SelectItem>
                    </Select>
                    <Button
                        color="primary"
                        className="bg-[#24AE7C] text-white"
                        onClick={handleSearch}
                        isLoading={isLoading}
                        size="sm"
                    >
                        Search
                    </Button>
                    <Button
                        color="default"
                        variant="flat"
                        className="border border-gray-700 text-white"
                        onClick={handleShowAllUsers}
                        size="sm"
                    >
                        Show All Users
                    </Button>
                </div>

                {searchResults.length > 0 ? (
                    <div>
                        <Table
                            aria-label="User search results"
                            classNames={{
                                base: "max-w-full overflow-auto",
                                table: "min-w-full",
                                thead: "bg-gray-900/50",
                                th: "text-gray-300 font-medium",
                                tr: "border-b border-gray-800/30 hover:bg-gray-800/20",
                                td: "text-gray-300"
                            }}
                        >
                            <TableHeader>
                                <TableColumn>NAME</TableColumn>
                                <TableColumn>EMAIL</TableColumn>
                                <TableColumn>SUBSCRIPTION</TableColumn>
                                <TableColumn>PROMPTS USED</TableColumn>
                                <TableColumn>ACTIONS</TableColumn>
                            </TableHeader>
                            <TableBody>
                                {items.map((user: any) => (
                                    <TableRow key={user.id}>
                                        <TableCell>{user.name || "N/A"}</TableCell>
                                        <TableCell>{user.email}</TableCell>
                                        <TableCell>
                                            {user.subscriptionType === "premium" ? (
                                                <div>
                                                    <span className="font-medium text-green-400">Premium</span>
                                                    {user.subscriptionPlan && (
                                                        <span className="ml-1 text-xs text-gray-400">
                                                            ({user.subscriptionPlan})
                                                        </span>
                                                    )}
                                                    {user.subscriptionStartDate && (
                                                        <div className="mt-1 text-xs text-gray-400">
                                                            Starts: {new Date(user.subscriptionStartDate).toLocaleDateString()}
                                                        </div>
                                                    )}
                                                    {user.subscriptionEndDate && (
                                                        <div className="mt-1 text-xs text-gray-400">
                                                            Ends: {new Date(user.subscriptionEndDate).toLocaleDateString()}
                                                        </div>
                                                    )}
                                                </div>
                                            ) : (
                                                <span className="text-gray-400">Free</span>
                                            )}
                                        </TableCell>
                                        <TableCell>
                                            <span>{user.promptsUsed || 0}</span>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex gap-2">
                                                <Button
                                                    size="sm"
                                                    color="primary"
                                                    className="bg-blue-700 text-xs"
                                                    onClick={() => handleEditUser(user)}
                                                    startContent={<FiEdit size={14} />}
                                                >
                                                    Edit
                                                </Button>
                                                <Button
                                                    size="sm"
                                                    color="danger"
                                                    className="bg-red-800 text-xs"
                                                    onClick={() => handleDeleteConfirm(user)}
                                                    startContent={<FiTrash2 size={14} />}
                                                >
                                                    Delete
                                                </Button>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>

                        {pages > 1 && (
                            <div className="mt-4 flex justify-center">
                                <Pagination
                                    total={pages}
                                    initialPage={1}
                                    page={page}
                                    onChange={setPage}
                                    size="sm"
                                    classNames={{
                                        item: "text-white bg-gray-800/50 border border-gray-700",
                                        cursor: "bg-[#24AE7C] text-white"
                                    }}
                                />
                            </div>
                        )}
                    </div>
                ) : (searchTerm || selectedSubscriptionType !== "all") && !isLoading ? (
                    <div className="py-8 text-center text-gray-400">
                        No users found matching your search criteria
                    </div>
                ) : !isLoading ? (
                    <div className="py-8 text-center text-gray-400">
                        Use the search box or filter options above to find users, or click &quot;Show All Users&quot;
                    </div>
                ) : null}
            </Card>

            <Modal
                isOpen={isOpen}
                onClose={onClose}
                backdrop="blur"
                classNames={{
                    base: "bg-[#0f172a] border border-gray-800 shadow-xl",
                    header: "border-b border-gray-800",
                    footer: "border-t border-gray-800"
                }}
            >
                <ModalContent>
                    {() => (
                        <>
                            <ModalHeader className="text-white">
                                Edit User
                            </ModalHeader>
                            <ModalBody>
                                {currentUser && (
                                    <div className="space-y-4">
                                        <div>
                                            <p className="mb-1 text-sm text-gray-400">User Info:</p>
                                            <p className="font-medium text-white">{currentUser.name || "N/A"}</p>
                                            <p className="text-sm text-gray-300">{currentUser.email}</p>
                                        </div>

                                        <div className="flex items-center justify-between">
                                            <p className="text-white">Premium Account</p>
                                            <Switch
                                                isSelected={isPremium}
                                                onValueChange={setIsPremium}
                                                color="success"
                                                aria-label="Toggle premium account status"
                                            />
                                        </div>

                                        {isPremium && (
                                            <>
                                                <div>
                                                    <p className="mb-1 text-sm text-gray-400">Subscription Plan:</p>
                                                    <Select
                                                        value={subscriptionPlan}
                                                        onChange={(e) => setSubscriptionPlan(e.target.value)}
                                                        variant="bordered"
                                                        aria-label="Select subscription plan"
                                                        classNames={{
                                                            trigger: "border-gray-600 text-white",
                                                            value: "text-white"
                                                        }}
                                                    >
                                                        <SelectItem key="weekly" value="weekly">
                                                            Weekly
                                                        </SelectItem>
                                                        <SelectItem key="monthly" value="monthly">
                                                            Monthly
                                                        </SelectItem>
                                                        <SelectItem key="yearly" value="yearly">
                                                            Yearly
                                                        </SelectItem>
                                                    </Select>
                                                </div>
                                                <div>
                                                    <p className="mb-1 text-sm text-gray-400">Subscription Start Date:</p>
                                                    <Input
                                                        type="date"
                                                        value={startDate}
                                                        onChange={(e) => setStartDate(e.target.value)}
                                                        variant="bordered"
                                                        aria-label="Subscription start date"
                                                        classNames={{
                                                            input: "text-white",
                                                            inputWrapper: "border-gray-600"
                                                        }}
                                                    />
                                                </div>
                                                <div>
                                                    <p className="mb-1 text-sm text-gray-400">Subscription End Date:</p>
                                                    <Input
                                                        type="date"
                                                        value={endDate}
                                                        onChange={(e) => setEndDate(e.target.value)}
                                                        variant="bordered"
                                                        aria-label="Subscription end date"
                                                        classNames={{
                                                            input: "text-white",
                                                            inputWrapper: "border-gray-600"
                                                        }}
                                                    />
                                                </div>
                                            </>
                                        )}

                                        <div>
                                            <p className="mb-1 text-sm text-gray-400">Prompts Used:</p>
                                            <Input
                                                type="number"
                                                min="0"
                                                value={promptsUsed.toString()}
                                                onChange={(e) => setPromptsUsed(parseInt(e.target.value) || 0)}
                                                variant="bordered"
                                                aria-label="Number of prompts used"
                                                classNames={{
                                                    input: "text-white",
                                                    inputWrapper: "border-gray-600"
                                                }}
                                            />
                                        </div>
                                    </div>
                                )}
                            </ModalBody>
                            <ModalFooter>
                                <Button color="default" variant="light" onPress={onClose}>
                                    Cancel
                                </Button>
                                <Button
                                    color="primary"
                                    className="bg-[#24AE7C]"
                                    onPress={handleSaveUser}
                                    isLoading={isLoading}
                                >
                                    Save Changes
                                </Button>
                            </ModalFooter>
                        </>
                    )}
                </ModalContent>
            </Modal>

            <Modal
                isOpen={isDeleteOpen}
                onClose={onDeleteClose}
                backdrop="blur"
                classNames={{
                    base: "bg-[#0f172a] border border-gray-800 shadow-xl",
                    header: "border-b border-gray-800",
                    footer: "border-t border-gray-800"
                }}
            >
                <ModalContent>
                    {() => (
                        <>
                            <ModalHeader className="text-white">
                                Confirm Deletion
                            </ModalHeader>
                            <ModalBody>
                                {userToDelete && (
                                    <div>
                                        <p className="mb-4 text-white">Are you sure you want to delete this user?</p>
                                        <div className="rounded-md bg-gray-800/50 p-3">
                                            <p className="font-medium text-white">{userToDelete.name || "N/A"}</p>
                                            <p className="text-sm text-gray-300">{userToDelete.email}</p>
                                        </div>
                                        <p className="mt-4 text-sm text-red-500">
                                            This action cannot be undone. All user data will be permanently removed.
                                        </p>
                                    </div>
                                )}
                            </ModalBody>
                            <ModalFooter>
                                <Button color="default" variant="light" onPress={onDeleteClose}>
                                    Cancel
                                </Button>
                                <Button
                                    color="danger"
                                    className="bg-red-700"
                                    onPress={handleDeleteUser}
                                    isLoading={isLoading}
                                >
                                    Delete User
                                </Button>
                            </ModalFooter>
                        </>
                    )}
                </ModalContent>
            </Modal>
        </div>
    );
}