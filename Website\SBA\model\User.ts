import mongoose from 'mongoose';
import crypto from 'crypto';

// Helper function to generate a random string for secretKey
const generateRandomKey = () => {
  return crypto.randomBytes(32).toString('hex');
};

const userSchema = new mongoose.Schema({
  clerkId: { type: String, unique: true, sparse: true }, // Made optional with sparse: true
  name: { type: String },
  email: { type: String, required: true, unique: true },
  password: { type: String }, // Optional if keeping email/password sign-up
  provider: { type: String, default: 'clerk' },
  avatar: { type: String, default: '/default-avatar.png' },
  countryCode: { type: String, default: 'US' }, // Added country code field
  subscriptionType: { type: String, default: 'free' },
  subscriptionPlan: { type: String, default: '' },
  subscriptionStartDate: { type: Date },
  subscriptionEndDate: { type: Date },
  promptsUsed: { type: Number, default: 0 },
  lastResetDate: { type: Date },
  secretKey: { type: String, default: generateRandomKey, unique: true },
  isAdmin: { type: Boolean, default: false },
  resetCode: { type: String },
  resetCodeExpires: { type: Date },
});

export const User = mongoose.models.User || mongoose.model('User', userSchema);