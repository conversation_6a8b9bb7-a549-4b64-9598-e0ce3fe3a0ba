import { randomBytes } from "crypto";

import { clerkClient } from "@clerk/clerk-sdk-node";
import { getAuth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";

import connectDB from "@/lib/mongodb";
import { User } from "@/model/User";


// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  console.log("API key route triggered");
  
  try {
    // Get the authentication data from Clerk
    const auth = getAuth(req);
    const { userId } = auth;
    
    console.log("Auth check: userId =", userId);
    
    if (!userId) {
      console.log("No userId found in request");
      return NextResponse.json({ message: "Unauthorized", error: "No userId" }, { status: 401 });
    }

    // Connect to MongoDB
    console.log("Connecting to MongoDB");
    await connectDB();
    console.log("MongoDB connected");
    
    try {
      // Get the current user from Clerk to determine authentication method
      const clerkUser = await clerkClient.users.getUser(userId);
      
      // Extract primary email from Clerk user data
      const primaryEmail = clerkUser.emailAddresses.find(email => 
        email.id === clerkUser.primaryEmailAddressId
      )?.emailAddress;
      
      if (!primaryEmail) {
        console.log("No primary email found for user:", userId);
        return NextResponse.json({ message: "User email not found" }, { status: 400 });
      }
      
      // Determine the current auth method
      const isUsingOAuth = clerkUser.externalAccounts.some(account => account.provider === "google");
      console.log("User is using OAuth:", isUsingOAuth);
      
      // Generate a random API key for new users
      const secretKey = randomBytes(16).toString('hex');
      
      // First try to find the user by their Clerk ID
      console.log("Finding user with clerkId:", userId);
      let user = await User.findOne({ clerkId: userId });
      
      if (!user) {
        console.log("User not found by clerkId, checking by email:", primaryEmail);
        const existingUserByEmail = await User.findOne({ email: primaryEmail });
        
        if (existingUserByEmail) {
          // If user exists with this email but with different auth method
          if (isUsingOAuth && existingUserByEmail.provider === 'email') {
            console.log("Found traditional account with same email, but user is using OAuth now");
            // Create a new record for OAuth login to separate from traditional login
            user = new User({
              clerkId: userId,
              name: clerkUser.firstName ? `${clerkUser.firstName} ${clerkUser.lastName || ''}` : 'New User',
              email: primaryEmail,
              avatar: clerkUser.imageUrl || '/default-avatar.png',
              role: "user",
              subscriptionType: "free",
              promptsUsed: 0,
              isAdmin: false,
              provider: "google", // Mark as Google provider
              secretKey,
              createdAt: new Date()
            });
            await user.save();
            console.log("Created separate OAuth user record for:", primaryEmail);
          } else {
            // Link existing account by updating the ClerkID
            existingUserByEmail.clerkId = userId;
            
            // Generate a new key if they don't have one
            if (!existingUserByEmail.secretKey) {
              existingUserByEmail.secretKey = secretKey;
            }
            
            if (isUsingOAuth) {
              existingUserByEmail.provider = "google";
            }
            
            await existingUserByEmail.save();
            user = existingUserByEmail;
            console.log("Updated existing user with new ClerkID:", userId);
          }
        } else {
          // Create a new user with the Clerk ID and a secret key
          user = new User({
            clerkId: userId,
            name: clerkUser.firstName ? `${clerkUser.firstName} ${clerkUser.lastName || ''}` : 'New User',
            email: primaryEmail,
            avatar: clerkUser.imageUrl || '/default-avatar.png',
            role: "user",
            subscriptionType: "free",
            promptsUsed: 0,
            isAdmin: false,
            provider: isUsingOAuth ? "google" : "clerk",
            secretKey,
            createdAt: new Date()
          });
          
          await user.save();
          console.log("New user created with ID and API key:", user._id);
        }
      } else {
        console.log("User found:", user._id);
        
        // Update provider information if needed
        if (isUsingOAuth && user.provider !== "google") {
          user.provider = "google";
          await user.save();
          console.log("Updated user provider to Google");
        }
        
        // If user exists but doesn't have a secret key, generate one
        if (!user.secretKey) {
          user.secretKey = secretKey;
          await user.save();
          console.log("Generated new API key for existing user");
        }
      }
      
      // Return the API key
      return NextResponse.json({
        apiKey: user.secretKey || "",
        status: 'success',
        message: 'API key fetched successfully'
      });
    } catch (clerkError) {
      console.error("Error with Clerk authentication:", clerkError);
      return NextResponse.json({ 
        message: "Error processing authentication", 
        error: clerkError instanceof Error ? clerkError.message : "Clerk API error" 
      }, { status: 500 });
    }
  } catch (error) {
    console.error("API key error:", error);
    return NextResponse.json({ 
      message: "Internal server error", 
      error: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 });
  }
}