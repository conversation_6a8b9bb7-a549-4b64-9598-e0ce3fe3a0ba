import { NextResponse } from 'next/server';
import { Resend } from 'resend';

import ContactEmail from '@/emails/EmailTemplate'; // Admin email template
import FollowUpEmail from '@/emails/FollowUpEmail'; // User follow-up email template

const resend = process.env.RESEND_API_KEY
  ? new Resend(process.env.RESEND_API_KEY)
  : null;

export async function POST(request: Request) {
  if (!resend) {
    console.error('RESEND_API_KEY is not set');
    return NextResponse.json(
      { error: 'Server configuration error: Missing RESEND_API_KEY' },
      { status: 500 }
    );
  }

  try {
    const { email, name, subject, message } = await request.json();
    console.log('Received form data:', { email, name, subject, message });

    // Send email to admin/support
    const adminEmail = await resend.emails.send({
      from: '<EMAIL>',
      to: ['<EMAIL>'],
      reply_to: email,
      subject: `New Contact Form Submission: ${subject}`,
      text: message,
      react: ContactEmail({ name, email, subject, message }),
    });

    console.log('Admin email sent successfully:', adminEmail);

    // Send follow-up email to the user
    const userEmail = await resend.emails.send({
      from: '<EMAIL>',
      to: [email],
      subject: 'We’ve Received Your Request',
      text: `Dear ${name},\n\nThank you for reaching out to Interview Cracker. We have successfully received your submission.`,
      react: FollowUpEmail({
        name,
        time: new Date().toLocaleString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: 'numeric',
          minute: 'numeric',
          timeZoneName: 'short',
        }),
        message: '', // Not needed for success case
        type: 'success',
      }),
    });

    console.log('User follow-up email sent successfully:', userEmail);

    return NextResponse.json({ data: { adminEmail, userEmail } });
  } catch (error: any) {
    console.error('Email sending error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

// Handle non-POST requests
export async function GET() {
  return NextResponse.json({ error: 'Method Not Allowed' }, { status: 405 });
}